---
- name: Setup cqa git repo
  hosts: all
  vars_files: ../global_vars.yml

  gather_facts: true

  tasks:
  - name: Setup sanity test git repo
    block:
    - name: Remove C:\sanity_test_windows folder if exists
      win_file:
        path: C:\sanity_test_windows
        state: absent

    - name: git lfs install
      win_command: git lfs install

    - name: git clone sanity_test_windows repo
      win_command: git clone https://{{ sanity_win_token }}@gitlab-master.nvidia.com/yiczhang/sanity_test_windows.git
      args:
        chdir: C:\

    - name: git pull"
      win_command: git pull
      args:
        chdir: C:\sanity_test_windows

    rescue:
    - name: Print when errors
      debug:
        msg: 'Setup windows test_sanity code repo failed, please check'

    - name: Store errors info
      set_fact:
        errors: "{{ errors + ['Setup windows test_sanity code repo failed'] }}"
