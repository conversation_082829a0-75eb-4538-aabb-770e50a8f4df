@echo off
set "python37_root=C:\Python37"
set "python311_root=C:\Python311"
set "link_folder=C:\PythonLinkTest"
rmdir %link_folder%
if exist "%python311_root%" (
    mklink /d "%link_folder%" "%python311_root%"
    echo Link %link_folder% to %python311_root% to run test cases
    set "result=0"
) else (
    if exist "%python37_root%" (
        mklink /d "%link_folder%" "%python37_root%"
        echo No %python311_root%
        echo Fallback to link %link_folder% to %python37_root% to run test cases
        echo You can upgrade to Python3.11 with playbook, see https://confluence.nvidia.com/display/CQS/Upgrade+Python3.7+to+Python3.11
        set "result=1"
    ) else (
        echo Folder "%python37_root%" and "%python311_root%" does not exist, exit execution
        set "result=1"
    )
)
exit %result%
