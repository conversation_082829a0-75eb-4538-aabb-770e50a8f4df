# -*- encoding: utf-8 -*-
import os
import sys
import multiprocessing
from multiprocessing import Pool, Manager, Process
import copyreg
from datetime import datetime

from cuda.cuda import CUgraphDeviceNode
from torch.distributions import Gumbel

from common_utils import *
import time
import logging
import argparse
import urllib.request as urllib
import http.client as httplib
from configparser import ConfigParser
from mobile_yaml_mgr import MobileYamlManger

path = os.getcwd()
common_case_yaml = 'C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml'
yaml_mgr = MobileYamlManger(common_case_yaml)
case_config = yaml_mgr.load()
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
host_home = case_config['global']['env']['HOST_HOME']
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH1']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH1']
    sample_2_path = sample_0_path + '/../2_Concepts_and_Techniques/'
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH1']
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH']
    sample_2_path = case_config['global']['env']['SAMPLE_2_PATH']
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH']
    device_sln = 'deviceQuery_vs2022.sln'
    streamOrderedAllocation_sln = 'streamOrderedAllocation_vs2022.sln'
    vectorAddDrv_sln = 'vectorAddDrv_vs2022.sln'
else:
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH2']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH2']
    sample_2_path = case_config['global']['env']['SAMPLE_2_PATH2']
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH2']
    device_sln = 'deviceQuery.sln'
    streamOrderedAllocation_sln = 'streamOrderedAllocation.sln'
    vectorAddDrv_sln = 'vectorAddDrv.sln'
tools_home = case_config['global']['env']['TOOLS_HOME']
platform = case_config['global']['env']['PLATFORM'].lower()
output_flag = case_config['global']['env']['OUTPUT_FLAG']
password1 = case_config['global']['env']['CQA_PASSWORD']
user1 = case_config['global']['env']['CQA_USER']
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
cuda_major = case_config['global']['env']['CUDA_MAJOR']
driver_branch = case_config['global']['env']['DRV_BRANCH']
cuda_min = case_config['global']['env']['CUDA_MINOR']
date1 = case_config['global']['env']['DATE1']
dvs_build = case_config['global']['env']['DVS_BUILD']
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
cuda_app_download_to_path = case_config['global']['env']['CUDA_APP_PATH']
cuda_app_run_path = case_config['global']['env']['CUDA_APP_RUN_PATH']
cupti_download_to_path = case_config['global']['env']['CUPTI_PATH']
cupti_path = case_config['global']['env']['CUPTI_PATH']
sanitizer_path = case_config['global']['env']['SANITIZER_PATH']
rebel_path = case_config['global']['env']['REBEL_PATH']
cupti_host_path = case_config['global']['env']['CUPTI_HOST_PATH']
cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH']
rebel_download_to_path = case_config['global']['env']['REBEL_PATH']
rebel_run_path = case_config['global']['env']['REBEL_RUN_PATH']
sanitizer_download_to_path = case_config['global']['env']['SANITIZER_PATH']
sanitizer_run_path = case_config['global']['env']['SANITIZER_RUN_PATH']
logger = logging.getLogger()
logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('common_case.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)
log_name = 'common_case.txt'


def get_sm():
    """
    Get the SM of the GPU on Windows
    """
    sm_cmd = case_config['PREPARE']['CMD3'] % (sample_1_path, device_sln)
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        # To resolve the "ZWNBSP in SM string" problem, updated by Alex Li on Sep 7, 2023
        sm_output = sm_out['output']
        sm_temp = sm_output.split('\n')[-1].strip(' ')
        print("sm_temp is %s" % sm_temp)
        print("sm_temp(encoding with UTF-8-sig) is %s" % sm_temp.strip().encode('UTF-8-sig'))
        sm = sm_temp.encode('ascii', 'ignore').decode().strip()
        print("sm is %s" % sm)
        return float(sm)
    else:
        logger.info('we get the sm failed')
        return 0.0


SM = get_sm()


# CUDA Device Driver Mode (TCC or WDDM): WDDM (Windows Display Driver Model) or TCC (Tesla Compute Cluster Driver)
def get_driver_mode():
    """
    Get the driver mode on Windows, return TCC or WDDM

    Attention: This function has been obsoleted since 2024.07.17. Please use the new API common_get_driver_mode() in common_utils.py instead.
    """
    tcc_wddm_cmd = case_config['PREPARE']['CMD_TCC_WDDM'] % (sample_1_path, device_sln)
    print(tcc_wddm_cmd)
    tcc_wddm_out = run_loc_cmd(tcc_wddm_cmd)
    if tcc_wddm_out.succeeded:
        tcc_wddm = tcc_wddm_out['output']
        return 'TCC' if 'TCC' in tcc_wddm else 'WDDM'
    else:
        logger.info('we get the TCC or WDDM failed')


def prepare(tool=None):
    """
    Download and extract the smoke pkg
    param: tool ('rebel' 'sanitizer' 'cuda_app' 'cupti')
    type param: str
    """
    os_type = get_os_type()
    if os_type == 'woa':
        download_path = get_dvs_package(tool, 'woa', cuda_short_version)
    else:
        download_path = get_dvs_package(tool, 'windows', cuda_short_version)
    index = tool.upper() + '_PATH'
    download_to_path = case_config['global']['env'][index]
    package = download_path.split('/')[-1]
    logger.info('we will download the package from %s' % download_path)
    logger.info('we will download the package to %s ' % download_to_path)
    mkdir(download_to_path)
    os.chdir(download_to_path)
    create_file(download_to_path, 'version_pkg.txt')
    if check_file(download_to_path, 'version_pkg.txt') is True:
        logger.info('we had download the package %s, no need download again' % package)
        index1 = tool.upper() + '_RUN_PATH'
        sample_path = case_config['global']['env'][index1]
        if tool.lower() == 'cupti' and os_type == 'woa':
            sample_path = cupti_path + '/target/windows-desktop-win10-t23x-a64'
        if tool.lower() == 'rebel' and os_type == 'woa':
            sample_path = rebel_path + '/Rebel-Release-public-test/target/windows-desktop-win10-t23x-a64/cuda'
        return sample_path
    else:
        cmd2 = "cd %s; lftp -c 'set ssl:verify-certificate no; glob -- pget -n 80 %s'" % (download_to_path, download_path)
        logger.info('we will use the command to download package, the command is %s ' % cmd2)
        out2 = run_loc_cmd(cmd2)
        if out2.succeeded:
            if tool == 'sanitizer':
                cmd3 = 'cd %s; unzip -o %s; unzip -o Sanitizer_Public_Release_DVS_amd64.zip' % (download_to_path, package)
                sample_path = sanitizer_run_path
            elif tool == 'cupti':
                cmd3 = 'cd %s; unzip -o %s; unzip -o CUDA-AgoraCupti-package.zip' % (download_to_path, package)
                sample_path = cupti_run_path
                if os_type == 'woa':
                    sample_path = cupti_path + '/target/windows-desktop-win10-t23x-a64'
            elif tool == 'rebel':
                cmd3 = 'cd %s; unzip -o %s; unzip -o Rebel-Release-public-test-Windows.*.zip' % (download_to_path, package)
                sample_path = rebel_run_path
                if os_type == 'woa':
                    cmd3 = 'cd %s; unzip -o %s; unzip -o Rebel-Release-public-test-WoA.*.zip' % (download_to_path, package)
                    sample_path = rebel_path + '/Rebel-Release-public-test/target/windows-desktop-win10-t23x-a64/cuda'
            else:
                cmd3 = 'cd %s; unzip -o %s' % (download_to_path, package)
                sample_path = cuda_app_run_path
            logger.info('we will use the command to extract package, the command is %s ' % cmd3)
            out3 = run_loc_cmd(cmd3, timeout=300)
            if out3.succeeded:
                logger.info('extract the %s package successful', tool)
                with open('%s/version_pkg.txt' % download_to_path, 'a+') as f:
                    f.write(package)
                    f.write('\n')
            else:
                logger.info('extract the %s package failed', tool)
        else:
            logger.info('Failed to download %s package', tool)
    return sample_path


class ExternalSupport():
    def __init__(self, name, log_name, log_path, cupti_test, sm=None):
        self.name = name
        self.result = {}
        self.log_name = log_name
        self.log_path = log_path
        self.cupti_test = True
        self.sm = sm

    def sample(self, way):
        """
        prepare sample
        para: way (cupti, rebel, ncu, cuda_app, build)
        type: str
        """
        if way != 'build':
            sample_path = prepare(way)
            # 334 380 391
            if way == 'cuda_app':
                sample_path = sample_path + '/' + self.name.strip('.exe')
                sample_cmd1 = case_config['SAMPLE']['CMD1'] % (sample_path, '*', self.log_path)
                check_result(sample_cmd1, 'prepare sample to log path by ---%s' % sample_cmd1, self.log_name, self.result)
                if self.cupti_test:
                    prepare('cupti')
                    sample_cmd2 = case_config['SAMPLE']['CMD1'] % (sample_path, '*', cupti_run_path)
                    check_result(sample_cmd2, 'prepare sample to cupti path by ---%s' % sample_cmd2, self.log_name, self.result)
            else:
                sample_cmd1 = case_config['SAMPLE']['CMD1'] % (sample_path, self.name, self.log_path)
                check_result(sample_cmd1, 'prepare sample to log path by ---%s' % sample_cmd1, self.log_name, self.result)
                if self.cupti_test and way != 'cupti':
                    prepare('cupti')
                    sample_cmd2 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                    check_result(sample_cmd2, 'prepare sample for cupti by ---%s' % sample_cmd2, self.log_name, self.result)
        else:
            if self.name == 'alloca.exe':
                sample_cmd3 = case_config['SAMPLE']['ALLOCA'] % (self.log_path, user, password, base_url, user, password, base_url, self.sm, self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
            elif self.name == 'fp16-basic.exe':
                sample_cmd3 = case_config['SAMPLE']['FB16-BASIC'] % (self.log_path, user, password, base_url, self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'relative-return.exe':
                sample_cmd3 = case_config['SAMPLE']['RELATIVE-RETUREN'] % (self.log_path, user, password, base_url, self.sm, self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
            elif self.name == 'CuNvtxContextStream.exe':
                sample_cmd3 = case_config['SAMPLE']['CUNVTXCONTEXTSTREAM'] % (self.log_path, user, password, base_url)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'vectorAddDrv.exe':
                sample_cmd3 = case_config['SAMPLE']['VECTORADDDRV_1'] % (sample_0_path, sample_0_path, user, password, base_url, vectorAddDrv_sln)
                check_result(sample_cmd3, 'get source file and build to bin path by ---%s' % sample_cmd3, self.log_name, self.result)
                sample_cmd3_2 = case_config['SAMPLE']['VECTORADDDRV_2'] % (sample_0_path, self.log_path, sample_bin_path, self.name, self.log_path, sample_0_path, vectorAddDrv_sln)
                check_result(sample_cmd3_2, 'get source file to log path by ---%s' % sample_cmd3_2, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, '*', cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'vectorAddDrv_readonly.exe':
                sample_cmd3_1 = case_config['SAMPLE']['PREPARE_CMD'] % self.log_path
                run_loc_cmd(sample_cmd3_1)
                for file1 in ['vectorAddDrv_readonly.cpp', 'vectorAdd_kernel.cu']:
                    sample_cmd3_2 = case_config['SAMPLE']['VECTORADDDRV_READONLY_CMD1'] % (self.log_path, user, password, base_url, file1)
                    run_loc_cmd(sample_cmd3_2)
                sample_cmd3_2 = case_config['SAMPLE']['VECTORADDDRV_READONLY_CMD2'] % (self.log_path, self.log_path)
                check_result(sample_cmd3_2, 'build sample to log path by ---%s' % sample_cmd3_2, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['VECTORADDDRV_READONLY_CMD3'] % (self.log_path, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'streamOrderedAllocation.exe':
                sample_cmd3 = case_config['SAMPLE']['STREANORDERED'] % (sample_2_path, streamOrderedAllocation_sln, sample_bin_path, self.name, self.log_path)
                check_result(sample_cmd3, 'build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'test.exe':
                for file in ['test.exe', 'Makefile', 'test.cu', 'kernel.sm_90.cubin', 'kernel.sm_90.nvsm', 'kernel.sm_90-ori.nvsm']:
                    sample_cmd3 = case_config['SAMPLE']['TEST'] % (self.log_path, user, password, base_url, file)
                    run_loc_cmd(sample_cmd3)
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, '*', cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'l2descriptor.exe':
                if cuda_short_version < '13.0':
                    sample_cmd3 = case_config['SAMPLE']['L2DESCRIPTOR'] % (self.log_path, user, password, base_url, '', self.sm)
                else:
                    sample_cmd3 = case_config['SAMPLE']['L2DESCRIPTOR'] % (self.log_path, user, password, base_url, '-std=c++17', self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'pil-sample.exe':
                sample_cmd3 = case_config['SAMPLE']['PIL'] % (self.log_path, user, password, base_url)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'vector_atomics_globals.exe':
                sample_cmd3 = case_config['SAMPLE']['VECTORATOMIC'] % (self.log_path, user, password, base_url, self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'ctaReconfig.exe':
                if cuda_short_version < '12.9':
                    sample_cmd3 = case_config['SAMPLE']['CTARECONFIG'] % (self.log_path, user, password, base_url)
                else:
                    sample_cmd3 = case_config['SAMPLE']['CTARECONFIG_1'] % (self.log_path, user, password, base_url)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
            elif self.name == 'pilDefault.exe':
                sample_cmd3 = case_config['SAMPLE']['PILDEFAULT'] % (self.log_path, user, password, base_url)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'nvFatbin.exe':
                sample_cmd3 = case_config['SAMPLE']['NVFATBIN'] % (self.log_path, user, password, base_url, self.sm)
                check_result(sample_cmd3, 'get source file and build to log path by ---%s' % sample_cmd3, self.log_name, self.result)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            elif self.name == 'optixRaycasting.exe':
                for file in [self.name, 'glad.dll', 'glfw3.dll', 'sutil_7_sdk.dll']:
                    sample_cmd3 = case_config['SAMPLE']['CMD1'] % (optix_bin_path, file, self.log_path)
                    run_loc_cmd(sample_cmd3)
                prepare('cupti')
                sample_cmd4 = case_config['SAMPLE']['CMD1'] % (self.log_path, self.name, cupti_run_path)
                check_result(sample_cmd4, 'copy to cupti target path by ---%s' % sample_cmd4, self.log_name, self.result)
            else:
                logger.warning("please add cmd for sample build")
        return self.result

    def sanitizer(self):
        santizer_check_point1 = case_config['SANITIZER']['CHECK_POINT1']
        santizer_check_point2 = case_config['SANITIZER']['CHECK_POINT2']
        if self.name == 'cudaGraphs.exe':
            subset = case_config['SAMPLE']['SUBTEST']
            sanitizer_cmd = case_config['SANITIZER']['CMD5'] % (self.log_path, self.name, subset)
            check_result(sanitizer_cmd, 'run sanitizer cmd by {}'.format(sanitizer_cmd), self.log_name, self.result, santizer_check_point1)
        elif self.name == 'multithread.exe':
            sanitizer_cmd = case_config['SANITIZER']['multithread'] % (self.log_path, self.name)
            check_result(sanitizer_cmd, 'run sanitizer cmd by {}'.format(sanitizer_cmd), self.log_name, self.result, santizer_check_point1)
        elif self.name == 'CuGraphNodeDisable1.exe':
            santizer_memcheck = case_config['SANITIZER']['GRAP_CMD1'] % (self.log_path, self.name)
            santizer_initcheck = case_config['SANITIZER']['GRAP_CMD2'] % (self.log_path, self.name)
            santizer_synccheck = case_config['SANITIZER']['GRAP_CMD3'] % (self.log_path, self.name)
            santizer_racecheck = case_config['SANITIZER']['GRAP_CMD4'] % (self.log_path, self.name)
            check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        elif self.name == 'CtaReconfig.exe':
            sanitizer_cmd = case_config['SANITIZER']['CTA'] % (self.log_path, self.name)
            santizer_cta = case_config['SANITIZER']['CTA_CHECK']
            check_result(sanitizer_cmd, 'run sanitizer cmd by {}'.format(sanitizer_cmd), self.log_name, self.result, santizer_cta)
        elif self.name == 'cluster_test.exe':
            if '3153125' in self.log_name:
                subtest = case_config['SAMPLE']['SUB2_CLUSTER_TEST']
                santizer_memcheck = case_config['SANITIZER']['CMD1'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_initcheck = case_config['SANITIZER']['CMD2'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
            else:
                subtest = case_config['SAMPLE']['SUB_CLUSTER_TEST']
                santizer_memcheck = case_config['SANITIZER']['CMD1'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_initcheck = case_config['SANITIZER']['CMD2'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
                warning_msg = 'Warning: CUDA Dynamic Parallelism is not supported by the selected tool'
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, warning_msg)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, warning_msg)
                check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, warning_msg)
        elif self.name == 'CuFastDepLaunches.exe':
            santizer_memcheck = case_config['SANITIZER']['CMD1'] % (self.log_path, self.name + ' --nocheck')
            santizer_initcheck = case_config['SANITIZER']['CMD2'] % (self.log_path, self.name + ' --nocheck')
            santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, self.name + ' --nocheck')
            santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, self.name + ' --nocheck')
            check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        elif self.name == 'universal_function_pointers.exe':
            subtest = case_config['SAMPLE']['SUB_FPIFP']
            santizer_check_point3 = case_config['SANITIZER']['CHECK_POINT3']
            santizer_check_point4 = case_config['SANITIZER']['CHECK_POINT4']
            santizer_memcheck = case_config['SANITIZER']['SUB_CMD1'] % (self.log_path, self.name, subtest)
            santizer_initcheck = case_config['SANITIZER']['SUB_CMD2'] % (self.log_path, self.name, subtest)
            santizer_synccheck = case_config['SANITIZER']['SUB_CMD3'] % (self.log_path, self.name, subtest)
            santizer_racecheck = case_config['SANITIZER']['SUB_CMD4'] % (self.log_path, self.name, subtest)
            check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point4)
            check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point3)
            check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        elif self.name in ['kernel_large_args.exe', 'cudaIpcSanity.exe']:
            index = 'SUB_' + self.name.strip('.exe').upper()
            subtest = case_config['SAMPLE'][index]
            santizer_memcheck = case_config['SANITIZER']['SUB_CMD1'] % (self.log_path, self.name, subtest)
            santizer_initcheck = case_config['SANITIZER']['SUB_CMD2'] % (self.log_path, self.name, subtest)
            santizer_synccheck = case_config['SANITIZER']['SUB_CMD3'] % (self.log_path, self.name, subtest)
            santizer_racecheck = case_config['SANITIZER']['SUB_CMD4'] % (self.log_path, self.name, subtest)
            check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        elif self.name == 'optixRaycasting.exe':
            santizer_memcheck = case_config['SANITIZER']['OPTIX_CMD1'] % (self.log_path, self.name)
            santizer_initcheck = case_config['SANITIZER']['OPTIX_CMD2'] % (self.log_path, self.name)
            santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, self.name)
            santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, self.name)
            check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
            check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        else:
            santizer_memcheck = case_config['SANITIZER']['CMD1'] % (self.log_path, self.name)
            santizer_initcheck = case_config['SANITIZER']['CMD2'] % (self.log_path, self.name)
            santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, self.name)
            santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, self.name)
            if self.name == 'CuCnpv2.exe':
                santizer_cnp = case_config['SANITIZER']['CNP_CHECK']
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_cnp)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_cnp)
                check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_cnp)
            elif self.name == 'CuClusterLaunches.exe':
                # santizer_check_point5 = case_config['SANITIZER']['CHECK_POINT5']
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
                # check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point5)
            elif self.name == 'CuMemAllocsAsync.exe':
                santizer_check_point3 = case_config['SANITIZER']['CHECK_POINT3']
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point3)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
            else:
                check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
                check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        return self.result

    def sanitizer2(self, param=None):
        santizer_check_point1 = case_config['SANITIZER']['CHECK_POINT1']
        santizer_check_point2 = case_config['SANITIZER']['CHECK_POINT2']
        if param:
            sample_name = self.name + param
        else:
            sample_name = self.name
        santizer_memcheck = case_config['SANITIZER']['CMD1'] % (self.log_path, sample_name)
        santizer_initcheck = case_config['SANITIZER']['CMD2'] % (self.log_path, sample_name)
        santizer_synccheck = case_config['SANITIZER']['CMD3'] % (self.log_path, sample_name)
        santizer_racecheck = case_config['SANITIZER']['CMD4'] % (self.log_path, sample_name)
        check_result(santizer_memcheck, 'run (sanitizer----memcheck) by {}'.format(santizer_memcheck), self.log_name, self.result, santizer_check_point1)
        check_result(santizer_initcheck, 'run (sanitizer----initcheck) by {}'.format(santizer_initcheck), self.log_name, self.result, santizer_check_point1)
        check_result(santizer_synccheck, 'run (sanitizer----synccheck) by {}'.format(santizer_synccheck), self.log_name, self.result, santizer_check_point1)
        check_result(santizer_racecheck, 'run (sanitizer----racecheck) by {}'.format(santizer_racecheck), self.log_name, self.result, santizer_check_point2)
        return self.result

    def memcheck(self):
        memcheck_memcheck = case_config['MEMCHECK']['CMD1'] % (self.log_path, self.name)
        if self.name == 'streamOrderedAllocation.exe':
            memcheck_check_point3 = case_config['MEMCHECK']['CHECK_POINT3']
            check_result(memcheck_memcheck, 'run (memcheck----memcheck) by {}'.format(memcheck_memcheck), self.log_name, self.result, memcheck_check_point3)
        else:
            memcheck_initcheck = case_config['MEMCHECK']['CMD2'] % (self.log_path, self.name)
            memcheck_synccheck = case_config['MEMCHECK']['CMD3'] % (self.log_path, self.name)
            memcheck_racecheck = case_config['MEMCHECK']['CMD4'] % (self.log_path, self.name)
            memcheck_check_point1 = case_config['MEMCHECK']['CHECK_POINT1']
            memcheck_check_point2 = case_config['MEMCHECK']['CHECK_POINT2']
            check_result(memcheck_memcheck, 'run (memcheck----memcheck) by {}'.format(memcheck_memcheck), self.log_name, self.result, memcheck_check_point1)
            check_result(memcheck_initcheck, 'run (memcheck----initcheck) by {}'.format(memcheck_initcheck), self.log_name, self.result, memcheck_check_point1)
            check_result(memcheck_synccheck, 'run (memcheck----synccheck) by {}'.format(memcheck_synccheck), self.log_name, self.result, memcheck_check_point1)
            check_result(memcheck_racecheck, 'run (memcheck----racecheck) by {}'.format(memcheck_racecheck), self.log_name, self.result, memcheck_check_point2)
        return self.result

    def ncu(self):
        ncu_check_point1 = case_config['NCU']['CHECK_POINT1']
        ncu_check_point2 = case_config['NCU']['CHECK_POINT2']
        ncu_check_point3 = case_config['NCU']['CHECK_POINT3']
        ncu_check_point4 = case_config['NCU']['CHECK_POINT4']
        ncu_check_point5 = case_config['NCU']['CHECK_POINT5']
        if self.name == 'CuFastDepLaunches.exe':
            ncu_cmd1 = case_config['NCU']['CMD3'] % (self.log_path, self.name)
            ncu_cmd2 = case_config['NCU']['CMD4'] % (self.log_path, self.name)
            check_result(ncu_cmd1, 'run ncu cli by {}'.format(ncu_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'cudaGraphs.exe':
            subset = case_config['SAMPLE']['SUBTEST']
            ncu_cmd = case_config['NCU']['CMD2'] % (self.log_path, self.name, subset)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'multithread.exe':
            ncu_cmd = case_config['NCU']['multithread'] % (self.log_path, self.name)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'mmap_api.exe':
            ncu_cmd = case_config['NCU']['mmap_api'] % (self.log_path, self.name)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'CuGraphNodeDisable1.exe':
            ncu_cmd = case_config['NCU']['CuGraphNodeDisable'] % (self.log_path, self.name)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'test.exe':
            ncu_cmd = case_config['NCU']['test'] % (self.log_path, self.name)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'CuCnpv2.exe':
            ncu_cmd1 = case_config['NCU']['CMD1'] % (self.log_path, self.name)
            ncu_cmd2 = case_config['NCU']['CNP_CMD1'] % (self.log_path, self.name)
            ncu_cmd3 = case_config['NCU']['CNP_CMD2'] % (self.log_path, self.name)
            ncu_cnp1 = case_config['NCU']['CNP_CHECK1']
            ncu_cnp2 = case_config['NCU']['CNP_CHECK2']
            ncu_cnp3 = case_config['NCU']['CNP_CHECK3']
            ncu_cnp4 = case_config['NCU']['CNP_CHECK4']
            ncu_cnp5 = case_config['NCU']['CNP_CHECK5']
            ncu_cnp6 = case_config['NCU']['CNP_CHECK6']
            check_result(ncu_cmd1, 'run ncu cli by {}'.format(ncu_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd3, 'run ncu cli by {}'.format(ncu_cmd3), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_cnp1)
            check_result(ncu_cmd3, 'run ncu cli by {}'.format(ncu_cmd3), self.log_name, self.result, ncu_cnp2, ncu_cnp3, ncu_cnp4, ncu_cnp5, ncu_cnp6)
        elif self.name in ['DGLChild.exe', 'DGLTail.exe', 'DGLChildAndTail.exe']:
            if cuda_short_version < '13.0':
                ncu_cmd1 = case_config['NCU']['CMD1'] % (self.log_path, self.name)
            else:
                ncu_cmd1 = case_config['NCU']['CMD1_1'] % (self.log_path, self.name)
            ncu_cmd2 = case_config['NCU']['CMD1'] % (self.log_path, '--graph-profiling graph ' + self.name)
            check_result(ncu_cmd1, 'run ncu cli by {}'.format(ncu_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        # to do refactor
        elif self.name in ['cluster_test.exe', 'universal_function_pointers.exe', 'kernel_large_args.exe', 'cudaIpcSanity.exe']:
            if '3153125' in self.log_name:
                index = 'SUB2_' + self.name.strip('.exe').upper()
            else:
                index = 'SUB_' + self.name.strip('.exe').upper()
            subtest = case_config['SAMPLE'][index]
            ncu_cmd = case_config['NCU']['CMD1'] % (self.log_path, '--target-processes all ' + self.name + ' ' + subtest)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == 'DGLSibling.exe':
            sibling_warning1 = case_config['NCU']['sibling_WARN1']
            if cuda_short_version >= '13.0':
                sibling_warning1 = case_config['NCU']['sibling_WARN1_1']
            sibling_warning2 = case_config['NCU']['sibling_WARN2']
            ncu_cmd1 = case_config['NCU']['sibling_CMD1'] % (self.log_path, self.name)
            ncu_cmd2 = case_config['NCU']['sibling_CMD2'] % (self.log_path, self.name)
            check_result(ncu_cmd1, 'run ncu cli by {}'.format(ncu_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            if cuda_short_version > '12.9':
                check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
                check_result(ncu_cmd2, 'run ncu cli to check warning by {}'.format(ncu_cmd2), self.log_name, self.result, sibling_warning1)
            else:
                check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, sibling_warning1, sibling_warning2)
        elif self.name == 'CuGraphsPolymorphic.exe':
            ncu_cmd1 = case_config['NCU']['sibling_CMD1'] % (self.log_path, self.name)
            ncu_cmd2 = case_config['NCU']['sibling_CMD2'] % (self.log_path, self.name)
            check_result(ncu_cmd1, 'run ncu cli by {}'.format(ncu_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
            check_result(ncu_cmd2, 'run ncu cli by {}'.format(ncu_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        elif self.name == "cg_apitests.exe":
            ncu_cmd = case_config['NCU']['CMD1'] % (self.log_path, self.name + ' --gtest_filter=cg_sync.arrive_wait')
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        else:
            ncu_cmd = case_config['NCU']['CMD1'] % (self.log_path, self.name)
            check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        return self.result

    def ncu2(self, param=None):
        if param:
            sample_name = self.name + param
        else:
            sample_name = self.name
        ncu_check_point1 = case_config['NCU']['CHECK_POINT1']
        ncu_check_point2 = case_config['NCU']['CHECK_POINT2']
        ncu_check_point3 = case_config['NCU']['CHECK_POINT3']
        ncu_check_point4 = case_config['NCU']['CHECK_POINT4']
        ncu_check_point5 = case_config['NCU']['CHECK_POINT5']
        ncu_graph_cmd1 = case_config['NCU']['CMD1_GRAPH'] % (self.log_path, sample_name)
        ncu_graph_cmd2 = case_config['NCU']['CMD2_GRAPH'] % (self.log_path, sample_name)
        if self.name == 'CuGraphsConditional.exe':
            connode_warning1 = case_config['NCU']['ConNode_WARN1']
            connode_warning2 = case_config['NCU']['ConNode_WARN2']
            check_result(ncu_graph_cmd1, 'run ncu cli by {}'.format(ncu_graph_cmd1), self.log_name, self.result, connode_warning1, connode_warning2)
        else:
            check_result(ncu_graph_cmd1, 'run ncu cli by {}'.format(ncu_graph_cmd1), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        check_result(ncu_graph_cmd2, 'run ncu cli by {}'.format(ncu_graph_cmd2), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        return self.result

    def ncu_final(self, param=None, type="normal"):
        if param:
            sample_name = self.name + param
        else:
            sample_name = self.name
        ncu_check_point1 = case_config['NCU']['CHECK_POINT1']
        ncu_check_point2 = case_config['NCU']['CHECK_POINT2']
        ncu_check_point3 = case_config['NCU']['CHECK_POINT3']
        ncu_check_point4 = case_config['NCU']['CHECK_POINT4']
        ncu_check_point5 = case_config['NCU']['CHECK_POINT5']
        ncu_cmd = case_config['NCU']['CMD1_GRAPH'] % (self.log_path, sample_name)
        ncu_graph_cmd = case_config['NCU']['CMD2_GRAPH'] % (self.log_path, sample_name)
        if type not in ["normal", "optix"]:
            check_result(ncu_graph_cmd, 'run ncu cli by {}'.format(ncu_graph_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        check_result(ncu_cmd, 'run ncu cli by {}'.format(ncu_cmd), self.log_name, self.result, ncu_check_point1, ncu_check_point2, ncu_check_point3, ncu_check_point4, ncu_check_point5, flag=2)
        return self.result

    def run_specifial(self, cmd, *args, **kwargs):
        check_result(cmd, 'run special cmd by {}'.format(cmd), self.log_name, self.result, *args, **kwargs)
        return self.result

    def nvprof(self):
        pass

    def cupti_profile(self, app=True):
        cupti_check_point = case_config['CUPTI']['CHECK_POINT']
        cupti_check_point2 = case_config['CUPTI']['CHECK_POINT2']
        cupti_check_point3 = case_config['CUPTI']['CHECK_POINT3']
        if self.name == 'CuGraphNodeDisable.exe':
            cupti_profile_cmd1 = case_config['CUPTI']['CMD1'] % (cupti_run_path, self.name)
            cupti_profile_cmd2 = case_config['CUPTI']['CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['CMD3'] % (cupti_run_path, self.name)
            if '3069377' in self.log_name:
                cupti_profile_cmd1 = case_config['CUPTI']['CMD1'] % (cupti_run_path, self.name + ' memcpy-memset')
                cupti_profile_cmd2 = case_config['CUPTI']['CMD2'] % (cupti_run_path, self.name + ' memcpy-memset')
                cupti_profile_cmd3 = case_config['CUPTI']['CMD3'] % (cupti_run_path, self.name + ' memcpy-memset')
        elif self.name == 'test.exe':
            cupti_profile_cmd1 = case_config['CUPTI']['CMD1'] % (cupti_run_path, '\"' + self.name + ' kernel.sm_90.cubin\"')
            cupti_profile_cmd2 = case_config['CUPTI']['CMD2'] % (cupti_run_path, '\"' + self.name + ' kernel.sm_90.cubin\"')
            cupti_profile_cmd3 = case_config['CUPTI']['CMD3'] % (cupti_run_path, '\"' + self.name + ' kernel.sm_90.cubin\"')
        elif self.name == 'CuContextlessLoading.exe':
            cupti_profile_cmd1 = case_config['CUPTI']['CMD1'] % (cupti_run_path, '\"' + self.name + ' -b 0 -a 1\"')
            cupti_profile_cmd2 = case_config['CUPTI']['CMD2'] % (cupti_run_path, '\"' + self.name + ' -b 0 -a 1\"')
            cupti_profile_cmd3 = case_config['CUPTI']['CMD3'] % (cupti_run_path, '\"' + self.name + ' -b 0 -a 1\"')
        elif self.name == 'CuTma.exe':
            cupti_profile_cmd1 = case_config['CUPTI']['TMA_CMD1'] % (cupti_run_path, self.name)
            cupti_profile_cmd2 = case_config['CUPTI']['TMA_CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['TMA_CMD3'] % (cupti_run_path, self.name)
        elif self.name == 'vector_atomics_globals.exe':
            cupti_profile_cmd1 = case_config['CUPTI']['VAG_CMD1'] % (cupti_run_path, self.name)
            cupti_profile_cmd2 = case_config['CUPTI']['VAG_CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['VAG_CMD3'] % (cupti_run_path, self.name)
        elif self.name == 'cluster_test.exe' and '3153125' in self.log_name:
            cupti_profile_cmd1 = case_config['CUPTI']['CLUSTER_CMD1'] % (cupti_run_path, self.name)
            cupti_profile_cmd2 = case_config['CUPTI']['CLUSTER_CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['CLUSTER_CMD3'] % (cupti_run_path, self.name)
        elif self.name in ['DGLChild.exe', 'DGLTail.exe', 'DGLChildAndTail.exe', 'CuGraphsPolymorphic.exe', 'DGLSibling.exe']:
            cupti_profile_cmd2 = case_config['CUPTI']['GRAPH_CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['GRAPH_CMD3'] % (cupti_run_path, self.name)
            cupti_profile_cmd1 = case_config['CUPTI']['GRAPH_CMD1'] % (cupti_run_path, self.name)
        elif self.name in ['universal_function_pointers.exe', 'kernel_large_args.exe', 'cudaIpcSanity.exe', 'cluster_test.exe', 'mmap_api.exe'] and '3153125' not in self.log_name:
            index = 'SUB_' + self.name.strip('.exe').upper()
            subtest = case_config['SAMPLE'][index]
            cupti_profile_cmd1 = case_config['CUPTI']['SUB_CMD1'] % (cupti_run_path, self.name, subtest)
            cupti_profile_cmd2 = case_config['CUPTI']['SUB_CMD2'] % (cupti_run_path, self.name, subtest)
            cupti_profile_cmd3 = case_config['CUPTI']['SUB_CMD3'] % (cupti_run_path, self.name, subtest)
        else:
            cupti_profile_cmd1 = case_config['CUPTI']['CMD1'] % (cupti_run_path, self.name)
            if self.name == 'fp16-basic.exe':
                cupti_profile_cmd1 = case_config['CUPTI']['FP16_CMD1'] % (cupti_run_path, self.name)
            cupti_profile_cmd2 = case_config['CUPTI']['CMD2'] % (cupti_run_path, self.name)
            cupti_profile_cmd3 = case_config['CUPTI']['CMD3'] % (cupti_run_path, self.name)
        if app:
            check_result(cupti_profile_cmd2, 'run cupti profile-injection2 by %s' % cupti_profile_cmd2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd3, 'run cupti profile-injection3 by %s' % cupti_profile_cmd3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd1, 'run cupti profile-injection1 by %s' % cupti_profile_cmd1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        return self.result

    def cupti_profile2(self, app=True, param=None):
        cupti_check_point = case_config['CUPTI']['CHECK_POINT']
        cupti_check_point2 = case_config['CUPTI']['CHECK_POINT2']
        cupti_check_point3 = case_config['CUPTI']['CHECK_POINT3']
        if param:
            sample_name = '\"' + self.name + param + '\"'
        else:
            sample_name = self.name
        cupti_profile_cmd1_1 = case_config['CUPTI']['CMD1_1'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_2 = case_config['CUPTI']['CMD1_2'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_3 = case_config['CUPTI']['CMD1_3'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_4 = case_config['CUPTI']['CMD1_4'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_1 = case_config['CUPTI']['CMD2_1'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_2 = case_config['CUPTI']['CMD2_2'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_3 = case_config['CUPTI']['CMD2_3'] % (cupti_run_path, sample_name)
        cupti_profile_cmd3_1 = case_config['CUPTI']['CMD3_1'] % (cupti_run_path, sample_name)
        cupti_profile_cmd3_2 = case_config['CUPTI']['CMD3_2'] % (cupti_run_path, sample_name)
        cupti_profile_cmd3_3 = case_config['CUPTI']['CMD3_3'] % (cupti_run_path, sample_name)
        if app:
            check_result(cupti_profile_cmd2_1, 'run cupti profile-injection2_1 by %s' % cupti_profile_cmd2_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd2_2, 'run cupti profile-injection2_2 by %s' % cupti_profile_cmd2_2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd2_3, 'run cupti profile-injection2_3 by %s' % cupti_profile_cmd2_3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd3_1, 'run cupti profile-injection3_1 by %s' % cupti_profile_cmd3_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd3_2, 'run cupti profile-injection3_2 by %s' % cupti_profile_cmd3_2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd3_3, 'run cupti profile-injection3_3 by %s' % cupti_profile_cmd3_3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd1_1, 'run cupti profile-injection1_1 by %s' % cupti_profile_cmd1_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd1_2, 'run cupti profile-injection1_2 by %s' % cupti_profile_cmd1_2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd1_3, 'run cupti profile-injection1_3 by %s' % cupti_profile_cmd1_3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd1_4, 'run cupti profile-injection1_4 by %s' % cupti_profile_cmd1_4, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        return self.result

    def cupti_profile3(self, kernel=True, app=True, param=None):
        """ This is for graph sample cupti profile
            kernel = True : will excute the kernel replay 4 commands
            app = True : will excute the app replay 1 command
        """
        cupti_check_point = case_config['CUPTI']['CHECK_POINT']
        cupti_check_point2 = case_config['CUPTI']['CHECK_POINT2']
        cupti_check_point3 = case_config['CUPTI']['CHECK_POINT3']
        if param:
            sample_name = '\"' + self.name + param + '\"'
        else:
            sample_name = self.name
        cupti_profile_cmd1_1 = case_config['CUPTI']['CMD1_1'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_2 = case_config['CUPTI']['CMD1_2'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_3 = case_config['CUPTI']['CMD1_3'] % (cupti_run_path, sample_name)
        cupti_profile_cmd1_4 = case_config['CUPTI']['CMD1_4'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_1 = case_config['CUPTI']['CMD_GRAPH1'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_2 = case_config['CUPTI']['CMD_GRAPH2'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_3 = case_config['CUPTI']['CMD_GRAPH3'] % (cupti_run_path, sample_name)
        cupti_profile_cmd2_4 = case_config['CUPTI']['CMD_GRAPH4'] % (cupti_run_path, sample_name)
        cupti_profile_cmd3_1 = case_config['CUPTI']['CMD_GRAPH5'] % (cupti_run_path, sample_name)
        if kernel:
            check_result(cupti_profile_cmd1_1, 'run cupti profile-injection1_1 by %s' % cupti_profile_cmd1_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd1_2, 'run cupti profile-injection1_2 by %s' % cupti_profile_cmd1_2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd1_3, 'run cupti profile-injection1_3 by %s' % cupti_profile_cmd1_3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(cupti_profile_cmd1_4, 'run cupti profile-injection1_4 by %s' % cupti_profile_cmd1_4, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        if app:
            check_result(cupti_profile_cmd3_1, 'run cupti profile-injection3_1 by %s' % cupti_profile_cmd3_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd2_1, 'run cupti profile-injection2_1 by %s' % cupti_profile_cmd2_1, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd2_2, 'run cupti profile-injection2_2 by %s' % cupti_profile_cmd2_2, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd2_3, 'run cupti profile-injection2_3 by %s' % cupti_profile_cmd2_3, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        check_result(cupti_profile_cmd2_4, 'run cupti profile-injection3_1 by %s' % cupti_profile_cmd2_4, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        return self.result

    def cupti_profile_final(self, type="normal", kernel=True, app=True, param=None):
        """ This is for all samples cupti profile including normal sample, graph sample, deviece graph sample, confitional node sample and optix sample.
            type(str): "normal", "graph", "device", "confitional" or "optix".
            kernel(bool): True to excute the kernel replay 2 commands
            app(bool): True to excute the app replay 4 command
            param(str): Optional
        """
        cupti_check_point = case_config['CUPTI']['CHECK_POINT']
        cupti_check_point2 = case_config['CUPTI']['CHECK_POINT2']
        cupti_check_point3 = case_config['CUPTI']['CHECK_POINT3']
        if param:
            sample_name = '\"' + self.name + param + '\"'
        else:
            sample_name = self.name
        # normal sample
        normal_kernel_single_cmd = case_config['CUPTI']['NORMAL_KERNERL_SINGLE'] % (cupti_run_path, sample_name)
        normal_kernel_multi_cmd = case_config['CUPTI']['NORMAL_KERNEL_MULTI'] % (cupti_run_path, sample_name)
        normal_app_single_cmd = case_config['CUPTI']['NORMAL_APP_SINGLE'] % (cupti_run_path, sample_name)
        normal_app_multi_cmd = case_config['CUPTI']['NORMAL_APP_MULTI'] % (cupti_run_path, sample_name)
        normal_app_user_single_cmd = case_config['CUPTI']['NORMAL_APP_USER_SINGLE'] % (cupti_run_path, sample_name)
        normal_app_user_multi_cmd = case_config['CUPTI']['NORMAL_APP_USER_MULTI'] % (cupti_run_path, sample_name)
        # gragh sample
        gragh_node_single_cmd = case_config['CUPTI']['GRAPH_NODE_SINGLE'] % (cupti_run_path, sample_name)
        graph_node_multi_cmd = case_config['CUPTI']['GRAPH_NODE_MULTI'] % (cupti_run_path, sample_name)
        graph_single_cmd = case_config['CUPTI']['GRAPH_SINGLE'] % (cupti_run_path, sample_name)
        graph_multi_cmd = case_config['CUPTI']['GRAPH_MULTI'] % (cupti_run_path, sample_name)
        # optix sample
        optix_kernel_single_cmd = case_config['CUPTI']['OPTIX_KERNAL_SINGLE'] % (cupti_run_path, sample_name)
        optix_app_single_cmd = case_config['CUPTI']['OPTIX_APP_SINGLE'] % (cupti_run_path, sample_name)

        if type == "normal":
            if kernel:
                check_result(normal_kernel_single_cmd, 'run cupti profile-injection normal kernel single by %s' % normal_kernel_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
                check_result(normal_kernel_multi_cmd, 'run cupti profile-injection normal kernel multi by %s' % normal_kernel_multi_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            if app:
                check_result(normal_app_single_cmd, 'run cupti profile-injection normal app single by %s' % normal_app_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
                check_result(normal_app_multi_cmd, 'run cupti profile-injection normal app multi by %s' % normal_app_multi_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
                check_result(normal_app_user_single_cmd, 'run cupti profile-injection normal app user single by %s' % normal_app_user_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
                check_result(normal_app_user_multi_cmd, 'run cupti profile-injection normal app user multi by %s' % normal_app_user_multi_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        elif type == "optix":
            if kernel:
                check_result(optix_kernel_single_cmd, 'run cupti profile-injection optix kernel single by %s' % optix_kernel_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            if app:
                check_result(optix_app_single_cmd, 'run cupti profile-injection optix app single by %s' % optix_app_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
        else:
            check_result(graph_single_cmd, 'run cupti profile-injection gragh single by %s' % graph_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            check_result(graph_multi_cmd, 'run cupti profile-injection graph multi by %s' % graph_multi_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
            if type == "device":
                check_result(gragh_node_single_cmd, 'run cupti profile-injection gragh node single by %s' % gragh_node_single_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)
                check_result(graph_node_multi_cmd, 'run cupti profile-injection graph node multi by %s' % graph_node_multi_cmd, self.log_name, self.result, cupti_check_point, cupti_check_point2, cupti_check_point3, flag=2)

        return self.result

    def cupti_trace(self):
        n1 = self.name.strip('.exe')
        n2 = self.name
        n3 = self.name.strip('.exe') + '.json'
        dict1 = {"tests": []}
        if self.name == 'CuFastDepLaunches.exe':
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["--nocheck"]})
        # To do refactor
        elif self.name == 'mmap_api.exe':
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["-t memmap_ipc_basic_sanity"]})
        elif self.name == 'CuGraphNodeDisable.exe' and '3069377' in self.log_name:
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["memcpy-memset"]})
        elif self.name == 'test.exe':
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["kernel.sm_90.cubin"]})
        elif self.name == 'cg_apitests.exe':
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["--gtest_filter=cg_sync.arrive_wait"]})
        # elif self.name == 'CuLaunchCompletion.exe':
            # dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            # dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["0 0 0 1"]})
        elif self.name == 'cudaMallocAsync.exe':
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": ["-t basic_mempool_host_numa_memset_sanity cpu_numa_memory_pool_access_sanity cpu_numa_memory_pool_alloc_sizes cpu_numa_memory_pool_trim host_numa_memcpyBasic host_numa_reuseBasic mempool_host_numa_access_test"], "activity": ["MEMORY2"]})
        # combine first
        elif self.name in ['universal_function_pointers.exe', 'kernel_large_args.exe', 'cudaIpcSanity.exe', 'cluster_test.exe']:
            if self.name == 'universal_function_pointers.exe' or '3153125' in self.log_name:
                index = 'SUB2_' + self.name.strip('.exe').upper()
            else:
                index = 'SUB_' + self.name.strip('.exe').upper()
            subtest = case_config['SAMPLE'][index]
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2, "exe-args": [subtest]})
        else:
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2})
        dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (cupti_host_path + '/' + n3))
        cupti_trace_cmd1 = case_config['CUPTI']['CMD4'] % (cupti_host_path, n3, self.log_path, n1)
        cupti_trace_cmd2 = case_config['CUPTI']['CMD5'] % (self.log_path, n1)
        check_result(cupti_trace_cmd1, 'run cupti trace-injection by %s' % cupti_trace_cmd1, self.log_name, self.result)
        check_result(cupti_trace_cmd2, 'check cupti trace-injection result by %s' % cupti_trace_cmd2, self.log_name, self.result, '1')
        return self.result

    def cupti_trace_final(self, param=None):
        n1 = self.name.strip('.exe')
        n2 = self.name
        n3 = self.name.strip('.exe') + '.json'
        check_trace = '1'
        dict1 = {"tests": []}
        if param is not None:
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            if self.name == 'mmap_api.exe' or self.name == 'cudaMallocAsync.exe':
                param_number = 0
                for param1 in param.split(' '):
                    if param1 != '' and param1 != '-t':
                        param_number += 1
                        dict1["tests"].append({"name": param1, "exe": n2, "exe-args": ["-t " + param1], "activity": ["MEMORY2"]})
                check_trace = str(param_number)
            else:
                dict1["tests"].append({"name": n1, "exe": n2, "exe-args": [param]})
        else:
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            dict1["tests"].append({"name": n1, "exe": n2})
        dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (cupti_host_path + '/' + n3))
        cupti_trace_cmd1 = case_config['CUPTI']['CMD4'] % (cupti_host_path, n3, self.log_path, n1)
        cupti_trace_cmd2 = case_config['CUPTI']['CMD5'] % (self.log_path, n1)
        check_result(cupti_trace_cmd1, 'run cupti trace-injection by %s' % cupti_trace_cmd1, self.log_name, self.result)
        check_result(cupti_trace_cmd2, 'check cupti trace-injection result by %s' % cupti_trace_cmd2, self.log_name, self.result, check_trace)
        return self.result

    def cupti_trace_vanadium(self, sample, sample_python):
        n1 = sample
        n2 = sample_python
        n3 = sample + '.json'
        dict1 = {"tests": []}
        dict2 = {
            "tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        dict1["tests"].append({"name": n1, "command": ["%s" % n2]})
        dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (cupti_host_path + '/' + n3))
        cupti_trace_cmd1 = case_config['CUPTI']['CMD4'] % (cupti_host_path, n3, self.log_path, n1)
        cupti_trace_cmd2 = case_config['CUPTI']['CMD5'] % (self.log_path, n1)
        check_result(cupti_trace_cmd1, 'run cupti trace-injection by %s' % cupti_trace_cmd1, self.log_name,
                     self.result)
        check_result(cupti_trace_cmd2, 'check cupti trace-injection result by %s' % cupti_trace_cmd2, self.log_name,
                     self.result, '1')
        return self.result


def log_init(func_name):
    log_path = case_config['global']['env']['LOG_PATH'] % func_name
    log_name = case_config['global']['env']['LOG_NAME'] % (func_name, func_name)
    log_json = case_config['global']['env']['LOG_JSON'] % (func_name, func_name)
    mkdir(log_path)
    return log_path, log_name, log_json


def result_address(result, log_json):
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, log_json)


def tools_support_alloca_3069382():
    func_name = sys._getframe().f_code.co_name
    log_path, log_name, log_json = log_init(func_name)
    Alloca = ExternalSupport('alloca.exe', log_name, log_path, False, int(SM * 10))
    Alloca.sample('build')
    Alloca.sanitizer()
    if cuda_short_version < '12.0':
        Alloca.memcheck()
    if SM < 7.5:
        Alloca.nvprof()
    Alloca.ncu()
    result_address(Alloca.result, log_json)
    return Alloca.result


def tools_support_cuda_graphic_2720334():
    func_name = sys._getframe().f_code.co_name
    log_path, log_name, log_json = log_init(func_name)
    cudaGraphs = ExternalSupport('cudaGraphs.exe', log_name, log_path, False)
    cudaGraphs.sample('cuda_app')
    cudaGraphs.sanitizer()
    cudaGraphs.ncu()
    result_address(cudaGraphs.result, log_json)
    return cudaGraphs.result


def tools_support_stream_order_3069387():
    if common_get_driver_mode() not in ['TCC']:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        streamOrderedAllocation = ExternalSupport('streamOrderedAllocation.exe', log_name, log_path, True)
        streamOrderedAllocation.sample('build')
        if cuda_short_version < '12.0':
            streamOrderedAllocation.memcheck()
        streamOrderedAllocation.sanitizer()
        streamOrderedAllocation.ncu()
        streamOrderedAllocation.cupti_profile(app=False)
        streamOrderedAllocation.cupti_trace()
        result_address(streamOrderedAllocation.result, log_json)
        return streamOrderedAllocation.result
    else:
        logger.info('not support this test with TCC or MCDM mode')


def tools_support_fp16_3069367():
    if cuda_short_version >= '11.8' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        fp16basic = ExternalSupport('fp16-basic.exe', log_name, log_path, True, int(SM * 10))
        fp16basic.sample('build')
        fp16basic.sanitizer()
        fp16basic.ncu()
        fp16basic.cupti_profile(app=True)
        fp16basic.cupti_trace()
        result_address(fp16basic.result, log_json)
        return fp16basic.result
    else:
        logger.info('not support this sample if cuda version less than 11.8 or sm less than 9.0')


def tools_support_public_ptx_3069374():
    if cuda_short_version >= '11.8' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuFastDepLaunches = ExternalSupport('CuFastDepLaunches.exe', log_name, log_path, True)
        CuFastDepLaunches.sample('rebel')
        CuFastDepLaunches.sanitizer()
        CuFastDepLaunches.ncu()
        CuFastDepLaunches.cupti_profile(app=True)
        CuFastDepLaunches.cupti_trace()
        result_address(CuFastDepLaunches.result, log_json)
        return CuFastDepLaunches.result
    else:
        logger.info('not support this sample if cuda version less than 11.8 or sm less than 9.0')


def tools_support_extensible_launch_api_3069371():
    if cuda_short_version >= '11.8' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuFastDepLaunches = ExternalSupport('CuFastDepLaunches.exe', log_name, log_path, True)
        CuFastDepLaunches.sample('rebel')
        CuFastDepLaunches.sanitizer()
        CuFastDepLaunches.ncu()
        CuFastDepLaunches.cupti_profile(app=True)
        CuFastDepLaunches.cupti_trace()
        result_address(CuFastDepLaunches.result, log_json)
        return CuFastDepLaunches.result
    else:
        logger.info('not support this sample if cuda version less than 11.8 or sm less than 9.0')


def tools_support_parallel_launch_3069386():
    func_name = sys._getframe().f_code.co_name
    log_path, log_name, log_json = log_init(func_name)
    multithread = ExternalSupport('multithread.exe', log_name, log_path, True)
    multithread.sample('cuda_app')
    multithread.sanitizer()
    multithread.ncu()
    if SM < 7.5:
        multithread.nvprof()
    result_address(multithread.result, log_json)
    return multithread.result


def tools_support_disable_null_kernel_launch_3069380():
    func_name = sys._getframe().f_code.co_name
    log_path, log_name, log_json = log_init(func_name)
    CuGraphNodeDisable = ExternalSupport('CuGraphNodeDisable.exe', log_name, log_path, True)
    CuGraphNodeDisable.sample('cupti')
    CuGraphNodeDisable.sanitizer()
    CuGraphNodeDisable.ncu()
    CuGraphNodeDisable.cupti_profile(app=False)
    CuGraphNodeDisable.cupti_trace()
    result_address(CuGraphNodeDisable.result, log_json)
    return CuGraphNodeDisable.result


def tools_support_l2_atomics_3069379():
    if SM >= 8.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        l2descriptor = ExternalSupport('l2descriptor.exe', log_name, log_path, True, int(SM * 10))
        l2descriptor.sample('build')
        l2descriptor.sanitizer()
        l2descriptor.ncu()
        l2descriptor.cupti_profile(app=True)
        l2descriptor.cupti_trace()
        result_address(l2descriptor.result, log_json)
        return l2descriptor.result
    else:
        logger.info('no support this case if sm is less than 8.0')


def tools_support_relative_return_3069381():
    func_name = sys._getframe().f_code.co_name
    log_path, log_name, log_json = log_init(func_name)
    RelativeReturn = ExternalSupport('relative-return.exe', log_name, log_path, True, int(SM * 10))
    RelativeReturn.sample('build')
    RelativeReturn.sanitizer()
    if cuda_short_version < '12.0':
        RelativeReturn.memcheck()
    RelativeReturn.ncu()
    result_address(RelativeReturn.result, log_json)
    return RelativeReturn.result


def tools_support_cuda_amallocAsync_3069390():
    if common_get_driver_mode() not in ['TCC'] and cuda_short_version > '11.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuMemAllocsAsyncGraph = ExternalSupport('CuMemAllocsAsyncGraph.exe', log_name, log_path, True)
        CuMemAllocsAsyncGraph.sample('cupti')
        CuMemAllocsAsyncGraph.sanitizer()
        CuMemAllocsAsyncGraph.ncu()
        CuMemAllocsAsyncGraph.cupti_profile(app=False)
        CuMemAllocsAsyncGraph.cupti_trace()
        result_address(CuMemAllocsAsyncGraph.result, log_json)
        return CuMemAllocsAsyncGraph.result
    else:
        logger.info('not support this test with TCC or MCDM mode or cuda version less than 11.5')


def tools_support_ipc_3069391():
    if common_get_driver_mode() not in ['TCC'] and cuda_short_version > '11.5':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        mmap_api = ExternalSupport('mmap_api.exe', log_name, log_path, True)
        mmap_api.sample('cuda_app')
        mmap_api.sanitizer2(param=' -t memmap_ipc_basic_sanity')
        mmap_api.ncu()
        mmap_api.cupti_profile(app=False)
        mmap_api.cupti_trace()
        result_address(mmap_api.result, log_json)
        return mmap_api.result
    else:
        logger.info('not support this test with TCC or MCDM mode or cuda version less than 11.5')


def tools_support_memops_api_3069378():
    if cuda_short_version > '11.6' and SM >= 8.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphsExternalDeps = ExternalSupport('CuGraphsExternalDeps.exe', log_name, log_path, True)
        CuGraphsExternalDeps.sample('rebel')
        CuGraphsExternalDeps.sanitizer()
        # CuGraphsExternalDeps.ncu()
        # CuGraphsExternalDeps.cupti_profile(app=True)
        CuGraphsExternalDeps.cupti_trace()
        result_address(CuGraphsExternalDeps.result, log_json)
        return CuGraphsExternalDeps.result
    else:
        logger.info('not support this case if sm < 8.0 or cuda version less than 11.5')


def tools_support_hopper_cluster_3069370():
    if cuda_short_version > '11.6' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuClusterLaunches = ExternalSupport('CuClusterLaunches.exe', log_name, log_path, True)
        CuClusterLaunches.sample('rebel')
        CuClusterLaunches.sanitizer()
        # CuClusterLaunches.ncu()
        CuClusterLaunches.cupti_profile(app=True)
        CuClusterLaunches.cupti_trace()
        result_address(CuClusterLaunches.result, log_json)
        return CuClusterLaunches.result
    else:
        logger.info('not support if cuda version less than 11.8 or sm less than 9.0')


def tools_support_cuda_graphic_extend_3069377():
    if cuda_short_version > '11.6':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphNodeDisable = ExternalSupport('CuGraphNodeDisable.exe', log_name, log_path, True)
        CuGraphNodeDisable.sample('rebel')
        CuGraphNodeDisable.sanitizer2(' memcpy-memset')
        CuGraphNodeDisable.ncu2(' memcpy-memset')
        CuGraphNodeDisable.cupti_profile()
        CuGraphNodeDisable.cupti_trace()
        result_address(CuGraphNodeDisable.result, log_json)
        return CuGraphNodeDisable.result
    else:
        logger.info('not support this case if cuda version less than 11.6')


def tools_support_cuda_graphic_node_3069376():
    if cuda_short_version > '11.6':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphNodePriority = ExternalSupport('CuGraphNodePriority.exe', log_name, log_path, True)
        CuGraphNodePriority.sample('cupti')
        CuGraphNodePriority.sanitizer()
        # CuGraphNodePriority.ncu()
        CuGraphNodePriority.cupti_profile(app=False)
        CuGraphNodePriority.cupti_trace()
        result_address(CuGraphNodePriority.result, log_json)
        return CuGraphNodePriority.result
    else:
        logger.info('not support this case if cuda version less than 11.7')


def tools_support_cta_3069373():
    if SM == 9.0 and cuda_short_version > '11.7':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CtaReconfig = ExternalSupport('CtaReconfig.exe', log_name, log_path, True)
        CtaReconfig.sample('sanitizer')
        CtaReconfig.sanitizer()
        CTatest = ExternalSupport('test.exe', log_name, log_path, True)
        CTatest.sample('build')
        CTatest.ncu()
        CTatest.cupti_profile(app=True)
        CTatest.cupti_trace()
        CTatest.result.update(CtaReconfig.result)
        result_address(CTatest.result, log_json)
        return CTatest.result
    else:
        logger.info('not support sm less than 9.0 or cuda version less than 11.8')


def tools_support_read_only_3085433():
    if cuda_short_version >= '12.0' and common_get_driver_mode() not in ['TCC']:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        vectorAddDrv_readonly = ExternalSupport('vectorAddDrv_readonly.exe', log_name, log_path, True)
        vectorAddDrv_readonly.sample('build')
        vectorAddDrv_readonly.sanitizer()
        vectorAddDrv_readonly.ncu()
        vectorAddDrv_readonly.cupti_profile(app=True)
        vectorAddDrv_readonly.cupti_trace()
        result_address(vectorAddDrv_readonly.result, log_json)
        return vectorAddDrv_readonly.result
    else:
        logger.info('not support this case with TCC or MCDM mode or cuda version less than 12.0')


def tools_support_stream_priority_3131714():
    if cuda_short_version >= '12.0':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuNvtxContextStream = ExternalSupport('CuNvtxContextStream.exe', log_name, log_path, True)
        CuNvtxContextStream.sample('build')
        CuNvtxContextStream.sanitizer()
        CuNvtxContextStream.ncu()
        if cuda_short_version >= '12.1':
            CuNvtxContextStream.cupti_profile(app=True)
            CuNvtxContextStream.cupti_trace()
        result_address(CuNvtxContextStream.result, log_json)
        return CuNvtxContextStream.result
    else:
        logger.info('not support this case if cuda version less than 12.0')


def tools_support_cll_api_3130393():
    if cuda_short_version >= '12.0':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphsCLL = ExternalSupport('CuGraphsCLL.exe', log_name, log_path, True)
        CuGraphsCLL.sample('rebel')
        CuGraphsCLL.sanitizer()
        CuGraphsCLL.ncu()
        CuGraphsCLL.cupti_profile(app=False)
        CuGraphsCLL.cupti_trace()
        result_address(CuGraphsCLL.result, log_json)
        return CuGraphsCLL.result
    else:
        logger.info('not support this case if cuda version less than 12.0')


def tools_support_context_less_3117914():
    if cuda_short_version >= '12.0':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuContextlessLoading = ExternalSupport('CuContextlessLoading.exe', log_name, log_path, True)
        CuContextlessLoading.sample('rebel')
        CuContextlessLoading.sanitizer()
        CuContextlessLoading.ncu()
        CuContextlessLoading.cupti_profile(app=False)
        CuContextlessLoading.cupti_trace()
        result_address(CuContextlessLoading.result, log_json)
        return CuContextlessLoading.result
    else:
        logger.info('not support this case if cuda version less than 12.0')


def tools_support_cnpv2_3124519():
    if cuda_short_version >= '12.0' and SM >= 7.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuCnpv2 = ExternalSupport('CuCnpv2.exe', log_name, log_path, True)
        CuCnpv2.sample('rebel')
        CuCnpv2.sanitizer()
        CuCnpv2.ncu()
        CuCnpv2.cupti_profile(app=True)
        CuCnpv2.cupti_trace()
        result_address(CuCnpv2.result, log_json)
        return CuCnpv2.result
    else:
        logger.info('not support this case if cuda version less than 12.0 or sm less than 7.0')


def tools_support_cuda_graph_3130387():
    if cuda_short_version >= '12.0':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        DGLChild = ExternalSupport('DGLChild.exe', log_name, log_path, True)
        DGLChild.sample('rebel')
        DGLChild.sanitizer()
        DGLChild.ncu()
        DGLChild.cupti_profile(app=True)
        DGLChild.cupti_trace()
        DGLTail = ExternalSupport('DGLTail.exe', log_name, log_path, True)
        DGLTail.sample('rebel')
        DGLTail.sanitizer()
        DGLTail.ncu()
        DGLTail.cupti_profile(app=True)
        DGLTail.cupti_trace()
        DGLChildAndTail = ExternalSupport('DGLChildAndTail.exe', log_name, log_path, True)
        DGLChildAndTail.sample('rebel')
        DGLChildAndTail.sanitizer()
        DGLChildAndTail.ncu()
        DGLChildAndTail.cupti_profile(app=True)
        DGLChildAndTail.cupti_trace()
        DGLChild.result.update(DGLTail.result)
        DGLChild.result.update(DGLChildAndTail.result)
        result_address(DGLChild.result, log_json)
        return DGLChild.result
    else:
        logger.info('not support this case if cuda version less than 12.0')


def tools_support_position_independent_loading_3130397():
    if cuda_short_version >= '12.0':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        pilsample = ExternalSupport('pil-sample.exe', log_name, log_path, True)
        pilsample.sample('build')
        pilsample.sanitizer()
        pilsample.ncu()
        pilsample.cupti_profile(app=True)
        pilsample.cupti_trace()
        result_address(pilsample.result, log_json)
        return pilsample.result
    else:
        logger.info('not support this case if cuda version less than 12.0')


def tools_support_TMA_tensor_3153125():
    if cuda_short_version >= '12.0' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuTma = ExternalSupport('CuTma.exe', log_name, log_path, True)
        CuTma.sample('rebel')
        CuTma.sanitizer()
        CuTma.ncu()
        CuTma.cupti_profile(app=True)
        CuTma.cupti_trace()
        clusterTest = ExternalSupport('cluster_test.exe', log_name, log_path, True)
        clusterTest.sample('cuda_app')
        # clusterTest.sanitizer()
        clusterTest.ncu()
        clusterTest.cupti_profile(app=False)
        clusterTest.cupti_trace()
        CuTma.result.update(clusterTest.result)
        result_address(CuTma.result, log_json)
        return CuTma.result
    else:
        logger.info('not support this case if cuda version less than 12.0 or sm less than 9.0')


def tools_support_cnp_cluster_3188019():
    if cuda_short_version >= '12.1' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        clusterTest = ExternalSupport('cluster_test.exe', log_name, log_path, True)
        clusterTest.sample('cuda_app')
        clusterTest.sanitizer()
        clusterTest.ncu()
        clusterTest.cupti_profile(app=False)
        clusterTest.cupti_trace()
        result_address(clusterTest.result, log_json)
        return clusterTest.result
    else:
        logger.info('not support this case if cuda version less than 12.1 or sm less than 9.0')


def tools_support_fpifp_3130389():
    if cuda_short_version >= '12.0' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        fpifpsample = ExternalSupport('universal_function_pointers.exe', log_name, log_path, True)
        fpifpsample.sample('cuda_app')
        fpifpsample.sanitizer()
        fpifpsample.ncu()
        fpifpsample.cupti_profile(app=True)
        fpifpsample.cupti_trace()
        result_address(fpifpsample.result, log_json)
        return fpifpsample.result
    else:
        logger.info('not support this case if cuda version less than 12.0 or sm less than 9.0')


def tools_support_large_kernel_3186545():
    if cuda_short_version >= '12.1' and SM >= 7.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        largesample = ExternalSupport('kernel_large_args.exe', log_name, log_path, True)
        largesample.sample('cuda_app')
        largesample.sanitizer()
        largesample.ncu()
        largesample.cupti_profile(app=False)
        largesample.cupti_trace()
        result_address(largesample.result, log_json)
        return largesample.result
    else:
        logger.info('not support this case if cuda version less than 12.1 or sm less than 7.0')


def tools_support_ipc_sync_3192060():
    if cuda_short_version >= '12.2':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuSyncMemopsFlag = ExternalSupport('CuSyncMemopsFlag.exe', log_name, log_path, True)
        CuSyncMemopsFlag.sample('rebel')
        CuSyncMemopsFlag.sanitizer()
        CuSyncMemopsFlag.ncu()
        CuSyncMemopsFlag.cupti_profile(app=True)
        CuSyncMemopsFlag.cupti_trace()
        result_address(CuSyncMemopsFlag.result, log_json)
        return CuSyncMemopsFlag.result
    else:
        logger.info('not support this case if cuda version less than 12.2')


def tools_support_vector_atomics_3192538():
    if cuda_short_version >= '12.1' and SM >= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        vectoratomics = ExternalSupport('vector_atomics_globals.exe', log_name, log_path, True, int(SM * 10))
        vectoratomics.sample('build')
        vectoratomics.sanitizer()
        vectoratomics.ncu()
        vectoratomics.cupti_profile(app=False)
        vectoratomics.cupti_trace()
        result_address(vectoratomics.result, log_json)
        return vectoratomics.result
    else:
        logger.info('not support this case if cuda version less than 12.1 or sm less than 9.0')


def tools_support_directX_TRIM_callback_3250753():
    if cuda_short_version >= '12.2' and common_get_driver_mode() not in ['TCC', 'MCDM']:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuMemAllocsAsync = ExternalSupport('CuMemAllocsAsync.exe', log_name, log_path, True)
        CuMemAllocsAsync.sample('rebel')
        CuMemAllocsAsync.sanitizer()
        CuMemAllocsAsync.ncu()
        CuMemAllocsAsync.cupti_profile(app=True)
        # CuMemAllocsAsync.cupti_trace()
        result_address(CuMemAllocsAsync.result, log_json)
        return CuMemAllocsAsync.result
    else:
        logger.info('not support this case if cuda version less than 12.2 or in TCC/MCDM mode')


def tools_support_graph_sibling_launch_3250754():
    if cuda_short_version >= '12.2':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        DGLSibling = ExternalSupport('DGLSibling.exe', log_name, log_path, True)
        DGLSibling.sample('rebel')
        DGLSibling.sanitizer()
        DGLSibling.ncu()
        # DGLSibling.nvprof()
        DGLSibling.cupti_profile(app=True)
        DGLSibling.cupti_trace()
        result_address(DGLSibling.result, log_json)
        return DGLSibling.result
    else:
        logger.info('not support this case if cuda version less than 12.2')


def tools_support_graph_polymorphic_apis_3255123():
    if cuda_short_version >= '12.2':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphsPolymorphic = ExternalSupport('CuGraphsPolymorphic.exe', log_name, log_path, True)
        CuGraphsPolymorphic.sample('rebel')
        CuGraphsPolymorphic.sanitizer()
        CuGraphsPolymorphic.ncu()
        CuGraphsPolymorphic.cupti_profile(app=True)
        CuGraphsPolymorphic.cupti_trace()
        result_address(CuGraphsPolymorphic.result, log_json)
        return CuGraphsPolymorphic.result
    else:
        logger.info('not support this case if cuda version less than 12.2')


def tools_support_cta_reconfig_3134541():
    if cuda_short_version >= '12.0' and SM == 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        ctaReconfig = ExternalSupport('ctaReconfig.exe', log_name, log_path, True)
        ctaReconfig.sample('build')
        ctaReconfig.sanitizer()
        if cuda_short_version >= '12.3':
            CuCtaReconfig = ExternalSupport('CuCtaReconfig.exe', log_name, log_path, True)
            CuCtaReconfig.sample('rebel')
            CuCtaReconfig.ncu()
            CuCtaReconfig.cupti_profile2(app=True)
            CuCtaReconfig.cupti_trace()
            ctaReconfig.result.update(CuCtaReconfig.result)
        result_address(ctaReconfig.result, log_json)
        return ctaReconfig.result
    else:
        logger.info('not support this case if cuda version less than 12.0 or sm less than 9.0')


def tools_support_split_arrive_and_wait_3288716():
    if cuda_short_version >= '12.2':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        cg_apitests = ExternalSupport('cg_apitests.exe', log_name, log_path, True)
        cg_apitests.sample('cuda_app')
        # cg_apitests.sanitizer2(param=' --gtest_filter=cg_sync.arrive_wait')
        # cg_apitests.ncu()
        cg_apitests.cupti_profile2(app=True, param=' --gtest_filter=cg_sync.arrive_wait')
        # cg_apitests.cupti_trace()
        result_address(cg_apitests.result, log_json)
        return cg_apitests.result
    else:
        logger.info('not support this case if cuda version less than 12.2')


def tools_support_optix_RTCore_source_hiding_3078056():
    if cuda_short_version >= '12.2':
        # https://nvbugs/4367430 Update optix related tests for better restriction definition
        # Filter case with TCC on Windows
        driver_mode = common_get_driver_mode()
        print("The driver mode is %s" % driver_mode)
        if driver_mode in ['TCC', 'MCDM']:
            logger.info('not support this case if driver mode is TCC or MCDM on Windows')
        else:
            func_name = sys._getframe().f_code.co_name
            log_path, log_name, log_json = log_init(func_name)
            optixRaycasting = ExternalSupport('optixRaycasting.exe', log_name, log_path, True)
            optixRaycasting.sample('build')
            optixRaycasting.sanitizer()
            optixRaycasting.ncu()
            optixRaycasting.cupti_profile2(app=True)
            optixRaycasting.cupti_trace()
            result_address(optixRaycasting.result, log_json)
            return optixRaycasting.result
    else:
        logger.info('not support this case if cuda version less than 12.2')


def tools_support_graph_edge_data_3439796():
    if cuda_short_version >= '12.3':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphsEdgeData = ExternalSupport('CuGraphsEdgeData.exe', log_name, log_path, True)
        CuGraphsEdgeData.sample('rebel')
        CuGraphsEdgeData.sanitizer()
        CuGraphsEdgeData.ncu()
        CuGraphsEdgeData.cupti_profile3(app=False)
        CuGraphsEdgeData.cupti_trace()
        result_address(CuGraphsEdgeData.result, log_json)
        return CuGraphsEdgeData.result
    else:
        logger.info('not support this case if cuda version less than 12.3')


def tools_support_FDL_record_completion_events_3439719():
    if cuda_short_version >= '12.3':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuLaunchCompletion = ExternalSupport('CuLaunchCompletion.exe', log_name, log_path, True)
        CuLaunchCompletion.sample('rebel')
        # CuLaunchCompletion.sanitizer2(param=" 0 0 0 1")
        CuLaunchCompletion.ncu_final(param=" 0 0 0 1")
        CuLaunchCompletion.cupti_profile2(param=" 0 0 0 1")
        CuLaunchCompletion.cupti_trace()
        spec_cmd1 = case_config['NCU']['CMD_SPECIAL3'] % (log_path, "CuLaunchCompletion.exe")
        CuLaunchCompletion.run_specifial(spec_cmd1, "error", "ERROR", "nan", "N/A", "n/a", flag=2)
        result_address(CuLaunchCompletion.result, log_json)
        return CuLaunchCompletion.result
    else:
        logger.info('not support this case if cuda version less than 12.3')


def tools_support_graph_conditionnal_node_3420115():
    if cuda_short_version >= '12.3':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGraphsConditional = ExternalSupport('CuGraphsConditional.exe', log_name, log_path, True)
        CuGraphsConditional.sample('rebel')
        CuGraphsConditional.sanitizer()
        CuGraphsConditional.ncu2()
        CuGraphsConditional.cupti_profile3(kernel=False, app=False)
        CuGraphsConditional.cupti_trace()
        result_address(CuGraphsConditional.result, log_json)
        return CuGraphsConditional.result
    else:
        logger.info('not support this case if cuda version less than 12.3')


def tools_support_publicPTX_3491667():
    if cuda_short_version >= '12.3' and SM == 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuTensorCoreWgmma = ExternalSupport('CuTensorCoreWgmma.exe', log_name, log_path, True)
        CuTensorCoreWgmma.sample('rebel')
        CuTensorCoreWgmma.sanitizer2()
        CuTensorCoreWgmma.ncu_final()
        CuTensorCoreWgmma.cupti_profile_final()
        CuTensorCoreWgmma.cupti_trace()
        spec_cmd1 = case_config['NCU']['CMD_SPECIAL1'] % (log_path, "CuTensorCoreWgmma.exe")
        spec_cmd2 = case_config['CUPTI']['PROFILE_SPECIAL1'] % (cupti_run_path, "CuTensorCoreWgmma.exe")
        CuTensorCoreWgmma.run_specifial(spec_cmd1, "error", "ERROR", "nan", "N/A", "n/a", flag=2)
        CuTensorCoreWgmma.run_specifial(spec_cmd2, "error", "ERROR", "nan", "N/A", "n/a", flag=2)
        result_address(CuTensorCoreWgmma.result, log_json)
        return CuTensorCoreWgmma.result
    else:
        logger.info('not support this case if cuda version less than 12.3 or sm less than 9.0')


def tools_support_postion_independent_3556126():
    if cuda_short_version >= '12.4' and SM >= 8.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        pilDefault = ExternalSupport('pilDefault.exe', log_name, log_path, True)
        pilDefault.sample('build')
        pilDefault.sanitizer2()
        pilDefault.ncu_final()
        pilDefault.cupti_profile_final()
        pilDefault.cupti_trace()
        result_address(pilDefault.result, log_json)
        return pilDefault.result
    else:
        logger.info('not support this case if cuda version less than 12.4 or sm less than 8.0')


def tools_support_device_node_update_3556167():
    if cuda_short_version >= '12.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        DGLDeviceNodeUpdate = ExternalSupport('DGLDeviceNodeUpdate.exe', log_name, log_path, True)
        DGLDeviceNodeUpdate.sample('rebel')
        DGLDeviceNodeUpdate.sanitizer2()
        DGLDeviceNodeUpdate.ncu_final(type="gragh")
        DGLDeviceNodeUpdate.cupti_profile_final(type="gragh")
        # DGLDeviceNodeUpdate.cupti_trace()
        result_address(DGLDeviceNodeUpdate.result, log_json)
        return DGLDeviceNodeUpdate.result
    else:
        logger.info('not support this case if cuda version less than 12.4')


def tools_support_create_fatbin_3564490():
    if cuda_short_version >= '12.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        nvFatbin = ExternalSupport('nvFatbin.exe', log_name, log_path, True, int(SM * 10))
        nvFatbin.sample('build')
        nvFatbin.sanitizer2()
        nvFatbin.ncu_final()
        nvFatbin.cupti_profile_final()
        nvFatbin.cupti_trace()
        result_address(nvFatbin.result, log_json)
        return nvFatbin.result
    else:
        logger.info('not support this case if cuda version less than 12.4')


def tools_support_Ada_FP8_QMMA_3568465():
    if cuda_short_version >= '12.4' and 8.9 <= SM <= 9.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuTensorCoreQmma = ExternalSupport('CuTensorCoreQmma.exe', log_name, log_path, True)
        CuTensorCoreQmma.sample('rebel')
        CuTensorCoreQmma.sanitizer2()
        CuTensorCoreQmma.ncu_final()
        CuTensorCoreQmma.cupti_profile_final()
        CuTensorCoreQmma.cupti_trace()
        result_address(CuTensorCoreQmma.result, log_json)
        # todo special check
        return CuTensorCoreQmma.result
    else:
        logger.info('not support this case if cuda version less than 12.4 or sm less than 8.9')


def tools_support_green_context_3568466():
    if cuda_short_version >= '12.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuGreenContexts = ExternalSupport('CuGreenContexts.exe', log_name, log_path, True)
        CuGreenContexts.sample('rebel')
        kernels_ptx = ExternalSupport('kernels.ptx', log_name, log_path, True)
        kernels_ptx.sample('rebel')
        CuGreenContexts.sanitizer2()
        CuGreenContexts.cupti_profile_final()
        CuGreenContexts.cupti_trace()
        # CuMandatoryConcurrent = ExternalSupport('CuMandatoryConcurrent.exe', log_name, log_path, True)
        # CuMandatoryConcurrent.sample('rebel')
        # CuMandatoryConcurrent.sanitizer2(param=' 16')
        # CuMandatoryConcurrent.cupti_profile_final(param=' 16')
        # CuMandatoryConcurrent.cupti_trace()
        CuGraphsGreenContexts = ExternalSupport('CuGraphsGreenContexts.exe', log_name, log_path, True)
        CuGraphsGreenContexts.sample('rebel')
        CuGraphsGreenContexts.sanitizer2()
        CuGraphsGreenContexts.cupti_profile_final(type="graph")
        CuGraphsGreenContexts.cupti_trace()
        CuGreenContexts.result.update(kernels_ptx.result)
        CuGreenContexts.result.update(CuGraphsGreenContexts.result)
        result_address(CuGreenContexts.result, log_json)
        return CuGreenContexts.result
    else:
        logger.info('not support this case if cuda version less than 12.4')


def tools_support_cbl2_3570006():
    if cuda_short_version >= '12.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        cbl2 = ExternalSupport('Cbl2.exe', log_name, log_path, True)
        cbl2.sample('rebel')
        cbl_kernel = ExternalSupport('cblKernels2.ptx', log_name, log_path, True)
        cbl_kernel.sample('rebel')
        cbl2.sanitizer2(param=' -l 4 -q 8')
        cbl2.ncu_final(param=' -l 4 -q 8')
        cbl2.cupti_profile_final(param=' -l 4 -q 8', app=False)
        cbl2.cupti_trace_final(param='-l 4 -q 8')
        spec_cmd1 = case_config['NCU']['CMD_SPECIAL2'] % (log_path, "Cbl2.exe")
        cbl2.run_specifial(spec_cmd1, "error", "ERROR", "nan", "N/A", "n/a", flag=2)
        result_address(cbl2.result, log_json)
        return cbl2.result
    else:
        logger.info('not support this case if cuda version less than 12.4')


def tools_support_rutime_API_3655684():
    if cuda_short_version >= '12.5':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuDriverEntryPoint = ExternalSupport('CuDriverEntryPoint.exe', log_name, log_path, True)
        CuDriverEntryPoint.sample('rebel')
        CuDriverEntryPoint.sanitizer2()
        CuDriverEntryPoint.ncu_final()
        CuDriverEntryPoint.cupti_profile_final()
        CuDriverEntryPoint.cupti_trace_final()
        result_address(CuDriverEntryPoint.result, log_json)
        return CuDriverEntryPoint.result
    else:
        logger.info('not support this case if cuda version less than 12.5')


def tools_support_get_managed_memory_3661948():
    if cuda_short_version >= '12.5':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuUvmCLL = ExternalSupport('CuUvmCLL.exe', log_name, log_path, True)
        CuUvmCLL.sample('rebel')
        CuUvmCLL.sanitizer2()
        CuUvmCLL.ncu_final()
        CuUvmCLL.cupti_profile_final()
        CuUvmCLL.cupti_trace_final()
        result_address(CuUvmCLL.result, log_json)
        return CuUvmCLL.result
    else:
        logger.info('not support this case if cuda version less than 12.5')


def tools_support_green_context_API_3664936():
    if cuda_short_version >= '12.5':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuMandatoryConcurrent = ExternalSupport('CuMandatoryConcurrent.exe', log_name, log_path, True)
        CuMandatoryConcurrent.sample('rebel')
        CuMandatoryConcurrent.sanitizer2(param=' 16')
        CuMandatoryConcurrent.ncu_final(param=' 16')
        CuMandatoryConcurrent.cupti_profile_final(param=' 16')
        CuMandatoryConcurrent.cupti_trace_final(param='16')
        result_address(CuMandatoryConcurrent.result, log_json)
        return CuMandatoryConcurrent.result
    else:
        logger.info('not support this case if cuda version less than 12.5')


def tools_support_ptx_blackwell_3802410():
    sm = get_sm()
    if cuda_short_version > '12.6' and sm == 10.0:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuTmem = ExternalSupport('CuTmem.exe', log_name, log_path, True)
        CuTmem.sample('rebel')
        CuTmem.sanitizer2(param=' valid')
        CuTmem.ncu_final(param=' valid')
        CuTmem.cupti_profile_final(param=' valid')
        CuTmem.cupti_trace_final(param='valid')
        result_address(CuTmem.result, log_json)
        return CuTmem.result
    else:
        logger.info('support this case since cuda 12.7 and blackwell')


def tools_support_batched_cuda_memcpy_3859196():
    if cuda_short_version > '12.8':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        cuda_batched_memcpy = ExternalSupport('cuda_batched_memcpy.exe', log_name, log_path, True)
        cuda_batched_memcpy.sample('cuda_app')
        cuda_batched_memcpy.sanitizer2(param=' -t basic_capture basic_d2d_sanity basic_d2h_pageable_sanity basic_d2h_sanity basic_h2d_sanity basic_h2h_sanity')
        cuda_batched_memcpy.ncu_final(param=' -t basic_capture basic_d2d_sanity basic_d2h_pageable_sanity basic_d2h_sanity basic_h2d_sanity basic_h2h_sanity')
        cuda_batched_memcpy.cupti_profile_final(param=' -t basic_capture basic_d2d_sanity basic_d2h_pageable_sanity basic_d2h_sanity basic_h2d_sanity basic_h2h_sanity')
        cuda_batched_memcpy.cupti_trace_final(param=' -t basic_capture basic_d2d_sanity basic_d2h_pageable_sanity basic_d2h_sanity basic_h2d_sanity basic_h2h_sanity')
        result_address(cuda_batched_memcpy.result, log_json)
        return cuda_batched_memcpy.result
    else:
        logger.info('support this case since cuda 12.9')


def tools_support_conditional_nodes_else_switch_3900193():
    if cuda_short_version > '12.7':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        CuTmem = ExternalSupport('CuGraphsConditional.exe', log_name, log_path, True)
        CuTmem.sample('rebel')
        CuTmem.sanitizer2(param=' 3')
        CuTmem.ncu2(param=' 3')
        CuTmem.cupti_profile_final(type='devices', param=' 3')
        CuTmem.cupti_trace_final(param='3')
        result_address(CuTmem.result, log_json)
        return CuTmem.result
    else:
        logger.info('support this case since cuda 12.8')


def tools_support_vanadium_python_sample_3570628():
    logger.info('if the case run fail, please check your python version, suggestion use python3.10 or need run pip install opencv-python numpy')
    if cuda_short_version > '12.4':
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        prepare_cmd1 = case_config['TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE']['PREPARE']['CMD1']
        logger.info('install the library via {}'.format(prepare_cmd1))
        run_loc_cmd(prepare_cmd1)
        prepare_cmd2 = case_config['TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE']['PREPARE']['CMD2'] % (log_path, user, password, base_url)
        run_loc_cmd(prepare_cmd2)
        logger.info('download vanadium sample via ---{}'.format(prepare_cmd2))
        sample_list = case_config['TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE']['SAMPLE_LIST'].split(',')
        for index, sample in enumerate(sample_list):
            prepare_cmd3 = case_config['TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE']['PREPARE']['CMD3'] % (log_path, 'vanadium_sample_%s' % index, 'vanadium_sample_%s' % index)
            run_loc_cmd(prepare_cmd3)
            with open('%s/vanadium_sample_%s.bat' % (log_path, index), 'r+') as f:
                f.write(sample)
        # run vanadium sample 0
        vanadium_sample_0 = ExternalSupport('python310 hello.py', log_name, log_path, True)
        vanadium_sample_0.ncu()
        vanadium_sample_0.sanitizer()
        prepare('cupti')
        prepare_cmd4 = case_config['TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE']['PREPARE']['CMD4'] % (log_path, cupti_run_path)
        run_loc_cmd(prepare_cmd4)
        vanadium_sample_0.cupti_trace_vanadium('vanadium_sample0', 'python310 hello.py')
        vanadium_sample_0_profile = ExternalSupport('vanadium_sample_0.bat', log_name, log_path, True)
        vanadium_sample_0_profile.cupti_profile_final()
        vanadium_sample_0.result.update(vanadium_sample_0_profile.result)
        # run vanadimu sample 1
        vanadium_sample_1 = ExternalSupport('python310 mandelbrot.py', log_name, log_path, True)
        vanadium_sample_1.ncu()
        vanadium_sample_1.sanitizer()
        vanadium_sample_1.cupti_trace_vanadium('vanadium_sample1', 'python310 mandelbrot.py')
        vanadium_sample_1_profile = ExternalSupport('vanadium_sample_1.bat', log_name, log_path, True)
        vanadium_sample_1_profile.cupti_profile_final()
        vanadium_sample_0.result.update(vanadium_sample_1.result)
        vanadium_sample_0.result.update(vanadium_sample_1_profile.result)
        # run vanadium sample 2
        vanadium_sample_2 = ExternalSupport('python310 sobel.py data/nvidia_logo.png', log_name, log_path, True)
        vanadium_sample_2.ncu()
        vanadium_sample_2.sanitizer()
        vanadium_sample_2.cupti_trace_vanadium('vanadium_sample2', 'python310 sobel.py data/nvidia_logo.png')
        vanadium_sample_2_profile = ExternalSupport('vanadium_sample_2.bat', log_name, log_path, True)
        vanadium_sample_2_profile.cupti_profile_final()
        vanadium_sample_0.result.update(vanadium_sample_2.result)
        vanadium_sample_0.result.update(vanadium_sample_2_profile.result)
        result_address(vanadium_sample_0.result, log_json)
        print(vanadium_sample_0.result)
        return vanadium_sample_0.result
    else:
        logger.info('not support this case if cuda version less than cuda 12.4')


def tools_support_cumemcreate_memasync_3926816():
    driver_mode = get_driver_mode()
    if cuda_short_version > '12.8' and common_get_driver_mode not in ['TCC']:
        func_name = sys._getframe().f_code.co_name
        log_path, log_name, log_json = log_init(func_name)
        mmap_api = ExternalSupport('mmap_api.exe', log_name, log_path, True)
        mmap_api.sample('cuda_app')
        '''
        mmap_api.sanitizer2(param=' -t memmap_ipc_basic_sanity -t basic_cpu_numa_id_access_sanity basic_cpu_numa_id_device_access_sanity closest_host_numa_id_device_attribute cpu_numa_allocation_ipc_sanity cpu_numa_id_memcpy_sanity mmap_host_numa_memcpy_sanity mmap_host_numa_pointer_attr')
        mmap_api.ncu_final(param=' -t basic_cpu_numa_id_access_sanity basic_cpu_numa_id_device_access_sanity closest_host_numa_id_device_attribute cpu_numa_allocation_ipc_sanity cpu_numa_id_memcpy_sanity mmap_host_numa_memcpy_sanity mmap_host_numa_pointer_attr')
        mmap_api.cupti_profile_final(param=' -t basic_cpu_numa_id_access_sanity basic_cpu_numa_id_device_access_sanity closest_host_numa_id_device_attribute cpu_numa_allocation_ipc_sanity cpu_numa_id_memcpy_sanity mmap_host_numa_memcpy_sanity mmap_host_numa_pointer_attr')
        '''
        mmap_api.cupti_trace_final(param='-t basic_cpu_numa_id_access_sanity basic_cpu_numa_id_device_access_sanity closest_host_numa_id_device_attribute cpu_numa_allocation_ipc_sanity cpu_numa_id_memcpy_sanity mmap_host_numa_memcpy_sanity mmap_host_numa_pointer_attr')
        cudaMallocAsync = ExternalSupport('cudaMallocAsync.exe', log_name, log_path, True)
        cudaMallocAsync.sample('cuda_app')
        '''
        cudaMallocAsync.sanitizer2(param=' -t basic_mempool_host_numa_memset_sanity cpu_numa_memory_pool_access_sanity cpu_numa_memory_pool_alloc_sizes cpu_numa_memory_pool_trim host_numa_memcpyBasic host_numa_reuseBasic mempool_host_numa_access_test')
        cudaMallocAsync.ncu_final(param=' -t basic_mempool_host_numa_memset_sanity cpu_numa_memory_pool_access_sanity cpu_numa_memory_pool_alloc_sizes cpu_numa_memory_pool_trim host_numa_memcpyBasic host_numa_reuseBasic mempool_host_numa_access_test')
        cudaMallocAsync.cupti_profile_final(param=' -t basic_mempool_host_numa_memset_sanity cpu_numa_memory_pool_access_sanity cpu_numa_memory_pool_alloc_sizes cpu_numa_memory_pool_trim host_numa_memcpyBasic host_numa_reuseBasic mempool_host_numa_access_test')
        '''
        cudaMallocAsync.cupti_trace_final(param='-t basic_mempool_host_numa_memset_sanity cpu_numa_memory_pool_access_sanity cpu_numa_memory_pool_alloc_sizes cpu_numa_memory_pool_trim host_numa_memcpyBasic host_numa_reuseBasic mempool_host_numa_access_test')
        mmap_api.result.update(cudaMallocAsync.result)
        result_address(mmap_api.result, log_json)
        return mmap_api.result
    else:
        logger.info('support this case since cuda 12.9 and not TCC mode')


example_text = """
Example:
python run_common_case.py -h # run all the function
python run_common_case.py -e tools_support_alloca # run single case
python run_common_case.py -el tools_support_alloca,tools_support_cuda_graphic # run multi cases
"""
reversion = '1.0'
check_list = ['tools_support_alloca_3069382', 'tools_support_cuda_graphic_2720334', 'tools_support_stream_order_3069387', 'tools_support_fp16_3069367',
              'tools_support_extensible_launch_api_3069371', 'tools_support_public_ptx_3069374', 'tools_support_parallel_launch_3069386',
              'tools_support_disable_null_kernel_launch_3069380', 'tools_support_l2_atomics_3069379', 'tools_support_relative_return_3069381',
              'tools_support_cuda_amallocAsync_3069390', 'tools_support_ipc_3069391', 'tools_support_cta_3069373',
              'tools_support_memops_api_3069378', 'tools_support_cuda_graphic_extend_3069377', 'tools_support_cuda_graphic_node_3069376',
              'tools_support_read_only_3085433', 'tools_support_hopper_cluster_3069370', 'tools_support_stream_priority_3131714', 'tools_support_cll_api_3130393',
              'tools_support_context_less_3117914', 'tools_support_cnpv2_3124519', 'tools_support_cuda_graph_3130387',
              'tools_support_position_independent_loading_3130397', 'tools_support_TMA_tensor_3153125', 'tools_support_cnp_cluster_3188019',
              'tools_support_fpifp_3130389', 'tools_support_large_kernel_3186545', 'tools_support_ipc_sync_3192060', 'tools_support_vector_atomics_3192538',
              'tools_support_directX_TRIM_callback_3250753', 'tools_support_graph_sibling_launch_3250754', 'tools_support_graph_polymorphic_apis_3255123',
              'tools_support_cta_reconfig_3134541', 'tools_support_split_arrive_and_wait_3288716', 'tools_support_optix_RTCore_source_hiding_3078056',
              'tools_support_graph_edge_data_3439796', 'tools_support_FDL_record_completion_events_3439719', 'tools_support_graph_conditionnal_node_3420115',
              'tools_support_publicPTX_3491667', 'tools_support_postion_independent_3556126', 'tools_support_device_node_update_3556167',
              'tools_support_create_fatbin_3564490', 'tools_support_Ada_FP8_QMMA_3568465', 'tools_support_green_context_3568466', 'tools_support_vanadium_python_sample_3570628',
              'tools_support_cbl2_3570006', 'tools_support_rutime_API_3655684', 'tools_support_get_managed_memory_3661948', 'tools_support_batched_cuda_memcpy_3859196',
              'tools_support_green_context_API_3664936', 'tools_support_ptx_blackwell_3802410', 'tools_support_conditional_nodes_else_switch_3900193', 
              'tools_support_cumemcreate_memasync_3926816']


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-c", "--config-file", dest="config_file", required=False,
        help='Specify test index file. e.g. cases')
    parser.add_argument(
        "-e", "--excute", action='append', required=False,
        choices=check_list,
        help="Specify function to be run.")
    parser.add_argument(
        "-el", "--excute_list", action='store', required=False,
        help="Specify multi function to be run.")
    # change_yaml()
    prepare('cupti')
    prepare('rebel')
    prepare('sanitizer')
    prepare('cuda_app')
    exit()
    args = parser.parse_args()
    case_str = args.excute_list
    case_list_single = args.excute
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            print(getattr(mod, case))
            getattr(mod, case)()
    elif case_list_single:
        mod = sys.modules["__main__"]
        getattr(mod, case_list_single[0])()
    else:
        logger.info('please give correct case to run')
