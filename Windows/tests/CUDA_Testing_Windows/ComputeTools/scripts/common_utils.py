# -*- encoding: utf-8 -*-
# from exceptions import Exception

import requests
from configparser import ConfigParser
import urllib.request as urllib2
from urllib.parse import urlparse
import logging
import os
import re
import sys
import subprocess
import tempfile
import time
import json
import base64
import copyreg
import collections
import signal
# from smb.SMBConnection import SMBConnection
import socket
import ftplib

logger = logging.getLogger()


class SMBServerConn(object):
    def __init__(self, server_name, smb_path):
        smb_path_list = smb_path.strip('/').split('/')
        server = '\\' + smb_path_list[0]
        shared = smb_path_list[1]
        path = '/'.join(smb_path_list[2:])
        self._smb_user_name = os.environ.get('SERVICE_ACCOUNT', '')
        self._smb_user_password = os.environ.get('SERVICE_PASSWORD', '')
        # self._smb_client_host_name = socket.gethostname()
        self._smb_client_host_name = "auto_sanitizer"
        self._smb_server_name = server_name
        self._smb_domain_name = "NVIDIA.com"

        if server_name == "sc-netapp16":
            self.__smb_conn = SMBConnection(self._smb_user_name,
                                            self._smb_user_password,
                                            self._smb_client_host_name,
                                            self._smb_server_name,
                                            self._smb_domain_name)
            server_ip = socket.gethostbyname(server_name)
            print(server_ip)
            self.__smb_conn.connect(server_ip)
        else:
            self.__smb_conn = SMBConnection(self._smb_user_name,
                                            self._smb_user_password,
                                            self._smb_client_host_name,
                                            self._smb_server_name,
                                            self._smb_domain_name,
                                            )
            self.__smb_conn.connect(server)
        self.__path = path
        self.__shared = shared

    def list_folders(self):
        files = self.__smb_conn.listPath(
            self.__shared,
            self.__path
        )
        return files

    def list_files(self, subfolder):
        path_name = "{}/{}".format(self.__path, subfolder)
        files = self.__smb_conn.listPath(
            self.__shared,
            path_name
        )
        return files

    def close(self):
        self.__smb_conn.close()


def fetch_builds_account():
    if "CQASYSTEM_TOKEN" in os.environ:
        api_token = os.environ["CQASYSTEM_TOKEN"]
    else:
        api_token_list = ['8c913a613f', '88b6f1ad91a', '815f13fd732']
        api_token = ''.join(api_token_list)
    cqasystem_host = 'cqasystem.nvidia.com'
    response = requests.get(f'http://{cqasystem_host}/api/credential/?ctype=service_account',
                            headers={'accept': 'application/json',
                                     'api-token': api_token}).json()
    return response['username'], response['password']


class FTPBase:
    ftp = ftplib.FTP()
    BUILDS_SERVER = "hqnvhwy02"

    def __init__(self, host=BUILDS_SERVER, username=None, password=None):
        self.ftp.connect(host=host)
        self.login(username=username, password=password)

    def login(self, username=None, password=None):
        try:
            if not username or not password:
                username, password = fetch_builds_account()
            self.ftp.login(username, password)
            self.send_noop()
            logger.info("Login server successfully!")
            return True
        except Exception as err:
            logger.error("Login server failed")
            return False

    def send_noop(self):
        self.ftp.voidcmd('NOOP')  # 发送NOOP命令以防止连接超时

    def list_file_directory(self, remote_dir):
        if not remote_dir:
            logger.warning(f"{remote_dir} does not exist!")
            return None
        try:
            self.ftp.cwd(remote_dir)
        except Exception as err:
            logger.warning(f"{remote_dir} does not exist!")
            logger.debug(err)
            return None

        dir_list = []
        self.ftp.dir('.', dir_list.append)

        folder_dict = {}
        for line in dir_list:
            col = line.split()
            datestr = ' '.join(col[:2])
            date = time.strptime(datestr, '%m-%d-%y %H:%M%p')
            folder_dict[col[3]] = date

        if not folder_dict:
            logger.warning(f"No valid folder driver can be found under {remote_dir}", )
            return None

        return folder_dict

    def get_dir_list(self, path):
        logger.info(f"Remote Dir: {path}")
        if not path:
            logger.warning(f"{path} is not exist!!!")
            return None
        try:
            self.ftp.cwd(path)
        except Exception as err:
            logger.warning(f"{path} is not exist!!!")
            logger.debug(err)
            return None
        dir_list = []
        self.ftp.dir('.', dir_list.append)
        folder_list = [line.split()[3] for line in dir_list]
        if not folder_list:
            logger.warning(f"No valid folder driver can be found under {path}")
            return None
        return folder_list

    def close(self):
        self.ftp.quit()


def dict_to_json(dict1, json_file):
    # type: (dict, str) -> None
    """
    Dump dictionary to JSON file
    """
    json_str = json.dumps(dict1, indent=4)
    with open(json_file, 'w') as f:
        f.write(json_str)


def calculate_result(dict1):
    passed, failed = 0, 0
    for key, value in dict1.items():
        if value == 'passed':
            passed += 1
        else:
            failed += 1
    dict1['passed'] = passed
    dict1['failed'] = failed
    return dict1


def print_run_time(func):
    def wrapper(*args, **kw):
        local_time = time.time()
        func(*args, **kw)
        print('current Function [%s] with %s option, it executes time is %.2f' % (func.__name__, args, time.time() - local_time))
    return wrapper


def save_log(log_name, cmd, label, output, pass1=None, fail1=None):
    run_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    with open(log_name, 'a+', encoding='utf-8') as f:
        f.write('%s----now we had ran the steps: %s \n' % (run_date, label))
        f.write('%s----now we had ran the command: %s \n' % (run_date, cmd))
        f.write(str(output))
        if pass1 is not None:
            f.write('%s----now we had ran the command passed number: %s \n' % (run_date, pass1))
        if fail1 is not None:
            f.write('%s----now we had ran the command failed number: %s \n' % (run_date, fail1))
        f.write('\n\n\n')


def check_result(cmd, label, log_name, result, *args, **kwargs):
    # type: (object, object, object, object, object, object) -> object
    '''
    :param cmd:
    :param label:
    :param log_name:
    :param result:
    :param args: check_point
    :param kwargs: flag=1, return is false and check point in output;
                   flag=2:return is true, checkpoint is not in output
                   flag=3(for cufft): return is true, checkpoint is not in output
    :return:
    '''
    passed, failed = 0, 0
    # Change the timeout limit from 1800(30 minutes) to 3600 (1 hour) on Jun 1, 2022
    out = run_loc_cmd(cmd, timeout=1800)
    logger.info('the command return value is %s' % out['exit'])
    if kwargs:
        # AttributeError: 'dict' object has no attribute 'iteritems' in Python 3, change to items()
        for key, value in kwargs.items():
            # value =1, command result is error, but the checkpoint is in output
            if value == 1:
                if args:
                    for i in args:
                        print('****************** we will check "%s" ******************' % i)
                        # Add support for regular expression match by Alex Li on 2024.06.18
                        if out.succeeded is not True and (i in out['output'] or re.search(i, out['output'], re.I)):
                            passed += 1
                        # Modified for case 1945656-cupti_guard.yaml on Windows, because the output of the CMD command is empty.
                        # Need to refactor the code according to the new situation in the future.
                        # Dec 28, 2021
                        elif out.succeeded is True and out['output'] == '':
                            passed += 1
                        else:
                            logger.error('we check------"%s" failed in %s' % (i, label))
                            failed += 1
                else:
                    if out.succeeded is not True:
                        passed += 1
                    else:
                        logger.error('we run "%s" failed' % cmd)
                        failed += 1
            elif value == 2:
                if args:
                    for i in args:
                        print('****************** we will check "%s" ******************' % i)
                        # Add support for regular expression match by Alex Li on 2024.06.18
                        if out.succeeded and (i not in out['output'] or not re.search(i, out['output'], re.I)):
                            passed += 1
                        else:
                            logger.error('we check------"%s" failed in %s' % (i, label))
                            failed += 1
                else:
                    if out.succeeded:
                        passed += 1
                    else:
                        logger.error('we run "%s" failed' % cmd)
                        failed += 1
            elif value == 3:
                if args:
                    for i in args:
                        if out.succeeded:
                            for j in out['output'].split('\n')[5::]:
                                # Add support for regular expression match by Alex Li on 2024.06.18
                                if i not in j or not re.search(i, j, re.I):
                                    passed += 1
                                else:
                                    logger.error('we check------"%s" FAILED, now "%s" is in %s' % (i, i, label))
                                    failed += 1
                        else:
                            logger.error('we check------"%s" FAILED in %s' % (i, label))
                            failed += 1
                else:
                    if out.succeeded:
                        passed += 1
                    else:
                        logger.error('we run "%s" failed' % cmd)
                        failed += 1
    else:
        if args:
            for i in args:
                print('****************** we will check "%s" ******************' % i)
                # Add support for regular expression match by Alex Li on 2024.06.18
                if out.succeeded and (i in out['output'] or re.search(i, out['output'], re.I)):
                    passed += 1
                    logger.info('we check------"%s" passed in %s' % (i, label))
                else:
                    logger.error('we check------"%s" failed in %s' % (i, label))
                    failed += 1
        else:
            if out.succeeded:
                passed += 1
            else:
                logger.error('we run "%s" failed' % cmd)
                failed += 1
    print('======= the command [ %s ] output as follows:' % cmd)
    print(out['output'])
    print('===================================================================================================')
    if failed != 0:
        result[label] = 'failed'
        logger.error('we run the command------"%s" failed' % cmd)
    else:
        result[label] = 'passed'
        logger.info('we run the command------"%s" passed' % cmd)
    save_log(log_name, cmd, label, out['output'], pass1=passed, fail1=failed)
    return out


def run_cmd(cmd, label, result1, result2, log_name, result, check_point1=None, check_point2=None):
    """
    Check the checkpoints in the output of the commands
    """
    out = run_loc_cmd(cmd)
    pass1, fail1 = 0, 0
    logger.info('start run the command-%s' % cmd)
    print(out['output'])
    if check_point1:
        if out.succeeded and check_point1 in out['output']:
            result[label] = result1
            pass1 += 1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            fail1 += 1
            logger.info('we run %s--%s failed' % (label, cmd))
    elif check_point2:
        if out.succeeded and check_point2 in out['output']:
            result[label] = result1
            pass1 += 1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            fail1 += 1
            logger.info('we run %s--%s failed' % (label, cmd))
    elif check_point1 and check_point2:
        if out.succeeded and check_point2 in out['output'] and check_point1 in out['output']:
            result[label] = result1
            pass1 += 1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            fail1 += 1
            logger.info('we run %s--%s failed' % (label, cmd))
    else:
        if out.succeeded is True:
            result[label] = result1
            pass1 += 1
            logger.info('we run %s--%s %s' % (label, cmd, result1))
        else:
            result[label] = result2
            fail1 += 1
            logger.info('we run %s--%s %s' % (label, cmd, result2))
    save_log(log_name, cmd, label, out['output'], pass1=pass1, fail1=fail1)
    return out


def run_cmd1(cmd, label, result1, result2, log_name, result, check_point1=None, check_point2=None):
    """
    Check the checkpoint not in the output of the commands
    """
    out = run_loc_cmd(cmd)
    save_log(log_name, cmd, label, out['output'])
    print(out['output'])
    if check_point1:
        if out.succeeded and check_point1 not in out['output']:
            result[label] = result1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            logger.info('we run %s--%s failed' % (label, cmd))
    elif check_point2:
        if out.succeeded and check_point2 not in out['output']:
            result[label] = result1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            logger.info('we run %s--%s failed' % (label, cmd))
    elif check_point1 and check_point2:
        if out.succeeded and check_point2 not in out['output'] and check_point1 not in out['output']:
            result[label] = result1
            logger.info('we run %s--%s successful' % (label, cmd))
        else:
            result[label] = result2
            logger.info('we run %s--%s failed' % (label, cmd))
    else:
        if out.succeeded is True:
            result[label] = result1
            logger.info('we run %s--%s %s' % (label, cmd, result1))
        else:
            result[label] = result2
            logger.info('we run %s--%s %s' % (label, cmd, result2))
    return out


def initlog(logfile=None, level=None, log_stdout=True):
    """
    Initialize the log, default log level is NOTSET, it will write the log
    message into logfile, and also print onto the screen.
    If set log_stdout to False, will not print the log message onto the screen.
    """
    log_levels = {'debug': logging.DEBUG,
                  'info': logging.INFO,
                  'warn': logging.WARN,
                  'warning': logging.WARNING,
                  'error': logging.ERROR,
                  'fatal': logging.FATAL,
                  'critical': logging.CRITICAL}
    log = logging.getLogger()
    if level not in log_levels:
        print("ERROR: Invalid log level specified")
        print("ERROR: Try to use the default one: debug")
        level = 'debug'
    if logfile is None and not log_stdout:
        print("ERROR: At least one of logfile and log_stdout is required")
        raise Exception('Specify logfile or log_stdout for logging')
    log_level = log_levels.get(level, logging.NOTSET)
    log.setLevel(log_level)
    # Customize the log format
    fmt = logging.Formatter('%(asctime)s %(levelname)-5.5s %(name)s: %(message)s',
                            '%Y-%m-%d %H:%M:%S')
    # Write the log message into logfile
    if logfile:
        file_log_handler = logging.FileHandler(logfile)
        log.addHandler(file_log_handler)
        file_log_handler.setFormatter(fmt)
    # Print the log message onto the screen
    if log_stdout:
        screen_log_handler = logging.StreamHandler()
        log.addHandler(screen_log_handler)
        screen_log_handler.setFormatter(fmt)
    return log


def b64_strip_decode(input_string):
    '''
    :param input_string:
    :return: decoded string from base64 code without "="
    '''
    length = len(input_string) % 4
    if length == 0:
        return base64.b64decode(input_string)
    else:
        for i in range(4 - int(length)):
            input_string = input_string + '='
        return base64.b64decode(input_string)


def pwd_decode(username, pwd):
    '''
    :param username:
    :param pwd:
    :return: decoded string
    read username and encoded pwd from yaml file and return the decoded string of password
    '''
    user_code = base64.b64encode(username).strip('=')
    pwd_code = b64_strip_decode(pwd.strip(user_code))
    return pwd_code


def pwd_b64_encode(username, pwd):
    '''
    :param username:
    :param pwd:
    :return: encoded password to be userd in yaml file
    specify the username and password, return encoded password which is used in yaml file
    '''
    user_code = base64.b64encode(username).strip('=')
    pwd_code = base64.b64encode(pwd).strip('=')
    encoded_pwd_code = user_code + pwd_code
    return encoded_pwd_code


class Storage(dict):
    """
    A Storage object is like a dictionary except `obj.foo` can be used
    in addition to `obj['foo']`, and setting obj.foo = None deletes item foo.
    @example:
        >>> o = Storage(a=1)
        >>> print o.a
        1

        >>> o['a']
        1

        >>> o.a = 2
        >>> print o['a']
        2

        >>> del o.a
        >>> print o.a
        None
    """
    __slots__ = ()
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__
    __getitem__ = dict.get
    __getattr__ = dict.get

    '''
    __getnewargs__ = lambda self: getattr(dict, self).__getnewargs__(self)
    __repr__ = lambda self: '<Storage %s>' % dict.__repr__(self)
    __getstate__ = lambda self: None
    __copy__ = lambda self: Storage(self)
    '''

    def __getnewargs__(self):
        return getattr(dict, self).__getnewargs__(self)

    def __repr__(self):
        return '<Storage %s>' % dict.__repr__(self)

    def __getstate__(self):
        return None

    def __copy__(self):
        return Storage(self)

    def getlist(self, key):
        """
        Return a Storage value as a list.

        If the value is a list it will be returned as-is.
        If object is None, an empty list will be returned.
        Otherwise, [value] will be returned.

        Example output for a query string of ?x=abc&y=abc&y=def
        >>> request = Storage()
        >>> request.vars = Storage()
        >>> request.vars.x = 'abc'
        >>> request.vars.y = ['abc', 'def']
        >>> request.vars.getlist('x')
        ['abc']
        >>> request.vars.getlist('y')
        ['abc', 'def']
        >>> request.vars.getlist('z')
        []
        """
        value = self.get(key, [])
        if value is None or isinstance(value, (list, tuple)):
            return value
        else:
            return [value]

    def getfirst(self, key, default=None):
        """
        Return the first or only value when given a request.vars-style key.

        If the value is a list, its first item will be returned;
        otherwise, the value will be returned as-is.

        Example output for a query string of ?x=abc&y=abc&y=def
        >>> request = Storage()
        >>> request.vars = Storage()
        >>> request.vars.x = 'abc'
        >>> request.vars.y = ['abc', 'def']
        >>> request.vars.getfirst('x')
        'abc'
        >>> request.vars.getfirst('y')
        'abc'
        >>> request.vars.getfirst('z')
        """
        values = self.getlist(key)
        return values[0] if values else default

    def getlast(self, key, default=None):
        """
        Returns the last or only single value when
        given a request.vars-style key.

        If the value is a list, the last item will be returned;
        otherwise, the value will be returned as-is.

        Simulated output with a query string of ?x=abc&y=abc&y=def
        >>> request = Storage()
        >>> request.vars = Storage()
        >>> request.vars.x = 'abc'
        >>> request.vars.y = ['abc', 'def']
        >>> request.vars.getlast('x')
        'abc'
        >>> request.vars.getlast('y')
        'def'
        >>> request.vars.getlast('z')
        """
        values = self.getlist(key)
        return values[-1] if values else default


class StorageList(Storage):
    """
    Like Storage but missing elements default to [] instead of None
    """
    def __getitem__(self, key):
        return self.__getattr__(key)

    def __getattr__(self, key):
        if key in self:
            return getattr(self, key)
        else:
            r = []
            setattr(self, key, r)
            return r


def search_keyword_in_file(filename, keyword):
    """
    Search a keyword on a file, if found, will return True.
    @Example:
        search_keyword_in_file('file.txt', r'^test')
    """
    if not os.path.isfile(filename):
        logging.error('Nonexistent file specified: %s' % filename)
        return False
    with open(filename, 'r') as my_file:
        lines = my_file.readlines()
        for line in lines:
            if re.search(keyword, line):
                return True
    return False


def run_loc_cmd(cmd, shell=None, sys_env=True, user_env=None, timeout=None, log_file_path=None):
    """
    A simple interface to execute local shell command.
    @param:
        cmd: Local shell command
        shell: Specify interpreter for command running (i.e /bin/bash)
        sys_env: Enable system environment variables (True/False)
        user_env: Export user environment variables for command running (Dict)
        timeout: Run command within timeout (Positive integer number),
                 set as None will disable timeout.
    @Example:
        out = run_loc_cmd('echo $PATH', user_env={'PATH':'$PATH:/usr/sbin'})
        print out.cmd        # Print the executed command
        print out.stdout     # Print the standard output
        print out.stderr     # Print the error output
        print out.output     # Print the command output
        print out.succeeded  # Print the return status: True or False
        print out.exit       # Print the exit code: 0, 1, 2...
        print out.timeout    # Print the timeout status: True or False
    """
    env_variables = {}
    if sys_env:
        env_variables = os.environ.copy()
    if not isinstance(user_env, dict):
        user_env = {}
    for k, v in user_env.items():
        k = str(k)
        v = str(v)
        if (k == 'PATH' or re.search(r':\${?%s}?' % k, v) or re.search(r'\${?%s}?:' % k, v)):
            # Variable that need to append
            try:
                final_elems = []
                ori_elems = v.split(':')
                if k in env_variables:
                    ori_elems.extend(env_variables[k].split(':'))
                for j in filter(not_empty, ori_elems):
                    if j.strip('{|}|$') != k:
                        final_elems.append(j)
                env_variables[k] = os.pathsep.join(list(set(final_elems)))
            except Exception as e:
                logging.error('Failed to append environment variable: %s' % k)
                logging.debug(e)
        else:
            # Variable that need to create or replace
            env_variables[k] = v
    if (not (isinstance(timeout, int) or isinstance(timeout, float)) or timeout <= 0):
        timeout = None
    out_stream = tempfile.SpooledTemporaryFile()
    err_stream = tempfile.SpooledTemporaryFile()

    if isinstance(cmd, dict) and "cmd_line" in cmd and "cmd_log" in cmd:
        cmd = cmd["cmd_line"]
    logger.debug('Command: %s', cmd)
    # Use PowerShell instead of bat on Windows, Oct 21, 2021
    p = subprocess.Popen(["powershell.exe", cmd], shell=True, stdin=subprocess.PIPE,
                         stdout=out_stream, stderr=err_stream,
                         executable=shell, close_fds=True,
                         env=env_variables)
    out = Storage()
    out.cmd = cmd
    out.timeout = False
    out.time_spend = -1
    if timeout:
        t_start = time.time()
        while p.poll() is None:
            t_current = time.time()
            if t_current - t_start >= timeout:
                out.timeout = True
                # os.killpg(p.pid, signal.SIGTERM)
                # Use taskill on Windows instead of os.killpg(), Apr 19, 2022
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(p.pid)])
                out.time_spend = timeout
                break
            time.sleep(0.1)
        time_spend = int(t_current - t_start)
        out.time_spend = time_spend
    else:
        p.communicate()
    out.exit = p.returncode
    out.succeeded = False
    if p.returncode == 0:
        out.succeeded = True
    out.stdout = ""
    out.stderr = ""
    out.output = ""
    out_stream.seek(0)
    err_stream.seek(0)
    out.stdout = out_stream.read().strip()
    out.stderr = err_stream.read().strip()
    out_stream.close()
    err_stream.close()
    # To resolve UnicodeDecodeError: 'utf-8' codec can't decode byte 0xd0 in position 231: invalid continuation byte
    if out.stdout and out.stderr:
        out.output = out.stderr.decode("utf-8", "ignore") + '\n' + out.stdout.decode("utf-8", "ignore")
    else:
        out.output = out.stderr.decode("utf-8", "ignore") + out.stdout.decode("utf-8", "ignore")

    if log_file_path:
        with open(log_file_path, 'w') as f:
            f.write(out.output)
    return out


def dict_output(dict1, flag):
    if flag == "1":
        print("we will print the key and value")
        print("{")
        pass_items = []
        fail_items = []

        for key, value in dict1.items():
            if isinstance(value, str) and ("fail" in value.lower()):
                fail_items.append((key, value))
            else:
                pass_items.append((key, value))

        for key, value in pass_items:
            print("%s:%s" % (key, value))

        for key, value in fail_items:
            print("%s:%s" % (key, value))
        print("}")


def is_empty_file(filename):
    """
    Check if target file is empty.
    """
    try:
        data = open(filename).read()
        return len(data) == 0
    except:
        return False


def is_empty_dir(dirname):
    """
    Check if target folder is empty.
    """
    if os.path.isdir(dirname) and not os.listdir(dirname):
        return True
    return False


def list_file(directory, pattern=None):
    """
    List all files with pattern under a directory.
    """
    filenames = []
    try:
        for i in os.listdir(directory):
            if re.search(pattern, i):
                filenames.append(i)
    except:
        pass
    return filenames


# This function is only implemented on Windows.
# The timeout interval, in seconds, '-1' means infinite.
def runAsAdmin(app, params, timeout=-1):
    # WAIT_TIMEOUT, WAIT_FAILED, WAIT_ABANDONED = 0x00000102L, 0xFFFFFFFF, 0x00000080L

    if os.name != 'nt':
        raise RuntimeError("This function is only implemented on Windows.")

    import win32api
    import win32con
    import win32event
    import win32process
    from win32com.shell.shell import ShellExecuteEx
    from win32com.shell import shellcon

    _timeout, cmdDir = '', 0
    # showCmd = win32con.SW_SHOWNORMAL
    showCmd = win32con.SW_HIDE
    lpVerb = 'runas'  # causes UAC elevation prompt.
    procInfo = ShellExecuteEx(nShow=showCmd,
                              fMask=shellcon.SEE_MASK_NOCLOSEPROCESS,
                              lpVerb=lpVerb,
                              lpFile=app,
                              lpParameters=params)

    if timeout is not None:
        # Convert miliseconds to seconds.
        _timeout = timeout * 1000 if timeout > 0 else timeout
        procHandle = procInfo['hProcess']
        rst = win32event.WaitForSingleObject(procHandle, _timeout)

        if rst == WAIT_TIMEOUT:
            raise RuntimeError("Execution timeout - app:'%s' params:'%s'." % (app, params), WAIT_TIMEOUT)

        rc = win32process.GetExitCodeProcess(procHandle)
    else:
        rc = None

    return rc


def mkdir(path):
    """
    Create a directory.
    @Example:
        mkdir('/tmp/folder')
    """
    try:
        if not os.path.exists(path):
            os.makedirs(path)
            os.chmod(path, 0o777)
        return True
    except Exception as e:
        logging.error('Failed to create the folder: %s' % path)
        logging.debug(e)
        return False


def save_file(file_name, output):
    with open(file_name, 'a+') as f:
        f.write(str(output))


def create_file(path, filename):
    if os.path.exists('%s/%s' % (path, filename)):
        logger.info('we have the build_file, do not create')
    else:
        cmd_file = 'cd %s; touch %s' % (path, filename)
        out = run_loc_cmd(cmd_file)
        if out.succeeded:
            logger.info('we created %s successful' % filename)
        else:
            print('we created %s fail' % filename)


def check_file(path, filename):
    """
    Check a file is empty or not

    @param:
       path
       filename
    """
    if os.path.getsize('%s/%s' % (path, filename)) > 0:
        return True
        print('the file is not Empty')
    else:
        return False
        print('the file is Empty')


def utf16_to_utf8(source, target):
    """
    Change utf-16 LE file to utf-8 file on Windows

    @param:
      source: source file
      traget: target file
    """
    try:
        with open(source, 'rb') as source_file:
            with open(target, 'w+b') as target_file:
                contents = source_file.read()
                target_file.write(contents.decode('utf-16').encode('utf-8'))
    except FileNotFoundError:
        print("That file doesn't seem to exist.")


def copy_binnary_to_bin(source_dir, target_dir, types):
    """
    copy all binnary to bin file
    """
    import os
    import shutil

    # source_dir = r'C:\ProgramData\NVIDIA Corporation\CUDA Samples\v12.9\build\Samples'
    # target_dir = r'C:\ProgramData\NVIDIA Corporation\CUDA Samples\v12.9\bin\win64\Debug'
    os.makedirs(target_dir, exist_ok=True)
    for root, dirs, files in os.walk(source_dir):
        if types in root:
            for file in files:
                if file.endswith('.exe'):
                    source_file = os.path.join(root, file)
                    target_file = os.path.join(target_dir, file)
                    shutil.copy2(source_file, target_file)
                    print(f"copied: {source_file} -> {target_file}")

    print("copy finished")


def build_all_samples(cuda_version='11.8', vs_devenv=r'C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv', vs_solution_file='Samples_VS2022.sln', build_type=3, build_mode='DEBUG'):
    """
    API for building all CUDA samples on Windows with Visual Studio 2019/2022 Professional

    @param:
       cuda_version: '12.0', '11.8', '11.7'
       vs_denv: 'C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv'
       vs_solution_file: 'Samples_VS2022.sln'
       build_type:
           0 Skip Build CUDA Samples
           1 Increment Build CUDA Samples
           3 Rebuild CUDA Samples
       build_mode: 'DEBUG', 'RELEASE', 'ALL'
    @example:
       build_all_samples(cuda_version=cuda_short_version, vs_devenv=devenv, vs_solution_file=solution_file, build_type=3, build_mode='DEBUG')
    """
    git_dict = {
        '11.8': 'public-release-r11.8',
        '12.0': 'public-release-r12.0',
        '12.1': 'public-release-r12.1',
        '12.2': 'public-release-r12.2',
        '12.3': 'public-release-r12.3',
        '12.4': 'public-release-r12.4',
        '12.5': 'v12.5',
        '12.6': 'public-release-r12.6',
        '12.7': 'public-release-r12.7',
        '12.8': 'v12.8',
        '12.9': 'v12.9',
        '13.0': 'master'
    }
    # Check the Samples code from GitLab repo
    repo_cuda_samples_path = r'C:\ProgramData\NVIDIA Corporation\CUDA Samples\v{}'.format(cuda_version)
    if cuda_version >= '11.6':
        repo_owner = 'cuda-samples'
        repo_token = os.environ.get('GITLAB_CUDA_SAMPLES_TOKEN', '')
        repo_branch_name = git_dict[cuda_version]
        user = b64_strip_decode('Y3FhdXNlcg').decode()
        password = b64_strip_decode('Y3FhdGVzdA').decode()
        _repo_clone = r'git clone -b {} https://github.com/NVIDIA/cuda-samples.git "{}"'.format(repo_branch_name, repo_cuda_samples_path)
        _repo_clone_msg = r'git clone -b {} https://{}@gitlab-master.nvidia.com/{}/cuda-samples.git "{}"'.format(repo_branch_name, '****:****', repo_owner, repo_cuda_samples_path)
        if cuda_version < '13.0':
            _repo_clone = r'git clone -b {} https://github.com/NVIDIA/cuda-samples.git "{}"'.format(repo_branch_name, repo_cuda_samples_path)
            _repo_clone_msg = r'git clone -b {} https://github.com/NVIDIA/cuda-samples.git "{}"'.format(repo_branch_name, repo_cuda_samples_path)
            if cuda_version == '12.6' or cuda_version == '12.7':
                _repo_clone_msg = r'git clone -b {} https://{}@gitlab-master.nvidia.com/{}/cuda-samples.git "{}"'.format(repo_branch_name, '****:****', repo_owner, repo_cuda_samples_path)
                _repo_clone = r'git clone -b {} https://{}@gitlab-master.nvidia.com/{}/cuda-samples.git "{}"'.format(repo_branch_name, repo_token, repo_owner, repo_cuda_samples_path)
        else:
            _repo_clone = r'git clone https://{}@gitlab-master.nvidia.com/{}/cuda-samples.git "{}"'.format(repo_token, repo_owner, repo_cuda_samples_path)
            _repo_clone_msg = r'git clone https://{}@gitlab-master.nvidia.com/{}/cuda-samples.git "{}"'.format('****:****', repo_owner, repo_cuda_samples_path)
        _repo_pull = r'cd "{}" ; git pull'.format(repo_cuda_samples_path)
        if os.path.exists(repo_cuda_samples_path):
            print(r"The folder {} already exists. Execute git pull command: {}".format(repo_cuda_samples_path, _repo_pull))
            out = run_loc_cmd(_repo_pull, timeout=600)
            print(out['output'])
            cmake_list_cmd = 'cd "{}"; C:/CTT/cmder/vendor/git-for-windows/usr/bin/mv CMakeLists.txt CMakeLists.txt.bak; C:/CTT/cmder/vendor/git-for-windows/usr/bin/wget --user {} --password {} http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest/P1072_T2829284/CMakeLists.txt'.format(repo_cuda_samples_path, user, password)
            run_loc_cmd(cmake_list_cmd)
            print(cmake_list_cmd)
        else:
            print(r"The folder {} does not exist. Execute git clone command: {} ".format(repo_cuda_samples_path, _repo_clone_msg))
            out = run_loc_cmd(_repo_clone, timeout=600)
            print(out['output'])
            cmake_list_cmd = 'cd "{}"; C:/CTT/cmder/vendor/git-for-windows/usr/bin/mv CMakeLists.txt CMakeLists.txt.bak; C:/CTT/cmder/vendor/git-for-windows/usr/bin/wget --user {} --password {} http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest/P1072_T2829284/CMakeLists.txt'.format(repo_cuda_samples_path, user, password)
            run_loc_cmd(cmake_list_cmd)
            print(cmake_list_cmd)

    # Skip building all samples
    if build_type == 0:
        print("BUILD_SAMPLES: False")
        return
    # Should install Visual Studio 2019/2022 Professional first
    _devenv = r'"{}"'.format(vs_devenv)
    solution_path = r'{}'.format(repo_cuda_samples_path)
    _solution_file = vs_solution_file
    if cuda_version > '12.9':
        _devenv = 'cmake --build . --parallel 50'
        _solution_file = 'cmake --build . --target copy_binaries'
    _solution_path = r'"{}\{}"'.format(solution_path, _solution_file)
    # 1 Increment Build CUDA Samples
    if 1 == build_type:
        _build_type = '/Build'
        if 'DEBUG' == build_mode or 'ALL' == build_mode:
            # Build All CUDA Samples in Debug Mode
            print("(Build)Building All CUDA Samples in Debug Mode: %s " % (_solution_path))
            _configuration = r'"Debug"'
            if cuda_version > '12.9':
                _configuration = ' '

            # Build command on Window
            if cuda_version < '13.0':
                _build = r'cd "{}" ; cmd.exe /c {} {} {} {}'.format(solution_path, _devenv, _solution_file, _build_type, _configuration)
            else:
                _build = r'cd "{}" ; mkdir build; cd build; cmake -S .. -B . -DCMAKE_BUILD_TYPE=Debug -DENABLE_CUDA_DEBUG=True -DALL_CCFLAGS="-G" -DBUILD_SELECTED_CQA_SAMPLES=ON; {}; {}'.format(solution_path, _devenv, _solution_file)
                print('since cuda 13.0, the build type is {}'.format(build_type))
                print('since cuda 13.0, the configuration is empty{}'.format(_configuration))
            print("(Build)Execute command:  " + str(_build))
            out = run_loc_cmd(_build, timeout=1800)
            print(out['output'])
            print("(Build)Building All CUDA Samples in Debug Mode Done!!!")
        elif 'RELEASE' == build_mode or 'ALL' == build_mode:
            # Build All CUDA Samples in Release Mode
            print("(Build)Building All CUDA Samples in Release Mode: %s " % (_solution_path))
            _configuration = r'"Release"'
            if cuda_version > '12.9':
                _configuration = ' '

            # Build command on Windows
            if cuda_version < '13.0':
                _build = r'cd "{}" ; cmd.exe /c {} {} {} {}'.format(solution_path, _devenv, _solution_file, _build_type, _configuration)
            else:
                _build = r'cd "{}" ; mkdir build; cd build; cmake -S .. -B . -DCMAKE_BUILD_TYPE=Release -DBUILD_SELECTED_CQA_SAMPLES=ON; {}; {}'.format(solution_path, _devenv, _solution_file)
                print('since cuda 13.0, the build type is {}'.format(build_type))
                print('since cuda 13.0, the configuration is empty{}'.format(_configuration))
            print("(Build)Execute command:  " + str(_build))
            out = run_loc_cmd(_build, timeout=1800)
            print(out['output'])
            print("(Build)Building All CUDA Samples in Release Mode Done!!!")
        else:
            print("Wrong build_mode parameter, please check ~~~")
            return
    # 3 Rebuild CUDA Samples
    elif 3 == build_type:
        _build_type = '/Rebuild'
        if 'DEBUG' == build_mode or 'ALL' == build_mode:
            # Build All CUDA Samples in Debug Mode
            print("(Rebuild)Building All CUDA Samples in Debug Mode: %s " % (_solution_path))
            _configuration = r'"Debug"'
            if cuda_version > '12.9':
                _configuration = ' '

            # Build command on Window
            if cuda_version < '13.0':
                _build = r'cd "{}" ; cmd.exe /c {} {} {} {}'.format(solution_path, _devenv, _solution_file, _build_type, _configuration)
            else:
                _build = r'cd "{}" ; mkdir build; cd build; cmake -S .. -B . -DCMAKE_BUILD_TYPE=Debug -DALL_CCFLAGS="-G" -DENABLE_CUDA_DEBUG=True -DBUILD_SELECTED_CQA_SAMPLES=ON; {}; {}'.format(solution_path, _devenv, _solution_file)
                print('since cuda 13.0, the build type is {}'.format(build_type))
                print('since cuda 13.0, the configuration is empty{}'.format(_configuration))
                print('_build is {}'.format(_build))
            print("(Rebuild)Execute command:  " + str(_build))
            out = run_loc_cmd(_build, timeout=1800)
            print(out['output'])
            print("(Rebuild)Building All CUDA Samples in Debug Mode Done!!!")
        elif 'RELEASE' == build_mode or 'ALL' == build_mode:
            # Build All CUDA Samples in Release Mode
            print("(Rebuild)Building All CUDA Samples in Release Mode: %s " % (_solution_path))
            _configuration = r'"Release"'
            if cuda_version > '12.9':
                _configuration = ' '

            # Build command on Windows
            if cuda_version < '13.0':
                _build = r'cd "{}" ; cmd.exe /c {} {} {} {}'.format(solution_path, _devenv, _solution_file, _build_type, _configuration)
            else:
                _build = r'cd "{}" ; mkdir build; cd build; cmake -S .. -B . -DCMAKE_BUILD_TYPE=Release -DBUILD_SELECTED_CQA_SAMPLES=ON; {}; {}'.format(solution_path, _devenv, _solution_file)
                print('since cuda 13.0, the build type is {}'.format(build_type))
                print('since cuda 13.0, the configuration is empty{}'.format(_configuration))
            print("(Rebuild)Execute command:  " + str(_build))
            out = run_loc_cmd(_build, timeout=1800)
            print(out['output'])
            print("(Rebuild)Building All CUDA Samples in Release Mode Done!!!")
        else:
            print("Wrong build_mode parameter, please check ~~~")
            return
    else:
        print("Wrong build_type parameter, please check ~~~")
        return


import win32gui
import re


class WindowManager:
    """Encapsulates some calls to the winapi for window management"""
    def __init__(self):
        """Constructor"""
        self._handle = None

    def find_window(self, class_name, window_name=None):
        """ Find the handle of window by classname"""
        self._handle = win32gui.FindWindow(class_name, window_name)

    def _window_enum_callback(self, hwnd, class_name_wildcard_list):
        """ Pass parameters to win32gui.EnumWindows(),check all the open windows"""
        class_name, wildcard = class_name_wildcard_list
        if win32gui.GetClassName(hwnd) == class_name and re.match(wildcard, str(win32gui.GetWindowText(hwnd))) is not None:
            self._handle = hwnd

    def find_window_wildcard(self, class_name, wildcard):
        """Find handle of window by class name and  window_name(Fuzzy query)"""
        self._handle = None
        win32gui.EnumWindows(self._window_enum_callback, [class_name, wildcard])
        return self._handle

    def set_foreground(self):
        """put the window in the foreground"""
        win32gui.SetForegroundWindow(self._handle)

    def get_hwnd(self):
        """return hwnd for further use"""
        return self._handle


def get_os_type():
    """
    Get the OS type of windows
    """
    get_os_type_cmd = 'systeminfo | findstr "System Type"'
    get_os_type_out = run_loc_cmd(get_os_type_cmd)
    print(get_os_type_cmd)
    if get_os_type_out.succeeded:
        # To resolve the "ZWNBSP in get_os_type string" problem, updated by Alex Li on Sep 7, 2023
        get_os_type_output = get_os_type_out['output']
        get_os_type_temp = get_os_type_output.split('\n')[-3].strip(' ').split(':')[1].strip()
        print("get_os_type_temp is : %s" % get_os_type_temp)
        if 'ARM' in get_os_type_temp:
            return 'woa'
        else:
            return 'x86_win'
    else:
        print('we get the get_os_type failed, please check your OS type, exit.......')
        return 'x86_win'


def get_dvs_package(tool, arch, cuda_version=None):
    """
    fetch latest dvs package
    :param tool: cupti, rebel, gdb, sanitizer, cuda_app, nvprof, memcheck, rebel_main
    :param arch: x86, arm, power, windows
    :param cuda_version: 11.6, 11.7, 11.8, 12.0, None...
    :return: full path with filename
    """
    Agora = {
        "11.6": "DTC_G",
        "11.7": "DTC_H",
        "11.8": "DTC_C",
        "12.0": "DTC_D",
        "12.1": "DTC_F",
        "12.2": "DTC_I",
        "12.3": "CUDA12.3",
        "12.4": "CUDA12.4",
        "12.5": "CUDA12.5",
        "12.6": "CUDA12.6",
        "12.7": "CUDA12.7",
        "12.8": "CUDA12.8",
        "12.9": "CUDA12.9",
        "13.0": "CUDA13.0",
        "13.1": "CUDA13.1"
    }
    Driver = {
        "11.6": "r510",
        "11.7": "r515",
        "11.8": "r520",
        "12.0": "r525",
        "12.1": "r530",
        "12.2": "r535",
        "12.3": "r545",
        "12.4": "r550",
        "12.5": "r555",
        "12.6": "r560",
        "12.7": "r565",
        "12.8": "r570",
        "12.9": "r575",
        "13.0": "r580",
        "13.1": "r590"
    }
    url_component = {
        "cupti": {
            "x86": "Linux%20Cupti",
            "arm": "Armserver%20Cupti",
            "power": "Linux%20Cupti%20PPC64LE",
            "windows": "Windows%20Cupti",
            "woa": "WoA%20Cupti"
        },
        "nexus": {
            "windows": "Windows%20Nexus",
            "arm": "aarch64sbsa%20GPGPU%20CUDA%20GDB",
            'woa': "WoA%20Nexus"
        },
        "rebel": {
            "x86": "Linux",
            "arm": "Balboa",
            "power": "PPC64LE",
            "windows": "Windows",
            'woa': 'WoA'
        },
        "sanitizer": {
            "x86": "Linux",
            "arm": "Balboa",
            "power": "PPC64LE",
            "windows": "Windows",
            'woa': 'WoA'
        },
        "cuda_app": {
            "x86": "Linux%20AMD64%20CUDA%20DVS%20Test",
            "arm": "Linux%20aarch64sbsa%20CUDA%20DVS%20Test",
            "power": "Linux%20ppc64le%20CUDA%20DVS%20Test",
            "windows": "Windows%20AMD64%20Cuda%20Sanity",
            'woa': "Windows%20aarch64%20Cuda%20Sanity"
        },
        "nvprof": {
            "x86": "Linux",
            "windows": "Windows"
        },
        "memcheck": {
            "x86": "Linux%20AMD64",
            "power": "Linux%20ppc64le",
            "windows": "Windows%20AMD64"
        },
        "cublas": {
            "x86": "AMD64%20GPGPU%20CUDA%20CUBLAS",
            'arm': ""
        }
    }

    original_url = 'http://ausdvs.nvidia.com/Query/PackageDetails?which_option=find_cl_for_package&onlySuccessful=true&package='
    tool = tool.lower()
    if tool == 'cupti':
        # Agora_Rel_DTC_C%20Release%20Linux%20Cupti
        url = "{}Agora_Rel_{}%20Release%20{}".format(original_url, Agora[cuda_version], url_component[tool][arch])
        print(url)
    if tool == 'rebel':
        # Agora_Rel_DTC_C%20Release%20Linux%20Rebel%20Public
        url = "{}Agora_Rel_{}%20Release%20{}%20Rebel%20Public".format(original_url, Agora[cuda_version],
                                                                      url_component[tool][arch])
    if tool == 'rebel_main':
        # Agora_Dev_Rebel_Main%20Release%20Linux%20Rebel%20Public
        url = "{}Agora_Dev_Rebel_Main%20Release%20{}%20Rebel%20Public".format(original_url, url_component[tool.replace("_main", "")][arch])
    if tool == 'nexus':
        # gpu_drv_r520_00%20Release%20Linux%20AMD64%20GPGPU%20CUDA%20DEBUGGER
        url = "{}Agora_Rel_{}%20Release%20{}%20Public".format(original_url, Agora[cuda_version], url_component[tool][arch])
    if tool == 'sanitizer':
        # Agora_Rel_DTC_C%20Release%20Linux%20Sanitizer%20Public
        url = "{}Agora_Rel_{}%20Release%20{}%20Sanitizer%20Public".format(original_url, Agora[cuda_version],
                                                                          url_component[tool][arch])
    if tool == 'cuda_app':
        # gpu_drv_r520_00%20Release%20Linux%20ppc64le%20CUDA%20DVS%20Test
        url = "{}gpu_drv_{}_00%20Release%20{}".format(original_url, Driver[cuda_version], url_component[tool][arch])
    if tool == 'nvprof':
        # gpu_drv_r520_00%20Release%20Linux%20AMD64%20GPGPU%20CUDA%20NVPROF
        url = "{}gpu_drv_{}_00%20Release%20{}%20AMD64%20GPGPU%20CUDA%20NVPROF".format(original_url,
                                                                                      Driver[cuda_version],
                                                                                      url_component[tool][arch])
    if tool == 'memcheck':
        # gpu_drv_r520_00%20Release%20Linux%20AMD64%20GPGPU%20CUDA%20MEMCHECK
        url = "{}gpu_drv_{}_00%20Release%20{}%20GPGPU%20CUDA%20MEMCHECK".format(original_url, Driver[cuda_version],
                                                                                url_component[tool][arch])
    if tool == 'cublas':
        url = "{}gpu_drv_{}_00%20Release%20Linux%20{}".format(original_url, Driver[cuda_version], url_component[tool][arch])
    # print("%s_%s_%s API:%s" % (tool, arch, cuda_version, url))
    try:
        website = urllib2.urlopen(url)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False
    html = website.read()
    dvs_url = html.decode().split('"')[-2].strip(' ')
    # print("%s_%s_%s URL:%s" % (tool, arch, cuda_version, dvs_url))
    print(dvs_url)
    return dvs_url


def kill_process_by_name(process_name):
    os.system(r'taskkill /f /t /im "%s.exe"' % process_name)


def process_is_running(process_name):
    try:
        out = run_loc_cmd('cmd.exe /c tasklist | findstr ' + process_name)
        process = len(out["stdout"].decode('utf-8'))
        if process > 0:
            return True
        else:
            return False
    except:
        return False


# Judge a string is a number or not. Added by Alex Li on Dec 20, 2023
def is_int_number(s):
    try:
        int(s)
        return True
    except ValueError:
        pass
    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass
    return False


class Out:
    def __init__(self, cmd):
        self.cmd = cmd
        self.timeout = False
        self.ltimeout = False

    def set_timeout(self):
        self.timeout = True

    def set_ltimeout(self):
        self.ltimeout = True

    def set_output(self, output):
        self.output = output

    def set_rc(self, return_code):
        self.rc = return_code

    def set_cmd(self, cmd):
        self.cmd = cmd

    def set_logger(self, logger):
        self.logger = logger

    def set_qsize(self, qsize):
        if not qsize:
            self.result_queue = collections.deque()
        else:
            self.result_queue = collections.deque(maxlen=qsize)

    def out_log(self):
        self.logger = logger
        output = "\nrun $ cmd:\n{0}\
                \nrun $ output:\n{1}\
                \nrun $ return code: {2}".format(self.cmd, self.output, self.rc)
        if self.timeout or self.ltimeout:
            output += "\nrun $ timeout: {0}\
                \nrun $ ltimeout: {1}".format(self.timeout, self.ltimeout)

        if self.logger.level == 10:
            self.logger.info(output)
        else:
            self.logger.debug(output)


def run_test_cmd(cmd, logfile=None, workspace=None, sys_env=True, user_env=None, timeout=None, ltimeout=None, intime=False, qsize=None):
    """New implementation for running command, main feature is to run command in specified workspace & set whole process timeout & set line output timeout & final output qsize could be consise

    Args:
        cmd (str): run command
        logfile (str, optional): log to store. Defaults to None for not storing log.
        workspace (str, optional): dir to run command. Defaults to None at current pwd.
        sys_env (bool, optional): for whether using sys_env. Defaults to True to use sys_env.
        user_env (dict, optional): for adding customized user_env, dict for {$NAME: $Value}. Defaults to None.
        timeout (int, optional): for whole subprocess.Popen process. Defaults to None for 9345s.
        ltimeout (int, optional): for line output hang timeout. Defaults to None for 1028s.
        intime (bool, optional): for intime print out output. Defaults to False for not print out.
        qsize (int, optional): for concise output to qsize line. Defaults to None for output all stdoutput.
    """
    def not_empty(x):
        if x is not None and str(x).strip() != '':
            return x

    def timeout_handler(signum, frame):
        logger.critical("Line output hang exceed Timeout Limit - Triggered by signal")
        raise TimeoutError("Line output hang, execution is longer than expected")

    env_variables = {}
    if sys_env:
        env_variables = os.environ.copy()
    if not isinstance(user_env, dict):
        user_env = {}
    for k, v in user_env.items():
        k = str(k)
        v = str(v)
        if (k in ['PATH', 'LD_LIBRARY_PATH'] or re.search(r':\${?%s}?' % k, v) or re.search(r'\${?%s}?:' % k, v)):
            try:
                final_elems = []
                ori_elems = v.split(':')
                if k in env_variables:
                    ori_elems.extend(env_variables[k].split(':'))
                for j in filter(not_empty, ori_elems):
                    if j.strip('{|}|$') != k:
                        final_elems.append(j)
                env_variables[k] = os.pathsep.join(list(set(final_elems)))
            except Exception as e:
                logger.error("Failed to append environment variable: {0}".format(k))
                logger.error(e)
        else:
            # Variable that need to create or replace
            env_variables[k] = v
    if workspace is not None:
        logger.info("run @ {}".format(workspace))
    logger.info("run > {}".format(cmd))

    out = Out(cmd=cmd)
    child = subprocess.Popen(["powershell.exe", cmd], shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, cwd=workspace, env=env_variables)

    if not timeout:
        timeout = 9345
    if not ltimeout:
        ltimeout = 1028

    if logfile:
        with open(logfile, 'a') as f:
            test_time = time.strftime("%Y-%m-%d %a %H:%M:%S %Z", time.localtime(time.time()))
            f.write("\n===================={}====================\n".format(test_time))
            f.write(">>>>>>>>>> {}\n".format(cmd))
        log_file_handler = open(logfile, "ab")

    if not qsize:
        result_queue = collections.deque()
    else:
        result_queue = collections.deque(maxlen=qsize)

    t_start = time.time()
    line = iter(child.stdout.readline, b'')
    try:
        # TODO on Windows: AttributeError: module 'signal' has no attribute 'SIGALRM'  by Alex Li on Apr 19, 2024
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(ltimeout)
        out_line = next(line)
    except StopIteration:
        out_line = None
    except TimeoutError as e:
        out_line = None
        kill_process(child.pid)
        out.set_ltimeout()
        logger.critical("CMD Line Output Hang {}".format(ltimeout))
        if logfile:
            log_file_handler.write("CMD Line Output Hang {}".format(ltimeout).encode())

    while child.poll() is None and out_line:
        output_line = out_line.decode(encoding="utf-8", errors="backslashreplace").strip()
        if intime:
            print(output_line)
        if logfile:
            log_file_handler.write(out_line)
        result_queue.append(output_line)
        t_current = time.time()
        if t_current - t_start >= timeout:
            kill_process(child.pid)
            out.set_timeout()
            logger.critical("CMD Program Execution timeout {}".format(timeout))
            if logfile:
                log_file_handler.write("CMD Program Execution timeout {}".format(timeout).encode())
            break
        try:
            line = iter(child.stdout.readline, b'')
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(ltimeout)
            out_line = next(line)
        except StopIteration:
            out_line = None
        except TimeoutError as e:
            out_line = None
            kill_process(child.pid)
            out.set_ltimeout()
            logger.critical("CMD Line Output Hang {}".format(ltimeout))
            if logfile:
                log_file_handler.write("CMD Line Output Hang {}".format(ltimeout).encode())

    signal.alarm(0)
    child.wait()
    output_text = "\n".join(result_queue)
    rc = child.returncode
    out.set_rc(rc)
    out.set_output(output_text)
    out.out_log()
    if logfile:
        log_file_handler.close()
    return out


def download_rebel_smoke_test_pacakge(package_path, cuda_version):
    """
       Download rebel smoke test package to a defined path
       :param package_path: the path to store the rebel smoke package
       :param cuda_version: CUDA Version, like "12.0", "12.4", "12.8", ...
       :return: full path with filename
       """
    rebel_smoke_app_path = os.path.join(package_path, r'Rebel-Release-public-test\target\windows-desktop-win7-x64\cuda')
    print("The Rebel smoke test app path is %s" % rebel_smoke_app_path)

    if not os.path.exists(package_path):
        mkdir(package_path)
    elif os.path.exists(package_path) and not os.path.exists(rebel_smoke_app_path):
        pass
    else:
        print(">>>>>> The Rebel smoke test pacakge has been downloaded and uncompressed, no need to download it again!")
        return
    rebel_dvs_url = get_dvs_package(tool='rebel', arch='windows', cuda_version=cuda_version)
    print(">>>>>> The latest %s DVS URL is %s" % ('Rebel', rebel_dvs_url))
    rebel_package = rebel_dvs_url.split('/')[-1]
    print('>>>>>> The %s package is : %s' % ("Rebel", rebel_package))
    download_cmd = r"cd %s; lftp -c 'set ssl:verify-certificate false ; glob -- pget -n 80 %s'" % (package_path, rebel_dvs_url)
    print(">>>>>> The download Rebel smoke test package is %s" % download_cmd)
    download_out = run_loc_cmd(download_cmd)
    print(download_out['output'])
    uncompress_cmd = r"cd %s; unzip -o %s; unzip -o Rebel-Release-public-test-Windows.CUDA%s.zip" % (package_path, rebel_package, cuda_version)
    print(">>>>>> The uncompress Rebel smoke test package is %s" % uncompress_cmd)
    uncompress_out = run_loc_cmd(uncompress_cmd)   # Rebel-Release-public-test-Windows.CUDA12.6.zip
    print(uncompress_out['output'])

    return


# Get driver mode on Windows: WDDM (Windows Display Driver Model) or TCC (Tesla Compute Cluster Driver) or MCDM (MSFT Compute Driver Module)
def common_get_driver_mode():
    """
    Get the driver mode on Windows, return TCC or WDDM or MCDM
    WDDM (Windows Display Driver Model)
    TCC (Tesla Compute Cluster Driver)
    MCDM (MSFT Compute Driver Module)
    return: driver_mode ('WDDM', or 'TCC', or 'MCDM')
    """
    cmd = r"nvidia-smi --query-gpu=driver_model.current --format=csv,noheader"
    print(">>>>>> The command is %s" % cmd)
    out = run_loc_cmd(cmd)
    if out.succeeded:
        print(">>>>>> We get the driver mode on Windows, Driver mode list is %s." % out['output'].split('\r\n'))
        driver_mode = out['output'].split('\r\n')[0]   # multiple GPUs on Windows, I get the first GPU's driver mode.
        print(">>>>>> We get the driver mode on Windows successfully. Driver mode is %s." % driver_mode)
        return driver_mode
    else:
        print('>>>>>> We get driver mode on Windows failed')
        return None


if __name__ == '__main__':
    # Test strings and numbers
    print(is_int_number('foo'))  # False
    print(is_int_number('1'))  # True
    print(is_int_number('1.3'))  # False
    print(is_int_number('-1.37'))  # False
    print(is_int_number('1e3'))  # False

    # Test unicode
    # Arabic 5
    print(is_int_number('٥'))  # True
    # Thai 2
    print(is_int_number('๒'))  # True
    # Chinese
    print(is_int_number('四'))  # True
    # Copyright
    print(is_int_number('©'))  # False

    aa = common_get_driver_mode()
    print(aa)
    if aa == 'MCDM':
        print("The drive mode is MCDM")
    else:
        print('The driver mode is not MCDM. It is %s' % aa)
