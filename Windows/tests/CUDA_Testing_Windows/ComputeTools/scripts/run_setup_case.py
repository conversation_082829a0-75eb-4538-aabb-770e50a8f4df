# -*- encoding: utf-8 -*-
import os
import sys
import yaml
import json
try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser

import argparse
import base64
from common_utils import *
import logging

import time
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2

try:
    # Python3Linux
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
import socket
import string
from mobile_yaml_mgr import MobileYamlManger
import threading

setup_yaml = 'C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml'
yaml_mgr = MobileYamlManger(setup_yaml)
case_config = yaml_mgr.load()
cuda_major = case_config['global']['env']['CUDA_MAJOR']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
device = case_config['global']['env']['DEVICES']
bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
platform = case_config['global']['env']['PLATFORM']
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_115_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_115_PATH1']
    sample_6_path = case_config['global']['env']['SAMPLE_115_PATH0']
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_116_PATH1']
    sample_6_path = case_config['global']['env']['SAMPLE_0_PATH']
else:
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH2']
    sample_1_path = case_config['global']['env']['SAMPLE_116_PATH2']
    sample_6_path = case_config['global']['env']['SAMPLE_0_PATH2']
sample_path = case_config['global']['env']['SAMPLE_PATH']
tools_home = case_config['global']['env']['TOOLS_HOME']
if os.path.exists(tools_home):
    print("tools_home is exist")
else:
    mkdir('%s' % tools_home)
sanitizer_bin_path = case_config['global']['env']['SANITIZER_BIN_PATH']
date1 = time.strftime("%Y%m%d")
password1 = case_config['global']['env']['CQA_PASSWORD']
user1 = case_config['global']['env']['CQA_USER']
# password = b64_strip_decode(password1)
# user = b64_strip_decode(user1)
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
# C:\Users\<USER>\Downloads\cmder\vendor\git-for-windows\usr\bin\wget.exe

logger = logging.getLogger()
logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('setup_case.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)
# Added on Dec 31, 2021
output_flag = case_config['global']['env']['OUTPUT_FLAG']
cmder_bin_path = case_config['global']['env']['CMDER_BIN_PATH']
devenv = case_config['global']['env']['DEVENV']
if cuda_short_version > '12.9':
    solution_file = case_config['global']['env']['SOLUTION_FILE1']
else:
    solution_file = case_config['global']['env']['SOLUTION_FILE']
solution_file = case_config['global']['env']['SOLUTION_FILE']
results_home = case_config['global']['env']['RESULTS_HOME']

# Need to update according to the new CUDA Toolkit versions
DEVTOOLS_VERSION_DICT = {
    "11.8": ["DTC_C", "2022.3", "2022"],  # CUDA 11.8 2022.3 reuse DTC_C branch
    "12.0": ["DTC_D", "2022.4", "2022"],  # CUDA 12.0 2022.4 reuse DTC_D branch
    "11.4": ["DTC_E", "2021.2", "2021"],
    "12.1": ["DTC_F", "2023.1", "2023"],  # CUDA 12.1 2023.1 reuse DTC_F branch
    "12.2": ["DTC_I", "2023.2", "2023"],  # CUDA 12.2 2023.2 use DTC_I branch
    "11.6": ["DTC_G", "2022.1", "2022"],
    "11.7": ["DTC_H", "2022.2", "2022"],
    "12.3": ["CUDA12.3", "2023.3", "2023"],
    "12.4": ["CUDA12.4", "2024.1", "2024"],
    "12.5": ["CUDA12.5", "2024.2", "2024"],
    "12.6": ["CUDA12.6", "2024.3", "2024"],
    "12.7": ["CUDA12.7", "2024.4", "2024"],
    "12.8": ["CUDA12.8", "2025.1", "2025"],
    "12.9": ["CUDA12.9", "2025.2", "2025"],
    "13.0": ["CUDA13.0", "2025.3", "2025"]
}


@print_run_time
def version_copyright_check_1821312():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SETUP_SAMPLE_LOG_PATH']
    log_name = case_config['VERSION_COPYRIGHT_CHECK_1821312']['LOG_NAME']
    mkdir(log_path)
    cuda_version = cuda_short_version
    tools_version = DEVTOOLS_VERSION_DICT[cuda_short_version][1]
    copyright_year = DEVTOOLS_VERSION_DICT[cuda_short_version][2]
    print("CUDA Toolkit version is %s; Compute Tools version is %s" % (cuda_version, tools_version))
    # run the step
    step3_cmd = case_config['VERSION_COPYRIGHT_CHECK_1821312']['STEP3']['CMD']
    check_result(step3_cmd, 'Step 3-Check Sanitizer version by ----%s' % step3_cmd, log_name, result, tools_version)
    if cuda_short_version < '13.0':
        step4_cmd = case_config['VERSION_COPYRIGHT_CHECK_1821312']['STEP4']['CMD']
        nvprof_copyright = r'Copyright (c) 2012 - %s NVIDIA Corporation' % copyright_year
        check_result(step4_cmd, 'Step 4-Check nvprof version by ----%s' % step4_cmd, log_name, result, cuda_version, nvprof_copyright)
    step6_cmd = case_config['VERSION_COPYRIGHT_CHECK_1821312']['STEP6']['CMD']
    ncu_copyright = r'Copyright (c) 2018-%s NVIDIA Corporation' % copyright_year
    check_result(step6_cmd, 'Step 6-Check Nsight Compute(Rebel CLI) version by ----%s' % step6_cmd, log_name, result, tools_version, ncu_copyright)
    step9_cmd = case_config['VERSION_COPYRIGHT_CHECK_1821312']['STEP9']['CMD']
    check_result(step9_cmd, 'Step 9-Check Lib version by ----%s' % step9_cmd, log_name, result, tools_version)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/version_copyright_check_1821312.json' % log_path)
    return result, passed, failed


@print_run_time
def package_components_check_1821314():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SETUP_SAMPLE_LOG_PATH']
    log_name = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['LOG_NAME']
    mkdir(log_path)
    cuda_version = cuda_short_version
    tools_version = DEVTOOLS_VERSION_DICT[cuda_short_version][1]
    print("CUDA Toolkit version is %s; Compute Tools version is %s" % (cuda_version, tools_version))
    # run the step
    step1_cmd = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP1']['CMD']
    if cuda_version < '13.0':
        check_result(step1_cmd, 'Step 1-Check nvvp/nvprof/nvcc by ----%s' % step1_cmd, log_name, result, 'nvvp', 'nvprof', 'nvcc')
    else:
        check_result(step1_cmd, 'Step 1-Check nvcc by ----%s' % step1_cmd, log_name, result, 'nvcc')
    step2_cmd = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP2']['CMD']
    check_result(step2_cmd, 'Step 2-Check extras/CUPTI by ----%s' % step2_cmd, log_name, result, 'doc', 'include', 'lib64', 'samples')
    step4_cmd = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP4']['CMD']
    check_result(step4_cmd, 'Step 4-Check Nsight Compute installed by ----%s' % step4_cmd, log_name, result, 'Nsight Compute %s' % tools_version)
    step5_cmd = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP5']['CMD']
    check_result(step5_cmd, 'Step 5-Check Nsight VSE installed by ----%s' % step5_cmd, log_name, result, 'Nsight Visual Studio Edition %s' % tools_version)
    step6_cmd1 = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP6']['CMD1']
    check_result(step6_cmd1, 'Step 6-Check Sanitizer installed by ----%s' % step6_cmd1, log_name, result, 'docs', 'include', 'x86', 'sanitizer-public.lib')
    step6_cmd2 = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP6']['CMD2']
    check_result(step6_cmd2, 'Step 6-Check compute-sanitizer.bat by ----%s' % step6_cmd2, log_name, result, 'compute-sanitizer.bat')
    step6_cmd3 = case_config['PACKAGE_COMPONENTS_CHECK_1821314']['STEP6']['CMD3']
    check_result(step6_cmd3, 'Step 6-Check InterceptorInjectionTarget.dll/TreeLauncherTargetInjection.dll in x86 by ----%s' % step6_cmd3, log_name, result, 'InterceptorInjectionTarget.dll', 'TreeLauncherTargetInjection.dll')

    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/package_components_check_1821314.json' % log_path)
    return result, passed, failed


@print_run_time
def package_nvtx_v1v2v3_check_2157381():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SETUP_SAMPLE_LOG_PATH']
    log_name = case_config['PACKAGE_NVTX_V1V2V3_CHECK_2157381']['LOG_NAME']
    mkdir(log_path)
    cuda_version = cuda_short_version
    tools_version = DEVTOOLS_VERSION_DICT[cuda_short_version][1]
    print("CUDA Toolkit version is %s; Compute Tools version is %s" % (cuda_version, tools_version))
    # run the step
    # Check v1 installed on Windows(no need since CUDA12.0), and v2 installed on Linux
    if cuda_version < '12.9':
        if cuda_version >= '12.0':
            step1_cmd = case_config['PACKAGE_NVTX_V1V2V3_CHECK_2157381']['STEP1']['CMD1']
            check_result(step1_cmd, 'Step 1-Check NVTX v1(NvToolsExt) was removed from CUDA 12.0 by ----%s' % step1_cmd, log_name, result, ' No such file or directory', flag=1)
        else:
            step1_cmd = case_config['PACKAGE_NVTX_V1V2V3_CHECK_2157381']['STEP1']['CMD2']
            check_result(step1_cmd, 'Step 1-Check NVTX v1(NvToolsExt) exist with CUDA earlier than 12.0 by ----%s' % step1_cmd, log_name, result)
    step2_cmd = case_config['PACKAGE_NVTX_V1V2V3_CHECK_2157381']['STEP2']['CMD']
    if cuda_version < '12.9':
        check_result(step2_cmd, 'Step 2-Check nvtx3 headers installed by ----%s' % step2_cmd, log_name, result, 'nvToolsExt.h', 'nvToolsExtCuda.h', 'nvToolsExtCudaRt.h', 'nvToolsExtOpenCL.h', 'nvToolsExtSync.h', 'nvtxDetail')
    else:
        check_result(step2_cmd, 'Step 2-Check nvtx3 headers installed by ----%s' % step2_cmd, log_name, result, 'nvToolsExt.h', 'nvToolsExtCuda.h', 'nvToolsExtCudaRt.h', 'nvToolsExtOpenCL.h', 'nvToolsExtSync.h', 'nvtxDetail', 'nvtx3.hpp')
        step2_cmd1 = case_config['PACKAGE_NVTX_V1V2V3_CHECK_2157381']['STEP2']['CMD1']
        check_result(step2_cmd1, 'Step 2-Check nvtx3 detail folder ----%s' % step2_cmd1, log_name, result, 'nvtxImplCore.h', 'nvtxImplCudaRt_v3.h', 'nvtxImplCuda_v3.h', 'nvtxImpl.h', 'nvtxImplOpenCL_v3.h', 'nvtxImplSync_v3.h', 'nvtxInitDecls.h', 'nvtxInitDefs.h', 'nvtxInit.h', 'nvtxLinkOnce.h', 'nvtxTypes.h')
        file_list1 = ['nvToolsExt.h', 'nvToolsExtCuda.h', 'nvToolsExtCudaRt.h', 'nvToolsExtOpenCL.h', 'nvToolsExtSync.h', 'nvtx3.hpp']
        file_list2 = ['nvtxImplCore.h', 'nvtxImplCudaRt_v3.h', 'nvtxImplCuda_v3.h', 'nvtxImpl.h', 'nvtxImplOpenCL_v3.h', 'nvtxImplSync_v3.h', 'nvtxInitDecls.h', 'nvtxInitDefs.h', 'nvtxInit.h', 'nvtxLinkOnce.h', 'nvtxTypes.h']
        path1 = f"C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v{cuda_short_version}/include/nvtx3"
        path2 = f'C:/Program Files//NVIDIA GPU Computing Toolkit/CUDA/v{cuda_short_version}/include/nvtx3/nvtxDetail'
        data_time = time.ctime().split(' ')[-1]
        path3 = "C:/Program Files/NVIDIA Corporation/nsight-systems/target-windows*/nvtx/include/nvtx3"
        path4 = 'C:/Program Files/NVIDIA Corporation/nsight-systems/target-windows*/nvtx/include/nvtx3/nvtxDetail'

        def get_copy_right_number(list1, list2, path1, path2, data_time):
            copy_right_number = 0
            copy_right_list = []
            for i in list1:
                cmd = 'cd "{}"; Get-Content "{}" | Select-String -Pattern "Copyright"'.format(path1, i)
                print(cmd)
                out = run_loc_cmd(cmd)
                print(out['output'])
                save_log(log_name, cmd, 'check-nvtx3 file copyright', out['output'])
                if 'NVIDIA CORPORATION' in out['output']:
                    copy_right_number += 1
                    copy_right_list.append(out['output'].split('\n')[0])
            for j in list2:
                cmd1 = 'cd "{}"; Get-Content "{}" | Select-String -Pattern "Copyright"'.format(path2, j)
                out1 = run_loc_cmd(cmd1)
                print(out['output'])
                save_log(log_name, cmd1, 'check-nvtx3 detail file copyright', out1['output'])
                if 'NVIDIA CORPORATION' in out1['output']:
                    copy_right_number += 1
                    copy_right_list.append(out['output'].split('\n')[0])
            return copy_right_number, copy_right_list
        if os.path.exists(path1) and os.path.exists(path2):
            copy_right_number, copy_right_list = get_copy_right_number(file_list1, file_list2, path1, path2, data_time)
            copy_right_list1 = [i for i in copy_right_list if data_time in i]
            if copy_right_number == len(copy_right_list1):
                result['check-nvtx3 file copyright'] = 'passed'
                logger.info('check nvtx file copy right successful')
            else:
                result['check-nvtx3 file copyright'] = 'failed'
        else:
            result['check-nvtx3 file copyright'] = 'failed'
            logger.error('there is no nvtx3 folder, please make sure toolkit installed')
        if os.path.exists('C:/Program Files/NVIDIA Corporation/nsight-systems'):
            copy_right_number_nsys, copy_right_number_nsys_list = get_copy_right_number(file_list1, file_list2, path3, path4, data_time)
            copy_right_number_nsys_list1 = [i for i in copy_right_number_nsys_list if data_time in i]
            if copy_right_number_nsys == len(copy_right_number_nsys_list1):
                result['check-nvtx3 file copyright-nsys'] = 'passed'
                logger.info('check nsys nvtx file copy right successful')
            else:
                result['check-nvtx3 file copyright-nsys'] = 'failed'
        else:
            result['check-nvtx3 file copyright-nsys'] = 'failed'
            logger.error('there is no nsys folder, please make sure nsys installed')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/package_nvtx_v1v2v3_check_2157381.json' % log_path)
    return result, passed, failed


def fetch_service_account():
    import requests
    if "CQASYSTEM_TOKEN" in os.environ:
        api_token = os.environ["CQASYSTEM_TOKEN"]
    else:
        api_token_list = ['8c913a613f', '88b6f1ad91a', '815f13fd732']
        api_token = ''.join(api_token_list)
    response = requests.get('http://cqasystem.nvidia.com/api/credential/?ctype=service_account',
                            headers={'accept': 'application/json',
                                     'api-token': api_token}).json()
    return response['username'], urllib2.quote(response['password'])


def fetch_latest_tools_packages(branch, tools):
    ftp = FTPBase()
    tools = tools.lower()
    package_addr = "//builds/Prerelease/devtools/%s/Rel/CUDA%s" % (tools, branch)
    if tools == 'nsys':
        package_addr = "//builds/prerel/devtools/QuadD/Rel/CUDA%s" % branch
    CL_list = ftp.get_dir_list(package_addr)
    latest_build = CL_list[-1]
    source_path = "%s/%s" % (package_addr, latest_build)
    if tools == 'nexus':
        if 'x86' in platform.lower():
            source_path = source_path + "/PUBLIC-x64"
        else:
            source_path = source_path + "/PUBLIC-a64"
    file_list = ftp.get_dir_list(source_path)
    package = None
    if tools == 'nsys':
        for i in file_list:
            if 'NsightSystems-DVS.msi' in i:
                package = i
    elif tools == 'nexus':
        for i in file_list:
            if 'msi' in i:
                package = i
    else:
        for i in file_list:
            if 'x86' in platform.lower():
                if 'msi' in i and 'x86_64' in i and ('public' in i or 'PUBLIC' in i):
                    package = i
            if 'arm' in platform.lower():
                if 'msi' in i and 'arm64' in i and ('public' in i or 'PUBLIC' in i):
                    package = i
    '''
    for i in file_list:
        if 'msi' in i and '64' in i and ('public' in i or 'PUBLIC' in i):
            package = i
    '''
    print(package)
    packege_download_addr = "%s/%s/%s" % (package_addr, latest_build, package)
    if tools == 'nexus':
        if 'x86' in platform.lower():
            packege_download_addr = "%s/%s/PUBLIC-x64/%s" % (package_addr, latest_build, package)
        else:
            packege_download_addr = "%s/%s/PUBLIC-a64/%s" % (package_addr, latest_build, package)
    return packege_download_addr, package


def download_tools_package(branch, tools):
    download_file_path, latest_file = fetch_latest_tools_packages(branch, tools)
    download_user, download_password = fetch_builds_account()
    download_to_path = 'C:/'
    print('============================')
    print(download_file_path)
    print('============================')
    if os.path.exists('%s/%s' % (download_to_path, latest_file)):
        logger.info('downloaded the package, no need download again')
    else:
        download_command = "cd %s; %s/lftp -c 'open %s:%s@hqnvhwy02 ; set ssl:verify-certificate false ; glob -- pget -n 80 %s'" % (download_to_path, cmder_bin_path, download_user, download_password, download_file_path)
        print(download_command)
        run_loc_cmd(download_command)
    if os.path.exists('%s/%s' % (download_to_path, latest_file)):
        logger.info('download the package successful')
        return latest_file
    else:
        logger.info('download the package failed')


def verify_report_files(required_files, file_name, tools):
    """Verify files in report_{name}.txt using regex patterns

    Args:
        required_files (list): List of regex patterns to match, e.g. ['cupti64.*\\.dll', 'checkpoint\\.dll']
        file_name (str): Path to the report file to check

    Returns:
        dict: Result containing pass/fail status and details
    """
    result = {'passed': True, 'missing_files': [], 'unexpected_lines': []}

    try:
        # Read the report file
        with open(file_name, 'r') as f:
            lines = f.readlines()

            # Skip header lines and empty lines
            content_lines = [line.strip() for line in lines if ('NVIDIA Corporation' in line and 'Yes' in line) or tools in line]  # Skip table formatting lines
        # Check if all required files are present
        found_files = []
        required_files1 = []

        for line in content_lines:
            for pattern in required_files:
                required_files1 = [line.split('|')[1].split('\\')[-1] for line in required_files if 'Yes' in line and 'NVIDIA Corporation' in line]
                if re.search(pattern, line, re.IGNORECASE) and 'Yes' in line.split(' '):
                    found_files.append(pattern)
                    break
        # Check for missing files
        missing_files = [i for i in required_files1 if i not in found_files]
        # Update result
        if missing_files:
            result['passed'] = False
            result['missing_files'] = list(missing_files)
            logger.error(f"Missing required files: {missing_files}")
        # Log success if everything is fine
        if result['passed']:
            logger.info("All required files found and no unexpected lines present")
    except Exception as e:
        result['passed'] = False
        logger.error(f"Error while verifying files: {str(e)}")

    return result


def check_digital_signatures():
    """Check digital signatures for various NVIDIA components"""
    case_name = 'CHECK_DIGITAL_SIGNATURES_4145453'
    log_name = case_config[case_name]['LOG_NAME']
    log_path = case_config['global']['env']['CHECK_DIGITAL_SIGNATURES_PATH']
    mkdir(log_path)
    result = {}
    visual_studio_dict = {'12.9': '2025.2', '13.0': '2025.3'}
    try:
        # Prepare steps
        prepare_cmd1 = case_config[case_name]['PREPARE']['CMD1']
        run_loc_cmd(prepare_cmd1, timeout=6)
        for file1 in ['installTabulate.py', 'compliance_tooling.py', '7z.exe', 'signtool.exe']:
            prepare_cmd1_1 = case_config[case_name]['PREPARE']['CMD1_1'].format(user, password, file1)
            # check_result(prepare_cmd1, 'prepare_cmd1', log_name, result)
            run_loc_cmd(prepare_cmd1_1, timeout=6)
        # Step1: Verify CUPTI DLL signatures
        step1_cmd = case_config[case_name]['STEP1']['CMD']
        check_result(step1_cmd, 'step1--verfiy CUPTI DLL signatures via {}'.format(step1_cmd), log_name, result)
        check_result_cupti = verify_report_files(['cupti64', 'checkpoint.dll', 'nvperf_host.dll', 'nvperf_target.dll', 'pcsamplingutil.dll'], f'{log_path}/WinSignHelper/report_cupti.txt', 'CUPTI')
        print(check_result_cupti)
        if check_result_cupti['passed']:
            result['step1--check CUPTI DLL signatures report '] = 'passed'
        else:
            result['step1--check CUPTI DLL signatures report '] = 'failed'
        # Step2: Verify Compute-Sanitizer signatures
        step2_cmd = case_config[case_name]['STEP2']['CMD']
        check_result(step2_cmd, 'step2--verfiy Compute-Sanitizer signatures via {}'.format(step2_cmd), log_name, result)
        check_result_sanitizer = verify_report_files(['compute-sanitizer.exe', 'InterceptorInjectionTarget.dll', 'sanitizer-collection.dll', 'sanitizer-public.dll', 'TreeLauncherTargetInjection.dll'], f'{log_path}/WinSignHelper/report_sanitizer.txt', 'compute-sanitizer')
        print(check_result_sanitizer)
        if check_result_sanitizer['passed']:
            result['step2--check Compute-Sanitizer signatures report '] = 'passed'
        else:
            result['step2--check Compute-Sanitizer signatures report '] = 'failed'
        # Step3: Verify Rebel signatures
        if os.path.exists(r'C:/nsight_compute-public-windows-x86_64.msi'):
            prepare_cmd2 = case_config[case_name]['PREPARE']['CMD2']
            check_result(prepare_cmd2, 'prepare_cmd2--via commad {}'.format(prepare_cmd2), log_name, result)
        download_tools_package(cuda_short_version, 'Rebel')
        step3_cmd1 = case_config[case_name]['STEP3']['CMD1']
        step3_cmd2 = case_config[case_name]['STEP3']['CMD2']
        check_result(step3_cmd1, 'step3_1--verfiy Rebel msi signatures via {}'.format(step3_cmd1), log_name, result)
        check_result(step3_cmd2, 'step3_2--verfiy Rebel signatures  via {}'.format(step3_cmd2), log_name, result)
        check_result_rebel_msi = verify_report_files(['nsight_compute-public-windows-x86_64.msi'], f'{log_path}/WinSignHelper/report_rebel-msi.txt', 'nsight')
        if check_result_rebel_msi['passed']:
            result['step3--check Rebel msi signatures report '] = 'passed'
        else:
            result['step3--check Rebel msi signatures report '] = 'failed'
        check_result_rebel = verify_report_files(['atlas.dll', 'bifrost.dll', 'bifrost_loader.2.dll', 'bifrost_plugin.2.dll', 'CudaGpuInfoDumper.exe', 'cupti64_102.dll', 'cupti64_110.dll', 'cupti64_111.dll', 'cupti64_112.dll', 'cupti64_113.dll', 'cupti64_114.dll', 'cupti64_115.dll', 'cupti64_116.dll', 'cupti64_117.dll', 'cupti64_118.dll', 'cupti64_129.dll', 'cupti64_130.dll', 'nsight-sys-service.exe', 'nsys.exe', 'nvperf_grfx_host.dll', 'nvperf_host.dll', 'nvperf_target.dll', 'nvsym.dll', 'odin.dll', 'sqlite3.dll', 'sqlite3.exe', 'ToolsInjection64.dll', 'ToolsInjectionHelper64.dll', 'ToolsInjectionPython64.dll', 'ToolsInjectionWindowsHook64.dll', 'AgentAPI.dll', 'Analysis.dll', 'AnalysisContainersData.dll', 'AnalysisData.dll', 'AnalysisProto.dll', 'api-ms-win-core-file-l1-2-0.dll', 'api-ms-win-core-file-l2-1-0.dll', 'api-ms-win-core-localization-l1-2-0.dll', 'api-ms-win-core-processthreads-l1-1-1.dll', 'api-ms-win-core-synch-l1-2-0.dll', 'api-ms-win-core-timezone-l1-1-0.dll', 'api-ms-win-crt-convert-l1-1-0.dll', 'api-ms-win-crt-environment-l1-1-0.dll', 'api-ms-win-crt-filesystem-l1-1-0.dll', 'api-ms-win-crt-heap-l1-1-0.dll', 'api-ms-win-crt-locale-l1-1-0.dll', 'api-ms-win-crt-math-l1-1-0.dll', 'api-ms-win-crt-multibyte-l1-1-0.dll', 'api-ms-win-crt-runtime-l1-1-0.dll', 'api-ms-win-crt-stdio-l1-1-0.dll', 'api-ms-win-crt-string-l1-1-0.dll', 'api-ms-win-crt-time-l1-1-0.dll', 'api-ms-win-crt-utility-l1-1-0.dll', 'AppLib.dll', 'AppLibInterfaces.dll', 'arrow_devtools.dll', 'Assert.dll', 'boost_atomic-vc141-mt-x64-1_78.dll', 'boost_chrono-vc141-mt-x64-1_78.dll', 'boost_container-vc141-mt-x64-1_78.dll', 'boost_date_time-vc141-mt-x64-1_78.dll', 'boost_filesystem-vc141-mt-x64-1_78.dll', 'boost_iostreams-vc141-mt-x64-1_78.dll', 'boost_locale-vc141-mt-x64-1_78.dll', 'boost_program_options-vc141-mt-x64-1_78.dll', 'boost_python312-vc141-mt-x64-1_78.dll', 'boost_regex-vc141-mt-x64-1_78.dll', 'boost_serialization-vc141-mt-x64-1_78.dll', 'boost_system-vc141-mt-x64-1_78.dll', 'boost_thread-vc141-mt-x64-1_78.dll', 'boost_timer-vc141-mt-x64-1_78.dll', 'CommonProtoServices.dll', 'CommonProtoStreamSections.dll', 'Core.dll', 'CrashReporter.exe', 'CudaDrvApiWrapper.dll', 'DeviceProperty.dll', 'DevicePropertyProto.dll', 'ETWEventsHandlers.dll', 'EventSource.dll', 'EventsView.dll', 'exporter.dll', 'GenericHierarchy.dll', 'GpuInfo.dll', 'GpuTraits.dll', 'HostCommon.dll', 'InterfaceData.dll', 'InterfaceShared.dll', 'InterfaceSharedBase.dll', 'InterfaceSharedCore.dll', 'InterfaceSharedLoggers.dll', 'libcrypto-3-x64.dll', 'libssl-3-x64.dll', 'msdia140.dll', 'ncu-ui.exe', 'NetworkInfo.dll', 'NvLog.dll', 'NvmlWrapper.dll', 'NvQtGui.dll', 'nvsym.dll', 'NvtxExtData.dll', 'odin.dll', 'opengl32sw.dll', 'parquet_devtools.dll', 'ProcessLauncher.dll', 'protobuf-shared.dll', 'ProtobufComm.dll', 'ProtobufCommClient.dll', 'ProtobufCommProto.dll', 'QdstrmImporter.exe', 'Qt6Charts.dll', 'Qt6Concurrent.dll', 'Qt6Core.dll', 'Qt6Designer.dll', 'Qt6DesignerComponents.dll', 'Qt6Gui.dll', 'Qt6Help.dll', 'Qt6Multimedia.dll', 'Qt6MultimediaQuick.dll', 'Qt6MultimediaWidgets.dll', 'Qt6Network.dll', 'Qt6OpenGL.dll', 'Qt6OpenGLWidgets.dll', 'Qt6Positioning.dll', 'Qt6PrintSupport.dll', 'Qt6Qml.dll', 'Qt6QmlModels.dll', 'Qt6QmlWorkerScript.dll', 'Qt6Quick.dll', 'Qt6QuickParticles.dll', 'Qt6QuickTest.dll', 'Qt6QuickWidgets.dll', 'Qt6Sensors.dll', 'Qt6Sql.dll', 'Qt6StateMachine.dll', 'Qt6Svg.dll', 'Qt6SvgWidgets.dll', 'Qt6Test.dll', 'Qt6UiTools.dll', 'Qt6WebChannel.dll', 'Qt6WebEngineCore.dll', 'Qt6WebEngineWidgets.dll', 'Qt6WebSockets.dll', 'Qt6Widgets.dll', 'Qt6Xml.dll', 'QtPropertyBrowser.dll', 'QtWebEngineProcess.exe', 'QuiverContainers.dll', 'QuiverEvents.dll', 'ssh.dll', 'SshClient.dll', 'StreamSections.dll', 'SymbolAnalyzerLight.dll', 'SymbolDemangler.dll', 'TelemetryQuadDClient.dll', 'TimelineAssert.dll', 'TimelineCommon.dll', 'TimelineUIUtils.dll', 'TimelineWidget.dll', 'ucrtbase.dll', 'zlib.dll', 'ExternalIntegrationPlugin.dll', 'LinuxPlatformPlugin.dll', 'TPSConnectionPlugin.dll', 'TPSSystemServerPlugin.dll', 'WindowsPlatformPlugin.dll', 'CorePlugin.dll', 'qgif.dll', 'qico.dll', 'qjpeg.dll', 'qsvg.dll', 'qtga.dll', 'qtiff.dll', 'qwbmp.dll', 'qwindows.dll', 'QuadDPlugin.dll', 'RebelPlugin.dll', 'SassDebuggerPlugin.dll', 'TimelinePlugin.dll', 'qcertonlybackend.dll', 'qopensslbackend.dll', 'cuda-injection.dll', 'icudt71.dll', 'icuuc71.dll', 'InterceptorInjectionTarget.dll', 'ncu.exe', 'nvperf_host.dll', 'nvperf_target.dll', 'TreeLauncherTargetInjection.dll', 'InterceptorInjectionTarget.dll', 'TreeLauncherTargetInjection.dll'], f'{log_path}/WinSignHelper/report_rebel.txt', 'nsight')
        if check_result_rebel['passed']:
            result['step3--check Rebel signatures report '] = 'passed'
        else:
            result['step3--check Rebel signatures report '] = 'failed'
        # Step4: Verify Nexus signatures
        latest_file = download_tools_package(cuda_short_version, 'Nexus')
        step4_cmd1 = case_config[case_name]['STEP4']['CMD1'].format(latest_file)
        step4_cmd2 = case_config[case_name]['STEP4']['CMD2'].format(visual_studio_dict[cuda_short_version])
        check_result(step4_cmd1, 'step4_1-verfiy Nexus msi signatures via {}'.format(step4_cmd1), log_name, result)
        check_result(step4_cmd2, 'step4_2-verfiy Nexus signatures via {}'.format(step4_cmd2), log_name, result)
        check_result_nexus_msi = verify_report_files(['Nexus.x64.Release.PUBLIC'], f'{log_path}/WinSignHelper/report_nexus-msi.txt', 'Nexus')
        if check_result_nexus_msi['passed']:
            result['step4--check Nexus msi signatures report '] = 'passed'
        else:
            result['step4--check Nexus msi signatures report '] = 'failed'
            # logger.error(f"Failed to verify Nexus msi signatures: {check_result_nexus_msi['missing_files']}")
        check_nexus_list = ['AppLib.dll', 'AppLibInterfaces.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Ark.Vsip.dll', 'CrashReporter.exe', 'ICSharpCode.AvalonEdit.dll', 'icudt60.dll', 'icuin60.dll', 'icuuc60.dll', 'Nsight.CoreDumpLoader.exe', 'Nvda.Build.Nvcc.dll', 'Nvda.CoreDump.Interop.dll', 'Nvda.CoreDumpUi.dll', 'Nvda.CppCodeGen.Sass.dll', 'Nvda.CppExpressions.dll', 'Nvda.Cuda.InfoView.dll', 'Nvda.Cuda.WarpWatch.dll', 'Nvda.CudaFocusPickerUi.dll', 'Nvda.Device.dll', 'Nvda.DeviceUi.dll', 'Nvda.Diagnostics.dll', 'Nvda.Gpu.Diagnostics.dll', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.Math.Interop.dll', 'Nvda.Messaging.dll', 'Nvda.MicroCode.Sass.dll', 'Nvda.NLog.dll', 'Nvda.NvGpuArch.dll', 'Nvda.ObjectModel.Cuda.dll', 'Nvda.ObjectModel.dll', 'Nvda.ObjectModel.Gpu.Debugger.dll', 'Nvda.ObjectModel.Monitor.dll', 'Nvda.PageContent.dll', 'Nvda.Platform.Common.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Platform.Cuda.Messaging.dll', 'Nvda.Platform.Windows.Interop.dll', 'Nvda.Platform.Windows.Messaging.dll', 'Nvda.Project.dll', 'Nvda.ShiftReduceParser.dll', 'Nvda.Symbolics.Common.dll', 'Nvda.Symbolics.Common.Interop.dll', 'Nvda.Symbolics.Cuda.dll', 'Nvda.Symbolics.Cuda.Interop.dll', 'Nvda.Symbolics.Demangler.Gnu3.dll', 'Nvda.Telemetry.dll', 'Nvda.Test.Framework.Cuda.dll', 'Nvda.Test.Framework.dll', 'Nvda.Test.Framework.Gpu.Debugger.dll', 'Nvda.UnifiedDebugger.Engine.dll', 'Nvda.UnifiedDebugger.Package.dll', 'Nvda.UnifiedDebugger.Views.dll', 'Nvda.VersionNotification.dll', 'Nvda.Vsip.Debugger.Cuda.dll', 'Nvda.Vsip.Debugger.Device.dll', 'Nvda.Vsip.Debugger.dll', 'Nvda.Vsip.dll', 'Nvda.Vsip.Net.dll', 'NvLog.dll', 'NvQtGui.dll', 'Qt5ChartsNvda.dll', 'Qt5CoreNvda.dll', 'Qt5GuiNvda.dll', 'Qt5MultimediaNvda.dll', 'Qt5MultimediaWidgetsNvda.dll', 'Qt5NetworkNvda.dll', 'Qt5OpenGLNvda.dll', 'Qt5PositioningNvda.dll', 'Qt5PrintSupportNvda.dll', 'Qt5QmlNvda.dll', 'Qt5QuickNvda.dll', 'Qt5SensorsNvda.dll', 'Qt5SqlNvda.dll', 'Qt5SvgNvda.dll', 'Qt5WebChannelNvda.dll', 'Qt5WidgetsNvda.dll', 'Qt5XmlNvda.dll', 'QtPropertyBrowser.dll', 'System.ComponentModel.Composition.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'TestRunner.exe', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.UnifiedDebugger.UI.dll', 'icudt60.dll', 'icuin60.dll', 'icuuc60.dll', 'Qt5ChartsNvda.dll', 'Qt5CoreNvda.dll', 'Qt5GuiNvda.dll', 'Qt5MultimediaNvda.dll', 'Qt5MultimediaWidgetsNvda.dll', 'Qt5NetworkNvda.dll', 'Qt5OpenGLNvda.dll', 'Qt5PositioningNvda.dll', 'Qt5PrintSupportNvda.dll', 'Qt5QmlNvda.dll', 'Qt5QuickNvda.dll', 'Qt5SensorsNvda.dll', 'Qt5SqlNvda.dll', 'Qt5SvgNvda.dll', 'Qt5WebChannelNvda.dll', 'Qt5WidgetsNvda.dll', 'Qt5XmlNvda.dll', 'qwindows.dll', 'qwindows.dll', 'NvCoreDumpLoader.exe', 'NvDebugAgent.exe', 'NvCudaDebuggerInjection.dll', 'AppLib.dll', 'AppLibInterfaces.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Ark.Vsip.dll', 'CrashReporter.exe', 'Nvda.CoreDump.Interop.dll', 'Nvda.CoreDumpUi.dll', 'Nvda.Device.dll', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.ObjectModel.dll', 'Nvda.ObjectModel.Monitor.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Project.dll', 'Nvda.Telemetry.dll', 'Nvda.UnifiedDebugger.Engine.dll', 'Nvda.UnifiedDebugger.Package.dll', 'Nvda.UnifiedDebugger.Views.dll', 'Nvda.VersionNotification.dll', 'Nvda.Vsip.Debugger.Device.dll', 'Nvda.Vsip.Debugger.dll', 'Nvda.Vsip.dll', 'Nvda.Vsip.Net.dll', 'NvLog.dll', 'NvQtGui.dll', 'Qt6ChartsNvda.dll', 'Qt6CoreNvda.dll', 'Qt6GuiNvda.dll', 'Qt6MultimediaNvda.dll', 'Qt6MultimediaWidgetsNvda.dll', 'Qt6NetworkNvda.dll', 'Qt6OpenGLNvda.dll', 'Qt6PositioningNvda.dll', 'Qt6PrintSupportNvda.dll', 'Qt6QmlNvda.dll', 'Qt6QuickNvda.dll', 'Qt6SensorsNvda.dll', 'Qt6SqlNvda.dll', 'Qt6SvgNvda.dll', 'Qt6SvgWidgetsNvda.dll', 'Qt6WebChannelNvda.dll', 'Qt6WidgetsNvda.dll', 'Qt6XmlNvda.dll', 'QtPropertyBrowser.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.UnifiedDebugger.UI.dll', 'Qt6ChartsNvda.dll', 'Qt6CoreNvda.dll', 'Qt6GuiNvda.dll', 'Qt6MultimediaNvda.dll', 'Qt6MultimediaWidgetsNvda.dll', 'Qt6NetworkNvda.dll', 'Qt6OpenGLNvda.dll', 'Qt6PositioningNvda.dll', 'Qt6PrintSupportNvda.dll', 'Qt6QmlNvda.dll', 'Qt6QuickNvda.dll', 'Qt6SensorsNvda.dll', 'Qt6SqlNvda.dll', 'Qt6SvgNvda.dll', 'Qt6SvgWidgetsNvda.dll', 'Qt6WebChannelNvda.dll', 'Qt6WidgetsNvda.dll', 'Qt6XmlNvda.dll', 'qwindowsNvda.dll', 'qwindowsNvda.dll', 'NvCoreDumpLoader.exe', 'NvDebugAgent.exe', 'NvCudaDebuggerInjection.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Nsight.CoreDumpLoader.exe', 'Nsight.Monitor.exe', 'Nvda.CppCodeGen.Sass.dll', 'Nvda.CppExpressions.dll', 'Nvda.Device.dll', 'Nvda.Diagnostics.dll', 'Nvda.Gpu.DebugTool.exe', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.Math.Interop.dll', 'Nvda.Messaging.dll', 'Nvda.Messaging.Interop.dll', 'Nvda.Messaging.Native.dll', 'Nvda.MicroCode.Sass.dll', 'Nvda.NLog.dll', 'Nvda.NvGpuArch.dll', 'Nvda.PageContent.dll', 'Nvda.Platform.Common.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Platform.Cuda.dll', 'Nvda.Platform.Cuda.Messaging.dll', 'Nvda.Platform.Windows.dll', 'Nvda.Platform.Windows.Interop.dll', 'Nvda.Platform.Windows.Messaging.dll', 'Nvda.ShiftReduceParser.dll', 'Nvda.Symbolics.Common.dll', 'Nvda.Symbolics.Common.Interop.dll', 'Nvda.Symbolics.Cuda.dll', 'Nvda.Symbolics.Cuda.Interop.dll', 'Nvda.Symbolics.Demangler.Gnu3.dll', 'Nvda.VersionNotification.dll', 'NvLog.dll', 'System.ComponentModel.Composition.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.Cuda.Injection.dll', 'Nvda.Gpu.Configuration.Dump.exe', 'Nvda.Gpu.Diagnostics.ReportGeneration.dll', 'Nvda.NvDebugApi.dll', 'Nvda.ProcessInfo.exe', 'msdia110.dll', 'msdia110.dll']
        # check_nexus_list = ['AppLib.dll', 'AppLibInterfaces.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Ark.Vsip.dll', 'CrashReporter.exe', 'ICSharpCode.AvalonEdit.dll', 'icudt60.dll', 'icuin60.dll', 'icuuc60.dll', 'Nsight.CoreDumpLoader.exe', 'Nvda.Build.Nvcc.dll', 'Nvda.CoreDump.Interop.dll', 'Nvda.CoreDumpUi.dll', 'Nvda.CppCodeGen.Sass.dll', 'Nvda.CppExpressions.dll', 'Nvda.Cuda.InfoView.dll', 'Nvda.Cuda.WarpWatch.dll', 'Nvda.CudaFocusPickerUi.dll', 'Nvda.Device.dll', 'Nvda.DeviceUi.dll', 'Nvda.Diagnostics.dll', 'Nvda.Gpu.Diagnostics.dll', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.Math.Interop.dll', 'Nvda.Messaging.dll', 'Nvda.MicroCode.Sass.dll', 'Nvda.NLog.dll', 'Nvda.NvGpuArch.dll', 'Nvda.ObjectModel.Cuda.dll', 'Nvda.ObjectModel.dll', 'Nvda.ObjectModel.Gpu.Debugger.dll', 'Nvda.ObjectModel.Monitor.dll', 'Nvda.PageContent.dll', 'Nvda.Platform.Common.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Platform.Cuda.Messaging.dll', 'Nvda.Platform.Windows.Interop.dll', 'Nvda.Platform.Windows.Messaging.dll', 'Nvda.Project.dll', 'Nvda.ShiftReduceParser.dll', 'Nvda.Symbolics.Common.dll', 'Nvda.Symbolics.Common.Interop.dll', 'Nvda.Symbolics.Cuda.dll', 'Nvda.Symbolics.Cuda.Interop.dll', 'Nvda.Symbolics.Demangler.Gnu3.dll', 'Nvda.Telemetry.dll', 'Nvda.Test.Framework.Cuda.dll', 'Nvda.Test.Framework.dll', 'Nvda.Test.Framework.Gpu.Debugger.dll', 'Nvda.UnifiedDebugger.Engine.dll', 'Nvda.UnifiedDebugger.Package.dll', 'Nvda.UnifiedDebugger.Views.dll', 'Nvda.VersionNotification.dll', 'Nvda.Vsip.Debugger.Cuda.dll', 'Nvda.Vsip.Debugger.Device.dll', 'Nvda.Vsip.Debugger.dll', 'Nvda.Vsip.dll', 'Nvda.Vsip.Net.dll', 'NvLog.dll', 'NvQtGui.dll', 'Qt5ChartsNvda.dll', 'Qt5CoreNvda.dll', 'Qt5GuiNvda.dll', 'Qt5MultimediaNvda.dll', 'Qt5MultimediaWidgetsNvda.dll', 'Qt5NetworkNvda.dll', 'Qt5OpenGLNvda.dll', 'Qt5PositioningNvda.dll', 'Qt5PrintSupportNvda.dll', 'Qt5QmlNvda.dll', 'Qt5QuickNvda.dll', 'Qt5SensorsNvda.dll', 'Qt5SqlNvda.dll', 'Qt5SvgNvda.dll', 'Qt5WebChannelNvda.dll', 'Qt5WidgetsNvda.dll', 'Qt5XmlNvda.dll', 'QtPropertyBrowser.dll', 'System.ComponentModel.Composition.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'TestRunner.exe', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.UnifiedDebugger.UI.dll', 'icudt60.dll', 'icuin60.dll', 'icuuc60.dll', 'Qt5ChartsNvda.dll', 'Qt5CoreNvda.dll', 'Qt5GuiNvda.dll', 'Qt5MultimediaNvda.dll', 'Qt5MultimediaWidgetsNvda.dll', 'Qt5NetworkNvda.dll', 'Qt5OpenGLNvda.dll', 'Qt5PositioningNvda.dll', 'Qt5PrintSupportNvda.dll', 'Qt5QmlNvda.dll', 'Qt5QuickNvda.dll', 'Qt5SensorsNvda.dll', 'Qt5SqlNvda.dll', 'Qt5SvgNvda.dll', 'Qt5WebChannelNvda.dll', 'Qt5WidgetsNvda.dll', 'Qt5XmlNvda.dll', 'qwindows.dll', 'qwindows.dll', 'NvCoreDumpLoader.exe', 'NvDebugAgent.exe', 'NvCudaDebuggerInjection.dll', 'AppLib.dll', 'AppLibInterfaces.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Ark.Vsip.dll', 'CrashReporter.exe', 'Nvda.CoreDump.Interop.dll', 'Nvda.CoreDumpUi.dll', 'Nvda.Device.dll', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.ObjectModel.dll', 'Nvda.ObjectModel.Monitor.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Project.dll', 'Nvda.Telemetry.dll', 'Nvda.UnifiedDebugger.Engine.dll', 'Nvda.UnifiedDebugger.Package.dll', 'Nvda.UnifiedDebugger.Views.dll', 'Nvda.VersionNotification.dll', 'Nvda.Vsip.Debugger.Device.dll', 'Nvda.Vsip.Debugger.dll', 'Nvda.Vsip.dll', 'Nvda.Vsip.Net.dll', 'NvLog.dll', 'NvQtGui.dll', 'Qt6ChartsNvda.dll', 'Qt6CoreNvda.dll', 'Qt6GuiNvda.dll', 'Qt6MultimediaNvda.dll', 'Qt6MultimediaWidgetsNvda.dll', 'Qt6NetworkNvda.dll', 'Qt6OpenGLNvda.dll', 'Qt6PositioningNvda.dll', 'Qt6PrintSupportNvda.dll', 'Qt6QmlNvda.dll', 'Qt6QuickNvda.dll', 'Qt6SensorsNvda.dll', 'Qt6SqlNvda.dll', 'Qt6SvgNvda.dll', 'Qt6SvgWidgetsNvda.dll', 'Qt6WebChannelNvda.dll', 'Qt6WidgetsNvda.dll', 'Qt6XmlNvda.dll', 'QtPropertyBrowser.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.UnifiedDebugger.UI.dll', 'Qt6ChartsNvda.dll', 'Qt6CoreNvda.dll', 'Qt6GuiNvda.dll', 'Qt6MultimediaNvda.dll', 'Qt6MultimediaWidgetsNvda.dll', 'Qt6NetworkNvda.dll', 'Qt6OpenGLNvda.dll', 'Qt6PositioningNvda.dll', 'Qt6PrintSupportNvda.dll', 'Qt6QmlNvda.dll', 'Qt6QuickNvda.dll', 'Qt6SensorsNvda.dll', 'Qt6SqlNvda.dll', 'Qt6SvgNvda.dll', 'Qt6SvgWidgetsNvda.dll', 'Qt6WebChannelNvda.dll', 'Qt6WidgetsNvda.dll', 'Qt6XmlNvda.dll', 'qwindowsNvda.dll', 'qwindowsNvda.dll', 'NvCoreDumpLoader.exe', 'NvDebugAgent.exe', 'NvCudaDebuggerInjection.dll', 'Ark.dll', 'Ark.Interop.dll', 'Ark.Presentation.dll', 'Ark.Ui.dll', 'Nsight.CoreDumpLoader.exe', 'Nsight.Monitor.exe', 'Nvda.CppCodeGen.Sass.dll', 'Nvda.CppExpressions.dll', 'Nvda.Device.dll', 'Nvda.Diagnostics.dll', 'Nvda.Gpu.DebugTool.exe', 'Nvda.Hosting.dll', 'Nvda.Identifiers.dll', 'Nvda.Math.Interop.dll', 'Nvda.Messaging.dll', 'Nvda.Messaging.Interop.dll', 'Nvda.Messaging.Native.dll', 'Nvda.MicroCode.Sass.dll', 'Nvda.NLog.dll', 'Nvda.NvGpuArch.dll', 'Nvda.PageContent.dll', 'Nvda.Platform.Common.dll', 'Nvda.Platform.Common.Messaging.dll', 'Nvda.Platform.Cuda.dll', 'Nvda.Platform.Cuda.Messaging.dll', 'Nvda.Platform.Windows.dll', 'Nvda.Platform.Windows.Interop.dll', 'Nvda.Platform.Windows.Messaging.dll', 'Nvda.ShiftReduceParser.dll', 'Nvda.Symbolics.Common.dll', 'Nvda.Symbolics.Common.Interop.dll', 'Nvda.Symbolics.Cuda.dll', 'Nvda.Symbolics.Cuda.Interop.dll', 'Nvda.Symbolics.Demangler.Gnu3.dll', 'Nvda.VersionNotification.dll', 'NvLog.dll', 'System.ComponentModel.Composition.dll', 'Telerik.Windows.Controls.Chart.dll', 'Telerik.Windows.Controls.Charting.dll', 'Telerik.Windows.Controls.Data.dll', 'Telerik.Windows.Controls.DataVisualization.dll', 'Telerik.Windows.Controls.dll', 'Telerik.Windows.Controls.Docking.dll', 'Telerik.Windows.Controls.GridView.dll', 'Telerik.Windows.Controls.Input.dll', 'Telerik.Windows.Controls.Navigation.dll', 'Telerik.Windows.Data.dll', 'Telerik.Windows.PersistenceFramework.dll', 'WPFToolkit.Design.dll', 'WPFToolkit.dll', 'WPFToolkit.VisualStudio.Design.dll', 'Nvda.Cuda.Injection.dll', 'Nvda.Gpu.Configuration.Dump.exe', 'Nvda.Gpu.Diagnostics.ReportGeneration.dll', 'Nvda.NvDebugApi.dll', 'Nvda.ProcessInfo.exe', 'msdia110.dll']
        check_result_nexus = verify_report_files(check_nexus_list, f'{log_path}/WinSignHelper/report_nexus.txt', 'Nsight Visual Studio')
        if check_result_nexus['passed']:
            result['step4--check Nexus signatures report '] = 'passed'
        else:
            result['step4--check Nexus signatures report '] = 'failed'
            # logger.error(f"Failed to verify Nexus signatures: {check_result_nexus['missing_files']}")
        # Step5: Verify UD backend signatures
        step5_cmd = case_config[case_name]['STEP5']['CMD']
        check_result(step5_cmd, 'step5---verfiy UD backend signatures via {}'.format(step5_cmd), log_name, result)
        check_result_ud = verify_report_files(['nvcudadebugger.dll'], f'{log_path}/WinSignHelper/report_ud.txt', 'debugger')
        if check_result_ud['passed']:
            result['step5--check UD backend signatures report '] = 'passed'
        else:
            result['step5--check UD backend signatures report '] = 'failed'
            logger.error(f"Failed to verify UD backend signatures: {check_result_ud['missing_files']}")
        # Step6: Verify Nsys signatures
        download_tools_package(cuda_short_version, 'nsys')
        step6_cmd1 = case_config[case_name]['STEP6']['CMD1']
        step6_cmd2 = case_config[case_name]['STEP6']['CMD2']
        check_result(step6_cmd1, 'step6_1--verfiy Nsys msi signatures via {}'.format(step6_cmd1), log_name, result)
        check_result(step6_cmd2, 'step6_2--verfiy Nsys signatures via {}'.format(step6_cmd2), log_name, result)
        check_result_nsys_msi = verify_report_files(['NsightSystems-DVS.msi'], f'{log_path}/WinSignHelper/report_nsys-msi.txt', 'NsihtSystems')
        if check_result_nsys_msi['passed']:
            result['step6--check Nsys msi signatures report '] = 'passed'
        else:
            result['step6--check Nsys msi signatures report '] = 'failed'
            logger.error(f'Failed to verify Nsys signatures: {check_result_nsys_msi["missing_files"]}')
        nsys_check_list = ['AgentAPI.dll', 'Analysis.dll', 'AnalysisContainersData.dll', 'AnalysisData.dll', 'AnalysisProto.dll', 'api-ms-win-core-file-l1-2-0.dll', 'api-ms-win-core-file-l2-1-0.dll', 'api-ms-win-core-localization-l1-2-0.dll', 'api-ms-win-core-processthreads-l1-1-1.dll', 'api-ms-win-core-synch-l1-2-0.dll', 'api-ms-win-core-timezone-l1-1-0.dll', 'api-ms-win-crt-convert-l1-1-0.dll', 'api-ms-win-crt-environment-l1-1-0.dll', 'api-ms-win-crt-filesystem-l1-1-0.dll', 'api-ms-win-crt-heap-l1-1-0.dll', 'api-ms-win-crt-locale-l1-1-0.dll', 'api-ms-win-crt-math-l1-1-0.dll', 'api-ms-win-crt-multibyte-l1-1-0.dll', 'api-ms-win-crt-runtime-l1-1-0.dll', 'api-ms-win-crt-stdio-l1-1-0.dll', 'api-ms-win-crt-string-l1-1-0.dll', 'api-ms-win-crt-time-l1-1-0.dll', 'api-ms-win-crt-utility-l1-1-0.dll', 'AppLib.dll', 'AppLibInterfaces.dll', 'arrow_devtools.dll', 'Assert.dll', 'boost_atomic-vc141-mt-x64-1_78.dll', 'boost_chrono-vc141-mt-x64-1_78.dll', 'boost_container-vc141-mt-x64-1_78.dll', 'boost_date_time-vc141-mt-x64-1_78.dll', 'boost_filesystem-vc141-mt-x64-1_78.dll', 'boost_iostreams-vc141-mt-x64-1_78.dll', 'boost_locale-vc141-mt-x64-1_78.dll', 'boost_program_options-vc141-mt-x64-1_78.dll', 'boost_python310-vc141-mt-x64-1_78.dll', 'boost_regex-vc141-mt-x64-1_78.dll', 'boost_serialization-vc141-mt-x64-1_78.dll', 'boost_system-vc141-mt-x64-1_78.dll', 'boost_thread-vc141-mt-x64-1_78.dll', 'boost_timer-vc141-mt-x64-1_78.dll', 'CommonProtoServices.dll', 'CommonProtoStreamSections.dll', 'Core.dll', 'CrashReporter.exe', 'CudaDrvApiWrapper.dll', 'DeviceProperty.dll', 'DevicePropertyProto.dll', 'DumpTimeline.exe', 'ETWEventsHandlers.dll', 'EventSource.dll', 'EventsView.dll', 'exporter.dll', 'GenericHierarchy.dll', 'GpuInfo.dll', 'GpuTraits.dll', 'HostCommon.dll', 'ImportNvtxt.exe', 'InjectionCommunicator.dll', 'InterfaceData.dll', 'InterfaceShared.dll', 'InterfaceSharedBase.dll', 'InterfaceSharedCore.dll', 'InterfaceSharedLoggers.dll', 'libcrypto-3-x64.dll', 'libssl-3-x64.dll', 'msdia140.dll', 'NetworkInfo.dll', 'nsys-ui.exe', 'NvLog.dll', 'NvmlWrapper.dll', 'NvQtGui.dll', 'nvsym.dll', 'NvtxExtData.dll', 'NvtxwBackend.dll', 'odin.dll', 'opengl32sw.dll', 'parquet_devtools.dll', 'ProcessLauncher.dll', 'protobuf-shared.dll', 'ProtobufComm.dll', 'ProtobufCommClient.dll', 'ProtobufCommProto.dll', 'QdstrmImporter.exe', 'Qt6Charts.dll', 'Qt6Concurrent.dll', 'Qt6Core.dll', 'Qt6Designer.dll', 'Qt6DesignerComponents.dll', 'Qt6Gui.dll', 'Qt6Help.dll', 'Qt6Multimedia.dll', 'Qt6MultimediaQuick.dll', 'Qt6MultimediaWidgets.dll', 'Qt6Network.dll', 'Qt6OpenGL.dll', 'Qt6OpenGLWidgets.dll', 'Qt6Positioning.dll', 'Qt6PrintSupport.dll', 'Qt6Qml.dll', 'Qt6QmlModels.dll', 'Qt6Quick.dll', 'Qt6QuickParticles.dll', 'Qt6QuickTest.dll', 'Qt6QuickWidgets.dll', 'Qt6Sensors.dll', 'Qt6Sql.dll', 'Qt6StateMachine.dll', 'Qt6Svg.dll', 'Qt6SvgWidgets.dll', 'Qt6Test.dll', 'Qt6UiTools.dll', 'Qt6WebChannel.dll', 'Qt6WebEngineCore.dll', 'Qt6WebEngineWidgets.dll', 'Qt6WebSockets.dll', 'Qt6Widgets.dll', 'Qt6Xml.dll', 'QtPropertyBrowser.dll', 'QtWebEngineProcess.exe', 'QuiverContainers.dll', 'QuiverEvents.dll', 'ResolveSymbols.exe', 'sqlite3.dll', 'sqlite3.exe', 'ssh.dll', 'SshClient.dll', 'StreamSections.dll', 'SymbolAnalyzerLight.dll', 'SymbolDemangler.dll', 'TelemetryQuadDClient.dll', 'TimelineAssert.dll', 'TimelineCommon.dll', 'TimelineUIUtils.dll', 'TimelineWidget.dll', 'ucrtbase.dll', 'zlib.dll', 'CorePlugin.dll', 'qgif.dll', 'qico.dll', 'qjpeg.dll', 'qsvg.dll', 'qtga.dll', 'qtiff.dll', 'qwbmp.dll', 'qwindows.dll', 'QuadDPlugin.dll', 'TimelinePlugin.dll', 'qcertonlybackend.dll', 'qopensslbackend.dll', 'python.exe', 'atlas.dll', 'bifrost.dll', 'bifrost_loader.2.dll', 'bifrost_plugin.2.dll', 'CudaGpuInfoDumper.exe', 'cupti64_102.dll', 'cupti64_110.dll', 'cupti64_111.dll', 'cupti64_112.dll', 'cupti64_113.dll', 'cupti64_114.dll', 'cupti64_115.dll', 'cupti64_116.dll', 'cupti64_117.dll', 'cupti64_118.dll', 'cupti64_129.dll', 'nsight-sys-service.exe', 'nsys.exe', 'nvperf_grfx_host.dll', 'nvperf_host.dll', 'nvperf_target.dll', 'nvsym.dll', 'odin.dll', 'sqlite3.dll', 'sqlite3.exe', 'ToolsInjection64.dll', 'ToolsInjectionHelper32.dll', 'ToolsInjectionHelper64.dll', 'ToolsInjectionPython64.dll', 'ToolsInjectionWindowsHook64.dll', 'python.exe']
        check_result_nsys = verify_report_files(nsys_check_list, f'{log_path}/WinSignHelper/report_nsys.txt', 'NsihtSystems')
        if check_result_nsys['passed']:
            result['step6--check Nsys signatures report '] = 'passed'
        else:
            result['step6--check Nsys signatures report '] = 'failed'
            logger.error(f'Failed to verify Nsys signatures: {check_result_nsys["missing_files"]}')
    except Exception as e:
        logger.error(f"An error occurred during signature verification: {str(e)}")
        result['some steps fail in check_digital_signatures'] = 'failed'
    # Calculate final results
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, f'{log_path}/check_digital_signatures.json')


example_text = """
Example:
"""
reversion = '1.0'
check_list = ["version_copyright_check_1821312", "package_components_check_1821314", "package_nvtx_v1v2v3_check_2157381", 'check_digital_signatures']


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-c", "--config-file", dest="config_file", required=False,
        help='Specify test index file. e.g. cases')
    parser.add_argument(
        "-e", "--excute", action='append', required=False,
        choices=check_list,
        help="Specify function to be run.")
    parser.add_argument(
        "-el", "--excute_list", action='store', required=False,
        help="Specify multi function to be run.")
    args = parser.parse_args()

    case_str = args.excute_list
    case_list_single = args.excute
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            getattr(mod, case)()
    elif case_list_single:
        mod = sys.modules["__main__"]
        getattr(mod, case_list_single[0])()
    else:
        pass
