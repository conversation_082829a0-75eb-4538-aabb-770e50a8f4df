# -*- encoding: utf-8 -*-
import os
import sys
import yaml
import json
from configparser import ConfigParser
import argparse
import base64
from common_utils import *
import logging
import time
import urllib.request as urllib2
import http.client as httplib

# For Graphics Samples and OptiX Samples
import win32gui
import win32con
import string
from mobile_yaml_mgr import MobileYamlManger
import threading
from multiprocessing import Process, Manager

sanitizer_yaml = 'C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml'
yaml_mgr = MobileYamlManger(sanitizer_yaml)
case_config = yaml_mgr.load()
cuda_major = case_config['global']['env']['CUDA_MAJOR']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
device = case_config['global']['env']['DEVICES']
bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
platform = case_config['global']['env']['PLATFORM']
# modify sdk sample in sample_0_path_1, build sample in sample_0_path
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
sample_bin_path_release = case_config['global']['env']['SAMPLE_BIN_PATH_RELEASE']
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_115_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_115_PATH1']
    sample_0_path_1 = case_config['global']['env']['SAMPLE_115_PATH0']
    sample_1_path_1 = case_config['global']['env']['SAMPLE_115_PATH1']
    sample_6_path = case_config['global']['env']['SAMPLE_115_PATH1']
    sample_6_path_1 = case_config['global']['env']['SAMPLE_115_PATH1']
    run_vectoradd_path = sample_bin_path
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_116_PATH1']
    sample_0_path_1 = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_1_path_1 = case_config['global']['env']['SAMPLE_116_PATH1']
    sample_6_path = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_6_path_1 = case_config['global']['env']['SAMPLE_116_PATH0']
    run_vectoradd_path = sample_bin_path
    run_asyncAPI_path = sample_bin_path
    run_simpleAWBarrier_path = sample_bin_path
    device_sln = 'deviceQuery_vs2022.sln'
    vectoradd_sln = 'vectorAdd_vs2022.sln'
    asyncAPI_sln = 'asyncAPI_vs2022.sln'
    oceanFFT_sln = 'oceanFFT_vs2022.sln'
    matrixMul_sln = 'matrixMul_vs2022.sln'
    mergeSort_sln = 'mergeSort_vs2022.sln'
    vectorAddDrv_sln = 'vectorAddDrv_vs2022.sln'
    solution_file = case_config['global']['env']['SOLUTION_FILE']
else:
    sample_0_path_1 = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_1_path_1 = case_config['global']['env']['SAMPLE_116_PATH1']
    sample_0_path = case_config['global']['env']['SAMPLE_SAMPLE0_PATH']
    sample_1_path = case_config['global']['env']['SAMPLE_SAMPLE1_PATH']
    sample_6_path_1 = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_6_path = case_config['global']['env']['SAMPLE_SAMPLE0_PATH']
    run_vectoradd_path = sample_0_path + '/vectorAdd/Debug'
    run_asyncAPI_path = sample_0_path + '/asyncAPI/Debug'
    run_simpleAWBarrier_path = sample_0_path + '/simpleAWBarrier/Debug'
    device_sln = 'deviceQuery.sln'
    vectoradd_sln = 'vectorAdd.sln'
    matrixMul_sln = 'matrixMul.sln'
    mergeSort_sln = 'mergeSort.sln'
    asyncAPI_sln = 'asyncAPI.sln'
    oceanFFT_sln = 'oceanFFT.sln'
    vectorAddDrv_sln = 'vectorAddDrv.sln'
    solution_file = case_config['global']['env']['SOLUTION_FILE1']
#  SAMPLE_SAMPLE1_PATH: "${SAMPLE_PATH}/build/Samples/1_Utilities"
sample_path = case_config['global']['env']['SAMPLE_PATH']
tools_home = case_config['global']['env']['TOOLS_HOME']
if os.path.exists(tools_home):
    print("tools_home is exist")
else:
    mkdir('%s' % tools_home)
sanitizer_bin_path = case_config['global']['env']['SANITIZER_BIN_PATH']
date1 = time.strftime("%Y%m%d")
password1 = case_config['global']['env']['CQA_PASSWORD']
user1 = case_config['global']['env']['CQA_USER']
# password = b64_strip_decode(password1)
# user = b64_strip_decode(user1)
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
# C:\Users\<USER>\Downloads\cmder\vendor\git-for-windows\usr\bin\wget.exe
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
# Added support for CUDA 12.0 and 12.0+, Sep 1, 2022
if cuda_major >= '11':
    sanitizer_addr_x86_win = case_config['TOOLS_X86_WIN']['SANITIZER_11_URL']
else:
    sanitizer_addr_x86_win = case_config['TOOLS_X86_WIN']['SANITIZER_URL']
download_to_path = case_config['TOOLS_X86_WIN']['SANITIZER_PATH']
run_path = download_to_path + '/nvidia-compute-sanitizer-test'
optix_bin_path = case_config['global']['env']['OPTIX_SAMPLE_BIN_PATH']
rebel_smoke_test_package_path = case_config['global']['env']['REBEL_SMOKE_TEST_PACKAGE_PATH']
rebel_smoke_test_app_path = case_config['global']['env']['REBEL_SMOKE_TEST_APP_PATH']

os_type = get_os_type()
print(f'os type is {os_type}')
exit()
logger = logging.getLogger()
logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('sanitizer_case.log', encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)
# Added on Dec 31, 2021
output_flag = case_config['global']['env']['OUTPUT_FLAG']
cmder_bin_path = case_config['global']['env']['CMDER_BIN_PATH']
devenv = case_config['global']['env']['DEVENV']
results_home = case_config['global']['env']['RESULTS_HOME']


def get_sm():
    """
    Get the SM of the GPU on Windows
    """
    sm_cmd = case_config['PREPARE']['CMD3'] % (sample_1_path, device_sln)
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        # To resolve the "ZWNBSP in SM string" problem, updated by Alex Li on Sep 7, 2023
        sm_output = sm_out['output']
        sm_temp = sm_output.split('\n')[-1].strip(' ')
        print("sm_temp is %s" % sm_temp)
        print("sm_temp(encoding with UTF-8-sig) is %s" % sm_temp.strip().encode('UTF-8-sig'))
        sm = sm_temp.encode('ascii', 'ignore').decode().strip()
        print("sm is %s" % sm)
        return float(sm)
    else:
        logger.info('we get the sm failed')
        return 0.0


SM = get_sm()


# CUDA Device Driver Mode (TCC or WDDM): WDDM (Windows Display Driver Model) or TCC (Tesla Compute Cluster Driver)
def get_driver_mode():
    """
    Get the driver mode on Windows, return TCC or WDDM

    Attention: This function has been obsoleted since 2024.07.17. Please use the new API common_get_driver_mode() in common_utils.py instead.
    """
    tcc_wddm_cmd = case_config['PREPARE']['CMD_TCC_WDDM'] % sample_1_path
    print(tcc_wddm_cmd)
    tcc_wddm_out = run_loc_cmd(tcc_wddm_cmd)
    if tcc_wddm_out.succeeded:
        tcc_wddm = tcc_wddm_out['output']
        return 'TCC' if 'TCC' in tcc_wddm else 'WDDM'
    else:
        logger.info('we get the TCC or WDDM failed')


def parse_sanitizer_page(arch=case_config['global']['env']['PLATFORM']):
    try:
        print(sanitizer_addr_x86_win)
        website = urllib2.urlopen(sanitizer_addr_x86_win)
        print(website)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except httplib.HTTPException as e:
        print('HTTPException')
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False

    html = website.read()
    matchs = re.findall('SW_.*Release_Windows_Sanitizer_Public.zip', html.decode())
    matchs.sort()
    latest_build_name = matchs[-1].split('"')
    return latest_build_name


def getstatusoutput(*args, **kwargs):
    # Set the PowerShell instead of bat on Windows
    p = subprocess.Popen(["powershell.exe", *args], **kwargs)
    stdout, stderr = p.communicate()
    return p.returncode, stdout, stderr


def prepare_sanitizer(arch=None):
    # Use get_dvs_package() instead of the old url way, Feb 15, 2023
    # sanitizer_package = parse_sanitizer_page(arch='x86_win')[0]
    # download_path = sanitizer_addr_x86_win + sanitizer_package
    os_type = get_os_type()
    if os_type == 'woa':
        download_path = get_dvs_package('sanitizer', 'woa', cuda_short_version)
    else:
        download_path = get_dvs_package('sanitizer', 'windows', cuda_short_version)
    sanitizer_package = download_path.split('/')[-1]
    logger.info('we will download the package from %s' % download_path)
    logger.info('we will download the package to %s ' % download_to_path)
    # cmd1 = 'mkdir %s' % download_to_path.replace('/','\\')
    if not os.path.exists(download_to_path):
        os.makedirs(download_to_path)
    # Clear the exist files
    logger.info("====== Remove the exist files in %s ...... " % download_to_path)
    remove_cmd = "cd %s ; %s/rm -fr *" % (download_to_path, cmder_bin_path)
    print("====== remove_cmd is %s " % remove_cmd)
    run_loc_cmd(remove_cmd)
    logger.info("====== Remove the exist files in %s successfully!" % download_to_path)
    if os.path.exists(sanitizer_package):
        logger.info('we had download the package, no need download again')
    else:
        # lftp.exe is in cmder\vendor\git-for-windows\usr\bin
        cmd2 = "cd %s; lftp -c 'open %s:%s@hqnvhwy02; glob -- pget -n 80 %s'" % (download_to_path, os.environ.get('SERVICE_ACCOUNT', ''), os.environ.get('SERVICE_PASSWORD', ''), download_path)
        logger.info('we will use the command to download package, the command is %s ' % cmd2)
        out2 = getstatusoutput(cmd2)[0]
        if out2 == 0:
            # Use uzip -o to override the existing files
            cmd3 = 'cd %s; unzip %s; unzip -o Sanitizer_Public_Release_DVS_amd64.zip' % (download_to_path, sanitizer_package)
            print(cmd3)
            logger.info('we will use the command to tar package, the command is %s ' % cmd3)
            out3 = getstatusoutput(cmd3)[0]
            if out3 == 0:
                logger.info('extract the sanitizer package successful')
            else:
                logger.info('extract the sanitizer package failed')
                exit(0)
        else:
            logger.info('Failed to download sanitizer package')
            exit(2)


@print_run_time
def nvtx_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['NVTX_OPTION_PATH']
    log_name = case_config['NVTX_OPTION']['LOG_NAME']
    mkdir(log_path)
    # Prepare sanitizer dvs package
    prepare_sanitizer(arch=platform)

    if os.path.exists('%s/MemoryPool.exe' % run_path):
        # run step1
        if cuda_short_version < '12.0':
            step1_cmd = case_config['NVTX_OPTION']['STEP1']['CMD'] % run_path
        else:
            step1_cmd = case_config['NVTX_OPTION']['STEP1']['CMD_1'] % run_path
        step1_check_point = case_config['NVTX_OPTION']['STEP1']['CHECK_POINT']
        if cuda_short_version < '13.0':
            check_result(step1_cmd, 'nvtx_optin-step1------%s' % step1_cmd, log_name, result, step1_check_point)
        else:
            check_result(step1_cmd, 'nvtx_optin-step1------%s' % step1_cmd, log_name, result, step1_check_point, flag=1)
        # run step2
        step2_cmd = case_config['NVTX_OPTION']['STEP2']['CMD'] % run_path
        step2_check_point = case_config['NVTX_OPTION']['STEP2']['CHECK_POINT']
        check_result(step2_cmd, 'nvtx_optin-step2------%s' % step2_cmd, log_name, result, step2_check_point)
    else:
        logger.info('please check the sample-MemoryPool, make sure it exists')
    if os.path.exists('%s/Naming.exe' % run_path):
        # run step3
        step3_cmd = case_config['NVTX_OPTION']['STEP3']['CMD'] % run_path
        step3_check_point = case_config['NVTX_OPTION']['STEP3']['CHECK_POINT']
        step3_check_point1 = case_config['NVTX_OPTION']['STEP3']['CHECK_POINT1']
        step3_check_point2 = case_config['NVTX_OPTION']['STEP3']['CHECK_POINT2']
        out = run_loc_cmd(step3_cmd)
        save_log(log_name, step3_cmd, 'nvtx_option-step3', out['output'])
        print(out['output'])
        if out.succeeded and step3_check_point in out['output'] and step3_check_point1 in out['output'] and step3_check_point2 in out['output']:
            result['nvtx_option-steps3------%s' % step3_cmd] = 'passed'
            passed += 1
            logger.info('we run nvtx_option-step3----%s successful' % step3_cmd)
        else:
            result['nvtx_option-steps3------%s' % step3_cmd] = 'failed'
            failed += 1
            logger.info('we run nvtx_option-step3----%s failed' % step3_cmd)
    else:
        logger.info('please check the sample-MemoryPool, make sure it exists')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/nvtx_option.json' % log_path)
    return result, passed, failed


@print_run_time
def check_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['BASE_LOG_PATH2']
    log_name = case_config['SANITIZER_OPTION_CHECK']['LOG_NAME']
    mkdir(log_path)
    try:
        prepare_cmd = case_config['SANITIZER_OPTION_CHECK']['PREPARE']['PREPARE_CMD']
        logger.info(f'will prepare the vector err sample---{prepare_cmd}')
        run_loc_cmd(prepare_cmd)
        cmd = case_config['SANITIZER_OPTION_CHECK']['PREPARE']['CMD'] % (user, password, base_url)
        logger.info(f'we will download source file-{cmd}')
        check_result(cmd, 'prepare err vector', log_name, result)
        cmd1 = case_config['SANITIZER_OPTION_CHECK']['PREPARE']['CMD1']
        out = run_loc_cmd(cmd1)
        logging.info(str(out))
        if out.succeeded:
            logging.info('prepare---create the vector err sample successful')
        else:
            logging.info('prepare---create the vector err sample failed')
            # exit(1)
        # run step1
        step1_cmd = case_config['SANITIZER_OPTION_CHECK']['STEP1']['CMD']
        check_point = case_config['SANITIZER_OPTION_CHECK']['STEP1']['CHECK_POINT']
        check_result(step1_cmd, 'run_step1-cmd-----%s' % step1_cmd, log_name, result, check_point, flag=1)
        # run step2
        step2_cmd1 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CMD1'] % (sample_0_path, asyncAPI_sln)
        check_point1 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CHECK_POINT1']
        step2_cmd2 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CMD2']
        check_point2 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CHECK_POINT2']
        step2_cmd3 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CMD3']
        if cuda_short_version <= '11.4':
            check_point3 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CHECK_POINT3']
        else:
            check_point3 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CHECK_POINT3_1']
        check_result(step2_cmd1, 'step2_cmd1------%s' % step2_cmd1, log_name, result, check_point1)
        out2 = run_loc_cmd(step2_cmd2)
        save_log(log_name, step2_cmd2, 'step2_2', out2['output'])
        if out2.succeeded is False and check_point2 in out2['output'] and out2['exit'] == 1:
            result[f'step2_2 -- {step2_cmd2}'] = 'passed'
            passed += 1
            logging.info('step2_2 run the vector err sample successful')
        else:
            result[f'step2_2 -- {step2_cmd2}'] = 'failed'
            failed += 1
            logging.info('step2_2 run the vector err sample failed')
        out3 = run_loc_cmd(step2_cmd3)
        save_log(log_name, step2_cmd3, 'step2_3', out3['output'])
        logger.info('step2_cmd2 output is %s' % out3['output'])
        check_point4 = case_config['SANITIZER_OPTION_CHECK']['STEP2']['CHECK_POINT4']
        # The exit code on Windows is 1, not 6, so change 6 == out3['exit'] to 1 == out3['exit'] on Windows. Nov 4, 2021
        if not out3.succeeded and 1 == out3['exit'] and '******' in out3['output'] and check_point3 in out3['output'] and check_point4 in out3['output']:
            result['step2_3----%s' % step2_cmd3] = 'passed'
            logging.info('step2_3 run the vector err sample successful')
        else:
            result['step2_3----%s' % step2_cmd3] = 'failed'
            logging.info('step2_3 run the vector err sample failed')
        # run step3
        step3_cmd1 = case_config['SANITIZER_OPTION_CHECK']['STEP3']['CMD1']
        step3_cmd2 = case_config['SANITIZER_OPTION_CHECK']['STEP3']['CMD2']
        check_point = case_config['SANITIZER_OPTION_CHECK']['STEP3']['CHECK_POINT']
        out4 = run_loc_cmd(step3_cmd1)
        save_log(log_name, step3_cmd1, 'step3_1', out4['output'])
        if out4.succeeded or check_point not in out4['output'] or out4['exit'] != 1:
            result[f'step3_1--{step3_cmd1}'] = 'failed'
            failed += 1
            logging.info('step3_1 run the vector err sample failed')
        else:
            result[f'step3_1--{step3_cmd1}'] = 'passed'
            passed += 1
            logging.info('step3_1 run the vector err sample successful')
        out5 = run_loc_cmd(step3_cmd2)
        save_log(log_name, step3_cmd2, 'step3_2', out5['output'])
        # https://nvbugs/4297207 [Require Change] Test Procedure on 2181021/Options - prefix/error-exitcode/print-limit/check-exit-code
        # Updated on Dec 19, 2023
        if cuda_short_version >= '12.3':
            step3_2_return_code = 1
        else:
            step3_2_return_code = 0
        if check_point not in out5['output'] or out5['exit'] != step3_2_return_code:
            result[f'step3_2 -- {step3_cmd2}'] = 'failed'
            failed += 1
            logging.info('step3_2 run the vector err sample failed')
        else:
            result[f'step3_2---{step3_cmd2}'] = 'passed'
            passed += 1
            logging.info('step3_2 run the vector err sample successful')
    except Exception as e:
        logger.error(f'has some error happen----{str(e)}')
        result['some steps run fail, please check it'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/check_option.json' % log_path)
    return result, passed, failed


@print_run_time
def check_save_log():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['BASE_LOG_PATH7']
    log_name = case_config['SANITIZER_SAVE_LOG']['LOG_NAME']
    mkdir(log_path)
    # prepare vector_err sample
    try:
        prepare_cmd = case_config['SANITIZER_SAVE_LOG']['PREPARE']['PREPARE_CMD']
        logger.info(f'will prepare the vector err sample---{prepare_cmd}')
        run_loc_cmd(prepare_cmd)
        cmd = case_config['SANITIZER_SAVE_LOG']['PREPARE']['CMD'] % (user, password, base_url)
        logger.info('we will download source file')
        out = run_loc_cmd(cmd)
        save_log(log_name, cmd, 'prepare', out['output'])
        cmd1 = case_config['SANITIZER_SAVE_LOG']['PREPARE']['CMD1']
        out = run_loc_cmd(cmd1)
        logging.info(str(out))
        if out.succeeded:
            logging.info('prepare---create the vector err sample successful')
        else:
            logging.info('prepare---create the vector err sample failed')
            exit(1)
        # run step1
        step1_cmd = case_config['SANITIZER_SAVE_LOG']['STEP1']['CMD']
        step1_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP1']['CMD1']
        step1_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP1']['CMD2']
        check = case_config['SANITIZER_SAVE_LOG']['STEP1']['CHECK_POINT']
        check1 = case_config['SANITIZER_SAVE_LOG']['STEP1']['CHECK_POINT1']
        check2 = case_config['SANITIZER_SAVE_LOG']['STEP1']['CHECK_POINT2']
        check_result(step1_cmd, 'step1_1', log_name, result, flag=1)
        check_result(step1_cmd1, 'step1_2', log_name, result, flag=1)
        check_result(step1_cmd2, 'step1_3', log_name, result, check1, check)
        # run step2
        step2_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP2']['CMD']
        step2_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP2']['CMD1']
        check_step2 = case_config['SANITIZER_SAVE_LOG']['STEP2']['CHECK_POINT']
        check_result(step2_cmd1, 'step2_1', log_name, result, check_step2)
        check_result(step2_cmd2, 'step2_2', log_name, result, )
        # run step3
        step3_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP3']['CMD']
        step3_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP3']['CMD1']
        check_result(step3_cmd1, 'step3_1', log_name, result)
        check_result(step3_cmd2, 'step3_2', log_name, result)
        # run step4
        step4_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP4']['CMD']
        step4_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP4']['CMD1']
        step4_check = case_config['SANITIZER_SAVE_LOG']['STEP4']['CHECK_POINT']
        step4_check1 = case_config['SANITIZER_SAVE_LOG']['STEP4']['CHECK_POINT1']
        check_result(step4_cmd1, 'step4_1', log_name, result, step4_check)
        check_result(step4_cmd2, 'step4_2', log_name, result, step4_check1)
        # run step5
        step5_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP5']['CMD']
        step5_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP5']['CMD1']
        step5_check = case_config['SANITIZER_SAVE_LOG']['STEP5']['CHECK_POINT']
        check_result(step5_cmd1, 'step5_1', log_name, result, step5_check)
        check_result(step5_cmd2, 'step5_2', log_name, result)
        # run step6
        step6_cmd1 = case_config['SANITIZER_SAVE_LOG']['STEP6']['CMD']
        step6_cmd2 = case_config['SANITIZER_SAVE_LOG']['STEP6']['CMD1']
        step6_check = case_config['SANITIZER_SAVE_LOG']['STEP6']['CHECK_POINT']
        check_result(step6_cmd1, 'step6_1', log_name, result, step6_check, flag=1)
        check_result(step6_cmd2, 'step6_2', log_name, result, step6_check, flag=1)
    except Exception as e:
        logger.error(f'An error occurred during test execution: {str(e)}')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/check_save_log.json' % case_config['global']['env']['BASE_LOG_PATH7'])
    return result, passed, failed


@print_run_time
def check_launch_timeout():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['BASE_LOG_PATH8']
    log_name = case_config['SANITIZER_LAUNCH_TIMEOUT']['LOG_NAME']
    mkdir(log_path)
    try:
        prepare_cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['PREPARE']['PREPARE_CMD']
        logger.info('will prepare the vector while sample---{}'.format(prepare_cmd))
        run_loc_cmd(prepare_cmd)
        cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['PREPARE']['CMD'] % (user, password, base_url)
        out = run_loc_cmd(cmd)
        print(cmd)
        save_log(log_name, cmd, 'prepare', out['output'])
        if out.succeeded:
            logging.info('prepare---create the vector while sample successful')
        else:
            logging.info('prepare---create the vector while sample failed')
            exit(1)
        # run step1
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2182162   --kill option had “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            step1_cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP1']['CMD1']
        else:
            step1_cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP1']['CMD']
        step1_check = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP1']['CHECK_POINT']
        start1 = time.time()
        check_result(step1_cmd, 'step1', log_name, result, step1_check, flag=1)
        end1 = time.time()
        run_time1 = end1 - start1
        print("====== start1 is %s" % str(start1))
        print("====== end1 is %s" % str(end1))
        print("====== run_time1 is %s" % str(run_time1))
        # https://nvbugs/4395553 [Require Change] Test Procedure on 2182162/Options - launch-timeout/kill
        # Default timeout is 10 seconds for single process tracking and 60 seconds for multi-process.
        # 3. As “--target-processes”  default to “all"  in 12.4, so the step1 will delays 60s
        # Updated on Dec 20, 2023
        if cuda_short_version >= '12.4':
            step1__cmd_time_range = {"MIN": 60, "MAX": 70}
        else:
            step1__cmd_time_range = {"MIN": 10, "MAX": 20}
        if step1__cmd_time_range["MIN"] <= run_time1 <= step1__cmd_time_range["MAX"] and result['step1'] == 'passed':
            result[f'step1 -- {step1_cmd}'] = 'passed'
        else:
            result[f'step1 -- {step1_cmd}'] = 'failed'
        # run step2
        if cuda_short_version >= '12.3':
            step2_cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP2']['CMD1']
        else:
            step2_cmd = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP2']['CMD']
        step2_check = case_config['SANITIZER_LAUNCH_TIMEOUT']['STEP2']['CHECK_POINT']
        start2 = time.time()
        check_result(step2_cmd, 'step2', log_name, result, step2_check, flag=1)
        end2 = time.time()
        run_time2 = end2 - start2
        real_time = run_time2 - run_time1
        print("====== start2 is %s" % str(start2))
        print("====== end2 is %s" % str(end2))
        print("====== run_time2 is %s" % str(run_time2))
        if 15 <= run_time2 <= 40 and result['step2'] == 'passed':
            result[f'step2 -- {step2_cmd}'] = 'passed'
        else:
            result[f'step2 -- {step2_cmd}'] = 'failed'
    except Exception as e:
        logger.error(f'An error occurred during test execution: {str(e)}')
        result['check_launch_timeout'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/check_launch_timeout.json' % case_config['global']['env']['BASE_LOG_PATH8'])
    return result, passed, failed


@print_run_time
def check_demangle():
    result = {}
    passed, failed = 0, 0
    # prepare the sample
    prepare_cmd = case_config['CHECK_DEMANGLE']['PREPARE']['CMD'] % (user, password, base_url)
    out1 = run_loc_cmd(prepare_cmd)
    print(prepare_cmd)
    print(out1['output'])

    # step 1, build t777 sample
    log_path = case_config['global']['env']['BASE_LOG_PATH4']
    cmd = case_config['CHECK_DEMANGLE']['STEP1']['CMD']
    log_name = case_config['CHECK_DEMANGLE']['LOG_NAME']
    mkdir(log_path)
    check_point = case_config['CHECK_DEMANGLE']['STEP1']['CHECK_POINT']
    step1_out = run_loc_cmd(cmd)
    save_log(log_name, cmd, 'step1--%s' % cmd, step1_out['output'])
    if step1_out.succeeded and check_point in step1_out['output']:
        result['step1'] = 'passed'
        logger.info('we build the sample t744 successful')
    else:
        result['step1'] = 'failed'
        logger.info('we build the sample t744 failed')
        exit(2)
    # run step2
    cmd1 = case_config['CHECK_DEMANGLE']['STEP2']['CMD1']
    cmd2 = case_config['CHECK_DEMANGLE']['STEP2']['CMD2']
    check_point1 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT1']
    check_point2 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT2']
    if cuda_short_version < '11.4':
        check_point1_1 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT1_2']
        check_point2_1 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT2_1']
    else:
        check_point1_1 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT1_1']
        check_point2_1 = case_config['CHECK_DEMANGLE']['STEP2']['CHECK_POINT2_2']
    check_result(cmd1, 'step2_1----%s' % cmd1, log_name, result, check_point1, check_point1_1)
    check_result(cmd2, 'step2_2----%s' % cmd2, log_name, result, check_point2, check_point2_1)
    # run steps3
    cmd3 = case_config['CHECK_DEMANGLE']['STEP3']['CMD']
    check_point3 = case_config['CHECK_DEMANGLE']['STEP3']['CHECK_POINT']
    check_point3_1 = case_config['CHECK_DEMANGLE']['STEP3']['CHECK_POINT1']
    check_result(cmd3, 'step3----%s' % cmd3, log_name, result, check_point3, check_point3_1)
    # run steps4
    cmd4 = case_config['CHECK_DEMANGLE']['STEP4']['CMD']
    check_point4 = case_config['CHECK_DEMANGLE']['STEP4']['CHECK_POINT']
    check_point4_1 = case_config['CHECK_DEMANGLE']['STEP4']['CHECK_POINT1']
    check_result(cmd4, 'step4----%s' % cmd4, log_name, result, check_point4, check_point4_1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/check_demangle.json' % case_config['global']['env']['BASE_LOG_PATH4'])
    return result, passed, failed


@print_run_time
def check_filter():
    result = {}
    passed, failed = 0, 0
    # prepare the sample
    prepare_cmd = case_config['CHECK_FILTER']['PREPARE']['CMD'] % (user, password, base_url)
    out1 = run_loc_cmd(prepare_cmd)
    print(prepare_cmd)
    print(out1['output'])

    # step 1, build t777 sample
    log_path = case_config['global']['env']['BASE_LOG_PATH6']
    cmd = case_config['CHECK_FILTER']['STEP1']['CMD']
    log_name = case_config['CHECK_FILTER']['LOG_NAME']
    mkdir(log_path)
    step1_out = run_loc_cmd(cmd)
    expect_output1 = ['========= COMPUTE-SANITIZER', '31 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 ', '========= RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)']
    save_log(log_name, cmd, 'step1--%s' % cmd, step1_out['output'])
    if step1_out.succeeded:
        result['step1'] = 'passed'
        logger.info('we build the sample t744 successful')
    else:
        result['step1'] = 'failed'
        logger.info('we build the sample t744 failed')
        exit(2)
    # run step2
    cmd1 = case_config['CHECK_FILTER']['STEP2']['CMD']
    check_point1 = case_config['CHECK_FILTER']['STEP2']['CHECK_POINT']
    check_result(cmd1, 'step2--%s' % cmd1, log_name, result, check_point1)
    # run step 3
    # update to --kernel-name/ --kernel-name-exclude since cuda12.3
    if cuda_short_version >= '12.3':
        option = 'kernel-name'
        option1 = 'kernel-name-exclude'
    elif cuda_short_version >= '12.0':
        option = 'kernel-regex'
        option1 = 'kernel-regex-exclude'
    else:
        option = 'filter'
        option1 = 'exclude'
    step3_cmd1 = case_config['CHECK_FILTER']['STEP3']['CMD1'].format(option)
    step3_cmd2 = case_config['CHECK_FILTER']['STEP3']['CMD2'].format(option)
    step3_cmd3 = case_config['CHECK_FILTER']['STEP3']['CMD3'].format(option)
    check_point = case_config['CHECK_FILTER']['STEP3']['CHECK_POINT']
    step3_check1 = case_config['CHECK_FILTER']['STEP3']['CHECK_POINT1']
    step3_check2 = case_config['CHECK_FILTER']['STEP3']['CHECK_POINT2']
    step3_check2_1 = case_config['CHECK_FILTER']['STEP3']['CHECK_POINT2_1']
    step3_check3 = case_config['CHECK_FILTER']['STEP3']['CHECK_POINT3']

    def run_cmd_check(cmd, step, output, check1, result):
        out = run_loc_cmd(cmd)
        save_log(log_name, cmd, 'run-steps--%s' % step, out['output'])
        if out.succeeded and out['output'].split('\n') == output and check1 not in out['output']:
            result[step] = 'passed'
            logger.info('we run %s successful' % step)
        else:
            result[step] = 'failed'
            logger.info('we run %s failed' % step)
    step3_out1 = run_loc_cmd(step3_cmd1)
    if step3_out1.succeeded and step3_check1 in step3_out1['output'] and check_point not in step3_out1['output'] and 'aaa' not in step3_out1['output']:
        result['step3_1----%s' % step3_cmd1] = 'passed'
        logger.info('we run step3_1 successful')
    else:
        result['step3_1----%s' % step3_cmd1] = 'failed'
        logger.info('we run step3_1 failed')
    # check_result_check(step3_cmd1, 'step3_1', step3_check1, check_point)
    check_result(step3_cmd2, 'step3_2-----%s' % step3_cmd2, log_name, result, step3_check2, step3_check2_1)
    # run_cmd_check(step3_cmd3, 'step3_3', step3_check3, check_point)
    step3_out3 = run_loc_cmd(step3_cmd3)
    save_log(log_name, step3_cmd3, 'run-step3_cmd3--%s' % step3_cmd3, step3_out3['output'])
    if step3_out3.succeeded and step3_check3 in step3_out3['output'] and check_point not in step3_out3['output']:
        result['step3_3---%s' % step3_cmd3] = 'passed'
        logger.info('we run step3_3 successful')
    else:
        result['step3_3----%s' % step3_cmd3] = 'failed'
        logger.info('we run step3_3 failed')
    # run step 4
    step4_cmd1 = case_config['CHECK_FILTER']['STEP4']['CMD1'].format(option)
    step4_cmd2 = case_config['CHECK_FILTER']['STEP4']['CMD2'].format(option)
    step4_check1 = case_config['CHECK_FILTER']['STEP4']['CHECK_POINT1']
    step4_check2 = case_config['CHECK_FILTER']['STEP4']['CHECK_POINT2']
    step4_check1_1 = case_config['CHECK_FILTER']['STEP4']['CHECK_POINT1_1']
    check_result(step4_cmd1, 'step4_1---%s' % step4_cmd1, log_name, result, step4_check1, step4_check1_1)

    step4_out1 = run_loc_cmd(step4_cmd1)
    logger.info(step4_out1['output'])
    if step4_out1.succeeded and step4_check1 in step4_out1['output'] and step4_check1_1 in step4_out1['output']:
        result['step4_1'] = 'passed'
        logger.info('we run step4_1 successful')
    else:
        result['step4_1'] = 'failed'
        logger.info('we run step4_1 failed')

    check_result(step4_cmd2, 'step4_2---%s' % step4_cmd2, log_name, result, step4_check2)
    # run_cmd_check(step4_cmd2, 'step4_cmd2-----%s' % step4_cmd2, expect_output1, step4_check1_1, result)

    step4_out2 = run_loc_cmd(step4_cmd2)
    logger.info(step4_out2['output'])
    if step4_out2.succeeded and step4_check2 in step4_out2['output'] and step4_check1_1 not in step4_out2['output']:
        result['step4_2'] = 'passed'
        logger.info('we run step4_2 successful')
    else:
        result['step4_2'] = 'failed'
        logger.info('we run step4_2 failed')

    # run step 5 new step since CUDA 12.3
    # update to --kernel-name/ --kernel-name-exclude since cuda12.3
    if cuda_short_version >= '12.3':
        step5_cmd1 = case_config['CHECK_FILTER']['STEP5']['CMD1'].format(option)
        step5_cmd2 = case_config['CHECK_FILTER']['STEP5']['CMD2'].format(option)
        step5_check1 = case_config['CHECK_FILTER']['STEP5']['CHECK_POINT1']
        step5_check1_1 = case_config['CHECK_FILTER']['STEP5']['CHECK_POINT1_1']
        step5_check1_2 = case_config['CHECK_FILTER']['STEP5']['CHECK_POINT1_2']
        step5_check1_3 = case_config['CHECK_FILTER']['STEP5']['CHECK_POINT1_3']
        step5_check1_4 = case_config['CHECK_FILTER']['STEP5']['CHECK_POINT1_4']
        step5_check2 = case_config['CHECK_FILTER']['STEP4']['CHECK_POINT2']
        check_result(step5_cmd1, 'step5_1---%s' % step5_cmd1, log_name, result, step5_check1, step5_check1_1, step5_check1_2, step5_check1_3, step5_check1_4)
        check_result(step5_cmd2, 'step5_2---%s' % step5_cmd2, log_name, result, step5_check2)

    # run step 6
    step6_cmd = case_config['CHECK_FILTER']['STEP6']['CMD'].format(option1)
    step6_check2 = case_config['CHECK_FILTER']['STEP6']['CHECK_POINT1']
    check_result(step6_cmd, 'step6---%s' % step6_cmd, log_name, result, step6_check2)
    # run_cmd(step6_cmd, 'step6', 'passed', 'failed', log_name, result, step6_check1)
    # run_cmd_check(step6_cmd, 'step6----%s' % step6_cmd, expect_output1, step6_check2, result)
    step6_out = run_loc_cmd(step6_cmd)
    logger.info(step6_out['output'])
    if step6_out.succeeded and step6_check2 in step6_out['output']:
        result['step6'] = 'passed'
        logger.info('we run step6 successful')
    else:
        result['step6'] = 'failed'
        logger.info('we run step6 failed')

    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/check_filter.json' % case_config['global']['env']['BASE_LOG_PATH6'])
    return result, passed, failed


@print_run_time
def sanitizer_sanity():
    result = {}
    passed, failed = 0, 0
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2179905 All options that had “no” as default, no longer require yes/no  since 12.3 DTCS-1290
    if cuda_short_version >= '12.3':
        cmd_list = case_config['SANITIZER_SANITY']['STEP1']['CMD1']
    else:
        cmd_list = case_config['SANITIZER_SANITY']['STEP1']['CMD']
    log_path = case_config['global']['env']['BASE_LOG_PATH3']
    mkdir(log_path)
    check = case_config['SANITIZER_SANITY']['STEP1']['CHECK_POINT']
    check1 = case_config['SANITIZER_SANITY']['STEP1']['CHECK_POINT1']
    check2 = case_config['SANITIZER_SANITY']['STEP1']['CHECK_POINT2']
    check3 = case_config['SANITIZER_SANITY']['STEP1']['CHECK_POINT3']
    step2_cmd = case_config['SANITIZER_SANITY']['STEP2']['CMD']

    for cmd in cmd_list:
        logger.info('we will run %s' % cmd)
        if 'initcheck' in cmd:
            log_name = case_config['global']['env']['BASE_LOG_PATH3'] + '/' + 'initcheck.log'
            check_result(cmd, 'step1_1----%s' % cmd, log_name, result, check, check1)
            check_result(step2_cmd, 'step2_1', log_name, result, 'initcheck.log')
        elif 'leak-check' in cmd:
            log_name = case_config['global']['env']['BASE_LOG_PATH3'] + '/' + 'leak_check.log'
            check_result(cmd, 'step1_2----%s' % cmd, log_name, result, check2, check3)
            check_result(step2_cmd, 'step2_2', log_name, result, 'leak.log')
        elif 'synccheck' in cmd:
            log_name = case_config['global']['env']['BASE_LOG_PATH3'] + '/' + 'synccheck.log'
            check_result(cmd, 'step1_3----%s' % cmd, log_name, result)
            check_result(step2_cmd, 'step2_3', log_name, result, 'synccheck.log')
        elif 'racecheck' in cmd:
            log_name = case_config['global']['env']['BASE_LOG_PATH3'] + '/' + 'racecheck.log'
            check_result(cmd, 'step1_4----%s' % cmd, log_name, result)
            check_result(step2_cmd, 'step2_4', log_name, result)
        else:
            logger.info('there is check the option')

    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_sanity.json' % case_config['global']['env']['BASE_LOG_PATH3'])
    return result, passed, failed


@print_run_time
def sample_coverage():
    result = {}
    passed, failed = 0, 0
    sample_list = case_config['SANITIZER_SAMPLE_COVERAGE']['SAMPLE_LIST'].split(',')
    log_name = case_config['SANITIZER_SAMPLE_COVERAGE']['LOG_NAME']
    check_point = case_config['SANITIZER_SAMPLE_COVERAGE']['CHECK_POINT']
    mkdir(log_name)
    driver_mode = common_get_driver_mode()
    print("====== The driver mode is %s" % driver_mode)
    print("====== Sample list is %s" % str(sample_list))
    for sample in sample_list:
        print("++++++++++++++++++++++++++++++++++ running cuda sample: %s ++++++++++++++++++++++++++++++++++" % sample)
        if os.path.isfile('%s/%s' % (bin_path, sample + ".exe")):
            # run step1
            step1_cmd = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP1']['CMD'] % sample
            step1_point = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP1']['CHECK_POINT']
            step1_point1 = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP1']['CHECK_POINT1']
            logname1 = log_name + '/%s.log' % sample
            # run step2
            step2_cmd = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP2']['CMD'] % sample
            step2_point = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP2']['CHECK_POINT']
            logname2 = log_name + '/%s.log' % sample
            # run step3
            step3_cmd = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP3']['CMD'] % sample
            step3_point = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP3']['CHECK_POINT']
            logname3 = log_name + '/%s.log' % sample
            # run step4
            step4_cmd = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP4']['CMD'] % sample
            step4_point = case_config['SANITIZER_SAMPLE_COVERAGE']['STEP4']['CHECK_POINT']
            # Racecheck hang with reductionMultiBlockCG, can add "-c 1"
            if sample in ['reductionMultiBlockCG']:
                step4_cmd = r'cd "%s"; cmd.exe /c "%s/compute-sanitizer" --tool racecheck -c 1 %s.exe' % (bin_path, sanitizer_bin_path, sample)
            logname4 = log_name + '/%s.log' % sample
            # Add judgement for several samples with Windows driver TCC, Jun 20, 2022
            # Device 0 VIRTUAL ADDRESS MANAGEMENT SUPPORTED = 0.
            # Device 0 doesn't support VIRTUAL ADDRESS MANAGEMENT.
            # fix bug: 4795799
            if sample in ['vectorAddMMAP'] and driver_mode in ['TCC']:
                tcc_checkpoint = 'Device 0 doesn\'t support VIRTUAL ADDRESS MANAGEMENT'
                check_result(step1_cmd, 'step1_leakcheck -- %s' % step1_cmd, logname1, result, step1_point, tcc_checkpoint, flag=1)
                check_result(step2_cmd, 'step2_initcheck -- %s' % step2_cmd, logname2, result, step2_point, tcc_checkpoint, flag=1)
                check_result(step3_cmd, 'step3_synccheck -- %s' % step3_cmd, logname3, result, step3_point, tcc_checkpoint, flag=1)
                check_result(step4_cmd, 'step4_racecheck -- %s' % step4_cmd, logname4, result, step4_point, tcc_checkpoint, flag=1)
            else:
                check_result(step1_cmd, 'step1_leakcheck -- %s' % step1_cmd, logname1, result, step1_point, step1_point1)
                if sample == 'cdpAdvancedQuicksort':
                    check_result(step2_cmd, 'step2_initcheck -- %s' % step2_cmd, logname2, result, check_point)
                    check_result(step3_cmd, 'step3_synccheck -- %s' % step3_cmd, logname3, result, check_point)
                    check_result(step4_cmd, 'step4_racecheck -- %s' % step4_cmd, logname4, result, check_point)
                else:
                    check_result(step2_cmd, 'step2_initcheck -- %s' % step2_cmd, logname2, result, step2_point)
                    check_result(step3_cmd, 'step3_synccheck -- %s' % step3_cmd, logname3, result, step3_point)
                    check_result(step4_cmd, 'step4_racecheck -- %s' % step4_cmd, logname4, result, step4_point)
        else:
            logger.info('please check the sample whether exist or not')
            result['run sanitizer with sample -- {} failed, because there is no this sample'.format(sample)] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sample_coverage.json' % case_config['global']['env']['BASE_LOG_PATH1'])
    return result, passed, failed


@print_run_time
def padding_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['PADDING_OPTION_PATH']
    log_name = case_config['PADDING_OPTION']['LOG_NAME']
    mkdir(log_path)
    # Prepare sanitizer dvs package
    prepare_sanitizer(arch=platform)
    print("======================= " + run_path)
    if os.path.exists('%s/Padding.exe' % run_path):
        # run step1
        step1_cmd = case_config['PADDING_OPTION']['STEP1']['CMD'] % run_path
        step1_check_point = case_config['PADDING_OPTION']['STEP1']['CHECK_POINT']
        check_result(step1_cmd, 'padding_optin-step1------%s' % step1_cmd, log_name, result, step1_check_point)
    else:
        logger.info('please check the sample, it does not exist')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/padding_option.json' % log_path)
    return result, passed, failed


@print_run_time
def launch_count_skip():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['LAUNCH_COUNT_SKIP_PATH']
    log_name = case_config['LAUNCH_COUNT_SKIP']['LOG_NAME']
    mkdir(log_path)
    # Prepare sanitizer dvs package
    prepare_sanitizer(arch=platform)
    check_list1 = ['foo1()', 'foo2()', 'foo3()', 'foo4()', 'foo5()', 'foo6()']
    if os.path.exists('%s/raceSkipCount.exe' % run_path):
        # run step1
        step1_cmd = case_config['LAUNCH_COUNT_SKIP']['STEP1']['CMD'] % run_path
        step1_check_point = case_config['LAUNCH_COUNT_SKIP']['STEP1']['CHECK_POINT']
        check_result(step1_cmd, 'launch_count_skip-step1------%s' % step1_cmd, log_name, result, step1_check_point, check_list1[0], check_list1[1], check_list1[2], check_list1[3], check_list1[4], check_list1[5])
        # run step2
        step2_cmd = case_config['LAUNCH_COUNT_SKIP']['STEP2']['CMD'] % run_path
        step2_check_point = case_config['LAUNCH_COUNT_SKIP']['STEP2']['CHECK_POINT']
        check_result(step2_cmd, 'launch_count_skip-step2------%s' % step2_cmd, log_name, result, step2_check_point, check_list1[2], check_list1[3])
        # run step3
        step3_cmd = case_config['LAUNCH_COUNT_SKIP']['STEP3']['CMD'] % run_path
        step3_check_point = case_config['LAUNCH_COUNT_SKIP']['STEP3']['CHECK_POINT']
        check_result(step3_cmd, 'launch_count_skip-step3------%s' % step3_cmd, log_name, result, step3_check_point, check_list1[0], check_list1[1], check_list1[2], check_list1[3])
        # run step4
        step4_cmd = case_config['LAUNCH_COUNT_SKIP']['STEP4']['CMD'] % run_path
        step4_check_point = case_config['LAUNCH_COUNT_SKIP']['STEP4']['CHECK_POINT']
        check_result(step4_cmd, 'launch_count_skip-step4------%s' % step4_cmd, log_name, result, step4_check_point, check_list1[4], check_list1[5], check_list1[2], check_list1[3])
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/launch_count_skip.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_no_crash():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_NO_CRASH_PATH']
    log_name = case_config['SANITIZER_NO_CRASH']['LOG_NAME']
    mkdir(log_path)
    # Prepare alloc file
    cmd = case_config['SANITIZER_NO_CRASH']['PREPARE']['CMD'] % (user, password, base_url, 'alloca', 'Alloca1.cu')
    print(cmd)
    run_loc_cmd(cmd)
    if os.path.exists('%s/../alloca/Alloca1.cu' % tools_home):
        cmd1 = case_config['SANITIZER_NO_CRASH']['PREPARE']['CMD1']
        run_loc_cmd(cmd1)
    else:
        logger.warning('we download the needed file fail, exit.......')
        exit(2)
    check_point = case_config['SANITIZER_NO_CRASH']['CHECK_POINT']
    check_point1 = case_config['SANITIZER_NO_CRASH']['CHECK_POINT1']
    if os.path.exists('%s/../alloca/alloca.exe' % tools_home):
        # run step1
        step1_cmd = case_config['SANITIZER_NO_CRASH']['STEP1']['CMD']
        check_result(step1_cmd, 'sanitizer_no_crash-step1------%s' % step1_cmd, log_name, result, check_point)
        # run step2
        step2_cmd = case_config['SANITIZER_NO_CRASH']['STEP2']['CMD']
        check_result(step2_cmd, 'sanitizer_no_crash-step2------%s' % step2_cmd, log_name, result, check_point)
        # run step3
        step3_cmd = case_config['SANITIZER_NO_CRASH']['STEP3']['CMD']
        check_result(step3_cmd, 'sanitizer_no_crash-step3------%s' % step3_cmd, log_name, result, check_point)
        # run step4
        step4_cmd = case_config['SANITIZER_NO_CRASH']['STEP4']['CMD']
        check_result(step4_cmd, 'sanitizer_no_crash-step4------%s' % step4_cmd, log_name, result, check_point1)
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_no_crash.json' % log_path)
    return result, passed, failed


@print_run_time
def target_process_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['TARGET_PROCESS_OPTION_PATH']
    log_name = case_config['TARGET_PROCESS_OPTION']['LOG_NAME']
    mkdir(log_path)
    tools_home = case_config['global']['env']['TOOLS_HOME']
    check_point = case_config['TARGET_PROCESS_OPTION']['CHECK_POINT']
    check_point1 = case_config['TARGET_PROCESS_OPTION']['CHECK_POINT1']
    check_point2 = case_config['TARGET_PROCESS_OPTION']['CHECK_POINT2']
    check_point3 = case_config['TARGET_PROCESS_OPTION']['CHECK_POINT3']
    sample_path = case_config['global']['env']['SAMPLE_BIN_PATH']
    # prepare
    prepare_cmd1 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD1'] % (sample_0_path, matrixMul_sln)
    prepare_cmd2 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD2'] % (sample_0_path, asyncAPI_sln)
    prepare_cmd3 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD3'] % (sample_0_path, mergeSort_sln)
    restore_cmd1 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD1_1'] % (sample_0_path, matrixMul_sln)
    restore_cmd2 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD2_1'] % (sample_0_path, asyncAPI_sln)
    restore_cmd3 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD3_1'] % (sample_0_path, mergeSort_sln)
    prepare_cmd4 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD4']
    prepare_cmd5 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD5']
    prepare_cmd6 = case_config['TARGET_PROCESS_OPTION']['PREPARE']['CMD6']
    check_result(prepare_cmd1, 'build_matrixMul--%s' % prepare_cmd1, log_name, result)
    check_result(prepare_cmd2, 'build_asyncAPI--%s' % prepare_cmd2, log_name, result)
    check_result(prepare_cmd3, 'build_mergeSort--%s' % prepare_cmd3, log_name, result)
    check_result(prepare_cmd4, 'prepare_run.bat--%s' % prepare_cmd4, log_name, result)
    check_result(prepare_cmd5, 'prepare_run.py--%s' % prepare_cmd5, log_name, result)
    check_result(prepare_cmd6, 'prepare_run.pl--%s' % prepare_cmd6, log_name, result)
    if os.path.exists('%s/../test.bat' % tools_home) and os.path.exists('%s/../test.py' % tools_home) and os.path.exists('%s/../test.pl' % tools_home):
        with open('%s/../test.bat' % tools_home, 'r+') as f:
            f.write('start "" "%s/matrixMul.exe"\n' % sample_path)
            f.write('start "" "%s/asyncAPI.exe"\n' % sample_path)
            f.write('start "" "%s/mergeSort.exe"\n' % sample_path)
        with open('%s/../test.py' % tools_home, 'r+') as f1:
            f1.write('import subprocess\n')
            f1.write('subprocess.Popen("%s/matrixMul.exe")\n' % sample_path)
            f1.write('subprocess.Popen("%s/matrixMul.exe")\n' % sample_path)
            f1.write('subprocess.Popen("%s/mergeSort.exe")\n' % sample_path)
        with open('%s/../test.pl' % tools_home, 'r+') as f2:
            f2.write('use threads;\n')
            f2.write('my $th1 = async {system("%s/matrixMul.exe");};\n' % sample_path)
            f2.write('my $th2 = async {system("%s/matrixMul.exe");};\n' % sample_path)
            f2.write('my $th3 = async {system("%s/mergeSort.exe");};\n' % sample_path)
            f2.write('$th1->join();\n')
            f2.write('$th2->join();\n')
            f2.write('$th3->join();\n')

        # Default to all for target-processes since 12.4
        if cuda_short_version >= '12.4':
            step1_cmd1 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD1_12_4']
            step1_cmd2 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD2_12_4']
            step1_cmd3 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD3_12_4']
            step1_cmd4 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD4_12_4']
            step2_cmd1 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD1_12_4']
            step2_cmd2 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD2_12_4']
            step2_cmd3 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD3_12_4']
            step2_cmd4 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD4_12_4']
        else:
            step1_cmd1 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD1']
            step1_cmd2 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD2']
            step1_cmd3 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD3']
            step1_cmd4 = case_config['TARGET_PROCESS_OPTION']['STEP1']['CMD4']
            step2_cmd1 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD1']
            step2_cmd2 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD2']
            step2_cmd3 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD3']
            step2_cmd4 = case_config['TARGET_PROCESS_OPTION']['STEP2']['CMD4']
        # Step 1: Check --target-processes option
        check_result(step1_cmd1, 'target_process_option-step1-1------%s' % step1_cmd1, log_name, result, check_point2, check_point3, flag=1)
        check_result(step1_cmd2, 'target_process_option-step1-2------%s' % step1_cmd2, log_name, result, check_point)
        check_result(step1_cmd3, 'target_process_option-step1-3------%s' % step1_cmd3, log_name, result, check_point)
        check_result(step1_cmd4, 'target_process_option-step1-4------%s' % step1_cmd4, log_name, result, check_point)
        # run step2  Test  other checkers - initcheck, synccheck and racecheck
        check_result(step2_cmd1, 'target_process_option-step2-1------%s' % step2_cmd1, log_name, result, check_point)
        check_result(step2_cmd2, 'target_process_option-step2-2------%s' % step2_cmd2, log_name, result, check_point)
        check_result(step2_cmd3, 'target_process_option-step2-3------%s' % step2_cmd3, log_name, result, check_point)
        check_result(step2_cmd4, 'target_process_option-step2-4------%s' % step2_cmd4, log_name, result, check_point1)
        # restore the sample to release
        check_result(restore_cmd1, 'restore_matrixMul--%s' % restore_cmd1, log_name, result)
        check_result(restore_cmd2, 'restore_asyncAPI--%s' % restore_cmd2, log_name, result)
        check_result(restore_cmd3, 'restore_mergeSor--%s' % restore_cmd3, log_name, result)
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/target_process_option.json' % log_path)
    return result, passed, failed


@print_run_time
def racecheck_detect_print_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['RACECHECK_DETECT_PRINT_OPTION_PATH']
    log_name = case_config['RACECHECK_DETECT_PRINT_OPTION']['LOG_NAME']
    mkdir(log_path)
    # prepare race sample
    prepare_cmd = case_config['RACECHECK_DETECT_PRINT_OPTION']['PREPARE']['CMD'] % (user, password, base_url, '2681761', 'race.cu')
    prepare_cmd1 = case_config['RACECHECK_DETECT_PRINT_OPTION']['PREPARE']['CMD1']
    out1 = run_loc_cmd(prepare_cmd)
    out2 = run_loc_cmd(prepare_cmd1)
    print(prepare_cmd)
    print(out1['output'])
    print(prepare_cmd1)
    print(out2['output'])
    tools_home = case_config['global']['env']['TOOLS_HOME']
    check_point = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT']
    check_point1 = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT1']
    check_point1_1 = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT1_1']
    check_point2 = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT2']
    check_point3 = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT3']
    check_point4 = case_config['RACECHECK_DETECT_PRINT_OPTION']['CHECK_POINT4']
    if os.path.exists('%s/../2681761/race.exe' % tools_home):
        # run step1
        step1_cmd = case_config['RACECHECK_DETECT_PRINT_OPTION']['STEP1']['CMD']
        check_result(step1_cmd, 'racecheck_detect_print_option-step1------%s' % step1_cmd, log_name, result, check_point, check_point2, check_point4)
        # run step2
        step2_cmd = case_config['RACECHECK_DETECT_PRINT_OPTION']['STEP2']['CMD']
        check_result(step2_cmd, 'racecheck_detect_print_option-step2------%s' % step2_cmd, log_name, result, check_point1, check_point2, check_point3, check_point4)
        # run step3
        step3_cmd = case_config['RACECHECK_DETECT_PRINT_OPTION']['STEP3']['CMD']
        check_result(step3_cmd, 'racecheck_detect_print_option-step3------%s' % step3_cmd, log_name, result, check_point, check_point2, check_point4)
        # run step4
        step4_cmd = case_config['RACECHECK_DETECT_PRINT_OPTION']['STEP4']['CMD']
        check_result(step4_cmd, 'racecheck_detect_print_option-step4------%s' % step4_cmd, log_name, result, check_point1_1, check_point2)
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/racecheck_detect_print_option.json' % log_path)
    return result, passed, failed


@print_run_time
def cuda_coredump_disable():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_COREDUMP_DISABLE_PATH']
    log_name = case_config['CUDA_COREDUMP_DISABLE']['LOG_NAME']
    mkdir(log_path)
    # prepare needed sample
    prepare_cmd = case_config['CUDA_COREDUMP_DISABLE']['PREPARE']['CMD'] % (user, password, base_url)
    prepare_cmd1 = case_config['CUDA_COREDUMP_DISABLE']['PREPARE']['CMD1']
    print("prepare_cmd is \n %s " % prepare_cmd)
    print("prepare_cmd1 is \n %s " % prepare_cmd1)
    prepare_out = run_loc_cmd(prepare_cmd)
    prepare_out1 = run_loc_cmd(prepare_cmd1)
    print("prepare_out output is \n %s " % prepare_out['output'])
    print("prepare_out1 output is \n %s " % prepare_out1['output'])
    check_point = case_config['CUDA_COREDUMP_DISABLE']['CHECK_POINT']
    if os.path.exists('%s/../2524649/memcheck_demo.exe' % tools_home):
        # run step1
        step1_cmd = case_config['CUDA_COREDUMP_DISABLE']['STEP1']['CMD']
        check_result(step1_cmd, 'cuda_coredump_disable-step1------%s' % step1_cmd, log_name, result, check_point)
        # run step2
        step2_cmd = case_config['CUDA_COREDUMP_DISABLE']['STEP2']['CMD']
        check_result(step2_cmd, 'cuda_coredump_disable-step2------%s' % step2_cmd, log_name, result, check_point)
        # run step3
        step3_cmd = case_config['CUDA_COREDUMP_DISABLE']['STEP3']['CMD']
        check_result(step3_cmd, 'cuda_coredump_disable-step3------%s' % step3_cmd, log_name, result, check_point)
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_coredump_disable.json' % log_path)
    return result, passed, failed


@print_run_time
def cuda_leak_check():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_LEAK_CHECK_PATH']
    log_name = case_config['CUDA_LEAK_CHECK']['LOG_NAME']
    mkdir(log_path)
    try:
        # Prepare needed file
        cmd = case_config['CUDA_LEAK_CHECK']['PREPARE']['CMD'] % (user, password, base_url)
        check_result(cmd, 'cuda_leak_check-step1_prepare------%s' % cmd, log_name, result)
        check_point = case_config['CUDA_LEAK_CHECK']['CHECK_POINT']
        check_point1 = case_config['CUDA_LEAK_CHECK']['CHECK_POINT1']
        if os.path.exists('%s/simpleCubemapTexture_memleak/simpleCubemapTexture_memleak.exe' % log_path):
            # run step1
            step1_cmd = case_config['CUDA_LEAK_CHECK']['STEP1']['CMD']
            check_result(step1_cmd, 'cuda_leak_check-step2_run------%s' % step1_cmd, log_name, result, check_point, check_point1)
        else:
            logger.info('there is no sample to run, please check it')
            exit(2)
    except Exception as e:
        logger.error('has some error happen ----%s' % str(e))
        result['some steps run fail, please check it'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_leak_check.json' % log_path)
    return result, passed, failed


@print_run_time
def device_stack_frame():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['DEVICE_STACK_FRAME_PATH']
    log_name = case_config['DEVICE_STACK_FRAME']['LOG_NAME']
    mkdir(log_path)
    # prepare the sample
    prepare_cmd = case_config['DEVICE_STACK_FRAME']['PREPARE']['CMD'] % (user, password, base_url)
    out1 = run_loc_cmd(prepare_cmd)
    print(prepare_cmd)
    print(out1['output'])
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2764359 Per bug 3659869 fix in 12.3, new display format implemented
    if cuda_short_version >= '12.3':
        check_point1 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT1_12_3']
        check_point2 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT2_12_3']
        if cuda_short_version > '12.9':
            check_point2 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT2_13_0']
        check_point3 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT3_12_3']
        check_point4 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT4_12_3']
        check_point5 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT5_12_3']
        check_point6 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT6_12_3']
    else:
        check_point1 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT1']
        check_point2 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT2']
        check_point3 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT3']
        check_point4 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT4']
        check_point5 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT4']
        check_point6 = case_config['DEVICE_STACK_FRAME']['CHECK_POINT4']
    if os.path.exists('%s/../2764359/test.exe' % tools_home):
        # run step1
        step1_cmd = case_config['DEVICE_STACK_FRAME']['STEP1']['CMD']
        check_result(step1_cmd, 'device_stack_frame-step1_run------%s' % step1_cmd, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6)
    else:
        logger.info('there is no sample to run, please check it')
        exit(2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/device_stack_frame.json' % log_path)
    return result, passed, failed


@print_run_time
def correct_line_num():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CORRECT_LINE_NUM_PATH']
    log_name = case_config['CORRECT_LINE_NUM']['LOG_NAME']
    mkdir(log_path)
    # prepare the sample
    prepare_cmd = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD'] % (user, password, base_url)
    prepare_cmd_1 = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD_1']
    prepare_cmd_2 = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD_2']
    prepare_cmd1 = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd1_1 = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD1_1']
    prepare_cmd1_2 = case_config['CORRECT_LINE_NUM']['PREPARE']['CMD1_2']
    for cmd in [prepare_cmd, prepare_cmd_1, prepare_cmd_2, prepare_cmd1, prepare_cmd1_1, prepare_cmd1_2]:
        check_result(cmd, 'prepare_%s' % cmd, log_name, result)

    step1_cmd = case_config['CORRECT_LINE_NUM']['STEP1']['CMD']
    step1_cmd1 = case_config['CORRECT_LINE_NUM']['STEP1']['CMD1']
    step2_cmd = case_config['CORRECT_LINE_NUM']['STEP2']['CMD']
    step2_cmd1 = case_config['CORRECT_LINE_NUM']['STEP2']['CMD1']
    if cuda_short_version < '12.3':
        check_point1_1 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_1']
        check_point1_2 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_2']
        check_point1_3 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_3']
        check_point2_1 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_1']
        check_point2_2 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_2']
        check_point2_3 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_3']
        check_result(step1_cmd, 'correct_line_num-step1_run------%s(Has a known open bug 3659869)' % step1_cmd, log_name, result, check_point1_1, check_point1_2, check_point1_3, flag=1)
        check_result(step1_cmd1, 'correct_line_num-step1_run2------%s(Has a known open bug 3659869)' % step1_cmd1, log_name, result, check_point1_1, check_point1_2, check_point1_3, flag=1)
        check_result(step2_cmd, 'correct_line_num-step2_run------%s' % step2_cmd, log_name, result, check_point2_1, check_point2_2, check_point2_3, flag=1)
        check_result(step2_cmd1, 'correct_line_num-step2_run2------%s' % step2_cmd1, log_name, result, check_point2_1, check_point2_2, check_point2_3, flag=1)
    else:
        check_point1_4 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_4']
        check_point1_5 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_5']
        check_point1_6 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_6']
        check_point1_7 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_7']
        check_point1_8 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_8']
        check_point1_9 = case_config['CORRECT_LINE_NUM']['CHECK_POINT1_9']
        check_point2_4 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_4']
        check_point2_5 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_5']
        check_point2_6 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_6']
        check_point2_7 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_7']
        check_point2_8 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_8']
        check_point2_9 = case_config['CORRECT_LINE_NUM']['CHECK_POINT2_9']
        check_result(step1_cmd, 'correct_line_num-step1_run------%s' % step1_cmd, log_name, result, check_point1_7, check_point1_8, check_point1_9, check_point1_4, check_point1_5, check_point1_6, flag=1)
        check_result(step1_cmd1, 'correct_line_num-step1_run2------%s' % step1_cmd1, log_name, result, check_point1_4, check_point1_5, check_point1_6, check_point2_7, check_point2_8, check_point2_9, flag=1)
        check_result(step2_cmd, 'correct_line_num-step2_run------%s' % step2_cmd, log_name, result, check_point2_7, check_point2_8, check_point2_9, check_point2_4, check_point2_5, check_point2_6, flag=1)
        check_result(step2_cmd1, 'correct_line_num-step2_run2------%s' % step2_cmd1, log_name, result, check_point2_7, check_point2_8, check_point2_9, check_point2_4, check_point2_5, check_point2_6, flag=1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/correct_line_num.json' % log_path)
    return result, passed, failed


@print_run_time
def stop_using_warpfullmask():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['STOP_USING_WARPFULLMASK_PATH']
    log_name = case_config['STOP_USING_WARPFULLMASK']['LOG_NAME']
    mkdir(log_path)
    # prepare the file
    cmd = case_config['STOP_USING_WARPFULLMASK']['PREPARE']['CMD'] % (user, password, base_url)
    check_result(cmd, 'download the file by ----%s' % cmd, log_name, result)
    if is_empty_file('%s/../Bug200746055/repro.cu' % tools_home):
        logger.info('we download the file fail, exiting......')
    else:
        cmd1 = case_config['STOP_USING_WARPFULLMASK']['PREPARE']['CMD1']
        check_result(cmd1, 'build the sample by ----%s' % cmd1, log_name, result)
    # run the step1
    check_point = case_config['STOP_USING_WARPFULLMASK']['CHECK_POINT']
    step1_cmd = case_config['STOP_USING_WARPFULLMASK']['STEP1']['CMD']
    check_result(step1_cmd, 'run step1------%s' % step1_cmd, log_name, result, check_point)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/stop_using_warpfullmask.json' % log_path)
    return result, passed, failed


@print_run_time
def memcheck_with_free():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['MEMCHECK_WITH_FREE_PATH']
    log_name = case_config['MEMCHECK_WITH_FREE']['LOG_NAME']
    mkdir(log_path)
    # prepare the sample
    prepare_cmd = case_config['MEMCHECK_WITH_FREE']['PREPARE']['CMD'] % (user, password, base_url)
    prepare_cmd1 = case_config['MEMCHECK_WITH_FREE']['PREPARE']['CMD1']
    check_result(prepare_cmd, 'get the source file by cmd------%s' % prepare_cmd, log_name, result)
    check_result(prepare_cmd1, 'build the sample by cmd------%s' % prepare_cmd, log_name, result)
    # run the sample by compute sanitizer
    cmd = case_config['MEMCHECK_WITH_FREE']['STEP1']['CMD']
    if cuda_short_version < '13.0':
        check_point = case_config['MEMCHECK_WITH_FREE']['STEP1']['CHECK_POINT']
        check_point1 = case_config['MEMCHECK_WITH_FREE']['STEP1']['CHECK_POINT1']
    else:
        check_point = case_config['MEMCHECK_WITH_FREE']['STEP1']['CHECK_POINT_1']
        check_point1 = case_config['MEMCHECK_WITH_FREE']['STEP1']['CHECK_POINT1_1']
    check_point2 = case_config['MEMCHECK_WITH_FREE']['STEP1']['CHECK_POINT2']
    check_result(cmd, 'run the sample by sanitizer------%s' % cmd, log_name, result, check_point, check_point1, check_point2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/memcheck_with_free.json' % log_path)
    return result, passed, failed


@print_run_time
def relative_return_address_2800789():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['RELATIVE_RETURN_ADDRESS_PATH']
    log_name = case_config['RELATIVE_RETURN_ADDRESS']['LOG_NAME']
    mkdir(log_path)
    if SM < 7.0 and cuda_short_version < '11.5':
        logger.info('we do not support the sample less than SM 7.0')
    else:
        # prepare the sample
        prepare_cmd = case_config['RELATIVE_RETURN_ADDRESS']['PREPARE']['CMD'] % (user, password, base_url)
        prepare_cmd1 = case_config['RELATIVE_RETURN_ADDRESS']['PREPARE']['CMD1']
        check_result(prepare_cmd, 'get the source file by cmd------%s' % prepare_cmd, log_name, result)
        check_result(prepare_cmd1, 'build the sample by cmd------%s' % prepare_cmd1, log_name, result)
        # run the sample by compute sanitizer
        if cuda_short_version >= '12.8':
            cmd = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CMD1']
        else:
            cmd = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CMD']
        if cuda_short_version >= '12.3':
            check_point = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT_12_3']
            check_point1 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT1_12_3']
            check_point2 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT2_12_3']
            check_point3 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT3_12_3']
            check_point4 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT4_12_3']
            check_point5 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT5_12_3']
            check_point6 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT6_12_3']
            check_point7 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT7_12_3']
            check_point8 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT8_12_3']
            if cuda_short_version > '12.9':
                check_point8 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT8_13_0']
            check_point9 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT9_12_3']
        else:
            check_point = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT']
            check_point1 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT1']
            check_point2 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT2']
            check_point3 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT3']
            check_point4 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT4']
            check_point5 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT5']
            check_point6 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT6']
            check_point7 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT7']
            check_point8 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT8']
            check_point9 = case_config['RELATIVE_RETURN_ADDRESS']['STEP1']['CHECK_POINT9']
        check_result(cmd, 'run the sample by sanitizer------%s' % cmd, log_name, result, check_point, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7, check_point8, check_point9)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/relative_return_address.json' % log_path)
        return result, passed, failed


@print_run_time
def read_only_flag():
    if cuda_short_version > '11.8':
        # https://nvbugs/4565835 [Require change] Add TCC support in sanitizer case 2818363
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not Apr 19, 2024
        driver_mode = common_get_driver_mode()
        logger.info("the driver mode is %s." % driver_mode)
        if driver_mode in ['TCC']:
            logger.info('we waive this case if the driver mode on Windows is TCC')
            return
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['READ_ONLY_FLAG_PATH']
        log_name = case_config['READ_ONLY_FLAG']['LOG_NAME']
        mkdir(log_path)
        # prepare dvs package
        prepare_sanitizer('x86_win')
        # run the cmd
        cmd = case_config['READ_ONLY_FLAG']['STEP1']['CMD']
        nvidia_compute_sanitizer_test_path = r"{}/nvidia-compute-sanitizer-test".format(download_to_path)
        cmd1 = cmd % nvidia_compute_sanitizer_test_path
        check_point = case_config['READ_ONLY_FLAG']['CHECK_POINT']
        # "cd C:/tesla_automation_results/sanitizer_20211130/nvidia-compute-sanitizer-test ; cmd.exe /c "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.6/compute-sanitizer/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace no memmapBasic.exe read-only"
        # $LASTEXITCODE = 2 ===> Set the out.succeeded = True on Nov 30, 2021, Add flag=1
        check_result(cmd1, 'run the command--%s' % cmd1, log_name, result, check_point)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/read_only_flag.json' % log_path)
        return result, passed, failed
    else:
        logger.info('we can not support if the cuda version is less  than 12.0')


@print_run_time
def detail_session_option():
    if cuda_short_version > '11.4':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['DETAIL_SESSION_OPTION_PATH']
        log_name = case_config['DETAIL_SESSION_OPTION']['LOG_NAME']
        mkdir(log_path)
        check_point = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT']
        check_point1 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT1']
        check_point2 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT2']
        check_point3 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT3']
        check_point4 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT4']
        check_point5 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT5']
        check_point6 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT6']
        check_point7 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT7']
        check_point8 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT8']
        check_point9 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT9']
        check_point10 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT10']
        check_point11 = case_config['DETAIL_SESSION_OPTION']['CHECK_POINT11']
        # run step1
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2819761 This 2 options have “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            cmd1 = case_config['DETAIL_SESSION_OPTION']['STEP1']['CMD1']
        else:
            cmd1 = case_config['DETAIL_SESSION_OPTION']['STEP1']['CMD']
        check_result(cmd1, 'Step 1 compute-sanitizer to run test--%s' % cmd1, log_name, result, check_point, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7, check_point8, check_point9, check_point10, check_point11)
        # run step2
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2819761 This 2 options have “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            cmd2 = case_config['DETAIL_SESSION_OPTION']['STEP2']['CMD1']
        else:
            cmd2 = case_config['DETAIL_SESSION_OPTION']['STEP2']['CMD']
        check_result(cmd2, 'Step 2 compute-sanitizer to run test--%s' % cmd2, log_name, result, check_point, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7, check_point8, check_point9, check_point10, check_point11)
        # run step3
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2819761 This 2 options have “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            cmd3 = case_config['DETAIL_SESSION_OPTION']['STEP3']['CMD1']
        else:
            cmd3 = case_config['DETAIL_SESSION_OPTION']['STEP3']['CMD']
        check_result(cmd3, 'Step 3 compute-sanitizer to run test--%s' % cmd3, log_name, result, check_point, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7, check_point8, check_point9, check_point10, check_point11)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/detail_session_option.json' % log_path)
        return result, passed, failed
    else:
        logger.info('we do not support the case if cuda version is less than 11.5, it does not support')


# New. Added on Dec 31, 2021 based on Linux new case
@print_run_time
def sanitizer_xml_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_XML_OPTION_PATH']
    log_name = case_config['SANITIZER_XML_OPTION']['LOG_NAME']
    mkdir(log_path)
    # step1 prepare sample
    step1_cmd1 = case_config['SANITIZER_XML_OPTION']['STEP1']['CMD1'] % (user, password, base_url)
    step1_cmd2 = case_config['SANITIZER_XML_OPTION']['STEP1']['CMD2'] % (int(SM * 10), int(SM * 10))
    check_result(step1_cmd1, 'download the sourcefile by %s' % step1_cmd1, log_name, result)
    check_result(step1_cmd2, 'build the sample by %s' % step1_cmd2, log_name, result)
    # step2 generate the xml file
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2806646 -xml option has “no” as default, no longer require yes/no  since 12.3 DTCS-1290
    if cuda_short_version >= '12.3':
        step2_cmd1 = case_config['SANITIZER_XML_OPTION']['STEP2']['CMD1_1'] % ''
        if cuda_short_version >= '12.8':
            step2_cmd1 = case_config['SANITIZER_XML_OPTION']['STEP2']['CMD1_1'] % '--strip-paths no --backtrace-short no'
    else:
        step2_cmd1 = case_config['SANITIZER_XML_OPTION']['STEP2']['CMD1']
    print("execute by %s " % step2_cmd1)
    out = run_loc_cmd(step2_cmd1)
    list1 = []
    print(out['output'])
    with open('%s/2806646/out.xml' % tools_home, 'r') as f:
        for line in f.readlines():
            list1.append(line.split('>')[1].split('<')[0])
    list2 = [i for i in set(list1) if i not in ['/n', 'Precise', 'Api']]
    pass1, fail1 = 0, 0
    from xml.sax.saxutils import unescape
    for i in list2:
        # Filter the different errors for Ampere, Turing, Volta.
        # Ampere: Program hit cudaErrorNoKernelImageForDevice (error 209) due to "no kernel image is available for execution on the device" on CUDA API call to cudaLaunchKernel.
        # Turing: Program hit cudaErrorUnknown (error 999) due to "unknown error" on CUDA API call to cudaMemcpy.
        # Volta: Program hit cudaErrorLaunchFailure (error 719) due to "unspecified launch failure" on CUDA API call to cudaMemcpy.
        if 'Program hit cudaErrorLaunchFailure (error 719) due' not in i and 'Program hit cudaErrorUnknown (error 999) due' not in i and 'Program hit cudaErrorNoKernelImageForDevice (error 209) due' not in i:
            if 'Invalid __global__ read of size 4 bytes is misaligned' in i:
                pass
            else:
                if unescape(i) not in out['output']:
                    print(i)
                    fail1 += 1
                else:
                    pass1 += 1
    print(fail1)
    if fail1 != 0:
        result['check_xml_file'] = 'failed'
    else:
        result['check_xml_file'] = 'passed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_xml_option.json' % log_path)
    return result, passed, failed


@print_run_time
def cuda_barriers_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_BARRIERS_OPTION_PATH']
    log_name = case_config['CUDA_BARRIERS_OPTION']['LOG_NAME']
    mkdir(log_path)
    if SM < 8.0:
        logger.info('we should run this case on Ampere or new GPU')
    else:
        try:
            # prepare the file
            prepare_cmd = case_config['CUDA_BARRIERS_OPTION']['PREPARE']['PREPARE_CMD']
            print(prepare_cmd)
            run_loc_cmd(prepare_cmd)
            sm1 = str(SM).split('.')[0] + str(SM).split('.')[1]
            prepare_cmd1 = case_config['CUDA_BARRIERS_OPTION']['PREPARE']['CMD1'].format(user, password, base_url, sm1)
            print(prepare_cmd1)
            check_result(prepare_cmd1, 'build the sample by %s' % prepare_cmd1, log_name, result)
            # https://nvbugs/4613469 [Require Change] Test Procedure on 2819793/Option - num-cuda-barriers
            # Add support for Step 5 Repeat above steps with racecheck and we should get the similar results.

            # Please do the code change for latest update - skipp step4 + racecheck for Hopper+ due to Sanitizer does not rely on barrier tracking structures on Hopper+.
            # Updated on 2024.06.18
            tools_list = ['synccheck'] if SM >= 9.0 else ['synccheck', 'racecheck']
            print(">>>>>> sm is %s ====> tools_list is %s <<<<<<" % (SM, tools_list))

            for tool in tools_list:
                print(">>>>>> check --tool %s ...... <<<<<<" % tool)
                if tool == 'synccheck':
                    check_point = case_config['CUDA_BARRIERS_OPTION']['CHECK_POINT']
                    check_point1 = case_config['CUDA_BARRIERS_OPTION']['CHECK_POINT1']
                else:
                    check_point = case_config['CUDA_BARRIERS_OPTION']['CHECK_POINT_RACECHECK']
                    check_point1 = case_config['CUDA_BARRIERS_OPTION']['CHECK_POINT_RACECHECK1']
                check_point2 = case_config['CUDA_BARRIERS_OPTION']['CHECKPOINT2']
                # run step1
                step1_cmd = case_config['CUDA_BARRIERS_OPTION']['STEP1']['CMD'] % tool
                check_result(step1_cmd, 'run step1 - %s by %s' % (tool, step1_cmd), log_name, result, check_point)
                # run step2
                step2_cmd = case_config['CUDA_BARRIERS_OPTION']['STEP2']['CMD'] % tool
                check_result(step2_cmd, 'run step2 - %s by %s' % (tool, step2_cmd), log_name, result, check_point)
                # run step3
                step3_cmd = case_config['CUDA_BARRIERS_OPTION']['STEP3']['CMD'] % tool
                check_result(step3_cmd, 'run step3 - %s by %s' % (tool, step3_cmd), log_name, result, check_point)
                # run step4
                step4_cmd = case_config['CUDA_BARRIERS_OPTION']['STEP4']['CMD'] % tool
                if tool == 'synccheck':
                    check_result(step4_cmd, 'run step4- %s by %s' % (tool, step4_cmd), log_name, result, check_point1, check_point2, flag=1)
                else:
                    check_result(step4_cmd, 'run step4- %s by %s' % (tool, step4_cmd), log_name, result, check_point1, check_point2)
        except Exception as e:
            logger.error('has some error happen ----%s' % str(e))
            result['some steps run fail, please check it'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_barriers_option.json' % log_path)
        return result, passed, failed


@print_run_time
def leak_check_alloc():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['LEAK_CHECK_ALLOC_PATH']
    log_name = case_config['LEAK_CHECK_ALLOC']['LOG_NAME']
    mkdir(log_path)
    # prepare the file
    cmd = case_config['LEAK_CHECK_ALLOC']['PREPARE']['CMD'] % (user, password, base_url)
    check_result(cmd, 'download the file by ----%s' % cmd, log_name, result)
    if is_empty_file('%s/../Bug200746055/repro.cu' % tools_home):
        logger.info('we download the file fail, exiting......')
    else:
        cmd1 = case_config['LEAK_CHECK_ALLOC']['PREPARE']['CMD1']
        check_result(cmd1, 'build the sample by ----%s' % cmd1, log_name, result)
    # run the step1
    check_point = case_config['LEAK_CHECK_ALLOC']['CHECK_POINT']
    check_point1 = case_config['LEAK_CHECK_ALLOC']['CHECK_POINT1']
    step1_cmd = case_config['LEAK_CHECK_ALLOC']['STEP1']['CMD']
    check_result(step1_cmd, 'run step1------%s' % step1_cmd, log_name, result, check_point, check_point1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/leak_check_alloc.json' % log_path)
    return result, passed, failed


@print_run_time
def target_filter_option():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['TARGET_FILTER_OPTION_PATH']
    log_name = case_config['TARGET_FILTER_OPTION']['LOG_NAME']
    mkdir(log_path)
    try:
        # step1 prepare the test.sh file
        step1_cmd1 = case_config['TARGET_FILTER_OPTION']['STEP1']['CMD1'] % (user, password, base_url)
        step1_cmd2 = case_config['TARGET_FILTER_OPTION']['STEP1']['CMD2']
        check_result(step1_cmd1, 'build vectorADD-err sample by %s' % step1_cmd1, log_name, result)
        check_result(step1_cmd2, 'create test.sh file by %s' % step1_cmd2, log_name, result)
        prepare_cmd1 = case_config['TARGET_FILTER_OPTION']['PREPARE']['CMD1'] % (sample_0_path, matrixMul_sln)
        prepare_cmd3 = case_config['TARGET_FILTER_OPTION']['PREPARE']['CMD3'] % (sample_6_path, mergeSort_sln)
        check_result(prepare_cmd1, 'build_matrixMul', log_name, result)
        check_result(prepare_cmd3, 'build_mergeSort', log_name, result)
        if os.path.exists('%s/../test.bat' % tools_home):
            with open('%s/../test.bat' % tools_home, 'r+') as f:
                # f.write("#!/bin/bash\n")
                f.write('start "" "%s/matrixMul.exe"\n' % sample_bin_path)
                f.write('start "" "%s/vectorAdd_memleak/vectorAdd_memleak.exe"\n' % log_path)
                f.write('start "" "%s/mergeSort.exe"\n' % sample_bin_path)
        # step2 step3 run the cmd
        # Default to all for target-processes since 12.4 DTCS-1373
        # https://nvbugs/4348813 [Require Change] Test Procedure on 2834633/update command as default to all for target-processes since 12.4
        # Updated on Dec 19, 2023
        if cuda_short_version >= '12.4':
            step2_cmd1 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD1_12_4']
            step2_cmd2 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD2_12_4']
            step2_cmd3 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD3_12_4']
            step2_cmd4 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD4_12_4']
            step3_cmd1 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD1_12_4']
            step3_cmd2 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD2_12_4']
            step3_cmd3 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD3_12_4']
        else:
            step2_cmd1 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD1']
            step2_cmd2 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD2']
            step2_cmd3 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD3']
            step2_cmd4 = case_config['TARGET_FILTER_OPTION']['STEP2']['CMD4']
            step3_cmd1 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD1']
            step3_cmd2 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD2']
            step3_cmd3 = case_config['TARGET_FILTER_OPTION']['STEP3']['CMD3']
        check_point1 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT1']
        check_point2 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT2']
        check_point3 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT3']
        check_point4 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT4']
        check_point5 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT5']
        # Task https://nvbugs/4515059 [Require Change] Test Procedure on 2834633/Option - target-processes-filter
        # Update the new checkpoint since CUDA12.5 branch. Apr 19, 2024
        if cuda_short_version >= '12.5':
            check_point6 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT6_12_5']
            check_point7 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT7_12_5']
        else:
            check_point6 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT6']
            check_point7 = case_config['TARGET_FILTER_OPTION']['CHECK_POINT7']
        # run step2 and step3
        check_result(step2_cmd1, 'run step2_cmd1 by %s' % step2_cmd1, log_name, result, check_point1, check_point2)
        check_result(step2_cmd2, 'run step2_cmd2 by %s' % step2_cmd2, log_name, result, check_point3, check_point4)
        check_result(step2_cmd3, 'run step2_cmd3 by %s' % step2_cmd3, log_name, result, check_point3, check_point4)
        check_result(step2_cmd4, 'run step2_cmd4 by %s' % step2_cmd4, log_name, result, check_point1, check_point2)

        check_result(step3_cmd1, 'run step3_cmd1 by %s' % step3_cmd1, log_name, result, check_point5, flag=1)
        check_result(step3_cmd2, 'run step3_cmd2 by %s' % step3_cmd2, log_name, result, check_point5, flag=1)
        # A known bug for step3_cmd3 https://nvbugs/4386219 [CudaTools][12.4][Sanitizer]Application error when doing negative test of target-processes-filter
        check_result(step3_cmd3, 'run step3_cmd3 by %s' % step3_cmd3, log_name, result, check_point6, check_point7, flag=1)
    except Exception as e:
        logger.error(f'has some error hapeen----{str(e)}')
        result['some steps run fail, please check it'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/target_filter_option.json' % log_path)
    return result, passed, failed


@print_run_time
def stack_overflow_check():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['STACK_OVERFLOW_CHECK_PATH']
    log_name = case_config['STACK_OVERFLOW_CHECK']['LOG_NAME']
    mkdir(log_path)
    # prepare the file
    cmd = case_config['STACK_OVERFLOW_CHECK']['PREPARE']['CMD'] % (user, password, base_url)
    check_result(cmd, 'download the file by ----%s' % cmd, log_name, result)
    if is_empty_file('%s/../Bug200746055/repro.cu' % tools_home):
        logger.info('we download the file fail, exiting......')
    else:
        cmd1 = case_config['STACK_OVERFLOW_CHECK']['PREPARE']['CMD1'] % int(SM * 10)
        check_result(cmd1, 'build the sample by ----%s' % cmd1, log_name, result)
    # run the step1
    if cuda_short_version >= '12.3':
        check_point1 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT1_12_3']
        check_point2 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT2_12_3']
        check_point3 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT3_12_3']
        check_point4 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT4_12_3']
        check_point5 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT5_12_3']
    else:
        check_point1 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT1']
        check_point2 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT2']
        check_point3 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT3']
        check_point4 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT4']
        check_point5 = case_config['STACK_OVERFLOW_CHECK']['CHECK_POINT5']
    step1_cmd = case_config['STACK_OVERFLOW_CHECK']['STEP1']['CMD']
    check_result(step1_cmd, 'run step1------%s' % step1_cmd, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/stack_overflow_check.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_memcheck_demo():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_MEMCHECK_DEMO_PATH']
    log_name = case_config['SANITIZER_MEMCHECK_DEMO']['LOG_NAME']
    mkdir(log_path)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2931394 Per bug 3659869 fix in 12.3, new display format implemented
    if cuda_short_version >= '12.3':
        check_point1 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT1_1']
        check_point2 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT2_1']
        check_point3 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT3_1']
        check_point4 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT4_1']
        check_point5 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT5_1']
        check_point6 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT6_1']
        check_point7 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT7_1']
        check_point8 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT8_1']
    else:
        check_point1 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT4']
        check_point5 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT5']
        check_point6 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT6']
        check_point7 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT7']
        check_point8 = case_config['SANITIZER_MEMCHECK_DEMO']['CHECK_POINT8']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_MEMCHECK_DEMO']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_MEMCHECK_DEMO']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_MEMCHECK_DEMO']['PREPARE']['CMD3']
    prepare_cmd4 = case_config['SANITIZER_MEMCHECK_DEMO']['PREPARE']['CMD4']
    for cmd in [prepare_cmd1, prepare_cmd2]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - release_smaple
    check_result(prepare_cmd3, 'build release sample by %s' % prepare_cmd3, log_name, result)
    step1_cmd1 = case_config['SANITIZER_MEMCHECK_DEMO']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6)
    # run step2 --debug sample
    check_result(prepare_cmd4, 'build debug sample by %s' % prepare_cmd4, log_name, result)
    step2_cmd1 = case_config['SANITIZER_MEMCHECK_DEMO']['STEP2']['CMD1']
    check_result(step2_cmd1, 'run step2 debug build by %s' % step2_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7, check_point8)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_memcheck_demo.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_racecheck_warp():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_RACECHECK_WARP_PATH']
    log_name = case_config['SANITIZER_RACECHECK_WARP']['LOG_NAME']
    mkdir(log_path)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2931395 Per bug 3659869 fix in 12.3, new display format implemented
    if cuda_short_version >= '12.3':
        check_point1 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT1_1']
        check_point2 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT2_1']
        check_point3 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT3_1']
        check_point4 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT4_1']
        check_point5 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT5_1']
        check_point6 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT6_1']
        check_point7 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT7_1']
    else:
        check_point1 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT4']
        check_point5 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT5']
        check_point6 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT6']
        check_point7 = case_config['SANITIZER_RACECHECK_WARP']['CHECK_POINT7']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_RACECHECK_WARP']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_RACECHECK_WARP']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_RACECHECK_WARP']['PREPARE']['CMD3'] % (user, password, base_url)
    prepare_cmd4 = case_config['SANITIZER_RACECHECK_WARP']['PREPARE']['CMD4']
    for cmd in [prepare_cmd1, prepare_cmd2, prepare_cmd3]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - racecheck with warp
    check_result(prepare_cmd4, 'build release sample by %s' % prepare_cmd4, log_name, result)
    step1_cmd1 = case_config['SANITIZER_RACECHECK_WARP']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point7)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_racecheck_warp.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_racecheck_block():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_RACECHECK_BLOCK_PATH']
    log_name = case_config['SANITIZER_RACECHECK_BLOCK']['LOG_NAME']
    mkdir(log_path)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2931396 Per bug 3659869 fix in 12.3, new display format implemented
    if cuda_short_version >= '12.3':
        check_point1 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT1_1']
        check_point2 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT2_1']
        check_point3 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT3_1']
        check_point4 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT4_1']
        check_point5 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT5_1']
        check_point6 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT6_1']
    else:
        check_point1 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT4']
        check_point5 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT5']
        check_point6 = case_config['SANITIZER_RACECHECK_BLOCK']['CHECK_POINT6']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_RACECHECK_BLOCK']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_RACECHECK_BLOCK']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_RACECHECK_BLOCK']['PREPARE']['CMD3'] % (user, password, base_url)
    prepare_cmd4 = case_config['SANITIZER_RACECHECK_BLOCK']['PREPARE']['CMD4']
    for cmd in [prepare_cmd1, prepare_cmd2, prepare_cmd3]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - racecheck with warp
    check_result(prepare_cmd4, 'build release sample by %s' % prepare_cmd4, log_name, result)
    step1_cmd1 = case_config['SANITIZER_RACECHECK_BLOCK']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_racecheck_block.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_initcheck_error():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_INITCHECK_ERROR_PATH']
    log_name = case_config['SANITIZER_INITCHECK_ERROR']['LOG_NAME']
    mkdir(log_path)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2931397 Per bug 3659869 fix in 12.3, new display format implemented
    if cuda_short_version >= '12.3':
        check_point1 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT1_1']
        check_point2 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT2_1']
        check_point3 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT3_1']
        check_point4 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT4_1']
    else:
        check_point1 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_INITCHECK_ERROR']['CHECK_POINT4']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_INITCHECK_ERROR']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_INITCHECK_ERROR']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_INITCHECK_ERROR']['PREPARE']['CMD3']
    for cmd in [prepare_cmd1, prepare_cmd2]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - racecheck with warp
    check_result(prepare_cmd3, 'build release sample by %s' % prepare_cmd3, log_name, result)
    step1_cmd1 = case_config['SANITIZER_INITCHECK_ERROR']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_initcheck_error.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_leakcheck_memcheck_demo():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_LEAKCHECK_MEMCHECK_DEMO_PATH']
    log_name = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['LOG_NAME']
    mkdir(log_path)
    check_point1 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['CHECK_POINT1']
    check_point2 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['CHECK_POINT2']
    check_point3 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['CHECK_POINT3']
    check_point4 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['CHECK_POINT4']
    check_point5 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['CHECK_POINT5']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['PREPARE']['CMD3']
    for cmd in [prepare_cmd1, prepare_cmd2]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - debug_smaple
    check_result(prepare_cmd3, 'build release sample by %s' % prepare_cmd3, log_name, result)
    step1_cmd1 = case_config['SANITIZER_LEAKCHECK_MEMCHECK_DEMO']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_leakcheck_memcheck_demo.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_synccheck_divergent():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_SYNCCHECK_DIVERGENT_PATH']
    log_name = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['LOG_NAME']
    mkdir(log_path)
    check_point1 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['CHECK_POINT1']
    check_point2 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['CHECK_POINT2']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['PREPARE']['CMD3'] % (user, password, base_url)
    prepare_cmd4 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['PREPARE']['CMD4']
    for cmd in [prepare_cmd1, prepare_cmd2, prepare_cmd3]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - Synccheck with divergent
    check_result(prepare_cmd4, 'build release sample by %s' % prepare_cmd4, log_name, result)
    step1_cmd1 = case_config['SANITIZER_SYNCCHECK_DIVERGENT']['STEP1']['CMD1']
    if SM < 7.0:
        check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result, check_point1, check_point2)
    else:
        check_result(step1_cmd1, 'run step1 release build by %s' % step1_cmd1, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_synccheck_divergent.json' % log_path)
    return result, passed, failed


@print_run_time
def sanitizer_synccheck_syncwarp():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_SYNCCHECK_SYNCWARP_PATH']
    log_name = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['LOG_NAME']
    mkdir(log_path)
    check_point1 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['CHECK_POINT1']
    check_point2 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['CHECK_POINT2']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['PREPARE']['CMD3'] % (user, password, base_url)
    prepare_cmd4 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['PREPARE']['CMD4']
    prepare_cmd5 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['PREPARE']['CMD5']
    for cmd in [prepare_cmd1, prepare_cmd2, prepare_cmd3]:
        check_result(cmd, 'prepare file by %s' % cmd, log_name, result)
    # run step1 - Synccheck with divergent
    check_result(prepare_cmd4, 'build sample in debug mode by %s' % prepare_cmd4, log_name, result)
    step1_cmd1 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run step1 build in debug mode by %s' % step1_cmd1, log_name, result, check_point1, check_point2)
    # https://nvbugs/3533522 [CudaTools][Sanitizer][2022.2.0] No barrier errors detected on Ampere/Ada for samples built without debug options
    # bug 3533522 is fixed in \\builds\nightly\devtools\Sanitizer\Rel\DTC_D\2022.4.22200.0605_31584670 on GA10X
    if cuda_short_version >= '12.0':
        check_result(prepare_cmd5, 'build sample in release mode by %s' % prepare_cmd5, log_name, result)
        step1_cmd2 = case_config['SANITIZER_SYNCCHECK_SYNCWARP']['STEP1']['CMD2']
        check_result(step1_cmd2, 'run step1 build in release mode by %s' % step1_cmd2, log_name, result, check_point1, check_point2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_synccheck_syncwarp.json' % log_path)
    return result, passed, failed


@print_run_time
def leakcheck_devices_allocations():
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2594371
    # Not support vGPU/Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
    if driver_mode in ['TCC']:
        logger.info('we waive this case if the driver mode on Windows is TCC')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['LEAKCHECK_DEVICES_ALLOCATIONS_PATH']
        log_name = case_config['LEAKCHECK_DEVICES_ALLOCATIONS']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['LEAKCHECK_DEVICES_ALLOCATIONS']['CHECK_POINT1']
        check_point2 = case_config['LEAKCHECK_DEVICES_ALLOCATIONS']['CHECK_POINT2']
        # prepare sample
        prepare_sanitizer(arch=platform)
        # run sample
        step1_cmd1 = case_config['LEAKCHECK_DEVICES_ALLOCATIONS']['STEP1']['CMD1'] % run_path
        check_result(step1_cmd1, 'run-step1_cmd1 by %s' % step1_cmd1, log_name, result, check_point1, check_point2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/leakcheck_devices_allocations.json' % log_path)
        return result, passed, failed


@print_run_time
def memcheck_option_track_stream():
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2594371
    # Not support vGPU/Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
    if driver_mode in ['TCC']:
        logger.info('we waive this case if the driver mode on Windows is TCC')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['MEMCHECK_OPTION_TRACK_STREAM_PATH']
        log_name = case_config['MEMCHECK_OPTION_TRACK_STREAM']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT1']
        check_point2 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT2']
        check_point3 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT3']
        check_point4 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT4']
        check_point5 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT5']
        check_point6 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT6']
        check_point7 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['CHECK_POINT7']
        # prepare sample
        prepare_sanitizer(arch=platform)
        # run step1
        step1_cmd1 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['STEP1']['CMD1'] % run_path
        check_result(step1_cmd1, 'run-step1_cmd1 by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4)
        # run step2
        step2_cmd1 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['STEP2']['CMD1'] % run_path
        check_result(step2_cmd1, 'run-step2_cmd1 by %s' % step2_cmd1, log_name, result, check_point1, check_point2, check_point5)
        # run step3
        step3_cmd1 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['STEP3']['CMD1'] % run_path
        check_result(step3_cmd1, 'run-step3_cmd1 by %s' % step3_cmd1, log_name, result, check_point3, check_point6)
        # run step4
        step4_cmd1 = case_config['MEMCHECK_OPTION_TRACK_STREAM']['STEP4']['CMD1'] % run_path
        check_result(step4_cmd1, 'run-step4_cmd1 by %s' % step4_cmd1, log_name, result, check_point7)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/memcheck_option_track_stream.json' % log_path)
        return result, passed, failed


@print_run_time
def synccheck_option_miss_barrier():
    print("SM of the GPU is %s" % SM)
    if SM < 8.0:
        logger.info('we waive this case if SM is less than 8.0')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SYNCCHECK_OPTION_MISS_BARRIER_PATH']
        log_name = case_config['SYNCCHECK_OPTION_MISS_BARRIER']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['SYNCCHECK_OPTION_MISS_BARRIER']['CHECK_POINT1']
        check_point2 = case_config['SYNCCHECK_OPTION_MISS_BARRIER']['CHECK_POINT2']
        # prepare sample
        prepare_sanitizer(arch=platform)
        # run step1
        step1_cmd1 = case_config['SYNCCHECK_OPTION_MISS_BARRIER']['STEP1']['CMD1'] % run_path
        check_result(step1_cmd1, 'run-step1_cmd1 by %s' % step1_cmd1, log_name, result, check_point1, check_point2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/synccheck_option_miss_barrier.json' % log_path)
        return result, passed, failed


@print_run_time
def coredump_generation_support():
    if SM < 7.0:
        logger.info('We waive this case if SM is less than 7.0(Volta).')
    # Updated on Dec 15, 2022 Need to check Ada sm 8.9 unsupported
    # Ada unsupported (known issue3651018). Ada support since 12.1. Updated on Mar 15, 2023
    elif SM == 8.9 and cuda_short_version < '12.1':
        logger.info('We waive this case if SM is 8.9(Ada) and CUDA is less than 12.1. The support of Ada is since CUDA 12.1')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['COREDUMP_GENERATION_SUPPORT_PATH']
        log_name = case_config['COREDUMP_GENERATION_SUPPORT']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT1']
        check_point2 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT2']
        check_point3 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT3']
        # Task https://nvbugs/4380350 [Require Change] Test Procedure on 2806977 no need to check the sanitizer coredump exit code
        # Exit code changes to non-zero since 12.3 final
        # Updated on Dec 19, 2023
        if cuda_short_version >= '12.3':
            flag_exit_code = 1
        else:
            flag_exit_code = 0
        # run step1
        step1_cmd1 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run-step1_cmd1 by %s' % step1_cmd1, log_name, result, check_point1)
        # run step2
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2806977  --generate-coredump has “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            step2_cmd1 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP2']['CMD1_1']
        else:
            step2_cmd1 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP2']['CMD1']
        check_result(step2_cmd1, 'run-step2_cmd1 by %s' % step2_cmd1, log_name, result, check_point2, check_point3, flag=flag_exit_code)
        step2_cmd2 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP2']['CMD2']
        check_result(step2_cmd2, 'run-step2_cmd2 by %s' % step2_cmd2, log_name, result, check_point3)
        # Add for --coredump-name added since 12.1 on Mar 15, 2023
        if cuda_short_version >= '12.1':
            # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2806977  --generate-coredump has “no” as default, no longer require yes/no  since 12.3 DTCS-1290
            if cuda_short_version >= '12.3':
                step3_cmd1 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP3']['CMD1_1']
            else:
                step3_cmd1 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP3']['CMD1']
            step3_cmd2 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP3']['CMD2']
            check_point4 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT4']
            check_result(step3_cmd1, 'run-step3_cmd1 by %s' % step3_cmd1, log_name, result, check_point2, check_point4, flag=flag_exit_code)
            check_result(step3_cmd2, 'run-step3_cmd2 by %s' % step3_cmd2, log_name, result, check_point4)
            if cuda_short_version >= '12.2':
                # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2806977  --generate-coredump has “no” as default, no longer require yes/no  since 12.3 DTCS-1290
                if cuda_short_version >= '12.3':
                    step3_cmd3 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP3']['CMD3_1']
                else:
                    step3_cmd3 = case_config['COREDUMP_GENERATION_SUPPORT']['STEP3']['CMD3']
                check_point5 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT5']
                check_point6 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT6']
                check_point7 = case_config['COREDUMP_GENERATION_SUPPORT']['CHECK_POINT7']

                check_result(step3_cmd3, 'run-step3_cmd3 by %s' % step3_cmd3, log_name, result, check_point2, check_point5, check_point6, check_point7, flag=flag_exit_code)

        # Notes for step3 on Windows
        print("!!! Can not automate Step 3 use Visual Studio to open the core dump files, please execute manually.!!!")
        print("!!! The generated core dump file is at %s/2806977-coredump.!!!" % results_home)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/coredump_generation_support.json' % log_path)
        return result, passed, failed


@print_run_time
def sanitizer_build_all_samples():
    # Rebuild all CUDA Samples in DEBUG mode
    build_all_samples(cuda_version=cuda_short_version, vs_devenv=devenv, vs_solution_file=solution_file, build_type=3, build_mode='DEBUG')


def teardown_sanitizer_xxxxxx():
    import shutil
    if os.path.exists(download_to_path):
        shutil.rmtree(download_to_path)
        logger.info("Remove {} folder and its files sucessfully!".format(download_to_path))


@print_run_time
def call_host_device_3021849():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CALL_HOST_DEVICE_PATH']
    log_name = case_config['CALL_HOST_DEVICE']['LOG_NAME']
    mkdir(log_path)
    prepare_sanitizer(arch=platform)
    check_point1 = case_config['CALL_HOST_DEVICE']['CHECK_POINT1']

    def parse_output(output, device='None', host='None', trace='None'):
        device_num, host_num = 0, 0
        logger.info('the command output is {}'.format(output))
        logger.info('we do output parsing')
        if check_point1 in output:
            for i in output.split('\n'):
                if 'Device Frame:' in i:
                    device_num += 1
                elif 'Host Frame:' in i:
                    host_num += 1
                else:
                    continue
            if trace == 'None':
                if (device == 'None' and host == 'None') or (device == '0' and host == '0'):
                    # localRecursive On Linux  : 7 Host Frames
                    # localRecursive on Windows: 9 Host Frames, 2 extra frames:
                    #    Host Frame: BaseThreadInitThunk[0x7ff9792a6624] in C:\WINDOWS\System32\KERNEL32.DLL
                    #    Host Frame: RtlUserThreadStart[0x7ff97a79a7d8] in C:\WINDOWS\SYSTEM32\ntdll.dl
                    if device_num == 4 and host_num == 9:
                        return True
                    else:
                        return False
                else:
                    if device_num == int(device) and host_num == int(host):
                        return True
                    else:
                        return False
            else:
                if device_num == 0 and host_num == 0:
                    return True
                else:
                    return False
        else:
            return False

    step1_cmd1 = case_config['CALL_HOST_DEVICE']['STEP1']['CMD1'] % run_path
    step2_cmd1 = case_config['CALL_HOST_DEVICE']['STEP2']['CMD1'] % run_path
    step3_cmd1 = case_config['CALL_HOST_DEVICE']['STEP3']['CMD1'] % run_path
    step4_cmd1 = case_config['CALL_HOST_DEVICE']['STEP4']['CMD1'] % run_path
    step5_cmd1 = case_config['CALL_HOST_DEVICE']['STEP5']['CMD1'] % run_path
    # run step1
    # Task https://nvbugs/4565923 [Require change]Update the checkpoint in case 3021849
    # No need to check the numbers of host frames and device frames, check return code instead. Updated on Apr 18, 2024
    out1 = run_loc_cmd(step1_cmd1)
    save_log(log_name, step1_cmd1, 'run step1 by {}'.format(step1_cmd1), out1['output'])
    if out1.succeeded:
        result['step1--{}'.format(step1_cmd1)] = 'passed'
        logger.info('we run cmd ------ {} successful'.format(step1_cmd1))
    else:
        result['step1--{}'.format(step1_cmd1)] = 'failed'
        logger.info('we run cmd ------ {} fail'.format(step1_cmd1))
    # run step2
    # No need to check the numbers of host frames and device frames, check return code instead. Updated for Step 2 on 2024.06.18
    out2 = run_loc_cmd(step2_cmd1)
    save_log(log_name, step2_cmd1, 'run step1 by {}'.format(step2_cmd1), out2['output'])
    if out2.succeeded:
        result['step2--{}'.format(step2_cmd1)] = 'passed'
        logger.info('we run step2 cmd ------ {} successful'.format(step2_cmd1))
    else:
        result['step2--{}'.format(step2_cmd1)] = 'failed'
        logger.info('we run step2 cmd ------ {} fail'.format(step2_cmd1))
    # run step3
    out3 = run_loc_cmd(step3_cmd1)
    save_log(log_name, step3_cmd1, 'run step3 by {}'.format(step3_cmd1), out3['output'])
    if out3.succeeded and parse_output(out3['output'], device='1', host='1', trace='None'):
        result['step3--{}'.format(step3_cmd1)] = 'passed'
        logger.info('we run step3 cmd ------ {} successful'.format(step3_cmd1))
    else:
        result['step3--{}'.format(step3_cmd1)] = 'failed'
        logger.info('we run step3 cmd ------ {} fail'.format(step3_cmd1))
    # run step4
    out4 = run_loc_cmd(step4_cmd1)
    save_log(log_name, step4_cmd1, 'run step1 by {}'.format(step4_cmd1), out4['output'])
    if out4.succeeded and parse_output(out4['output'], device='2', host='2', trace='None'):
        result['step4--{}'.format(step4_cmd1)] = 'passed'
        logger.info('we run step4 cmd ------ {} successful'.format(step4_cmd1))
    else:
        result['run step4--{}'.format(step4_cmd1)] = 'failed'
        logger.info('we run step4 cmd ------ {} fail'.format(step4_cmd1))
    # run step5
    out5 = run_loc_cmd(step5_cmd1)
    save_log(log_name, step5_cmd1, 'run step1 by {}'.format(step5_cmd1), out5['output'])
    if out2.succeeded and parse_output(out5['output'], device='2', host='2', trace='no'):
        result['run step5--{}'.format(step5_cmd1)] = 'passed'
        logger.info('we run step5 cmd ------ {} successful'.format(step5_cmd1))
        passed += 1
    else:
        result['run step5--{}'.format(step5_cmd1)] = 'failed'
        logger.info('we run step5 cmd ------ {} fail'.format(step5_cmd1))
        failed += 1
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/call_host_device.json' % log_path)
    return result, passed, failed


@print_run_time
def deprecate_cuda_memcheck_2742928():
    if cuda_short_version < '11.5':
        logger.info('We waive this case if cuda version is less than 11.5')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['DEPRECAT_CUDA_MEMCHECK_PATH']
        log_name = case_config['DEPRECAT_CUDA_MEMCHECK']['LOG_NAME']
        mkdir(log_path)
        # run step1
        step1_cmd1 = case_config['DEPRECAT_CUDA_MEMCHECK']['STEP1']['CMD1'] % (sample_0_path, asyncAPI_sln)
        check_point1 = case_config['DEPRECAT_CUDA_MEMCHECK']['CHECK_POINT1']
        check_point2 = case_config['DEPRECAT_CUDA_MEMCHECK']['CHECK_POINT2']
        check_result(step1_cmd1, 'run step1 cmd by {}'.format(step1_cmd1), log_name, result, check_point1, check_point2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/deprecate_cuda_memcheck.json' % log_path)
        return result, passed, failed


@print_run_time
def memcheck_ordered_race_detection():
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2594371
    # Not support vGPU/Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
    if driver_mode in ['TCC']:
        logger.info('we waive this case if the driver mode on Windows is TCC')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['MEMCHECK_ORDERED_RACE_DETECTION_PATH']
        log_name = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT1']
        check_point2 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT2']
        check_point3 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT3']
        check_point4 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT4']
        check_point5 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT5']
        check_point6 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['CHECK_POINT6']
        # prepare sample
        prepare_sanitizer(arch=platform)
        # run step1
        step1_cmd1 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['STEP1']['CMD1'] % run_path
        check_result(step1_cmd1, 'run-step1_cmd1 by %s' % step1_cmd1, log_name, result, check_point1, check_point2, check_point3, check_point4)
        # run step2
        step2_cmd1 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['STEP2']['CMD1'] % run_path
        check_result(step2_cmd1, 'run-step2_cmd1 by %s' % step2_cmd1, log_name, result, check_point2, check_point3, check_point4, check_point5)
        # run step3
        step3_cmd1 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['STEP3']['CMD1'] % run_path
        check_result(step3_cmd1, 'run-step3_cmd1 by %s' % step3_cmd1, log_name, result, check_point2, check_point3, check_point4, check_point5)
        # run step4
        step4_cmd1 = case_config['MEMCHECK_ORDERED_RACE_DETECTION']['STEP4']['CMD1'] % run_path
        if cuda_short_version == '11.8':
            check_result(step4_cmd1, 'run-step4_cmd1 by %s' % step4_cmd1, log_name, result, check_point2, check_point3, check_point5, check_point6)
        else:
            check_result(step4_cmd1, 'run-step4_cmd1 by %s' % step4_cmd1, log_name, result, check_point2, check_point3, check_point5, check_point4)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/memcheck_ordered_race_detection.json' % log_path)
        return result, passed, failed


@print_run_time
def sanitizer_guardword_check_2608117():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_GUARDWORD_CHECK_PATH']
    log_name = case_config['SANITIZER_GUARDWORD_CHECK']['LOG_NAME']
    mkdir(log_path)
    # Step 1: Check strings
    step1_cmd = case_config['SANITIZER_GUARDWORD_CHECK']['STEP1']['CMD']
    step1_checkpoint = case_config['SANITIZER_GUARDWORD_CHECK']['STEP1']['CHECKPOINT']
    check_result(step1_cmd, 'run-step1 by %s' % step1_cmd, log_name, result, step1_checkpoint, flag=2)
    # Step 2: Run RunGuardwordCheck.py
    prepare_sanitizer(arch=platform)
    step2_cmd1 = case_config['SANITIZER_GUARDWORD_CHECK']['STEP2']['CMD1'] % run_path
    step2_cmd2 = case_config['SANITIZER_GUARDWORD_CHECK']['STEP2']['CMD2'] % run_path
    step2_checkpoint = case_config['SANITIZER_GUARDWORD_CHECK']['STEP2']['CHECKPOINT']
    check_result(step2_cmd1, 'run-step2-sanitizer-dvs-package by %s' % step2_cmd1, log_name, result, step2_checkpoint)
    check_result(step2_cmd2, 'run-step2-sanitizer-install-path by %s' % step2_cmd2, log_name, result, step2_checkpoint)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_guardword_check.json' % log_path)
    return result, passed, failed


@print_run_time
def launch_without_suffix_specified_explicitly_2680933():
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    # Graphic Samples can not support Windows TCC
    if driver_mode in ['TCC', 'MCDM']:
        logger.info('we waive this case if the driver mode on Windows is TCC or MCDM.')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY_PATH']
        log_name = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['LOG_NAME']
        mkdir(log_path)
        # build sample
        prepare_cmd = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['PREPARE']['CMD'] % (sample_0_path + '/../', oceanFFT_sln)
        prepare_checkpoint = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['PREPARE']['CHECKPOINT']
        check_result(prepare_cmd, 'build oceanFFT by %s' % prepare_cmd, log_name, result, prepare_checkpoint)

        def thread1():
            # run step1 cmd1
            step1_cmd1 = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['STEP1']['CMD1']
            step1_checkpoint = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['STEP1']['CHECKPOINT']
            check_result(step1_cmd1, 'launch compute-sanitizer occeanFFT.exe by %s' % step1_cmd1, log_name, result, step1_checkpoint, flag=1)
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '%s/launch_without_suffix_specified_explicitly.json' % log_path)
            return result, passed, failed

        def thread2():
            # run step1 cmd2
            step1_cmd2 = case_config['LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY']['STEP1']['CMD2']
            check_result(step1_cmd2, 'kill oceanFFT.exe by %s' % step1_cmd2, log_name, result)

        t1 = threading.Thread(target=thread1)
        t2 = threading.Thread(target=thread2)
        t1.start()
        time.sleep(30)   # Wait for 30 seconds, then start thread2() to kill the oceanFFT.exe process
        t2.start()


@print_run_time
def support_aligned_device_malloc_3071342():
    if cuda_short_version >= '12.0':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SUPPORT_ALIGNED_DEVICE_MALLOC_PATH']
        log_name = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['LOG_NAME']
        mkdir(log_path)
        # prepare the file
        cmd1 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['PREPARE']['CMD1'] % (user, password, base_url)
        check_result(cmd1, 'download the file by ----%s' % cmd1, log_name, result)
        if is_empty_file('%s/../P1072_T3071342/test.cu' % tools_home):
            logger.info('we download the file fail, exiting......')
        else:
            cmd2 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['PREPARE']['CMD2'] % int(SM * 10)
            check_result(cmd2, 'build the sample by ----%s' % cmd2, log_name, result)
        # run the step1
        check_point1 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['CHECK_POINT1']
        check_point2 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['CHECK_POINT2']
        step1_cmd1 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['STEP1']['CMD1']
        step1_cmd2 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['STEP1']['CMD2']
        step1_cmd3 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['STEP1']['CMD3']
        step1_cmd4 = case_config['SUPPORT_ALIGNED_DEVICE_MALLOC']['STEP1']['CMD4']
        check_result(step1_cmd1, 'run step1------%s' % step1_cmd1, log_name, result, check_point1)
        check_result(step1_cmd2, 'run step1------%s' % step1_cmd2, log_name, result, check_point1)
        check_result(step1_cmd3, 'run step1------%s' % step1_cmd3, log_name, result, check_point1)
        check_result(step1_cmd4, 'run step1------%s' % step1_cmd4, log_name, result, check_point2)

        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/support_aligned_device_malloc.json' % log_path)
        return result, passed, failed
    else:
        logger.info('we do not support, because the cuda version is less than 12.0')


@print_run_time
def sanitizer_support_LDSM_3079874():
    if SM >= 7.3 and cuda_short_version >= '12.0':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SUPPORT_LDSM_PATH']
        log_name = case_config['SUPPORT_LDSM']['LOG_NAME']
        mkdir(log_path)
        # prepare the file
        cmd1 = case_config['SUPPORT_LDSM']['PREPARE']['CMD1'] % (user, password, base_url)
        check_result(cmd1, 'download the file by ----%s' % cmd1, log_name, result)
        if is_empty_file('%s/../P1072_T3079874/CuLdSm.cu' % tools_home):
            logger.info('we download the file fail, exiting......')
        else:
            cmd2 = case_config['SUPPORT_LDSM']['PREPARE']['CMD2'] % int(SM * 10)
            check_result(cmd2, 'build the sample by ----%s' % cmd2, log_name, result)
        # run the step
        setp1_check_point1 = case_config['SUPPORT_LDSM']['STEP1']['CHECK_POINT1']
        setp1_check_point2 = case_config['SUPPORT_LDSM']['STEP1']['CHECK_POINT2']
        setep2_check_point1 = case_config['SUPPORT_LDSM']['STEP2']['CHECK_POINT1']
        step1_cmd1 = case_config['SUPPORT_LDSM']['STEP1']['CMD1']
        step2_cmd1 = case_config['SUPPORT_LDSM']['STEP2']['CMD1']
        check_result(step1_cmd1, 'run step1------%s' % step1_cmd1, log_name, result, setp1_check_point1, setp1_check_point2)
        check_result(step2_cmd1, 'run step1------%s' % step2_cmd1, log_name, result, setep2_check_point1)

        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/sanitizer_support_LDSM.json' % log_path)
        return result, passed, failed
    else:
        logger.info('we do not support, because the cuda version is less than 12.0 or SM<7.3')


def close_window(sample):
    sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'],
                   'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                   'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                   'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                   'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                   'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                   'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                   }
    myWM = WindowManager()
    title_name = sample_dict[sample][0]
    class_name = sample_dict[sample][1]
    hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
    print("HWND is %s" % hwnd)
    win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
    print("Closed window by HWND %s" % hwnd)


def run_optix(cmd, log_name, result, tools):
    if tools == 'trace':
        check_result(cmd, 'run_cmd by %s' % cmd, log_name, result)
    else:
        check_result(cmd, 'run_cmd by %s' % cmd, log_name, result, 'FAILED', 'Error', flag=2)


def multi_process(cmd_list, log_name, result, tools, sample):
    """
    Based on multiprocessing Pool,  result recorded as {cmd:passed/failed}
    """
    from multiprocessing import Pool, Manager, Process
    manager = Manager()
    mp_result = manager.dict()
    pool = Pool(2)

    try:
        logging.debug('start run %d' % os.getpid())
        pool.apply_async(run_optix, args=(cmd_list[0], log_name, mp_result, tools))
        # pool.apply_async(close_sample, args=(sample, mp_result))
        pool.apply_async(close_window, args=(sample))
        pool.close()
        pool.join()
    except:
        logging.debug('Process terminating')
        pool.terminate()
        pool.join()
        time.sleep(1)
        raise


def close_sample(sample, result):
    """
    Wait for 30 seconds, find the process of the running graphics samples, then stop the process on Windows
    """
    cmd = "Start-Sleep -Seconds 30; Stop-Process -name %s -Force" % sample
    out = run_loc_cmd(cmd)
    print(out)
    print(result)
    logger.info('we had run the command "%s"' % cmd)


@print_run_time
def graphic_sample_coverage_3084216():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0]:
        driver_mode = common_get_driver_mode()
        logger.info("the driver mode is %s." % driver_mode)
        if driver_mode in ['TCC', 'MCDM']:
            logger.info('not support 3084216-graphic_sample_coverage with driver mode TCC or MCDM on Windows.')
        else:
            GRAPHICS_SAMPLES_EXECUTION_DURATION = 15  # 15 seconds
            result = {}
            passed, failed = 0, 0
            log_path = case_config['global']['env']['GRAPHIC_SAMPLE_COVERAGE_PATH']
            log_name = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['LOG_NAME']
            mkdir(log_path)
            # get samples list
            sample_list_str = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['SAMPLE_LIST']
            print("original sample list string is %s" % sample_list_str)
            sample_list_temp = sample_list_str.split(',')
            print("temporary sample list is %s" % sample_list_temp)
            graphic_sample_list = sample_list_temp
            # DirectX interop D3D11/D3D12 support since CUDA 12.3. Updated on May 8, 2023
            if cuda_short_version < '12.3':
                graphic_sample_list.remove('simpleD3D11')
                graphic_sample_list.remove('simpleD3D12')
            print("Real graphic sample list is %s" % graphic_sample_list)

            sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'],
                           'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                           'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                           'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                           'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                           'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                           'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                           }
            check_point1 = "ERROR SUMMARY: 0 errors"
            check_point2 = "ERROR SUMMARY: 0 errors"
            check_point3 = "ERROR SUMMARY: 0 errors"
            check_point4 = "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

            def run_internal_cmd(step_cmd, check_point):
                # Class WindowManager is defined in common_utils.py
                myWM = WindowManager()

                def thread1():
                    check_result(step_cmd, 'run_cmd by %s' % step_cmd, log_name, result, check_point)

                def thread2():
                    title_name = sample_dict[sample][0]
                    class_name = sample_dict[sample][1]
                    hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                    print("HWND is %s" % hwnd)
                    win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                    print("Closed window by HWND %s" % hwnd)

                t1 = threading.Thread(target=thread1)
                t2 = threading.Thread(target=thread2)
                t1.start()
                time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)
                t2.start()
                t1.join()
                t2.join()
            # Traversal sample list
            for check_type in ['leak-check', 'initcheck', 'synccheck', 'racecheck']:
                for sample in graphic_sample_list:
                    if os.path.exists('%s/%s.exe' % (sample_bin_path, sample)) or os.path.exists('%s/%s.exe' % (optix_bin_path, sample)):
                        print(">>>>>>>>>>>>>>>>>>>>>Running sample %s for %s<<<<<<<<<<<<<<<<<<<<<<<<<<<" % (sample, check_type))
                        if 'optix' in sample:
                            # 2.1 run with add "--check-optix-leaks yes" in memcheck since CUDA 11.7. Updated on May 8, 2023
                            if cuda_short_version >= '11.7':
                                step1_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP1']['CMD1'] % (optix_bin_path, sample)
                            # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3084216 run with "--check-optix" and "--check-optix-leaks" since CUDA12.3 as options have “no” as default, no longer require yes/no  DTCS-1290
                            if cuda_short_version >= '12.3':
                                step1_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP1']['CMD1_1'] % (optix_bin_path, sample)
                            # 2.2 run with "--check-optix=yes"  in initcheck  since CUDA 12.2. Updated on May 8, 2023
                            if cuda_short_version >= '12.2':
                                step2_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP2']['CMD1'] % (optix_bin_path, sample)
                            # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3084216 run with "--check-optix" and "--check-optix-leaks" since CUDA12.3 as options have “no” as default, no longer require yes/no  DTCS-1290
                            if cuda_short_version >= '12.3':
                                step2_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP2']['CMD1_1'] % (optix_bin_path, sample)
                            step3_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP3']['CMD'] % (optix_bin_path, sample)
                            step4_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP4']['CMD'] % (optix_bin_path, sample)
                        else:
                            step1_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP1']['CMD'] % (sample_bin_path, sample)
                            step2_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP2']['CMD'] % (sample_bin_path, sample)
                            # Step3~4 2834485 WNF (if meet error with oceanFFT for racecheck/synccheck add --qatest as WAR //
                            # The sample oceanFFT does not finish by default causing sanitizer to run out of memory.
                            # Using -qatest makes the app finish in a finite amount of time)
                            # Updated on May 8, 2023
                            if "oceanFFT" == sample:
                                step3_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP3']['CMD1'] % (sample_bin_path, sample)
                                step4_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP4']['CMD1'] % (sample_bin_path, sample)
                            else:
                                step3_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP3']['CMD'] % (sample_bin_path, sample)
                                step4_cmd = case_config['GRAPHIC_SAMPLE_COVERAGE_3084216']['STEP4']['CMD'] % (sample_bin_path, sample)

                        if check_type == 'leak-check':
                            run_internal_cmd(step1_cmd, check_point1)
                            time.sleep(3)
                        elif check_type == 'initcheck':
                            run_internal_cmd(step2_cmd, check_point2)
                            time.sleep(3)
                        elif check_type == 'synccheck':
                            run_internal_cmd(step3_cmd, check_point3)
                            time.sleep(3)
                        else:
                            run_internal_cmd(step4_cmd, check_point4)
                            time.sleep(3)
                    else:
                        logger.warning('the sample --- {} does not exist'.format(sample))
                        result['run graphic sample ---- {}'.format(sample)] = 'failed'
                    # Pay attention: Have to wait for several seconds, important !!!
            time.sleep(3)
            # calculate result
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '%s/graphic_sample_coverage_3084216.json' % log_path)
            return result, passed, failed
    else:
        logger.info('not support this case in tesla GPU, such as:Ampere/hopper/blackwell')


@print_run_time
def initcheck_track_unused_memory_3090527():
    if cuda_short_version >= '12.0':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['INITCHECK_TRACK_UNUSED_MEMORY_PATH']
        log_name = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['LOG_NAME']
        mkdir(log_path)
        # prepare the file
        cmd1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['PREPARE']['CMD1'] % (user, password, base_url)
        check_result(cmd1, 'download the file by ----%s' % cmd1, log_name, result)
        if is_empty_file('%s/../3090527/unused.cu' % tools_home):
            logger.info('we download the file fail, exiting......')
        else:
            cmd2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['PREPARE']['CMD2']
            check_result(cmd2, 'build the sample by ----%s' % cmd2, log_name, result)
        # get commands and checkpoints
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3090527 --track-unused-memory and --xml have “no” as default, no longer require yes/no  since 12.3 DTCS-1290
        if cuda_short_version >= '12.3':
            step1_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP1']['CMD_12_3']
            step2_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP2']['CMD_12_3']
            step3_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP3']['CMD_12_3']
            step4_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP4']['CMD_12_3']
            step5_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP5']['CMD_12_3']
            step6_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CMD_12_3']
        else:
            step1_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP1']['CMD']
            step2_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP2']['CMD']
            step3_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP3']['CMD']
            step4_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP4']['CMD']
            step5_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP5']['CMD']
            step6_cmd = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CMD']
        step1_checkpoint1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP1']['CHECKPOINT1']
        step1_checkpoint2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP1']['CHECKPOINT2']
        step2_checkpoint1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP2']['CHECKPOINT1']
        step2_checkpoint2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP2']['CHECKPOINT2']
        step3_checkpoint1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP3']['CHECKPOINT1']
        step3_checkpoint2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP3']['CHECKPOINT2']
        step4_checkpoint1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP4']['CHECKPOINT1']
        step4_checkpoint2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP4']['CHECKPOINT2']
        step5_checkpoint = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP5']['CHECKPOINT']
        step6_checkpoint1 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CHECKPOINT1']
        step6_checkpoint2 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CHECKPOINT2']
        step6_checkpoint3 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CHECKPOINT3']
        step6_checkpoint4 = case_config['INITCHECK_TRACK_UNUSED_MEMORY']['STEP6']['CHECKPOINT4']
        # run
        check_result(step1_cmd, 'run step1 -- %s' % step1_cmd, log_name, result, step1_checkpoint1, step1_checkpoint2)
        check_result(step2_cmd, 'run step2 -- %s' % step2_cmd, log_name, result, step2_checkpoint1, step2_checkpoint2)
        check_result(step3_cmd, 'run step3 -- %s' % step3_cmd, log_name, result, step3_checkpoint1, step3_checkpoint2)
        check_result(step4_cmd, 'run step4 -- %s' % step4_cmd, log_name, result, step4_checkpoint1, step4_checkpoint2)
        check_result(step5_cmd, 'run step5 -- %s' % step5_cmd, log_name, result, step5_checkpoint, flag=1)
        check_result(step6_cmd, 'run step6 -- %s' % step6_cmd, log_name, result, step6_checkpoint1, step6_checkpoint2)
        # check the out.xml in step 6
        list1, list2 = [], []
        with open('%s/3090527/out.xml' % tools_home, 'r') as f:
            for line in f.readlines():
                print(line)
                list1.append(line.split('>')[1].split('<')[0])
        list2 = [i for i in set(list1) if i not in ['/n', 'Precise', 'Api', '\\n']]
        print("list2 is %s" % str(list2))
        if step6_checkpoint3 in list2 and step6_checkpoint4 in list2:
            result["check %s, and %s in the out.xml in step 6" % (step6_checkpoint3, step6_checkpoint4)] = "passed"
        else:
            result["check %s, and %s in the out.xml in step 6" % (step6_checkpoint3, step6_checkpoint4)] = "failed"
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/initcheck_track_unused_memory.json' % log_path)
        return result, passed, failed
    else:
        logger.info('we do not support, because the cuda version is less than 12.0')


@print_run_time
def sanitizer_steam_capture_3156524():
    print("The sm of GPU is %s" % SM)
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_STEAM_CAPTURE_PATH']
    log_name = case_config['SANITIZER_STEAM_CAPTURE']['LOG_NAME']
    mkdir(log_path)
    check_point1 = case_config['SANITIZER_STEAM_CAPTURE']['CHECK_POINT1']
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_STEAM_CAPTURE']['PREPARE']['CMD1'].format(user, password, base_url)
    prepare_cmd2 = case_config['SANITIZER_STEAM_CAPTURE']['PREPARE']['CMD2'].format(int(SM * 10))
    check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
    check_result(prepare_cmd2, 'prepare sample by {}'.format(prepare_cmd2), log_name, result)
    # run sanitizer
    step1_cmd1 = case_config['SANITIZER_STEAM_CAPTURE']['STEP1']['CMD1']
    check_result(step1_cmd1, 'run sanitizer with initcheck option by ---{}'.format(step1_cmd1), log_name, result, check_point1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/sanitizer_steam_capture.json'.format(log_path))
    return result, passed, failed


@print_run_time
def sanitizer_racecheck_memcpy_async_3158301():
    if cuda_short_version < '12.0':
        logger.info(' we only support this case since cuda 12.0')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH']
        log_name = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['CHECK_POINT3']
        # prepare file
        prepare_sanitizer()
        prepare_cmd1 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['PREPARE']['CMD1'].format(run_path)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        # run sanitizer racecheck
        step1_cmd1 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['STEP1']['CMD1']
        step1_cmd2 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['STEP1']['CMD2']
        check_result(step1_cmd1, 'run sanitizer with enable memcpy_async + 1 by ---{}'.format(step1_cmd1), log_name, result, check_point1)
        check_result(step1_cmd2, 'run sanitizer with enable memcpy_async + 2 by ---{}'.format(step1_cmd2), log_name, result, check_point2)
        # run step2 disable memcpy_async
        step2_cmd1 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['STEP2']['CMD1']
        step2_cmd2 = case_config['SANITIZER_RACECHECK_MEMCPY_ASYNC']['STEP2']['CMD2']
        check_result(step2_cmd1, 'run sanitizer with enable memcpy_async + 1 by ---{}'.format(step2_cmd1), log_name, result, check_point3)
        check_result(step2_cmd2, 'run sanitizer with enable memcpy_async + 2 by ---{}'.format(step2_cmd2), log_name, result, check_point3)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_racecheck_memcpy_async.json'.format(log_path))
        return result, passed, failed


@print_run_time
def sanitizer_check_read_access_3178884():
    if cuda_short_version < '12.1':
        logger.info('not support this case if cuda version less than 12.1')
    else:
        # Skipped as TCC mode driver does not support MallocAsync and MemPool features used in this sample. Updated on 2024.06.18
        driver_mode = common_get_driver_mode()
        logger.info("the driver mode is %s." % driver_mode)
        if driver_mode in ['TCC']:
            logger.info('not support this case 3178884-sanitizer_check_read_access can be skipped with driver mode TCC or MCDM on Windows.')
            return
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_CHECK_READ_ACCESS_PATH']
        log_name = case_config['SANITIZER_CHECK_READ_ACCESS']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['SANITIZER_CHECK_READ_ACCESS']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_CHECK_READ_ACCESS']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_CHECK_READ_ACCESS']['CHECK_POINT3']
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_CHECK_READ_ACCESS']['PREPARE']['CMD1'] % (user, password, base_url)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        # run sanitizer racecheck
        step1_cmd1 = case_config['SANITIZER_CHECK_READ_ACCESS']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run sanitizer with check_cache_control + no by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2, check_point3)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_check_read_access.json'.format(log_path))
        return result, passed, failed


@print_run_time
def sanitizer_support_unicode_file_3183138():
    if cuda_short_version < '12.1':
        logger.info('not support this case if cuda version less than 12.1')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_SUPPORT_UNICODE_FILE_PATH']
        log_name = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['CHECK_POINT3']
        language_dict = {'Chinese': '测试', 'Thailand': 'ผผู้ทดสอบ', 'Japanese': 'にほ', 'Korea': '문장가', 'Germany': 'prüfen', 'France': 'Logiciel'}
        # Updated for task https://nvbugs/4297883 [Require Change] Test Procedure on 3183138/Sanitizer supports Unicode files and paths
        # Dec 19, 2023
        for key, value in language_dict.items():
            # prepare file
            prepare_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['PREPARE']['CMD1'] % (value, value)
            prepare_cmd2 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['PREPARE']['CMD2'] % (value, user, password, base_url)
            prepare_cmd3 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['PREPARE']['CMD3'] % value
            check_result(prepare_cmd1, 'prepare sample under {} folder {}'.format(key, prepare_cmd1), log_name, result)
            check_result(prepare_cmd2, 'build sample under {} folder by {}'.format(key, prepare_cmd2), log_name, result)
            check_result(prepare_cmd3, 'prepare libsanitizer-collection file under {} folder by {}'.format(key, prepare_cmd3), log_name, result)
            # run sanitizer memcheck
            step1_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP1']['CMD1'] % value
            check_result(step1_cmd1, 'run sanitizer with vector_atomics under {} folder by ---{}'.format(key, step1_cmd1), log_name, result, check_point1)
            step2_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP2']['CMD1'] % (value, value)
            step2_cmd2 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP2']['CMD2'] % (value, value)
            check_result(step2_cmd1, 'run sanitizer with save log under {} folder by ---{}'.format(key, step2_cmd1), log_name, result, check_point1)
            check_result(step2_cmd2, 'run sanitizer with read save_log under {} folder by ---{}'.format(key, step2_cmd2), log_name, result, check_point1)
            step3_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP3']['CMD1'] % (value, value, value)
            check_result(step3_cmd1, 'run sanitizer with log_file under {} folder by ---{}'.format(key, step3_cmd1), log_name, result, check_point3)
            # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3183138 --xml has “no” as default, no longer require yes/no  since 12.3 DTCS-1290
            if cuda_short_version >= '12.3':
                step4_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP4']['CMD_12_3'] % (value, value, value)
            else:
                step4_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP4']['CMD1'] % (value, value, value)
            check_result(step4_cmd1, 'run sanitizer with save xml file under {} folder by ---{}'.format(key, step4_cmd1), log_name, result, check_point2)
            # Step 5 has a open bug on Windows: https://nvbugs/3907733 [CudaTools]sanitizer not work if the injection path including Unicode characters on Windows
            step5_cmd1 = case_config['SANITIZER_SUPPORT_UNICODE_FILE']['STEP5']['CMD1'] % (value, value)
            check_result(step5_cmd1, 'run sanitizer with injection_path under {} folder by ---{}'.format(key, step5_cmd1), log_name, result, check_point3)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_support_unicode_file.json'.format(log_path))
        return result, passed, failed


@print_run_time
def sanitizer_memcheck_vector_atomics_3192559():
    if cuda_short_version < '12.1' or SM < 9.0:
        logger.info('not support this case if cuda version less than 12.1 or sm less than 9.0')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH']
        log_name = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['LOG_NAME']
        mkdir(log_path)
        # Some changes of the output since CUDA 12.3
        if cuda_short_version >= '12.3':
            check_point1 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT1_12_3']
            check_point2 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT2_12_3']
            check_point3 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT3_12_3']
            check_point4 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT4_12_3']
            check_point5 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT5_12_3']
        else:
            check_point1 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT1']
            check_point2 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT2']
            check_point3 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT3']
            check_point4 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT4']
            check_point5 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['CHECK_POINT5']
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['PREPARE']['CMD1'] % (user, password, base_url)
        prepare_cmd2 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['PREPARE']['CMD2'] % int(SM * 10)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        # run sanitizer memcheck
        step1_cmd1 = case_config['SANITIZER_MEMCHECK_VECTOR_ATOMICS']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run sanitizer with vector_atomics by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_memcheck_vector_atomics.json'.format(log_path))
        return result, passed, failed


@print_run_time
def sanitizer_memcheck_cnp_support_3192566():
    if SM < 9.0:
        logger.info('not support this case if sm less than 9.0 ')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_MEMCHECK_CNP_SUPPORT_PATH']
        log_name = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['LOG_NAME']
        mkdir(log_path)
        # Some changes of the output since CUDA 12.3
        if cuda_short_version >= '12.3':
            check_point1 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT1_12_3']
            check_point2 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT2_12_3']
            check_point3 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT3_12_3']
        else:
            check_point1 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT1']
            check_point2 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT2']
            check_point3 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['CHECK_POINT3']
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['PREPARE']['CMD1'] % (user, password, base_url)
        prepare_cmd2 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['PREPARE']['CMD2'] % int(SM * 10)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        # run sanitizer memcheck
        step1_cmd1 = case_config['SANITIZER_MEMCHECK_CNP_SUPPORT']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run sanitizer with cnp_support by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2, check_point3)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_memcheck_cnp_support.json'.format(log_path))
        return result, passed, failed


def sanitizer_quite_cuda_init_3207945():
    if cuda_short_version < '12.1':
        logger.info('not support this case if cuda version less than 12.1 ')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_QUITE_CUDA_INIT_PATH']
        log_name = case_config['SANITIZER_QUITE_CUDA_INIT']['LOG_NAME']
        mkdir(log_path)
        check_point1 = case_config['SANITIZER_QUITE_CUDA_INIT']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_QUITE_CUDA_INIT']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_QUITE_CUDA_INIT']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_QUITE_CUDA_INIT']['CHECK_POINT4']
        check_point5 = case_config['SANITIZER_QUITE_CUDA_INIT']['CHECK_POINT5']
        try:
            # prepare file
            prepare_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['PREPARE']['CMD1'] % (user, password, base_url)
            prepare_cmd2 = case_config['SANITIZER_QUITE_CUDA_INIT']['PREPARE']['CMD2'] % (user, password, base_url)
            check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
            check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
            # run sanitizer memcheck
            step1_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP1']['CMD1']
            check_result(step1_cmd1, 'step1 run sanitizer with no quite option by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2)
            step2_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP2']['CMD1']
            check_result(step2_cmd1, 'step2 run sanitizer with quite option by ---{}'.format(step2_cmd1), log_name, result, check_point1, check_point2, flag=2)
            step3_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP3']['CMD1']
            check_result(step3_cmd1, 'step3 run sanitizer with attach by ---{}'.format(step3_cmd1), log_name, result, check_point3, check_point4, flag=1)
            step4_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP4']['CMD1']
            check_result(step4_cmd1, 'step4 run sanitizer with attach quite by ---{}'.format(step4_cmd1), log_name, result, check_point4, flag=1)
            step5_cmd1 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP5']['CMD1']
            check_result(step5_cmd1, 'step5_1 run sanitizer with helloword by ---{}'.format(step5_cmd1), log_name, result, 'Error', check_point5, flag=1)
            step5_cmd2 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP5']['CMD2']
            check_result(step5_cmd2, 'step5_2 run sanitizer with helloworld + cuda init by ---{}'.format(step5_cmd2), log_name, result, 'Error', flag=2)
            step5_cmd3 = case_config['SANITIZER_QUITE_CUDA_INIT']['STEP5']['CMD3']
            check_result(step5_cmd3, 'step5_3 run sanitizer with helloworld + cuda init + quite by ---{}'.format(step5_cmd3), log_name, result)
        except Exception as e:
            logger.error(f"has some error due to {str(e)}")
            result['some steps run fail, please check it'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_quite_cuda_init.json'.format(log_path))
        return result, passed, failed


def sanitizer_attach(process, log_name, result):
    check_point1 = case_config['SANITIZER_LAUNCH_ATTACH']['CHECK_POINT1']
    check_point2 = case_config['SANITIZER_LAUNCH_ATTACH']['CHECK_POINT2']
    step_cmd2 = case_config['SANITIZER_LAUNCH_ATTACH']['STEP']['CMD2'] % process
    rs = run_loc_cmd(step_cmd2).output.replace(' ', '')
    pid = re.findall("exe(.+?)(Console|RDP-Tcp)", rs)[0][0]
    step_cmd3 = case_config['SANITIZER_LAUNCH_ATTACH']['STEP']['CMD3'] % pid
    check_result(step_cmd3, 'run sanitizer attach by ---{}'.format(step_cmd3), log_name, result, check_point1, check_point2)
    return result


@print_run_time
def sanitizer_launch_attach_3207946():
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['SANITIZER_LAUNCH_ATTACH_PATH']
    log_name = case_config['SANITIZER_LAUNCH_ATTACH']['LOG_NAME']
    mkdir(log_path)
    return_process = Manager().dict()
    # prepare file
    prepare_cmd1 = case_config['SANITIZER_LAUNCH_ATTACH']['PREPARE']['CMD1'] % (sample_0_path, vectoradd_sln)
    check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
    # run sanitizer
    step_cmd1 = case_config['SANITIZER_LAUNCH_ATTACH']['STEP']['CMD1']
    step1 = Process(target=run_loc_cmd, args=(step_cmd1,))
    step1.start()
    time.sleep(2)
    step3 = Process(target=sanitizer_attach, args=('vectorAdd', log_name, return_process))
    step3.start()
    step3.join()
    result.update(return_process)
    # If vectorAdd.exe is still running, we need to mark the case FAILED and kill vectorAdd.exe process to avoid HANG problem.
    # Updated by Alex Li on Mar 20, 2023
    if process_is_running('vectorAdd'):
        print('====== Process vectorAdd.exe is still running, we need to mark the case FAILED.')
        result["Check the vectorAdd process is not running"] = 'failed'
        print('====== Process vectorAdd.exe is still running, we need to kill vectorAdd.exe process to avoid HANG problem.')
        kill_process_by_name('vectorAdd')
        print('====== Kill vectorAdd.exe process successfully.')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/sanitizer_launch_attach.json'.format(log_path))
    return result, passed, failed


@print_run_time
def sanitizer_capture_cuda_barrier_3222551():
    if cuda_short_version < '12.1' or SM < 9.0:
        logger.info('not support this case if cuda version less than 12.1 or sm less than 9.0')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_CAPTURE_CUDA_BARRIER_PATH']
        log_name = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['LOG_NAME']
        mkdir(log_path)
        check_point = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['CHECK_POINT']
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['PREPARE']['CMD1'] % (user, password, base_url)
        if cuda_short_version < '13.0':
            prepare_cmd2 = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['PREPARE']['CMD2'] % ('', int(SM * 10))
        else:
            prepare_cmd2 = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['PREPARE']['CMD2'] % ('-std=c++17', int(SM * 10))
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        # run sanitizer memcheck
        step_cmd1 = case_config['SANITIZER_CAPTURE_CUDA_BARRIER']['STEP']['CMD1']
        check_result(step_cmd1, 'run sanitizer with cuda barrier  by ---{}'.format(step_cmd1), log_name, result, check_point)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_capture_cuda_barrier.json'.format(log_path))
        return result, passed, failed


@print_run_time
def initcheck_optix_samples_coverage_3363004():
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    if driver_mode in ['TCC', 'MCDM']:
        logger.info('we do not support 3363004-initcheck_optix_samples_coverage with driver mode TCC or MCDM on Windows.')
    else:
        GRAPHICS_SAMPLES_EXECUTION_DURATION = 30  # 30 seconds
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['INITCHECK_OPTIX_SAMPLES_COVERAGE_PATH']
        log_name = case_config['INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004']['LOG_NAME']
        mkdir(log_path)
        # get samples list
        sample_list_str = case_config['INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004']['SAMPLE_LIST']
        print("original sample list string is %s" % sample_list_str)
        sample_list_temp = sample_list_str.split(',')
        print("temporary sample list is %s" % sample_list_temp)
        graphic_sample_list = sample_list_temp
        print("Real optix sample list is %s" % graphic_sample_list)

        sample_dict = {'optixVolumeViewer': ['optixVolumeViewer', 'GLFW30'],
                       'optixWhitted': ['optixWhitted', 'GLFW30'],
                       'optixHair': ['optixHair', 'GLFW30'],
                       'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                       'optixCallablePrograms': ['optixCallablePrograms', 'GLFW30'],
                       }
        check_point1 = "ERROR SUMMARY: 0 errors"

        def run_internal_cmd(step_cmd, check_point):
            # Class WindowManager is defined in common_utils.py
            myWM = WindowManager()

            def thread1():
                check_result(step_cmd, 'run_cmd by %s' % step_cmd, log_name, result, check_point)

            def thread2():
                title_name = sample_dict[sample][0]
                class_name = sample_dict[sample][1]
                hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                print("HWND is %s" % hwnd)
                win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                print("Closed window by HWND %s" % hwnd)

            t1 = threading.Thread(target=thread1)
            t2 = threading.Thread(target=thread2)
            t1.start()
            time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)
            t2.start()
            t1.join()
            t2.join()

        # Traversal sample list
        for sample in graphic_sample_list:
            if os.path.exists('%s/%s.exe' % (sample_bin_path, sample)) or os.path.exists('%s/%s.exe' % (optix_bin_path, sample)):
                print(">>>>>>>>>>>>>>>>>>>>>Running optix sample %s<<<<<<<<<<<<<<<<<<<<<<<<<<<" % (sample))
                if 'optix' in sample:
                    step1_cmd = case_config['INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004']['STEP1']['CMD'] % (optix_bin_path, sample)
                    # 2.2 run with "--check-optix=yes"  in initcheck  since CUDA 12.2. Updated on May 8, 2023
                    if cuda_short_version >= '12.2':
                        step1_cmd = case_config['INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004']['STEP1']['CMD_12_2'] % (optix_bin_path, sample)
                    # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3363004 run with "--check-optix" and "--check-optix-leaks" since CUDA12.3 as options have “no” as default, no longer require yes/no  DTCS-1290
                    if cuda_short_version >= '12.3':
                        step1_cmd = case_config['INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004']['STEP1']['CMD_12_3'] % (optix_bin_path, sample)

                run_internal_cmd(step1_cmd, check_point1)
                time.sleep(3)
            else:
                logger.warning('the optix sample --- {} does not exist'.format(sample))
                result['run optix sample ---- {}'.format(sample)] = 'failed'
            # Pay attention: Have to wait for several seconds, important !!!
        time.sleep(3)
        print("There is an open bug for optixHair: https://nvbugs/4084779 [cuda12.2][Optix7.7]initcheck issue with optixHair")
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/initcheck_optix_samples_coverage_3363004.json' % log_path)
        return result, passed, failed


@print_run_time
def option_suppressions_3568568():
    if cuda_short_version < '12.3':
        logger.info('not support this case if cuda version less than 12.3')
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['OPTION_SUPPRESSIONS_PATH']
        log_name = case_config['OPTION_SUPPRESSIONS_3568568']['LOG_NAME']
        mkdir(log_path)
        # Prepare the apps
        print("Running Prepare: " + ">" * 128)
        check_point1 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CHECK_POINT1']
        check_point2_1 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CHECK_POINT2_1']
        check_point2_2 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CHECK_POINT2_2']
        check_point3 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CHECK_POINT3']
        check_point4 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CHECK_POINT4']
        # run
        prepare_cmd1 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CMD1']
        check_result(prepare_cmd1, 'prepare sample by CMD1 {}'.format(prepare_cmd1), log_name, result, check_point1)
        prepare_cmd2 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CMD2']
        check_result(prepare_cmd2, 'prepare sample by CMD2 {}'.format(prepare_cmd2), log_name, result, check_point2_1, check_point2_2)
        prepare_cmd3 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CMD3']
        check_result(prepare_cmd3, 'prepare sample by CMD3 {}'.format(prepare_cmd3), log_name, result, check_point3)
        prepare_cmd4 = case_config['OPTION_SUPPRESSIONS_3568568']['PREPARE']['CMD4']
        check_result(prepare_cmd4, 'prepare sample by CMD4 {}'.format(prepare_cmd4), log_name, result, check_point4)
        # Step 1
        print("Running Step 1: " + ">" * 128)
        step1_cmd1 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CMD1']
        step1_cmd2 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CMD2']
        step1_cmd3 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CMD3']
        step1_check_point1 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CHECK_POINT1']
        step1_check_point2 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CHECK_POINT2']
        step1_check_point3 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP1']['CHECK_POINT3']
        check_result(step1_cmd1, 'Step1.1: run sanitizer with suppressions + suppressions_demo.exe by --- {}'.format(step1_cmd1), log_name, result, step1_check_point1)
        check_result(step1_cmd2, 'Step1.2: run sanitizer with suppressions + suppressions_demo.exe by --- {}'.format(step1_cmd2), log_name, result, step1_check_point2)
        check_result(step1_cmd3, 'Step1.3: run sanitizer with suppressions + suppressions_demo.exe by --- {}'.format(step1_cmd3), log_name, result, step1_check_point3)
        # Step 2
        print("Running Step 2: " + ">" * 128)
        step2_cmd1 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP2']['CMD1']
        step2_cmd2 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP2']['CMD2']
        step2_check_point1 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP2']['CHECK_POINT1']
        step2_check_point2 = case_config['OPTION_SUPPRESSIONS_3568568']['STEP2']['CHECK_POINT2']
        check_result(step2_cmd1, 'Step2.1: run sanitizer with suppressionsn + suppressions_initcheck_demo.exe by --- {}'.format(step2_cmd1), log_name, result,
                     step2_check_point1)
        check_result(step2_cmd2, 'Step2.2: run sanitizer with suppressions + suppressions_initcheck_demo.exe by --- {}'.format(step2_cmd2), log_name, result,
                     step2_check_point2)

        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/option_suppressions_3568568.json'.format(log_path))
        return result, passed, failed


@print_run_time
def sanitizer_rcca_3700598():
    if cuda_short_version >= '12.5':
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3700598')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_3700598_RCCA_PATH']
        log_name = case_config['SANITIZER_3700598_RCCA']['LOG_NAME']
        mkdir(log_path)
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_3700598_RCCA']['PREPARE']['CMD1'] % (user, password, base_url)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        prepare_cmd2 = case_config['SANITIZER_3700598_RCCA']['PREPARE']['CMD2']
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        # run sanitizer testing
        step1_cmd1 = case_config['SANITIZER_3700598_RCCA']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, 'Invalid', flag=2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_rcca_3700598.json'.format(log_path))
        return result, passed, failed
    else:
        logger.info('we only support this case since cuda 12.5')


@print_run_time
def option_preload_library_3532738():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3532738')
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['OPTION_PRELOAD_LIBRARY_PATH']
    log_name = case_config['OPTION_PRELOAD_LIBRARY']['LOG_NAME']
    mkdir(log_path)
    # Download and uncompress Rebel smoke test pacakge if needed
    download_rebel_smoke_test_pacakge(package_path=rebel_smoke_test_package_path, cuda_version=cuda_short_version)
    # prepare file
    prepare_cmd1 = case_config['OPTION_PRELOAD_LIBRARY']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['OPTION_PRELOAD_LIBRARY']['PREPARE']['CMD2']
    prepare_checkpont1 = case_config['OPTION_PRELOAD_LIBRARY']['PREPARE']['CHECKPOINT_1']
    prepare_checkpont2 = case_config['OPTION_PRELOAD_LIBRARY']['PREPARE']['CHECKPOINT_2']
    check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result, prepare_checkpont1)
    check_result(prepare_cmd2, 'prepare sample by {}'.format(prepare_cmd2), log_name, result, prepare_checkpont2)

    step1_cmd1 = case_config['OPTION_PRELOAD_LIBRARY']['STEP1']['CMD1']
    step1_checkpont1 = case_config['OPTION_PRELOAD_LIBRARY']['STEP1']['CHECKPOINT_1']
    step1_checkpont2 = case_config['OPTION_PRELOAD_LIBRARY']['STEP1']['CHECKPOINT_2']
    check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, step1_checkpont1, step1_checkpont2, flag=1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/option_preload_library_3532738.json'.format(log_path))
    return result, passed, failed


@print_run_time
def sanitizer_rcca_3797693():
    if cuda_short_version > '12.6':
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3797693')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_3797693_RCCA_PATH']
        log_name = case_config['SANITIZER_3797693_RCCA']['LOG_NAME']
        mkdir(log_path)
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_3797693_RCCA']['PREPARE']['CMD1'].format(user, password, base_url)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_point1 = case_config['SANITIZER_3797693_RCCA']['CHECK_POINT1']
        # run sanitizer testing
        step1_cmd1 = case_config['SANITIZER_3797693_RCCA']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, check_point1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_rcca_3797693.json'.format(log_path))
        return result, passed, failed
    else:
        logger.info('we only support this case since cuda 12.7')


@print_run_time
def optix_rtcore_source_hiding_3730298():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3730298')
    driver_mode = common_get_driver_mode()
    logger.info("the driver mode is %s." % driver_mode)
    sm = get_sm()
    if driver_mode in ['TCC', 'MCDM'] or sm in [8.0, 9.0, 10.0, 10.3]:
        logger.info('(not support this case) we waive this case if the driver mode on Windows is TCC or MCDM or on tesla GPU. ')
        return
    else:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['OPTIX_RTCORE_SOURCE_HIDING_PATH']
        log_name = case_config['OPTIX_RTCORE_SOURCE_HIDING']['LOG_NAME']
        mkdir(log_path)
        # prepare file
        prepare_cmd1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['PREPARE']['CMD1'].format(user, password, base_url)
        prepare_cmd2 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['PREPARE']['CMD2']
        prepare_cmd3 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['PREPARE']['CMD3']
        prepare_checkpont1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['PREPARE']['CHECKPOINT_1']
        check_result(prepare_cmd1, 'download source file by {}'.format(prepare_cmd1), log_name, result)
        # check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        run_loc_cmd(prepare_cmd2)
        check_result(prepare_cmd3, 'check sample exist by {}'.format(prepare_cmd3), log_name, result, prepare_checkpont1)

        def check_results_3730298(step, cmd, c1, c2, c3, c4, c5, c6):
            print("step %d, cmd %s, c1 %s, c2 %s, c3 %s, c4 %s, c5 %s, c6 %s" % (step, cmd, c1, c2, c3, c4, c5, c6))
            step_out = run_loc_cmd(step1_cmd1)
            output = step_out['output']
            logger.info(step_out['output'])
            # c1, c2, c3 not in output, c4, c5, c6 in output
            if not step_out.succeeded and (c1 not in output and c2 not in output and c3 not in output) and (c4 in output and c5 in output and c6 in output):
                result['Step %d by command: %s' % (step, cmd)] = 'passed'
                logger.info('we run Step %d by command: %s successfully' % (step, cmd))
            else:
                result['Step %d by command: %s' % (step, cmd)] = 'failed'
                logger.info('we run Step %d by command: %s failed' % (step, cmd))

        step1_cmd1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CMD1']
        step1_checkpont1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_1']
        step1_checkpont2 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_2']
        step1_checkpont3 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_3']
        step1_checkpont4 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_4']
        step1_checkpont5 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_5']
        if cuda_short_version > '12.7':
            step1_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_6_1']
        else:
            step1_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP1']['CHECKPOINT_6']
        check_results_3730298(1, step1_cmd1, step1_checkpont1, step1_checkpont2, step1_checkpont3, step1_checkpont4, step1_checkpont5, step1_checkpont6)
        if cuda_short_version < '13.1':
            step2_cmd1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CMD1']
            step2_checkpont1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_1']
            step2_checkpont2 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_2']
            step2_checkpont3 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_3']
            step2_checkpont4 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_4']
            step2_checkpont5 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_5']
            if cuda_short_version > '12.7':
                step2_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_6_1']
            else:
                step2_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP2']['CHECKPOINT_6']
            check_results_3730298(2, step2_cmd1, step2_checkpont1, step2_checkpont2, step2_checkpont3, step2_checkpont4, step2_checkpont5, step2_checkpont6)

            step3_cmd1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CMD1']
            step3_checkpont1 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_1']
            step3_checkpont2 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_2']
            step3_checkpont3 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_3']
            step3_checkpont4 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_4']
            step3_checkpont5 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_5']
            if cuda_short_version > '12.7':
                step3_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_6_1']
            else:
                step3_checkpont6 = case_config['OPTIX_RTCORE_SOURCE_HIDING']['STEP3']['CHECKPOINT_6']
            check_results_3730298(3, step3_cmd1, step3_checkpont1, step3_checkpont2, step3_checkpont3, step3_checkpont4, step3_checkpont5, step3_checkpont6)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/optix_rtcore_source_hiding_3730298.json'.format(log_path))
        return result, passed, failed


@print_run_time
def detect_missing_moudle_unload_3504892():
    if cuda_short_version > '12.2':
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3504892')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH']
        log_name = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['LOG_NAME']
        mkdir(log_path)
        try:
            # prepare file
            prepare_cmd = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['PREPARE']['PREPARE_CMD']
            run_loc_cmd(prepare_cmd)
            for file1 in ['vectorAddDrv_Err.cpp', 'vectorAdd_kernel.cu']:
                prepare_cmd1 = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['PREPARE']['CMD1'].format(user, password, base_url, file1)
                run_loc_cmd(prepare_cmd1)
            prepare_cmd2 = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['PREPARE']['CMD2']
            check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
            # run sanitizer testing
            step1_cmd1 = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['STEP1']['CMD1']
            check_point1 = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['CHECK_POINT1']
            check_point2 = case_config['MEMCHECK_DETECT_MISSING_MODULE_UNLOAD']['CHECK_POINT2']
            check_result(step1_cmd1, 'run sanitizer 3504892 by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2)
        except Exception as e:
            logger.error(f'has some error happen ---{str(e)}')
            result['some steps run fail, please check it'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/detect_missing_moudle_unload.json'.format(log_path))
        return result, passed, failed
    else:
        logger.info('not support this case if cuda version less than cuda 12.2')


@print_run_time
def sanitizer_memcheck_backtrace_short_3831779():
    if cuda_short_version >= '12.8':
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3831779')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH']
        log_name = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['LOG_NAME']
        mkdir(log_path)
        # prepare file
        for i in ['Makefile', 'memcheck_demo.cu']:
            prepare_cmd1 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['PREPARE']['CMD1'].format(user, password, base_url, i)
            check_result(prepare_cmd1, 'prepare file - {}'.format(i), log_name, result)
        prepare_cmd2 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['PREPARE']['CMD2']
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        check_point1 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT4']
        check_point5 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT5']
        check_point6 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT6']
        check_point7 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT7']
        check_point8 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT8']
        check_point9 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT9']
        check_point = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['CHECK_POINT']
        # run sanitizer testing
        step1_cmd1 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2, check_point3, check_point4, check_point5, check_point6, check_point)
        check_result(step1_cmd1, 'For step1, check file path not in output', log_name, result, log_path, flag=2)
        step2_cmd1 = case_config['SANITIZER_MEMCHECK_BACKTRACE_SHORT']['STEP2']['CMD1']
        check_result(step2_cmd1, 'run step2_1 by ---{}'.format(step2_cmd1), log_name, result, check_point7, check_point8, check_point9, check_point)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_memcheck_backtrace_short.json'.format(log_path))
        return result, passed, failed
    else:
        logger.info('we only support this case since cuda 12.8')


@print_run_time
def sanitizer_compiler_public_ptx_4076330():
    sm = get_sm()
    if cuda_short_version >= '12.9' and sm > 9.0:
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T4076330')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_COMPILER_PUBLIC_PTX_PATH']
        log_name = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['LOG_NAME']
        mkdir(log_path)
        sm1 = str(sm).split('.')[0] + str(sm).split('.')[1]
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['PREPARE']['CMD1'].format(user, password, base_url, user, password, base_url)
        prepare_cmd2 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['PREPARE']['CMD2'].format(sm1, sm1)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'build sample by {}'.format(prepare_cmd2), log_name, result)
        # run sanitizer step1
        step1_cmd1 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP1']['CMD1']
        check_point1 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP1']['CHECK_POINT1']
        check_point2 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP1']['CHECK_POINT2']
        check_point3 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP1']['CHECK_POINT3']
        check_point4 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP1']['CHECK_POINT4']
        check_result(step1_cmd1, 'run step1 by ---{}'.format(step1_cmd1), log_name, result, check_point1, check_point2, check_point3, check_point4)
        # run sanitizer step2
        step2_cmd1 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP2']['CMD1']
        step2_check_point1 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP2']['CHECK_POINT1']
        step2_check_point2 = case_config['SANITIZER_COMPILER_PUBLIC_PTX']['STEP2']['CHECK_POINT2']
        check_result(step2_cmd1, 'run step2 by ---{}'.format(step2_cmd1), log_name, result, step2_check_point1, step2_check_point2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_compiler_public_ptx.json'.format(log_path))
        # return PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED
    else:
        logger.info('we only support this case since cuda 12.9 and on blackwell++')
        # return WAIVED


@print_run_time
def sanitizer_track_ldgsts_4085254():
    sm = get_sm()
    if cuda_short_version >= '12.8' and sm > 7.5:
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T4085254')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_TRACK_LDGSTS_PATH']
        log_name = case_config['SANITIZER_TRACK_LDGSTS']['LOG_NAME']
        mkdir(log_path)
        sm1 = str(sm).split('.')[0] + str(sm).split('.')[1]
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_TRACK_LDGSTS']['PREPARE']['CMD1'].format(user, password, base_url, sm1)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_point1 = case_config['SANITIZER_TRACK_LDGSTS']['CHECK_POINT1']
        # run sanitizer testing
        step1_cmd1 = case_config['SANITIZER_TRACK_LDGSTS']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, check_point1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_track_ldgsts.json'.format(log_path))
        # return PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED
    else:
        logger.info('we only support this case since cuda 12.8 and on ampere+')
        # return WAIVED


@print_run_time
def sanitizer_rcca_4085255():
    sm = get_sm()
    if cuda_short_version > '12.7' and sm in [10.1, 10.0]:
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T4085255')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['SANITIZER_4085255_RCCA_PATH']
        log_name = case_config['SANITIZER_4085255_RCCA']['LOG_NAME']
        mkdir(log_path)
        # prepare file
        prepare_cmd1 = case_config['SANITIZER_4085255_RCCA']['PREPARE']['CMD1'].format(user, password, base_url)
        check_result(prepare_cmd1, 'prepare sample by {}'.format(prepare_cmd1), log_name, result)
        check_point1 = case_config['SANITIZER_4085255_RCCA']['CHECK_POINT1']
        # run sanitizer testing
        step1_cmd1 = case_config['SANITIZER_4085255_RCCA']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_1 by ---{}'.format(step1_cmd1), log_name, result, check_point1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sanitizer_rcca_4085255.json'.format(log_path))
        # return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we only support this case since cuda 12.8 and only on GB100/GB10b')
        # return WAIVED


example_text = """
Example:
python run_sanitizer_case.py # run all the function
python run_sanitizer_case.py -e sanitizer_sanity # run single case
python run_sanitizer_case.py -el sanitizer_sanity,sanitizer_smoke # run multi cases
function_list: 'sanitizer_non_graphics_sample', 'sanitizer_barrier_memory', 'sanitizer_sanity', 'racecheck_analysis', 'sanitizer_smoke'
"""
reversion = '1.0'
check_list = ['check_filter', 'cuda_leak_check', 'racecheck_detect_print_option', 'target_process_option',
              'sanitizer_no_crash', 'device_stack_frame', 'launch_count_skip', 'padding_option', 'nvtx_option', 'check_option', 'sanitizer_sanity',
              'check_demangle', 'correct_line_num', 'sample_coverage', 'check_save_log', 'check_launch_timeout', 'cuda_coredump_disable',
              'teardown_sanitizer_xxxxxx', 'stop_using_warpfullmask', 'memcheck_with_free', 'relative_return_address_2800789', 'read_only_flag',
              'detail_session_option', 'sanitizer_xml_option', 'cuda_barriers_option', 'leak_check_alloc', 'target_filter_option', 'stack_overflow_check',
              'sanitizer_memcheck_demo', 'sanitizer_racecheck_warp', 'sanitizer_racecheck_block', 'detect_missing_moudle_unload_3504892',
              'sanitizer_initcheck_error', 'sanitizer_leakcheck_memcheck_demo', 'sanitizer_synccheck_divergent', 'sanitizer_synccheck_syncwarp', 'leakcheck_devices_allocations',
              'memcheck_option_track_stream', 'synccheck_option_miss_barrier', 'sanitizer_build_all_samples', 'coredump_generation_support', 'call_host_device_3021849',
              'deprecate_cuda_memcheck_2742928', 'memcheck_ordered_race_detection', 'sanitizer_guardword_check_2608117', 'launch_without_suffix_specified_explicitly_2680933',
              'support_aligned_device_malloc_3071342', 'sanitizer_support_LDSM_3079874', 'initcheck_track_unused_memory_3090527', 'sanitizer_racecheck_memcpy_async_3158301',
              'sanitizer_steam_capture_3156524', 'graphic_sample_coverage_3084216', 'sanitizer_check_read_access_3178884', 'sanitizer_support_unicode_file_3183138',
              'sanitizer_memcheck_vector_atomics_3192559', 'sanitizer_memcheck_cnp_support_3192566', 'sanitizer_quite_cuda_init_3207945', 'sanitizer_launch_attach_3207946',
              'sanitizer_capture_cuda_barrier_3222551', 'initcheck_optix_samples_coverage_3363004', 'option_suppressions_3568568', 'sanitizer_rcca_3700598',
              'option_preload_library_3532738', 'sanitizer_rcca_3797693', 'optix_rtcore_source_hiding_3730298', 'sanitizer_memcheck_backtrace_short_3831779', 'sanitizer_compiler_public_ptx_4076330',
              'sanitizer_track_ldgsts_4085254', 'sanitizer_rcca_4085255']


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-c", "--config-file", dest="config_file", required=False,
        help='Specify test index file. e.g. cases')
    parser.add_argument(
        "-e", "--excute", action='append', required=False,
        choices=check_list,
        help="Specify function to be run.")
    parser.add_argument(
        "-el", "--excute_list", action='store', required=False,
        help="Specify multi function to be run.")
    args = parser.parse_args()

    case_str = args.excute_list
    case_list_single = args.excute
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            getattr(mod, case)()
    elif case_list_single:
        mod = sys.modules["__main__"]
        getattr(mod, case_list_single[0])()
    else:
        pass
