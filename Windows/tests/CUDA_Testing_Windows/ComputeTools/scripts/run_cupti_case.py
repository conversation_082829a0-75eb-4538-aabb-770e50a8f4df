# -*- encoding: utf-8 -*-

from configparser import ConfigParser
import urllib.request as urllib2
import http.client as httplib
import argparse
import base64

# from ComputeTools.scripts.common_utils import is_WSL
from common_utils import *
import logging
import time
import random
import threading
from mobile_yaml_mgr import MobileYamlManger
# For Graphics Samples and ptx_blackwell Samples
import win32gui
import win32con
from multiprocessing import Process, Manager


cupti_yaml = 'C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml'
yaml_mgr = MobileYamlManger(cupti_yaml)
case_config = yaml_mgr.load()

chip_list = ['ad102', 'ga100', 'ga102', 'ga103', 'ga104', 'ga106', 'ga107', 'ga10b', 'gh100', 'gv100', 'gv11b', 'tu102', 'tu104', 'tu106', 'tu116', 'tu117']
cuda_version = case_config['global']['env']['CUDA_VERSION']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
driver_version = case_config['global']['env']['DRIVER_VERSION']
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
base_path = 'C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v%s' % cuda_short_version
password = case_config['global']['env']['HOST_PASSWORD']
cuda_major = case_config['global']['env']['CUDA_MAJOR']
cuda_minor = case_config['global']['env']['CUDA_MINOR']
cuda_version = case_config['global']['env']['CUDA_VERSION']
cmder_bin_path = case_config['global']['env']['CMDER_BIN_PATH']
home_path = case_config['global']['env']['HOST_HOME']
platform = case_config['global']['env']['PLATFORM'].lower()
installer = case_config['global']['env']['INSTALLER'].lower()
password1 = case_config['global']['env']['CQA_PASSWORD']
user1 = case_config['global']['env']['CQA_USER']
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_115_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_115_PATH1']
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_116_PATH0']
    sample_1_path = case_config['global']['env']['SAMPLE_116_PATH1']
    device_sln = 'deviceQuery_vs2022.sln'
    asyncAPI_sln = 'asyncAPI_vs2022.sln'
    simpleMultiGPU_sln = 'simpleMultiGPU_vs2022.sln'
    solution_file = case_config['global']['env']['SOLUTION_FILE']
    streamOrderedAllocation_sln = 'streamOrderedAllocation_vs2022.sln'
else:
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH2']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH2']
    device_sln = 'deviceQuery.sln'
    asyncAPI_sln = 'asyncAPI.sln'
    simpleMultiGPU_sln = 'simpleMultiGPU.sln'
    solution_file = case_config['global']['env']['SOLUTION_FILE1']
    streamOrderedAllocation_sln = 'streamOrderedAllocation.sln'

if cuda_short_version == 10.1:
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.1']
elif cuda_short_version == 10.2:
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.2']
# Added support for CUDA 12.0 and 12.0+, Sep 1, 2022
elif cuda_major >= '11':
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_11']
else:
    logger.info('please give the correct cupti address')
cupti_path = case_config['global']['env']['CUPTI_PATH1']
tools_home = case_config['global']['env']['TOOLS_HOME']
if not os.path.exists('%s' % tools_home):
    mkdir('%s' % tools_home)
else:
    print('the logs folder exist')
output_flag = case_config['global']['env']['OUTPUT_FLAG']

logger.setLevel(level=logging.INFO)
# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/cupti_case.log' % tools_home)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger = logging.getLogger(__name__)
# Only for Windows, Dec 28, 2021
cmder_bin_path = case_config['global']['env']['CMDER_BIN_PATH']
devenv = case_config['global']['env']['DEVENV']
# X86_WIN
download_to_path = case_config['global']['env']['CUPTI_PATH1']
cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH1']
cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH1']
os_type = get_os_type()
if os_type == 'woa':
    cupti_run_path = cupti_path + '/host/windows-desktop-win10-t23x-a64'
    cupti_target_path = cupti_path + '/target/windows-desktop-win10-t23x-a64'
package = 'Cupti-Release-Public-Windows.zip'

gpu_dict = {
    'V100': 'gv100',
    'v100': 'gv100',
    'NVIDIA TITAN V': 'gv100',
    'RTX 2070': 'tu106',
    'RTX 2080': 'tu104',
    'RTX 2080 Ti': 'tu102',
    'RTX 3080 Ti': 'ga102',
    'GV100': 'gv100',
    'T4': 'tu104',
    'tu104': 'tu104',
    'TITAN V': 'tu102',
    'A100': 'ga100',
    'P100': 'gp100',
    'ga100': 'ga100',
    'gv100': 'gv100',
    'RTX 5000': 'tu104',
    'RTX 6000': 'tu102',
    'RTX 3070': 'ga104',
    'RTX 3070 Ti': 'ga104',
    'RTX 3080': 'ga102',
    'H100': 'GH100',
    'GTX TITAN X': 'GM200',
}

os.environ["PATH"] = "/usr/local/cuda-%s/bin" % case_config['global']['env']['CUDA_SHORT_VERSION'] + ";" + os.environ["PATH"]
os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']


def prepare_gpu():
    build_cmd = case_config['PREPARE']['CMD1'] % (sample_1_path, device_sln)
    gpu_cmd = case_config['PREPARE']['CMD2'] % (sample_1_path, device_sln)
    sm_cmd = case_config['PREPARE']['CMD3'] % (sample_1_path, device_sln)
    out1 = run_loc_cmd(build_cmd)
    logger.info(str(out1['output']))
    if out1.succeeded:
        out2 = run_loc_cmd(gpu_cmd)
        logger.info(str(out2['output']))
        out3 = run_loc_cmd(sm_cmd)
        logger.info(str(out3['output']))
        for key, value in gpu_dict.items():
            if key in out2['output']:
                return value
        if 'Graphics Device' in out2['output'] and out3['output'] == '7.0':
            return 'gv100'
        elif 'Graphics Device' in out2['output'] and out3['output'] == '8.0':
            return 'ga100'
        else:
            pass


def get_sm():
    """
    Get the SM on Windows, return 7.0, 7.5, 8.0, 8.6, etc.
    """
    sm_cmd = case_config['PREPARE']['CMD3'] % (sample_1_path, device_sln)
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        # To resolve the "ZWNBSP in SM string" problem, updated by Alex Li on Sep 7, 2023
        sm_output = sm_out['output']
        sm_temp = sm_output.split('\n')[-1].strip(' ')
        print("sm_temp is %s" % sm_temp)
        print("sm_temp(encoding with UTF-8-sig) is %s" % sm_temp.strip().encode('UTF-8-sig'))
        sm = sm_temp.encode('ascii', 'ignore').decode().strip()
        print("sm is %s" % sm)
        return float(sm)
    else:
        logger.info('we get the sm failed')
        return 0.0


SM = get_sm()


# CUDA Device Driver Mode (TCC or WDDM): WDDM (Windows Display Driver Model) or TCC (Tesla Compute Cluster Driver)
def get_driver_mode():
    """
    Get the driver mode on Windows, return TCC or WDDM

    Attention: This function has been obsoleted since 2024.07.17. Please use the new API common_get_driver_mode() in common_utils.py instead.
    """
    tcc_wddm_cmd = case_config['PREPARE']['CMD_TCC_WDDM'] % (sample_1_path, device_sln)
    print(tcc_wddm_cmd)
    tcc_wddm_out = run_loc_cmd(tcc_wddm_cmd)
    if tcc_wddm_out.succeeded:
        tcc_wddm = tcc_wddm_out['output']
        return 'TCC' if 'TCC' in tcc_wddm else 'WDDM'
    else:
        logger.info('we get the TCC or WDDM failed')


def run_uvm_sample():
    """
    Judge OS can support UVM or not
    """
    # systemWideAtomics does not support on Windows. ==> return False.
    run_uvm_cmd = case_config['PREPARE']['CMD_UVM'] % (sample_1_path, streamOrderedAllocation_sln)
    out = run_loc_cmd(run_uvm_cmd)
    if out.succeeded:
        return True
    else:
        return False


def parse_cupti_page(arch=case_config['global']['env']['PLATFORM'].lower()):
    try:
        website = urllib2.urlopen(cupti_addr)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except httplib.HTTPException as e:
        print('HTTPException')
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False
    print(website)
    html = website.read().decode('utf-8')
    if arch == 'x86_win':
        matchs = re.findall(r'SW_.*Release_Windows_Cupti.zip', html)
    else:
        matchs = re.findall(r'SW_.*Release_Linux_Cupti.tgz', html)
    matchs.sort()
    latest_build_name = matchs[-1].split('"')
    return latest_build_name


def prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower()):
    if arch == 'x86_win':
        # Use get_dvs_package() instead of the old url way, Feb 14, 2023
        # cupti_package = parse_cupti_page()[0]
        # download_path = cupti_addr + cupti_package
        os_type = get_os_type()
        if os_type == 'woa':
            download_path = get_dvs_package('cupti', 'woa', cuda_short_version)
        else:
            download_path = get_dvs_package('cupti', 'windows', cuda_short_version)
        cupti_package = download_path.split('/')[-1]
        download_to_path = cupti_path
        logger.info('we will download the package from %s' % download_path)
        logger.info('we will download the package to %s ' % download_to_path)
    else:
        cupti_package = parse_cupti_page()[0]
        download_path = cupti_addr + cupti_package
        download_to_path = cupti_path
        logger.info('we will download the package from %s' % download_path)
        logger.info('we will download the package to %s ' % download_to_path)
    # Check the download_to_path
    mkdir(download_to_path)
    # Clear the exist files
    logger.info("====== Remove the exist files in %s ...... " % download_to_path)
    remove_cmd = "cd %s ; %s/rm -fr *" % (download_to_path, cmder_bin_path)
    print("====== remove_cmd is %s " % remove_cmd)
    run_loc_cmd(remove_cmd)
    logger.info("====== Remove the exist files in %s successfully!" % download_to_path)
    # Download
    cmd2 = "cd %s ; %s/lftp -c 'open %s:%s@hqnvhwy02 ; glob -- pget -n 80 %s'" % (download_to_path, cmder_bin_path, os.environ.get('SERVICE_ACCOUNT', ''), os.environ.get('SERVICE_PASSWORD', ''), download_path)
    logger.info('we will use the command to download package, the command is %s ' % cmd2)
    out2 = run_loc_cmd(cmd2)
    if out2.succeeded:
        if arch == 'x86_win':
            # cmd3 = 'cd %s; unzip %s; for i in `ls *.tar.bz2`; do tar jxvf $i; done' % (download_to_path, cupti_package)
            cmd3 = 'cd %s ; unzip %s ; unzip -o Cupti-Release-Public-Windows.zip ; unzip -o CUDA-AgoraCupti-package.zip ' % (download_to_path, cupti_package)
            print(cmd3)
        else:
            cmd3 = 'cd %s; tar xzvf %s; for i in `ls *.tar.bz2`; do tar jxvf $i; done' % (download_to_path, cupti_package)
        logger.info('we will use the command to tar package, the command is %s ' % cmd3)
        out3 = run_loc_cmd(cmd3)
        if out3.succeeded:
            logger.info('extract the cupti package successful')
        else:
            logger.info('extract the cupti package failed')
    else:
        logger.info('Failed to download cupti package')
    return download_to_path


# Migrate the function from Linux on Apr 19, 2024
def check_cupti_output(output, expect_message):
    print(">>>>>> Calling API check_cupti_output() to check the output of CUPTI samples ...... [BEGIN] <<<<<<")
    print(output)
    new_output = [line for line in output.split('\n') if expect_message not in line]
    new_output_string = ''
    for line in new_output:
        new_output_string += line
    pattern = ['error', 'ERROR', 'fail', 'unsupport']
    ret = 0
    for pat in pattern:
        if re.search(pat, new_output_string):
            logger.error('check {} fail, {} is in output'.format(pat, pat))
            ret = 1
            break
    print(">>>>>> Calling API check_cupti_output() to check the output of CUPTI samples ...... [END] <<<<<<")
    return ret


# Retired
@print_run_time
def cupti_injection():
    # No need to run this case from CUDA 11.7
    if cuda_short_version >= '11.7':
        logger.info('we do not run this case if cuda version is greater than 11.7')
        return
    else:
        pass


@print_run_time
def cupti_library():
    result = {}
    passed, failed = 0, 0
    # Added for Windows. Dec 28, 2021
    cmd1 = r'cd "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v%s/extras/CUPTI/lib64" ; %s/ls cupti*.dll' % (cuda_short_version, cmder_bin_path)
    out1 = run_loc_cmd(cmd1)
    cupti = out1['output']
    print(cupti)
    log_name = case_config['CUPTI_LIBRARY']['LOG_NAME']
    log_path = case_config['global']['env']['LIBRARY_LOG_PATH']
    mkdir(log_path)
    check_path = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_PATH']
    check_list = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_X86_WIN'].split(',')
    # check_list.append(cupti_dict['%s' % cuda_short_version])
    check_list.append(cupti)
    logger.info("check_list is %s " % str(check_list))
    cmd = 'cd "%s" ; %s/ls' % (check_path, cmder_bin_path)
    for i in check_list:
        check_result(cmd, 'step1_check--%s' % i, log_name, result, i)
    result1 = calculate_result(result)
    dict_output(result1, output_flag)
    dict_to_json(result1, '%s/cupti_library.json' % case_config['global']['env']['LIBRARY_LOG_PATH'])
    return result, passed, failed


@print_run_time
def cupti_static():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_STATIC']['LOG_NAME']
    log_path = case_config['global']['env']['STATIC_LOG_PATH']
    mkdir(log_path)
    # prepare needed file
    if cuda_short_version < '11.3':
        cmd = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD'] % (user, password, base_url, 'cupti_static', 'Makefile_autorange_profiling_static')
    else:
        cmd = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD'] % (user, password, base_url, 'cupti_static', 'Makefile_autorange_profiling_static_new')
    cmd2 = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD1'] % (user, password, base_url, 'cupti_static', 'Makefile_cupti_query_static')
    check_result(cmd, 'we prepare the file----%s' % cmd, log_name, result)
    check_result(cmd2, 'we prepare the file----%s' % cmd2, log_name, result)
    if cuda_short_version < '11.0':
        cmd1 = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD1'] % (user, password, base_url, 'cupti_static', 'Makefile_activity_trace_static')
        check_result(cmd1, 'we prepare the file----%s' % cmd1, log_name, result)
    # prepare env
    '''
    sm_cmd = case_config['CUPTI_STATIC']['PREPARE']['CMD']
    sm_out = run_loc_cmd(sm_cmd)
    save_log(log_name, sm_cmd, 'prepare', sm_out['output'])
    if sm_out.succeeded:
        sm = sm_out['output']
    else:
        logger.info('we get the sm failed')
        exit()
    '''
    logger.info("sm is %s" % SM)
    pre_cmd2 = case_config['CUPTI_STATIC']['PREPARE']['CMD2']
    pre_cmd3 = case_config['CUPTI_STATIC']['PREPARE']['CMD3']
    pre_cmd4 = case_config['CUPTI_STATIC']['PREPARE']['CMD4']
    for cmd_pre in [pre_cmd4, pre_cmd2, pre_cmd3]:
        run_loc_cmd(cmd_pre)
    # step1
    if cuda_short_version >= '11.1':
        logger.info('now there is no this cupti sample')
    else:
        cmd1 = case_config['CUPTI_STATIC']['CMD1']
        check_result(cmd1, 'step1---%s' % cmd1, log_name, result)
    # step2
    if cuda_short_version < '13.0':
        cmd2 = case_config['CUPTI_STATIC']['CMD2']
        if SM > 7.0:
            check_result(cmd2, 'run-step3-------%s' % cmd2, log_name, result, flag=1)
        else:
            check_result(cmd2, 'run-step3-------%s' % cmd2, log_name, result)
    if cuda_short_version <= '11.2':
        cmd3 = case_config['CUPTI_STATIC']['CMD3']
    else:
        cmd3 = case_config['CUPTI_STATIC']['CMD4']
    if SM < 6.0:
        check_result(cmd3, 'run-step3-------%s' % cmd3, log_name, result, flag=1)
    else:
        check_result(cmd3, 'run-step3-------%s' % cmd3, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_static.json' % case_config['global']['env']['STATIC_LOG_PATH'])
    return result, passed, failed


@print_run_time
def cupti_guard():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_GUARD']['LOG_NAME']
    log_path = case_config['global']['env']['GUARD_LOG_PATH']
    mkdir(log_path)
    # Remove the code for CUDA 11.4, not use anymore on Jul 6, 2022
    prepare_cupti(arch=platform)
    cmd1 = 'cd %s ; unzip -o %s' % (download_to_path, package)
    check_result(cmd1, 'extract package--%s' % package, log_name, result)
    cmd2 = case_config['CUPTI_GUARD']['CMD'] % cupti_target_path
    check_point = case_config['CUPTI_GUARD']['CHECK_POINT']
    out = run_loc_cmd(cmd2)
    logger.info('run--{} result {}'.format(cmd2, out.output))
    if out.output == '':
        result['run--{}'.format(cmd2)] = 'passed'
        logger.info('we run the command------"{}" passed'.format(cmd2))
    else:
        if check_point not in out.output:
            result['run--{}'.format(cmd2)] = 'failed'
            logger.error('we run the command------"{}" failed, because "{}"  not in output'.format(cmd2, check_point))
        else:
            check_point = result['run--{}'.format(cmd2)] = 'passed'
            logger.info('we run the command------"{}" passed, because "{}" is in output'.format(cmd2, check_point))
    result1 = calculate_result(result)
    dict_output(result1, output_flag)
    dict_to_json(result1, '%s/cupti_guard.json' % case_config['global']['env']['GUARD_LOG_PATH'])
    return result, passed, failed


@print_run_time
def cupti_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['TRACE_LOG_PATH']
    mkdir(log_path)
    # prepare cupti dvs package
    prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower())
    # run step1:
    sample_list = case_config['CUPTI_TRACE']['SAMPLE_LIST'].split(',')
    for sample in sample_list:
        cmd = case_config['CUPTI_TRACE']['STEP1']['CMD1'] % sample
        cmd1 = case_config['CUPTI_TRACE']['STEP1']['CMD1_1'] % sample
        check_result(cmd, 'step1_trace_off-on_%s -- %s' % (sample, cmd), log_name, result)
        check_result(cmd1, 'step1_trace_off-on_%s -- %s' % (sample, cmd1), log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, output_flag)
    dict_to_json(result1, '%s/cupti_trace.json' % case_config['global']['env']['TRACE_LOG_PATH'])
    return result, passed, failed


def run_threading(func, cmd_list):
    threads1 = []
    for i in cmd_list:
        threads1.append(threading.Thread(target=func, args=(i,)))
    for t1 in threads1:
        t1.start()


@print_run_time
def cupti_trace_coverage():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['TRACE_COVERAGE_LOG_PATH']
    mkdir(log_path)
    # get the driver mode on Windows
    mode = common_get_driver_mode()
    print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
    # prepare cupti dvs package
    prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower())
    # prepare json file
    cmd1 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD1']
    prepare_out1 = run_loc_cmd(cmd1)
    logger.info('we will copy host/target to bin')
    save_log(log_name, cmd1, 'prepare_step1', prepare_out1['output'])
    sample_list = case_config['CUPTI_TRACE_COVERAGE']['SAMPLE_LIST'].split(',')
    # vectorAddMMAP can not run with TCC on Windows ===> added judgement in code
    if mode in ['TCC', 'MCDM']:
        sample_list.remove("vectorAddMMAP")
        print("vectorAddMMAP can not run with TCC or MCDM on Windows ===> removed vectorAddMMAP in the Samples List")
    print("sample_list is %s " % sample_list)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    cmd3 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD3']
    prepare_out3 = run_loc_cmd(cmd3)
    save_log(log_name, cmd3, 'prepare_step3', prepare_out3['output'])
    logger.info('we will generate the run json file')
    for sample in sample_list:
        if 'cdp' in sample:
            dict1["tests"].append({"name": "%s" % sample, "CC": ['<70'], "exe": "%s.exe" % sample})
        else:
            dict1["tests"].append({"name": "%s" % sample, "exe": "%s.exe" % sample})
    # 11.4
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    logger.info('the dict to generate the json file ... \n%s' % str(dict2))
    dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'test.json'))
    # run step1
    logger.info('we will run sample coverage')
    if cuda_short_version <= '11.4':
        cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD1']
        cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD1']
        cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD2']
    else:
        cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD1_1']
        cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD1_1']
        cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD2_1']
    #
    logger.info('running trace injection samples coverage ... \n%s' % cmd)
    check_result(cmd, 'step1_trace by----%s' % cmd, log_name, result)
    # run steps2 ,get passed, failed
    logger.info('checking the results of sample coverage test step1... \n%s' % cmd1)
    out1 = run_loc_cmd(cmd1)
    if out1.succeeded:
        result['failed'] = int(out1['output'])
    else:
        logger.info('please check the result')
    logger.info('checking the results of sample coverage test step2... \n%s' % cmd2)
    out2 = run_loc_cmd(cmd2)
    if out2.succeeded:
        result['passed'] = int(out2['output'])
    else:
        logger.info('please check the result')

    dict_output(result, output_flag)
    dict_to_json(result, '%s/cupti_trace_coverage.json' % case_config['global']['env']['TRACE_COVERAGE_LOG_PATH'])
    return result, passed, failed


@print_run_time
def cupti_trace_coverage_hes_5215382():
    sm = get_sm()
    if cuda_short_version >= '13.0' and sm > 9.0:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_COVERAGE_HES']['LOG_NAME']
        log_path = case_config['global']['env']['TRACE_COVERAGE_HES_LOG_PATH']
        mkdir(log_path)
        # get the driver mode on Windows
        mode = common_get_driver_mode()
        print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
        # prepare cupti dvs package
        prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower())
        # prepare json file
        cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD1']
        prepare_out1 = run_loc_cmd(cmd1)
        logger.info('we will copy host/target to bin')
        save_log(log_name, cmd1, 'prepare_step1', prepare_out1['output'])
        sample_list = case_config['CUPTI_TRACE_COVERAGE_HES']['SAMPLE_LIST'].split(',')
        # vectorAddMMAP can not run with TCC on Windows ===> added judgement in code
        if mode in ['TCC', 'MCDM']:
            sample_list.remove("vectorAddMMAP")
            print("vectorAddMMAP can not run with TCC or MCDM on Windows ===> removed vectorAddMMAP in the Samples List")
        print("sample_list is %s " % sample_list)
        if cuda_short_version <= '11.4':
            dict1 = {"tests": []}
            dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
        else:
            dict1 = {"tests": []}
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        cmd3 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD3']
        prepare_out3 = run_loc_cmd(cmd3)
        save_log(log_name, cmd3, 'prepare_step3', prepare_out3['output'])
        logger.info('we will generate the run json file')
        for sample in sample_list:
            if 'cdp' in sample:
                dict1["tests"].append({"name": "%s" % sample, "CC": ['<70'], "exe": "%s.exe" % sample})
            else:
                dict1["tests"].append({"name": "%s" % sample, "exe": "%s.exe" % sample})
        # 11.4
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        logger.info('the dict to generate the json file ... \n%s' % str(dict2))
        dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'test.json'))
        # run step1
        logger.info('we will run sample coverage')
        if cuda_short_version <= '11.4':
            cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD2']
        else:
            cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD1_1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD1_1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD2_1']
        #
        logger.info('running trace injection samples coverage ... \n%s' % cmd)
        check_result(cmd, 'step1_trace by----%s' % cmd, log_name, result)
        # run steps2 ,get passed, failed
        logger.info('checking the results of sample coverage test step1... \n%s' % cmd1)
        out1 = run_loc_cmd(cmd1)
        if out1.succeeded:
            result['failed'] = int(out1['output'])
        else:
            logger.info('please check the result')
        logger.info('checking the results of sample coverage test step2... \n%s' % cmd2)
        out2 = run_loc_cmd(cmd2)
        if out2.succeeded and search_keyword_in_file(log_path, 'Enabled HES tracing in CUPTI') and search_keyword_in_file(log_path, 'Requested HES to be enabled'):
            result['passed'] = int(out2['output'])
        else:
            logger.info('please check the result')

        dict_output(result, output_flag)
        dict_to_json(result, '%s/cupti_trace_coverage_hes.json' % log_path)
        return result, passed, failed
    else:
        logger.info('not support this case if cuda version less than 13.0')


# Only for Linux, not available for Windows. Dec 28, 2021
def openacc_trace():
    pass


@print_run_time
def cupti_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_SAMPLE_LOG_PATH']
    mkdir(log_path)
    # prepare get sm number
    print(SM)
    # prepare
    copy_detours_h = case_config['CUPTI_SAMPLE']['PREPARE']['COPY_DETOURS_H']
    copy_detours_lib = case_config['CUPTI_SAMPLE']['PREPARE']['COPY_DETOURS_LIB']
    check_result(copy_detours_h, 'copy detours.h by %s' % copy_detours_h, log_name, result)
    check_result(copy_detours_lib, 'copy detours.lib by %s' % copy_detours_lib, log_name, result)
    # change permission
    step1_cmd1 = case_config['CUPTI_SAMPLE']['STEP1']['CMD1']
    check_result(step1_cmd1, 'change permission by %s' % step1_cmd1, log_name, result)
    print(step1_cmd1)
    # build sample
    root_dir = 'C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v%s/extras/CUPTI/samples/' % cuda_short_version
    # get all samples in C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/extras/CUPTI/samples/
    dir_list = os.listdir(root_dir)
    ld_path = case_config['global']['env']['LD_PATH']
    print(dir_list)
    extension_path_list = [root_dir + i for i in dir_list if 'extension' in i]
    # Task 3977653: remove common folder in root_dir on Feb 14, 2023
    sample_path_list = [root_dir + i for i in dir_list if 'extension' not in i and 'common' not in i]
    cmd = r'cd "%s/src/profilerhost_util"; cumake clean; cumake' % extension_path_list[0]
    check_result(cmd, 'prepare build extensions by %s' % cmd, log_name, result)
    for i in sample_path_list:
        cmd1 = r'cd "%s"; cumake clean; cumake' % i
        check_result(cmd1, 'build cupti sample by %s' % cmd1, log_name, result)

    # run the cupti sample
    def run_sample(sample_list, checkpoint=None):
        for i in sample_list:
            for j in sample_path_list:
                if i == j.split('/')[-1]:
                    if i == 'autorange_profiling':
                        cmd = r'cd "%s"; $ENV:PATH+=";%s"; ./%s' % (j, ld_path, 'auto_range_profiling')
                    elif i == 'userrange_profiling':
                        cmd = r'cd "%s"; $ENV:PATH+=";%s"; ./%s' % (j, ld_path, 'user_range_profiling')
                    elif i == 'cupti_nvtx':
                        cmd = r'cd "%s"; $ENV:NVTX_INJECTION64_PATH+=";C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v%s/extras/CUPTI/cupti64_*.dll"; $ENV:PATH+=";%s"; ./cupti_nvtx' % (j, cuda_short_version, ld_path)
                    else:
                        cmd = 'cd "%s"; $ENV:PATH+=";%s"; ./%s' % (j, ld_path, i)
                    if checkpoint:
                        check_result(cmd, 'run cupti sample -- %s' % i, log_name, result, checkpoint, flag=1)
                    else:
                        check_result(cmd, 'run cupti sample -- %s' % i, log_name, result)

    step3_check4 = case_config['CUPTI_SAMPLE']['STEP3']['CHECK_POINT4']
    step3_check5 = case_config['CUPTI_SAMPLE']['STEP3']['CHECK_POINT5']
    nextgen_list = case_config['CUPTI_SAMPLE']['STEP3']['NEXTGEN_LIST'].split(',')
    legacy_list = case_config['CUPTI_SAMPLE']['STEP3']['LEGACY_LIST'].split(',')
    trace_list = case_config['CUPTI_SAMPLE']['STEP3']['TRACE_LIST'].split(',')
    # Judge SM
    if SM <= 7.2:
        run_sample(legacy_list)
    else:
        run_sample(legacy_list, checkpoint=step3_check4)
    run_sample(nextgen_list)
    run_sample(trace_list)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_sample.json' % case_config['global']['env']['CUPTI_SAMPLE_LOG_PATH'])
    return result, passed, failed


@print_run_time
def cupti_callback_event():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_CALLBACK_EVENT']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_EVENT_LOG_PATH']
        mkdir(log_path)
        # prepare env
        # PREPARE for Windows only added on Dec 22, 2021
        prepare_cmd = case_config['CUPTI_CALLBACK_EVENT']['PREPARE']['CMD']
        prepare_out = run_loc_cmd(prepare_cmd)
        save_log(log_name, prepare_cmd, 'step1_prepare', prepare_out['output'])
        logger.info('cupti_callback_event: %s %s %s' % ('step1_prepare', prepare_cmd, prepare_out['output']))
        # run step1 to get all the domain
        if SM <= 7.0:
            cmd1 = case_config['CUPTI_CALLBACK_EVENT']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            logger.info('cupti_callback_event: %s %s %s' % ('step1_getdomains', cmd1, out1['output']))
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    domain_list.append(i.replace('\r', ''))
            else:
                result['step1'] = 'failed'
            logger.info("domain_list is %s" % str(domain_list))
            # run step2 get events
            logger.info('we will get all the events-step2')
            cmd2 = case_config['CUPTI_CALLBACK_EVENT']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                logger.info('cupti_callback_event: %s-%s %s %s' % ('step2_event', str(i) + " " + domain_list[i], cmd, out2['output']))
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j.replace('\r', ''))
                else:
                    result['step2_%s' % i] = 'failed'
            logger.info("event_list is %s" % str(event_list))
            # run step3, callback all the event
            cmd3 = case_config['CUPTI_CALLBACK_EVENT']['STEP3']['CMD']
            logger.info('we will run the command: ./callback_event device event')
            for i in range(0, len(event_list)):
                cmd4 = cmd3 % event_list[i]
                out3 = run_loc_cmd(cmd4)
                save_log(log_name, cmd2, 'step3_run_event%s' % i, out3['output'])
                logger.info('cupti_callback_event: %s-%s %s %s' % ('step3_run_event', str(i) + " " + event_list[i], cmd4, out3['output']))
                if out3.succeeded:
                    result['step3_%s' % i] = 'passed'
                else:
                    result['step3_%s' % i] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_callback_event.json' % case_config['global']['env']['CUPTI_EVENT_LOG_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def cupti_callback_metric():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_CALLBACK_METRIC']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_METRIC_LOG_PATH']
        mkdir(log_path)
        # prepare env
        build_cmd = case_config['CUPTI_CALLBACK_METRIC']['BUILD']
        out0 = run_loc_cmd(build_cmd)
        if out0.succeeded:
            logger.info('build the cupti_query successful, %s' % build_cmd)
            result['build_cupti_query'] = 'passed'
        else:
            logger.info('build the cupti_query failed, %s' % build_cmd)
            result['build_cupti_query'] = 'failed'
        if SM <= 7.0:
            # run step1 to get all the metric
            cmd1 = case_config['CUPTI_CALLBACK_METRIC']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getmetrics', out1['output'])
            logger.info('CUPTI_CALLBACK_METRIC-step1_getmetrics, %s' % cmd1)
            metric_list = []
            logger.info('we will get all the metrics')
            if out1.succeeded:
                result['step1_get all the metrics'] = 'passed'
                for i in out1['output'].split('\n'):
                    metric_list.append(i)
            else:
                result['step1_get all the metric'] = 'failed'
            # run step2 run metric
            cmd2 = case_config['CUPTI_CALLBACK_METRIC']['STEP2']['CMD']
            for i in range(0, len(metric_list)):
                cmd4 = cmd2 % metric_list[i]
                logger.info('we will run the command: ./callback_metric device event, %s %s %s' % (str(i), metric_list[i], cmd4))
                out3 = run_loc_cmd(cmd4)
                save_log(log_name, cmd2, 'step2_run_metric%s' % i, out3['output'])
                logger.info('we will run the command: ./callback_metric device event, %s %s' % (cmd4, out3['output']))
                if out3.succeeded:
                    result['step2_%s_%s' % (i, metric_list[i])] = 'passed'
                else:
                    result['step2_%s_%s' % (i, metric_list[i])] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_callback_metric.json' % case_config['global']['env']['CUPTI_METRIC_LOG_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def cupti_event_sampling():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_EVENT_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_SAMPLING_LOG_PATH']
        mkdir(log_path)
        # PREPARE for Windows only, added on Dec 28, 2021
        prepare_cmd = case_config['CUPTI_EVENT_SAMPLING']['PREPARE']['CMD']
        out_prepare = run_loc_cmd(prepare_cmd)
        save_log(log_name, prepare_cmd, 'Prepare_Build CUPTI samples', out_prepare['output'])
        logger.info("cupti_event_sampling-PREPARE: %s %s" % (prepare_cmd, out_prepare['output']))
        # prepare env
        # run step1 to get all the domain
        if SM <= 7.0:
            cmd1 = case_config['CUPTI_EVENT_SAMPLING']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    domain_list.append(i.replace('\r', ''))
            else:
                result['step1'] = 'failed'
            logger.info("domain_list is %s" % str(domain_list))
            # run step2 get events
            logger.info('we will get all the events')
            cmd2 = case_config['CUPTI_EVENT_SAMPLING']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j.replace('\r', ''))
                else:
                    result['step2_%s' % i] = 'failed'
            logger.info("event_list is %s" % str(event_list))
            # run step3, event_sampling all the event
            cmd3 = case_config['CUPTI_EVENT_SAMPLING']['STEP3']['CMD']
            logger.info('we will run the command: ./event_sampling device event')
            for i in range(0, len(event_list)):
                cmd4 = cmd3 % event_list[i]
                out3 = run_loc_cmd(cmd4)
                save_log(log_name, cmd2, 'step3_run_event%s' % i, out3['output'])
                if out3.succeeded:
                    result['step3_%s_%s' % (i, event_list[i])] = 'passed'
                else:
                    result['step3_%s_%s' % (i, event_list[i])] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_event_sampling.json' % case_config['global']['env']['CUPTI_SAMPLING_LOG_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def cupti_query():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_QUERY']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_QUERY_LOG_PATH']
        mkdir(log_path)
        # PREPARE for Windows only, added on Dec 22, 2021
        prepare_cmd = case_config['CUPTI_QUERY']['PREPARE']['CMD']
        out_prepare = run_loc_cmd(prepare_cmd)
        save_log(log_name, prepare_cmd, 'Prepare_Build CUPTI samples', out_prepare['output'])
        logger.info("cupti_query-PREPARE: %s %s" % (prepare_cmd, out_prepare['output']))
        # prepare env
        # run step1 to get all the domain
        if SM <= 7.0:
            cmd1 = case_config['CUPTI_QUERY']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            logger.info("cupti_query-STEP1: %s %s" % (cmd1, out1['output']))
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                # value = re.compile(r'^[-+]?[0-9]+\.[0-9]+$')
                for i in out1['output'].split('\n'):
                    print(str(i))
                    domain_list.append(i.replace('\r', ''))
            else:
                result['step1'] = 'failed'
            print(domain_list)
            # run step2 get events
            logger.info('we will get all the events')
            cmd2 = case_config['CUPTI_QUERY']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                logger.info("cupti_query-STEP2: %s %s %s" % (domain_list[i], cmd, out2['output']))
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j.replace('\r', ''))
                else:
                    result['step2_%s' % i] = 'failed'
            # run step3 to get all the metric
            cmd3 = case_config['CUPTI_QUERY']['STEP3']['CMD']
            out3 = run_loc_cmd(cmd3)
            save_log(log_name, cmd3, 'step3_getmetrics', out3['output'])
            logger.info("cupti_query-STEP3: %s %s" % (cmd3, out3['output']))
            metric_list = []
            logger.info('we will get all the metrics')
            if out3.succeeded:
                result['step3'] = 'passed'
                for i in out3['output'].split('\n'):
                    metric_list.append(i)
            else:
                result['step3'] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_query.json' % case_config['global']['env']['CUPTI_QUERY_LOG_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def cupti_extra_replay():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_EXTRA_REPLAY']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_EXTRA_REPLAY_PATH']
    mkdir(log_path)
    # prepare env
    cmd1 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD1']
    if cuda_short_version < '11.3':
        cmd2 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD2'] % ('simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu')
        check_point = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CHECK_POINT']
        step1_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP1']['CMD'] % 'autoRangeSample'
        step2_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP2']['CMD'] % 'autoRangeSample'
        step3_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP3']['CMD'] % ('simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu')
    else:
        # 11.6  61 and 70
        # 11.7  65 and 74
        # 12.0  42 and 57
        # Task 3977691: auto_range_profiling.cu was changed from CUDA 12.0, so we need to write correct line number. Feb 14, 2023
        if cuda_short_version >= '12.0':
            line1, line2 = 42, 57
        elif cuda_short_version > '11.6':
            line1, line2 = 65, 74
        else:
            line1, line2 = 61, 70
        cmd2 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD2'] % ('auto_range_profiling.cu', 'auto_range_profiling.cu', 'auto_range_profiling.cu', line1, 'auto_range_profiling.cu', 'auto_range_profiling.cu', line2, 'auto_range_profiling.cu')
        step1_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP1']['CMD'] % 'auto_range_profiling'
        check_point = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CHECK_POINT1']
        step2_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP2']['CMD'] % 'auto_range_profiling'
        step3_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP3']['CMD'] % ('auto_range_profiling.cu', 'auto_range_profiling.cu', 'auto_range_profiling.cu')
    print("====== cmd2 is %s " % cmd2)
    cmd3 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD3']
    check_result(cmd1, 'prepare_cmd1 by %s' % cmd1, log_name, result)
    check_result(cmd2, 'prepare_cmd2 by %s' % cmd2, log_name, result)
    check_result(cmd3, 'prepare_cmd3 by %s' % cmd3, log_name, result, check_point)
    # run step1 to check single run
    logger.info('we will run the step1, the command is %s' % step1_cmd)
    out = run_loc_cmd(step1_cmd)
    save_log(log_name, step1_cmd, 'step1', out['output'])
    single_add, single_sub = [], []
    print(out['output'])
    for line in out['output'].split('came'):
        if 'vecAdd' in line:
            single_add.append(line)
        if 'vecSub' in line:
            single_sub.append(line)
    if out.succeeded and len(single_sub) == 1 and len(single_add) == 1:
        result['step1 by %s' % step1_cmd] = 'passed'
        logger.info('we run cupti_extra_replay step1 successful')
    else:
        result['step1 by %s' % step1_cmd] = 'failed'
        logger.info('we run cupti_extra_replay step1 failed')
    # run step2 to check multi run
    logger.info('we will run the step2, the command is %s' % step2_cmd)
    out = run_loc_cmd(step2_cmd)
    save_log(log_name, step2_cmd, 'step2', out['output'])
    multi_add, multi_sub = [], []
    for line in out['output'].split('came'):
        if 'vecAdd' in line:
            multi_add.append(line)
        if 'vecSub' in line:
            multi_sub.append(line)
    if out.succeeded and len(multi_sub) == len(multi_add) and len(multi_sub) > 1 and len(multi_add) > 1:
        result['step2 by %s' % step2_cmd] = 'passed'
        logger.info('we run cupti_extra_replay step2 successful')
    else:
        result['step2 by %s' % step2_cmd] = 'failed'
        logger.info('we run cupti_extra_replay step2 failed')
    # restore env
    check_result(step3_cmd, 'step3_restore_env by %s' % step3_cmd, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_extra_replay.json' % case_config['global']['env']['CUPTI_EXTRA_REPLAY_PATH'])
    return result, passed, failed


@print_run_time
def pc_sampling_continous():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_SAMPLING_CONTINOUS']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_SAMPLING_CONTINOUS_PATH']
    mkdir(log_path)
    # prepare
    parepare_detours_h = case_config['CUPTI_SAMPLING_CONTINOUS']['PREPARE']['COPY_DETOURS_H']
    parepare_detours_lib = case_config['CUPTI_SAMPLING_CONTINOUS']['PREPARE']['COPY_DETOURS_LIB']
    check_result(parepare_detours_h, 'prepare by %s' % parepare_detours_h, log_name, result)
    check_result(parepare_detours_lib, 'prepare by %s' % parepare_detours_lib, log_name, result)
    # build samples
    cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLING']
    cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLE'] % (sample_0_path, asyncAPI_sln)
    cmd3 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLE1'] % (sample_0_path, simpleMultiGPU_sln)
    check_result(cmd1, 'step1_cmd1 by %s' % cmd1, log_name, result)
    check_result(cmd2, 'step2_cmd2 by %s' % cmd2, log_name, result)
    check_result(cmd3, 'step3_cmd3 by %s' % cmd3, log_name, result)
    # run pc_sampling command
    # number_list = [5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31]
    number_list = [5, 7, 9, 11, 13]
    i = random.sample(number_list, 1)[0]
    # i = random.randint(5, 31)
    step2_cmd = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD']
    step3_cmd = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD'] % sample_bin_path
    step3_cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD2'] % sample_bin_path
    check_point = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_POINT']
    check_point_list = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_POINT1'].split(',')
    check_list = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_LIST'].split(',')
    # Windows
    step2_cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD1'] % ('echo "1"', sample_bin_path, i, 'asyncAPI')
    step2_cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD2'] % ('echo "1"', sample_bin_path, i, 'asyncAPI')
    step2_cmd3 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD3'] % ('echo "1"', sample_bin_path, i, 'asyncAPI')
    check_result(step2_cmd1, 'step2_cmd1-----%s' % step2_cmd1, log_name, result, 'Initialize injection')
    check_result(step3_cmd, 'step3_cmd-----%s' % step3_cmd, log_name, result, check_point)
    check_result(step2_cmd2, 'step2_cmd2-----%s' % step2_cmd2, log_name, result, 'Initialize injection')
    check_result(step3_cmd, 'step3_cmd-----%s' % step3_cmd, log_name, result, check_point)
    check_result(step2_cmd3, 'step2_cmd3-----%s' % step2_cmd3, log_name, result)
    out = run_loc_cmd(step3_cmd)
    if out.succeeded:
        result['step3_cmd_2----%s' % step3_cmd] = 'failed'
        logger.info('the pc_sampling_continous  option-disable-file-dump  run fail')
    else:
        result['step3_cmd_2----%s' % step3_cmd] = 'passed'
        logger.info('the pc_sampling_continous  option-disable-file-dump  run pass')
    step3_cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD1']
    check_result(step2_cmd, 'step2_cmd-----%s' % step2_cmd, log_name, result)
    step2_cmd_out1 = run_loc_cmd(step2_cmd)
    save_file("C:/pc_sampling_help.txt", step2_cmd_out1['output'])
    out1 = run_loc_cmd(step3_cmd1)
    help_list = out1['output'].split('\n ')
    h_list = [i.strip() for i in help_list]
    print("======================check_list is %s " % str(check_list))
    print("======================h_list is %s " % str(h_list))
    if set(check_list) == set(h_list):
        result['step2_cmd_help_list'] = 'passed'
        logger.info('the pc_sampling_continous --help run pass')
    else:
        result['step2_cmd_help_list'] = 'failed'
        logger.info('the pc_sampling_continous --help run fail')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_continous.json' % case_config['global']['env']['CUPTI_SAMPLING_CONTINOUS_PATH'])
    return result, passed, failed


@print_run_time
def autorange_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['AUTORANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['AUTORANGE_PROFILING_PATH']
    mkdir(log_path)
    check_point = case_config['AUTORANGE_PROFILING']['CHECK_POINT']
    # prepare sample
    cmd1 = case_config['AUTORANGE_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['AUTORANGE_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling by %s' % cmd1, log_name, result)
    check_result(cmd2, 'change_profiling_permission by %s' % cmd2, log_name, result)
    # build the profiling sample
    auto_range = case_config['AUTORANGE_PROFILING']['STEP1']['BUILD_AUTORANGE']
    check_result(auto_range, 'build_autorange_profiling by %s' % auto_range, log_name, result)
    # run autorange profiling sample
    if cuda_short_version < '11.3':
        run_auto = case_config['AUTORANGE_PROFILING']['STEP2']['RUN_AUTORANGE'] % 'autoRangeSample'
    else:
        run_auto = case_config['AUTORANGE_PROFILING']['STEP2']['RUN_AUTORANGE'] % 'auto_range_profiling'
    if SM < 7.0:
        check_result(run_auto, 'run_autorange_profiling by %s' % run_auto, log_name, result, check_point, flag=1)
    else:
        check_result(run_auto, 'run_autorange_profiling by %s' % run_auto, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/autorange_profiling.json' % case_config['global']['env']['AUTORANGE_PROFILING_PATH'])
    return result, passed, failed


@print_run_time
def cupti_nvtx():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_NVTX']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_NVTX_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the cupti nvtx sample
        cmd = case_config['CUPTI_NVTX']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_cupti_nvtx by %s' % cmd, log_name, result)
        # run cupti_nvtx sample
        run_cupti_nvtx = case_config['CUPTI_NVTX']['STEP1']['CMD']
        # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
        # The checkpoints have been changed since CUDA12.4, 2024.1.0
        # Updated on Dec 20, 2023
        # Refactored the code to call API check_cupti_output() on Apr 19, 2024
        if cuda_short_version >= '12.4':
            out = run_loc_cmd(run_cupti_nvtx)
            print(out.output)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.succeeded and ret == 0:
                result['run {}'.format(run_cupti_nvtx)] = 'passed'
                logger.info('run cupti_external_correlation by {} successful'.format(run_cupti_nvtx))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(run_cupti_nvtx)] = 'failed'
                logger.info('run cupti_external_correlation by {} fail'.format(run_cupti_nvtx))
        else:
            check_result(run_cupti_nvtx, 'run_cupti_nvtx by %s' % run_cupti_nvtx, log_name, result)
    else:
        logger.info('it has not this sample, please check')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_nvtx.json' % case_config['global']['env']['CUPTI_NVTX_PATH'])
    return result, passed, failed


@print_run_time
def cuda_graphs_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUDA_GRAPHS_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['CUDA_GRAPHS_TRACE_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the cuda_graphs_trace sample
        cmd = case_config['CUDA_GRAPHS_TRACE']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_cuda_graphs_trace by %s' % cmd, log_name, result)
        # run cuda_graphs_trace sample
        run_cuda_graphs_trace = case_config['CUDA_GRAPHS_TRACE']['STEP1']['CMD']
        # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
        # The checkpoints have been changed since CUDA12.4, 2024.1.0
        # Updated on Dec 20, 2023
        # Refactored the code to call API check_cupti_output() on Apr 19, 2024
        if cuda_short_version >= '12.4':
            out = run_loc_cmd(run_cuda_graphs_trace)
            print(out.output)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.succeeded and ret == 0:
                result['run {}'.format(run_cuda_graphs_trace)] = 'passed'
                logger.info('run cuda_graphs_trace by {} successful'.format(run_cuda_graphs_trace))
                print('\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n')
            else:
                result['run {}'.format(run_cuda_graphs_trace)] = 'failed'
                logger.info('run cuda_graphs_trace by {} fail'.format(run_cuda_graphs_trace))
        else:
            check_result(run_cuda_graphs_trace, 'run_cuda_graphs_trace by %s' % run_cuda_graphs_trace, log_name, result)
    else:
        logger.info('now it has not this sample, please check')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_graphs_trace.json' % case_config['global']['env']['CUDA_GRAPHS_TRACE_PATH'])
    return result, passed, failed


@print_run_time
def callback_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CALLBACK_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CALLBACK_PROFILING_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['CALLBACK_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['CALLBACK_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling by %s' % cmd1, log_name, result)
    check_result(cmd2, 'change_profiling_permission by %s' % cmd2, log_name, result)
    # build the profiling sample
    callback = case_config['CALLBACK_PROFILING']['STEP1']['BUILD_CALLBACK']
    check_result(callback, 'build_callback_profiling by %s' % callback, log_name, result)
    # run callback profiling sample
    run_callback = case_config['CALLBACK_PROFILING']['STEP2']['RUN_CALLBACK']
    check_point = case_config['CALLBACK_PROFILING']['CHECK_POINT']
    if SM < 7.0:
        check_result(run_callback, 'run_callback_profiling by %s' % run_callback, log_name, result, check_point, flag=1)
    else:
        check_result(run_callback, 'run_callback_profiling by %s' % run_callback, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/callback_profiling.json' % case_config['global']['env']['CALLBACK_PROFILING_PATH'])
    return result, passed, failed


@print_run_time
def userrange_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['USERRANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['USERRANGE_PROFILING_PATH']
    mkdir(log_path)
    check_point = case_config['USERRANGE_PROFILING']['CHECK_POINT']
    # prepare sample
    cmd1 = case_config['USERRANGE_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['USERRANGE_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling by %s' % cmd1, log_name, result)
    check_result(cmd2, 'change_profiling_permission by %s' % cmd2, log_name, result)
    # build the profiling sample
    user_range = case_config['USERRANGE_PROFILING']['STEP1']['BUILD_USERRANGE']
    check_result(user_range, 'user_range by %s' % user_range, log_name, result)
    # run userrange profiling sample
    if cuda_short_version < '11.3':
        run_user = case_config['USERRANGEE_PROFILING']['STEP2']['RUN_USERRANGE'] % 'userRangeSample'
    else:
        run_user = case_config['USERRANGE_PROFILING']['STEP2']['RUN_USERRANGE'] % 'user_range_profiling'
    if SM < 7.0:
        check_result(run_user, 'run_userrange_profiling by %s' % run_user, log_name, result, check_point, flag=1)
    else:
        check_result(run_user, 'run_userrange_profiling by %s' % run_user, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/userrange_profiling.json' % case_config['global']['env']['USERRANGE_PROFILING_PATH'])
    return result, passed, failed


@print_run_time
def nested_range_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NESTED_RANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['NESTED_RANGE_PROFILING_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['NESTED_RANGE_PROFILING']['PREPARE']['CMD1']
    check_result(cmd1, 'prepare_build_profiling by %s' % cmd1, log_name, result)
    # build the profiling sample
    nest_range = case_config['NESTED_RANGE_PROFILING']['STEP1']['BUILD_SAMPLE']
    check_result(nest_range, 'build_nest_range_profiling by %s' % nest_range, log_name, result)
    # run the nest range profiling sample
    run_nest_range = nest_range = case_config['NESTED_RANGE_PROFILING']['STEP2']['CMD']
    check_point = case_config['NESTED_RANGE_PROFILING']['CHECK_POINT']
    if SM < 7.0:
        check_result(run_nest_range, 'run_nest_range by %s' % run_nest_range, log_name, result, check_point, flag=1)
    else:
        check_result(run_nest_range, 'run_nest_range by %s' % run_nest_range, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/nested_range_profiling.json' % case_config['global']['env']['NESTED_RANGE_PROFILING_PATH'])
    return result, passed, failed


@print_run_time
def pc_sampling_start_stop():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_START_STOP']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_START_STOP_PATH']
    mkdir(log_path)
    # prepare sample
    cmd = case_config['PC_SAMPLING_START_STOP']['STEP1']['BUILD_SAMPLING']
    check_result(cmd, 'step1_prepare_sample by %s' % cmd, log_name, result)
    check_point = case_config['NESTED_RANGE_PROFILING']['CHECK_POINT']
    # run pc_sampling
    step2_cmd = case_config['PC_SAMPLING_START_STOP']['STEP2']['CMD']
    out = run_loc_cmd(step2_cmd)
    print("step2_cmd is %s" % step2_cmd)
    save_log(log_name, step2_cmd, 'run-step2_cmd', out['output'])
    if SM < 7.0:
        check_result(step2_cmd, 'run_pc_sampling_start_stop by %s' % step2_cmd, log_name, result, check_point)
    else:
        # account stallReasonCount is correct or wrong in every line
        list1, list2 = [], []
        # Task 3977701: the kernel name was changed since CUDA 12.0, so I add a judgement here. Feb 14, 2023
        key1 = 'Launching VectorMultiply' if cuda_short_version >= '12.0' else 'Launching VecMul'
        with open('%s/pc_sampling_start_stop.txt' % cupti_path, 'r') as f:
            for line in f.readlines():
                if 'pcOffset' in line or key1 in line:
                    list1.append(line.strip(' ').strip('\n').split(','))
                if 'Number of PCs' in line:
                    list2.append(int(line.strip(' ').strip('\n').split(',')[2].split(':')[1].strip(' ')))
        print('====== list1 is %s' % str(list1))
        success, fail = 0, 0
        index_list, pc_list, function_list = [], [], []
        for i in range(0, len(list1)):
            stall_list = []
            for index, value in enumerate(list1[i]):
                if 'stallReason' in value:
                    stall_list.append(value)
                if key1 in value:
                    index_list.append(i)
                if 'pcOffset' in value:
                    pc_list.append(value.split(':')[1].strip(' '))
                if 'functionName' in value:
                    function_list.append(value.split(':')[1].strip(' '))
            for stall in stall_list:
                if 'stallReasonCount' in stall:
                    if int(stall.split(':')[1].strip(' ')) == len(stall_list) - 1:
                        success += 1
                    else:
                        fail += 1
        print('====== index_list is %s' % str(index_list))

        # account the sample number in every line, then account the sum of all sample
        def get_sample_number(index1, index2, list3):
            sum_list, total_sample = [], []
            for i in range(index1, index2):
                sample_list = []
                for index, value in enumerate(list3[i]):
                    if 'samples' in value and 'not_issued' not in list3[i][index - 1]:
                        if int(value.split(':')[1].strip(' ')) != 0:
                            sample_list.append(int(value.split(':')[1].strip(' ')))
                        else:
                            exit(20)
                            logger.info('the sample number should not be 0, so exit')
                        total_sample.append(int(value.split(':')[1].strip(' ')))
                sum_list.append(sum(sample_list))
            return total_sample, sum(sum_list)
        sample_list1, sum_number1 = get_sample_number(1, index_list[1], list1)
        sample_list2, sum_number2 = get_sample_number(index_list[2], index_list[-1], list1)
        print(list2)
        print(index_list)
        print(sample_list1, sum_number1)
        print(sample_list2, sum_number2)
        print(pc_list)
        print(function_list)
        check_list1 = sample_list1 + sample_list2
        check_list2 = pc_list + function_list
        if out.succeeded and 'None' not in check_list2 and 0 not in check_list1 and sum_number1 == list2[0] and sum_number2 == list2[1] and fail == 0:
            result['run_pc_sampling_start_stop by %s' % step2_cmd] = 'passed'
            logger.info('we run pc_sampling_start_stop successful')
        else:
            result['run_pc_sampling_start_stop by %s' % step2_cmd] = 'failed'
            logger.info('we run pc_sampling_start_stop fail')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_start_stop.json' % case_config['global']['env']['PC_SAMPLING_START_STOP_PATH'])
    return result, passed, failed


@print_run_time
def pc_sampling_count():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_COUNT']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_COUNT_PATH']
        mkdir(log_path)
        # prepare
        prepare_cmd = case_config['PC_SAMPLING_COUNT']['PREPARE']['CMD']
        prepare_out1 = run_loc_cmd(prepare_cmd)
        message = prepare_out1['stdout'].decode()
        str1 = 'configPC.samplingPeriod=CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MIN;'
        prepare_cmd1 = case_config['PC_SAMPLING_COUNT']['PREPARE']['CMD1'] % message
        if message.strip(' ') != str1:
            check_result(prepare_cmd1, 'restore_pc_sampling by %s' % prepare_cmd1, log_name, result)
        # build sample
        cmd = case_config['PC_SAMPLING_COUNT']['STEP1']['BUILD_SAMPLING']
        check_result(cmd, 'step1_prepare_sample by %s' % cmd, log_name, result)
        # run pc_sampling
        step2_cmd = case_config['PC_SAMPLING_COUNT']['STEP2']['CMD']
        out = run_loc_cmd(step2_cmd)
        print(out['output'])
        out_list1, out_list2 = [], []
        with open('%s/pc_sampling.txt' % home_path, 'r') as f:
            for line in f.readlines():
                if 'samples ' in line:
                    out_list1.append(int(line.strip('\n').split(',')[4].split(' ')[2]))
                if 'totalSamples' in line:
                    out_list2.append(int(line.strip('\n').split(',')[1].split(' ')[2]))
        if out.succeeded and sum(out_list1) == sum(out_list2):
            result['step2_count by %s' % step2_cmd] = 'passed'
            logger.info('we run the pc_sampling and the count is same, successful')
        else:
            result['step2_count by %s' % step2_cmd] = 'passed'
            logger.info('we run the pc_sampling and the count is different, fail')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_count.json' % case_config['global']['env']['PC_SAMPLING_COUNT_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def pc_sampling_utility():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_UTILITY']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_UTILITY_PATH']
    mkdir(log_path)
    # prepare
    parepare_detours_h = case_config['PC_SAMPLING_UTILITY']['PREPARE']['COPY_DETOURS_H']
    parepare_detours_lib = case_config['PC_SAMPLING_UTILITY']['PREPARE']['COPY_DETOURS_LIB']
    check_result(parepare_detours_h, 'prepare by %s' % parepare_detours_h, log_name, result)
    check_result(parepare_detours_lib, 'prepare by %s' % parepare_detours_lib, log_name, result)
    # build samples
    cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLING1']
    cmd2 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLING']
    cmd3 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLE'] % (sample_0_path, asyncAPI_sln)
    check_result(cmd1, 'step1_build_pc_sampling_continous by %s' % cmd1, log_name, result)
    check_result(cmd2, 'step1_build_pc_sampling_utility by %s' % cmd2, log_name, result)
    check_result(cmd3, 'step1_build_async by %s' % cmd3, log_name, result)
    # run pc_sampling command
    # number_list = [5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31]
    number_list = [5, 7, 9, 11, 13, 15, 17, 19]
    i = random.sample(number_list, 1)[0]
    step2_cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD1'] % (sample_bin_path, i)
    step2_cmd2 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD2'] % sample_bin_path
    step2_cmd3 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD3']
    step2_cmd4 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD4'] % sample_bin_path
    step2_cmd5 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD5'] % sample_bin_path
    step3_cmd = case_config['PC_SAMPLING_UTILITY']['STEP3']['CMD'] % sample_bin_path
    step3_cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP3']['CMD1'] % sample_bin_path
    check_point = case_config['PC_SAMPLING_UTILITY']['CHECK_POINT']
    # generate dat file
    check_result(step2_cmd1, 'step 2 cmd1 by %s' % step2_cmd1, log_name, result)
    check_result(step3_cmd, 'step 3 by %s' % step3_cmd, log_name, result, check_point)
    # generate cubin file
    check_result(step2_cmd2, 'step 2 cmd2 by %s' % step2_cmd2, log_name, result)
    cubin_out = run_loc_cmd(step3_cmd1)
    print(cubin_out)
    cubin_list = cubin_out['output'].split('\n')
    print("================ cubin_list is %s." % str(cubin_list))
    for i in range(0, len(cubin_list)):
        change_cmd = step2_cmd3 % (sample_bin_path, cubin_list[i], i + 1)
        run_loc_cmd(change_cmd)
    # run pc_sampling_utility without --verbose option
    out = run_loc_cmd(step2_cmd4)
    save_log(log_name, step2_cmd4, 'step2_cmd4', out['output'])
    print("step2_cmd4 is %s ==================== out is %s" % (step2_cmd4, str(out)))
    out_list = []
    time.sleep(2)
    # Meet characters' encoding problem on Windows after open a file, use out_list = out['output'].split('\n') instead.
    # Jan 12, 2022
    # with open('%s/pc_sampling_utility.txt' % home_path, 'r') as f:
    #    for line in f.readlines():
    #         out_list.append(line.strip('\n'))
    out_list = out['output'].split('\n')
    print("==================== out_list is %s" % str(out_list))
    if cuda_short_version <= '11.4':
        check_list = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST'].split(',')
        check_list1 = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST1'].split(',')
    else:
        check_list = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST_1'].split(',')
        check_list1 = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST1_1'].split(',')
    print(out_list)
    print('+++++++++++++++++++++++')

    def get_list(list1, list2):
        name_list, value_list = [], []
        for i in list1:
            name_list.append(i.split(':')[0].strip(' '))
        for m in range(1, len(list2)):
            for n in list2[m].split(','):
                value_list.append(n.split(':')[1].strip(' '))
        return name_list, value_list
    name_list1, value_list1 = get_list(out_list[1].split(','), out_list)
    name_list2, value_list2 = get_list(out_list[2].split(','), out_list)
    print('========')
    print(name_list1, value_list1)
    print('====================')
    print(name_list2, value_list2)
    print(set(check_list) == set(name_list1))
    print('=============')
    print(set(check_list))
    print(set(name_list1))
    if out.succeeded and 'error' not in out['output'] and 'ERROR' not in out['output'] and set(check_list) == set(name_list1) and 'NULL' not in value_list1 and set(check_list1).issubset(set(name_list2)):
        result['step2_step4_run_pc_sampling_utility'] = 'passed'
        logger.info('we run pc_sampling_utility successful')
    else:
        result['step2_step4_run_pc_sampling_utility'] = 'failed'
        logger.info('we run pc_sampling_utility fail')
    # run pc_sampling_utility with --version option
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_utility.json' % case_config['global']['env']['PC_SAMPLING_UTILITY_PATH'])
    return result, passed, failed


@print_run_time
def pc_sampling_period():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_PERIOD']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PERIOD_PATH']
        mkdir(log_path)
        # prepare sample
        cmd1 = case_config['PC_SAMPLING_PERIOD']['PREPARE']['CMD']
        cmd2 = case_config['PC_SAMPLING_PERIOD']['PREPARE']['CMD1']
        check_point = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT']
        check_point1 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT1']
        check_point2 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT2']
        check_point3 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT3']
        check_point4 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT4']
        check_result(cmd1, 'prepare_pc_sampling by %s' % cmd1, log_name, result)
        # prepare env

        def get_run_cmd():
            out = run_loc_cmd(cmd2)
            replace_message = out['output']
            return replace_message
        # run pc_sampling with different value
        step1_cmd = case_config['PC_SAMPLING_PERIOD']['STEP1']['CMD']
        step2_cmd = case_config['PC_SAMPLING_PERIOD']['STEP2']['CMD']
        step3_cmd = case_config['PC_SAMPLING_PERIOD']['STEP3']['CMD']
        step4_cmd = case_config['PC_SAMPLING_PERIOD']['STEP4']['CMD']
        step5_cmd = case_config['PC_SAMPLING_PERIOD']['STEP5']['CMD']

        def run_pc_sampling(label, cmd, check_point_1):
            cmd1 = cmd % get_run_cmd()
            out = run_loc_cmd(cmd1)
            save_log(log_name, cmd, label, out['output'])
            print(out['output'])
            value = out['output'].split('\n')[-1].split(',')[-1].strip(' ').split(' ')[-1].strip(' ')
            print(">>>>>>>> the value is %s " % value)
            logger.info('we will run the cmd ------%s' % cmd1)
            # https://nvbugs/4688654 [Auto]P1072_T2572470/2606581 -- it is better to use regrex to capture totalSamples value Updated on 2024.06.19
            if out.succeeded and re.search(check_point_1, out['output'], re.I):
                result['%s_min by %s' % (label, cmd1)] = 'passed'
                logger.info('we run the pc_sampling %s successful, the checkpoint is %s ' % (label, check_point_1))
            else:
                result['%s_min by %s' % (label, cmd1)] = 'failed'
                logger.info('we run the pc_sampling %s fail, the checkpoint is %s ' % (label, check_point_1))
        run_pc_sampling('step1_min', step1_cmd, check_point)
        run_pc_sampling('step2_low', step2_cmd, check_point)
        run_pc_sampling('step3_mid', step3_cmd, check_point)
        if SM < 7.0:
            run_pc_sampling('step4_high', step4_cmd, check_point1)
            run_pc_sampling('step5_max', step5_cmd, check_point2)
        else:
            run_pc_sampling('step4_high', step4_cmd, check_point3)
            run_pc_sampling('step5_max', step5_cmd, check_point4)
        # restore the pc sampling
        cmd6 = case_config['PC_SAMPLING_PERIOD']['STEP6']['CMD']
        check_result(cmd6, 'restore_pc_sampling by %s' % cmd6, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_period.json' % case_config['global']['env']['PC_SAMPLING_PERIOD_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def concurrent_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CONCURRENT_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CONCURRENT_PROFILING_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the concurrent_profiling sample
        cmd = case_config['CONCURRENT_PROFILING']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_concurrent_profiling--%s' % cmd, log_name, result)
        # run concurrent_profiling sample
        run_concurrent_profiling = case_config['CONCURRENT_PROFILING']['STEP1']['CMD']
        check_result(run_concurrent_profiling, 'run_concurrent_profiling--%s' % run_concurrent_profiling, log_name, result)
    else:
        logger.info('it has not this sample, please check')
    result1 = calculate_result(result)
    dict_output(result1, output_flag)
    dict_to_json(result1, '%s/concurrent_profiling.json' % case_config['global']['env']['CONCURRENT_PROFILING_PATH'])
    return result, passed, failed


@print_run_time
def pc_sampling():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PATH']
        mkdir(log_path)
        # get sm and devicename
        prepare_cmd1 = case_config['PREPARE']['CMD3'] % (sample_1_path, device_sln)
        prepare_cmd2 = case_config['PREPARE']['CMD4'] % (sample_1_path, device_sln)
        out1 = run_loc_cmd(prepare_cmd1)
        out2 = run_loc_cmd(prepare_cmd2)
        device_name = out2['output'].strip('"')
        check_point1 = case_config['PC_SAMPLING']['CHECK_POINT1'] % SM
        check_point2 = case_config['PC_SAMPLING']['CHECK_POINT1'] % device_name
        print(check_point2)
        print(check_point1)
        print('========================')
        # build the concurrent_profiling sample
        cmd = case_config['PC_SAMPLING']['STEP1']['CMD']
        check_result(cmd, 'build_pc_sampling by %s' % cmd, log_name, result)
        # run pc_sampling sample
        run_pc_sampling = case_config['PC_SAMPLING']['STEP2']['CMD']
        check_result(run_pc_sampling, 'run_pc_sampling by %s' % run_pc_sampling, log_name, result, check_point1, check_point2, flag=2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling.json' % case_config['global']['env']['PC_SAMPLING_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this sample since cuda 13.0')


@print_run_time
def activity_trace_async():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['ACTIVITY_TRACE_ASYNC']['LOG_NAME']
    log_path = case_config['global']['env']['ACTIVITY_TRACE_ASYNC_PATH']
    mkdir(log_path)
    # build the activity_trace_async sample
    cmd = case_config['ACTIVITY_TRACE_ASYNC']['STEP1']['CMD']
    check_result(cmd, 'build_activity_trace_async by %s' % cmd, log_name, result)
    # run activity_trace_async sample
    run_activity_trace_async = case_config['ACTIVITY_TRACE_ASYNC']['STEP2']['CMD']
    # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
    # The checkpoints have been changed since CUDA12.4, 2024.1.0
    # Updated on Dec 20, 2023
    # Refactored the code to call API check_cupti_output() on Apr 19, 2024
    if cuda_short_version >= '12.4':
        out = run_loc_cmd(run_activity_trace_async)
        print(out.output)
        ret = check_cupti_output(out.output, 'Calling CUPTI API')
        if out.succeeded and ret == 0:
            result['run {}'.format(run_activity_trace_async)] = 'passed'
            logger.info('run cupti_external_correlation by {} successful'.format(run_activity_trace_async))
            print('\n\n')
            logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
            print('\n\n')
        else:
            result['run {}'.format(run_activity_trace_async)] = 'failed'
            logger.info('run cupti_external_correlation by {} fail'.format(run_activity_trace_async))
    else:
        check_result(run_activity_trace_async, 'run_activity_trace_async by %s' % run_activity_trace_async, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/activity_trace_async.json' % case_config['global']['env']['ACTIVITY_TRACE_ASYNC_PATH'])
    return result, passed, failed


@print_run_time
def activity_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['ACTIVITY_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['ACTIVITY_TRACE_PATH']
    mkdir(log_path)
    if cuda_short_version > '11.0':
        logger.info('now there is no activity_trace sample, no need to run it')
    else:
        # build the activity_trace sample
        cmd = case_config['ACTIVITY_TRACE']['STEP1']['CMD']
        check_result(cmd, 'build_activity_trace by %s' % cmd, log_name, result)
        # run activity_trace sample
        run_activity_trace = case_config['ACTIVITY_TRACE']['STEP2']['CMD']
        check_result(run_activity_trace, 'run_activity_trace by %s' % run_activity_trace, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/activity_trace.json' % case_config['global']['env']['ACTIVITY_TRACE_PATH'])
        return result, passed, failed


@print_run_time
def callback_timestamp():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CALLBACK_TIMESTAMP']['LOG_NAME']
    log_path = case_config['global']['env']['CALLBACK_TIMESTAMP_PATH']
    mkdir(log_path)
    # build the callback_timestamp sample
    cmd = case_config['CALLBACK_TIMESTAMP']['STEP1']['CMD']
    check_result(cmd, 'build_callback_timestamp by %s' % cmd, log_name, result)
    # run callback_timestamp sample
    run_callback_timestamp = case_config['CALLBACK_TIMESTAMP']['STEP2']['CMD']
    check_result(run_callback_timestamp, 'run_callback_timestamp by %s' % run_callback_timestamp, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/callback_timestamp.json' % case_config['global']['env']['CALLBACK_TIMESTAMP_PATH'])
    return result, passed, failed


@print_run_time
def sass_source_map():
    if cuda_short_version < '13.0':
        sm = get_sm()
        result = {}
        passed, failed = 0, 0
        log_name = case_config['SASS_SOURCE_MAP']['LOG_NAME']
        log_path = case_config['global']['env']['SASS_SOURCE_MAP_PATH']
        mkdir(log_path)
        # build the sass_source_map sample
        cmd = case_config['SASS_SOURCE_MAP']['STEP1']['CMD']
        check_result(cmd, 'build sass_source_map by %s' % cmd, log_name, result)
        # run sass_source_map sample
        run_sass_source_map = case_config['SASS_SOURCE_MAP']['STEP2']['CMD']
        if sm < 10.0:
            # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
            # The checkpoints have been changed since CUDA12.4, 2024.1.0
            # Updated on Dec 20, 2023
            # Refactored the code to call API check_cupti_output() on Apr 19, 2024
            if cuda_short_version >= '12.4':
                out = run_loc_cmd(run_sass_source_map)
                print(out.output)
                ret = check_cupti_output(out.output, 'Calling CUPTI API')
                if out.succeeded and ret == 0:
                    result['run CUPTI sample sass_source_map: {}'.format(run_sass_source_map)] = 'passed'
                    logger.info('run sass_source_map by {} successful'.format(run_sass_source_map))
                    print('\n\n')
                    logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                    print('\n\n')
                else:
                    result['run CUPTI sample sass_source_map: {}'.format(run_sass_source_map)] = 'failed'
                    logger.info('run sass_source_map by {} fail'.format(run_sass_source_map))
            else:
                check_result(run_sass_source_map, 'run_sass_source_map by %s' % run_sass_source_map, log_name, result)
        else:
            if cuda_short_version >= '12.8':
                out = run_loc_cmd(run_sass_source_map)
                print(out.output)
                if 'not supported on Blackwell+ GPU architecture' in out.output:
                    result['run {}'.format(run_sass_source_map)] = 'passed'
                    logger.info('run sass_source_map on blackwell++ vGPU successful')
                else:
                    result['run {}'.format(run_sass_source_map)] = 'failed'
                    logger.info('run sass_source_map on blackwell++ vGPU successful')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/sass_source_map.json' % case_config['global']['env']['SASS_SOURCE_MAP_PATH'])
        return result, passed, failed
    else:
        logger.info('do not support this sample since cuda 13.0')


@print_run_time
def unified_memory():
    sm = get_sm()
    if str(sm) in ['12.1']:
        logger.info('not support this case in woa')
    else:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['UNIFIED_MEMORY']['LOG_NAME']
        log_path = case_config['global']['env']['UNIFIED_MEMORY_PATH']
        mkdir(log_path)
        # build the unified_memory sample
        cmd = case_config['UNIFIED_MEMORY']['STEP1']['CMD']
        check_result(cmd, 'build_unified_memory by %s' % cmd, log_name, result)
        # run unified_memory sample
        run_unified_memory = case_config['UNIFIED_MEMORY']['STEP2']['CMD']
        # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
        # The checkpoints have been changed since CUDA12.4, 2024.1.0
        # Updated on Dec 20, 2023
        # Refactored the code to call API check_cupti_output() on Apr 19, 2024
        if cuda_short_version >= '12.4':
            out = run_loc_cmd(run_unified_memory)
            print(out.output)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.succeeded and ret == 0:
                result['run {}'.format(run_unified_memory)] = 'passed'
                logger.info('run cupti_external_correlation by {} successful'.format(run_unified_memory))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(run_unified_memory)] = 'failed'
                logger.info('run run_unified_memory by {} fail'.format(run_unified_memory))
        else:
            check_result(run_unified_memory, 'run_unified_memory by %s' % run_unified_memory, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/unified_memory.json' % case_config['global']['env']['UNIFIED_MEMORY_PATH'])
        return result, passed, failed


@print_run_time
def event_multi_gpu():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['EVENT_MULTI_GPU']['LOG_NAME']
        log_path = case_config['global']['env']['EVENT_MULTI_GPU_PATH']
        mkdir(log_path)
        # build the event_multi_gpu sample
        cmd = case_config['EVENT_MULTI_GPU']['STEP1']['CMD']
        run_event_multi_gpu = case_config['EVENT_MULTI_GPU']['STEP2']['CMD']
        check_result(cmd, 'build_event_multi_gpu -- %s' % cmd, log_name, result)
        out = run_loc_cmd("nvidia-smi -L | awk '{print NR}' | tail -n1")
        # Task 3960057: add several enhancements according to changes of template 2713172. Feb 14, 2023
        # For Multiple GPUs
        if out.output != '1':
            run_event_multi_gpu_out = run_loc_cmd(run_event_multi_gpu)
            print("run_event_multi_gpu_out\n%s" % str(run_event_multi_gpu_out.output))
            if run_event_multi_gpu_out.succeeded:
                result['run_event_multi_gpu by %s' % run_event_multi_gpu] = 'passed'
            else:
                checkpoint = case_config['EVENT_MULTI_GPU']['STEP2']['CHECKPOINT']
                check_result(run_event_multi_gpu, 'run_event_multi_gpu -- %s' % run_event_multi_gpu, log_name, result, checkpoint, flag=1)
        # For Single GPU
        else:
            print(">>>>>> Single GPU: report This multi-GPU test is waived on single GPU setup <<<<<<")
            checkpoint1 = case_config['EVENT_MULTI_GPU']['STEP2']['CHECKPOINT1']
            check_result(run_event_multi_gpu, 'run_event_multi_gpu -- %s' % run_event_multi_gpu, log_name, result, checkpoint1, flag=1)
        result1 = calculate_result(result)
        dict_output(result1, output_flag)
        dict_to_json(result1, '%s/event_multi_gpu.json' % case_config['global']['env']['EVENT_MULTI_GPU_PATH'])
        return result, passed, failed
    else:
        logger.info('do not support this case if cuda 13.0')


@print_run_time
def nvlink_bandwidth():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['NVLINK_BANDWIDTH']['LOG_NAME']
        log_path = case_config['global']['env']['NVLINK_BANDWIDTH_PATH']
        mkdir(log_path)
        # build the nvlink_bandwidth sample
        cmd = case_config['NVLINK_BANDWIDTH']['STEP1']['CMD']
        check_result(cmd, 'build_nvlink_bandwidth -- %s' % cmd, log_name, result)
        # Task 3960045
        run_nvlink_bandwidth1 = case_config['NVLINK_BANDWIDTH']['STEP2']['CMD1']
        run_nvlink_bandwidth2 = case_config['NVLINK_BANDWIDTH']['STEP2']['CMD2']
        out1 = run_loc_cmd(run_nvlink_bandwidth1)
        out2 = run_loc_cmd(run_nvlink_bandwidth2)
        print("out1:\n%s" % str(out1.output))
        print("out2:\n%s" % str(out2.output))
        if out1.succeeded and out2.succeeded:
            result['run_nvlink_bandwidth'] = 'passed'
            logger.info('we run the nvlink-bandwidth passed')
        else:
            # Task 3977709: The message in output was changed from CUDA 12.0, so I add a judgement. Feb 14, 2023
            checkpoint = case_config['NVLINK_BANDWIDTH']['CHECK_POINT120'] if cuda_short_version >= '12.0' else case_config['NVLINK_BANDWIDTH']['CHECK_POINT']
            # run nvlink_bandwidth sample
            check_result(run_nvlink_bandwidth1, 'run_nvlink_bandwidth1 -- %s' % run_nvlink_bandwidth1, log_name, result, checkpoint, flag=1)
            check_result(run_nvlink_bandwidth2, 'run_nvlink_bandwidth2 -- %s' % run_nvlink_bandwidth2, log_name, result, checkpoint, flag=1)
        result1 = calculate_result(result)
        dict_output(result1, output_flag)
        dict_to_json(result1, '%s/nvlink_bandwidth.json' % case_config['global']['env']['NVLINK_BANDWIDTH_PATH'])
        return result, passed, failed
    else:
        logger.info('do not support this case if cuda 13.0')


@print_run_time
def callback_event():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CALLBACK_EVENT']['LOG_NAME']
        log_path = case_config['global']['env']['CALLBACK_EVENT_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % (sample_1_path, device_sln)
        run_loc_cmd(prepare_cmd)
        # build the callback_event sample
        cmd = case_config['CALLBACK_EVENT']['STEP1']['CMD']
        check_result(cmd, 'build_callback_event by %s' % cmd, log_name, result)
        # run callback_event sample
        run_callback_event = case_config['CALLBACK_EVENT']['STEP2']['CMD']
        if SM < 7.2:
            check_result(run_callback_event, 'run_callback_event by %s' % run_callback_event, log_name, result)
        else:
            out = run_loc_cmd(run_callback_event)
            print(out['output'])
            check_point = case_config['CALLBACK_EVENT']['CHECK_POINT']
            save_log(log_name, run_callback_event, 'run_callback_event', out['output'])
            if out.succeeded is False and check_point in out['output']:
                result['run_callback_event by %s' % run_callback_event] = 'passed'
            else:
                result['run_callback_event by %s' % run_callback_event] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/callback_event.json' % case_config['global']['env']['CALLBACK_EVENT_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def callback_metric():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CALLBACK_METRIC']['LOG_NAME']
        log_path = case_config['global']['env']['CALLBACK_METRIC_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % (sample_1_path, device_sln)
        run_loc_cmd(prepare_cmd)
        # build the callback_metric sample
        cmd = case_config['CALLBACK_METRIC']['STEP1']['CMD']
        check_result(cmd, 'build_callback_metric by %s' % cmd, log_name, result)
        # run callback_metric sample
        run_callback_metric = case_config['CALLBACK_METRIC']['STEP2']['CMD']
        if SM < 7.2:
            check_result(run_callback_metric, 'run_callback_metric by %s' % run_callback_metric, log_name, result)
        else:
            out = run_loc_cmd(run_callback_metric)
            print(out['output'])
            check_point = case_config['CALLBACK_METRIC']['CHECK_POINT']
            save_log(log_name, run_callback_metric, 'run_callback_metric', out['output'])
            if out.succeeded is False and check_point in out['output']:
                result['run_callback_metric by %s' % run_callback_metric] = 'passed'
            else:
                result['run_callback_metric by %s' % run_callback_metric] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/callback_metric.json' % case_config['global']['env']['CALLBACK_METRIC_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def event_sampling():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['EVENT_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['EVENT_SAMPLING_LOG_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % (sample_1_path, device_sln)
        run_loc_cmd(prepare_cmd)
        # build the event_sampling sample
        cmd = case_config['EVENT_SAMPLING']['STEP1']['CMD']
        check_result(cmd, 'build_event_sampling by %s' % cmd, log_name, result)
        # run event_sampling sample
        run_event_sampling = case_config['EVENT_SAMPLING']['STEP2']['CMD']
        if SM < 7.2:
            check_result(run_event_sampling, 'run_event_sampling by %s' % run_event_sampling, log_name, result)
        else:
            out = run_loc_cmd(run_event_sampling)
            print(out['output'])
            check_point = case_config['EVENT_SAMPLING']['CHECK_POINT']
            save_log(log_name, run_event_sampling, 'run_event_sampling', out['output'])
            if out.succeeded is False and check_point in out['output']:
                result['run_event_sampling by %s' % run_event_sampling] = 'passed'
            else:
                result['run_event_sampling by %s' % run_event_sampling] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/event_sampling.json' % case_config['global']['env']['EVENT_SAMPLING_LOG_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def cupti_metric_properties():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_METRIC_PROPERTIES']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_METRIC_PROPERTIES_LOG_PATH']
    mkdir(log_path)
    # prepare sample
    prepare_cmd = case_config['CUPTI_METRIC_PROPERTIES']['PREPARE']['CMD1']
    prepare_cmd1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP1']['CMD']
    check_result(prepare_cmd, 'prepare_build_env', log_name, result)
    check_result(prepare_cmd1, 'build_cupti_metric_properties', log_name, result)
    # run the sample
    cmd1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD1']
    if cuda_short_version < '13.0':
        cmd2 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % chip_list[random.randrange(0, len(chip_list))]
    else:
        cmd2 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % chip_list[random.randrange(0, len(chip_list))].upper()
    cmd2_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % 'gx100'
    cmd3 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD3']
    cmd4 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD4']
    cmd4_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD4_1']
    cmd5 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD5']
    cmd5_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD5_1']
    cmd6 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD6']
    cmd7 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD7']
    cmd8 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD8']
    cmd9 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD9']
    check_point = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT']
    check_point1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT1']
    if cuda_short_version >= '13.0':
        check_point1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT1_1']
    check_point2 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT2']
    check_point3 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT3']
    check_point4 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT4']
    # Added for Step 2.1 show the usage of the executable file + 2.4 query the detail metrics and query the metrics with submetrics on Apr 17, 2024
    check_point7_1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT7_1']
    check_point7_2 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT7_2']
    check_point8_1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_1']
    if cuda_short_version >= '13.0':
        check_point8_1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_1_1']
    check_point8_2 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_2']
    check_point8_3 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_3']
    check_point8_4 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_4']
    check_point8_5 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_5']
    sm = get_sm()
    if sm < 10.0:
        check_point8_6 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_6']
    else:
        check_point8_6 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT8_6_1']
    check_point9_1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_1']
    check_point9_2 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_2']
    check_point9_3 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_3']
    check_point9_4 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_4']
    check_point9_5 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_5']
    check_point9_6 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_6']
    check_point9_7 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_7']
    check_point9_8 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_8']
    check_point9_9 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT9_9']

    if SM < 7.0:
        check_result(cmd1, 'run-cupti_metric_properties', log_name, result, check_point, flag=1)
        check_result(cmd2, 'run-cupti_metric_properties-with chip option', log_name, result)
        check_result(cmd2_1, 'run-cupti_metric_properties--with no-exit chip', log_name, result, check_point1)
        check_result(cmd3, 'run-cupti_metric_properties--with device id 0', log_name, result, check_point, flag=1)
        check_result(cmd4, 'run-cupti_metric_properties --with filename test.log', log_name, result, check_point, flag=1)
        # need check cmd4_1
        check_result(cmd4_1, 'run-cupti_metric_properties--with filename empty', log_name, result, check_point4, flag=1)
        check_result(cmd5, 'run-cupti_metric_properties --with csv option', log_name, result, check_point, flag=1)
        check_result(cmd6, 'run-cupti_metric_properties --with metric option', log_name, result, check_point, flag=1)
    else:
        check_result(cmd1, 'run-cupti_metric_properties: %s' % cmd1, log_name, result)
        check_result(cmd2, 'run-cupti_metric_properties-with chip option: %s' % cmd2, log_name, result)
        check_result(cmd2_1, 'run-cupti_metric_properties--with no-exit chip: %s' % cmd2_1, log_name, result, check_point1, flag=1)
        check_result(cmd3, 'run-cupti_metric_properties--with device id 0: %s' % cmd3, log_name, result)
        # https://nvbugs/4596485 [Auto]P1072_T2552099-need remove --file option ===> No need to test --file option since CUDA12.5.
        if cuda_short_version < '12.5':
            check_result(cmd4, 'run-cupti_metric_properties --with filename test.log: %s' % cmd4, log_name, result, check_point2)
            # need check cmd4_1
            check_result(cmd4_1, 'run-cupti_metric_properties--with filename empty: %s' % cmd4_1, log_name, result, check_point4, flag=1)
            check_result(cmd5, 'run-cupti_metric_properties --with csv option: %s' % cmd5, log_name, result, check_point3)
            check_result(cmd5_1, 'run-cupti_metric_properties--with csv option and filename empty: %s' % cmd5_1, log_name, result, check_point4, flag=1)
        check_result(cmd6, 'run-cupti_metric_properties --with metrics option: %s' % cmd6, log_name, result)
        check_result(cmd7, 'run-cupti_metric_properties to query the detail metrics: %s' % cmd7, log_name, result, check_point7_1, check_point7_2)
        check_result(cmd7, 'run-cupti_metric_properties to check no error message: %s' % cmd7, log_name, result, 'Error', 'error', 'fail', flag=2)
        check_result(cmd8, 'run-cpti_metric_properties to query the metrics with submetrics: %s' % cmd8, log_name, result, check_point7_1, check_point7_2, check_point8_1, check_point8_2, check_point8_3, check_point8_4, check_point8_5, check_point8_6)
        check_result(cmd9, 'run-cpti_metric_properties --help option: %s' % cmd9, log_name, result, check_point9_1, check_point9_2, check_point9_3, check_point9_4, check_point9_5, check_point9_6, check_point9_7, check_point9_8, check_point9_9)

    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_metric_properties.json' % log_path)
    return result, passed, failed


@print_run_time
def pc_sampling_period_increase():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_PERIOD_INCREASE']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PERIOD_INCREASE_PATH']
        mkdir(log_path)
        # prepare sample
        cmd1 = case_config['PC_SAMPLING_PERIOD_INCREASE']['PREPARE']['CMD']
        cmd2 = case_config['PC_SAMPLING_PERIOD_INCREASE']['PREPARE']['CMD1']
        check_result(cmd1, 'prepare_pc_sampling by %s' % cmd1, log_name, result)

        def get_run_cmd():
            out = run_loc_cmd(cmd2)
            replace_message = out['output']
            return replace_message
        # run pc_sampling with different value
        step1_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP1']['CMD']
        step2_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP2']['CMD']
        step3_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP3']['CMD']
        step4_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP4']['CMD']
        step5_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP5']['CMD']

        def run_pc_sampling(label, cmd):
            cmd1 = cmd % get_run_cmd()
            out = run_loc_cmd(cmd1)
            save_log(log_name, cmd, label, out['output'])
            print('==CMD==\n%s' % cmd1)
            print('==RESULT==\n%s' % out['output'])
            if out.succeeded:
                # https://nvbugs/4688654 [Auto]P1072_T2572470/2606581 -- it is better to use regrex to capture totalSamples value Updated on 2024.06.19
                value = out['output'].split('\n')[-3].split(',')[1].split(' ')[-1]
                print(">>>>>>>>>>> value is %s" % value)
                return value
            else:
                logger.error('we run this case fail')
                exit(2)

        min_value = run_pc_sampling('step1_min', step1_cmd)
        low_value = run_pc_sampling('step2_low', step2_cmd)
        mid_value = run_pc_sampling('step3_mid', step3_cmd)
        high_value = run_pc_sampling('step4_high', step4_cmd)
        max_value = run_pc_sampling('step5_max', step5_cmd)
        value_list = [min_value, low_value, mid_value, high_value, max_value]
        value = ['min', 'low', 'mid', 'high', 'max']
        value_dict = dict(zip(value, value_list))
        print('the totalsamples value list is %s' % value_list)
        print('the totalsamples value dict is %s' % value_dict)
        value_list1 = sorted(value_list)
        print('sorted the totalSamples value list is %s' % value_list1)
        value_list2 = value_list[::-1]
        print('reverse the totalSamples value list is %s' % value_list2)
        # - http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2606581&isTemplate=true
        # - note: MIN/LOW/MID are getting mapped to same sampling period, it is fine to have little variation in total samples.
        # - If you see total samples for LOW is larger than MIN, or MID is larger than LOW.
        # - It's allowed here as It is very difficult to get same number of samples everytime.
        if value_list1 == value_list2 or (max_value == '0' and high_value == '0') or (max_value > high_value):
            logger.info('we run pc_sampling_period increase successful')
            result['pc_sampling_period_increase'] = 'passed'
        else:
            logger.error('we run pc_sampling_period increase fail')
            result['pc_sampling_period_increase'] = 'failed'
        # restore the pc sampling
        cmd6 = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP6']['CMD']
        check_result(cmd6, 'restore_pc_sampling by %s' % cmd6, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_period_increase.json' % case_config['global']['env']['PC_SAMPLING_PERIOD_INCREASE_PATH'])
        return result, passed, failed
    else:
        logger.info('not support this case since cuda 13.0')


@print_run_time
def checkpoint_kernels():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CHECKPOINT_KERNELS']['LOG_NAME']
    log_path = case_config['global']['env']['CHECKPOINT_KERNELS_PATH']
    mkdir(log_path)
    # build the checkpoint kernels sample
    step1_cmd = case_config['CHECKPOINT_KERNELS']['STEP1']['CMD']
    check_result(step1_cmd, 'build_checkpoint_kernals-------%s' % step1_cmd, log_name, result)
    # run checkpoint_kernels sample
    run_checkpoint_kernels = case_config['CHECKPOINT_KERNELS']['STEP2']['CMD']
    check_result(run_checkpoint_kernels, 'run_checkpoint_kernels------%s' % run_checkpoint_kernels, log_name, result)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/checkpoint_kernels.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_external_correlation():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_EXTERNAL_CORRELATION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_EXTERNAL_CORRELATION_PATH']
    mkdir(log_path)
    # build the cupti_external_correlation sample
    cmd = case_config['CUPTI_EXTERNAL_CORRELATION']['STEP1']['CMD']
    check_result(cmd, 'build_cupti_external_correlation by %s' % cmd, log_name, result)
    # run cupti_external_correlation sample
    run_cupti_external_correlation = case_config['CUPTI_EXTERNAL_CORRELATION']['STEP2']['CMD']
    # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
    # The checkpoints have been changed since CUDA12.4, 2024.1.0
    # Updated on Dec 20, 2023
    # Refactored the code to call API check_cupti_output() on Apr 19, 2024
    if cuda_short_version >= '12.4':
        out = run_loc_cmd(run_cupti_external_correlation)
        ret = check_cupti_output(out.output, 'Calling CUPTI API')
        if out.succeeded and ret == 0:
            result['run {}'.format(run_cupti_external_correlation)] = 'passed'
            logger.info('run cupti_external_correlation by {} successful'.format(run_cupti_external_correlation))
            print('\n\n')
            logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
            print('\n\n')
        else:
            result['run {}'.format(run_cupti_external_correlation)] = 'failed'
            logger.info('run cupti_external_correlation by {} fail'.format(run_cupti_external_correlation))
    else:
        check_result(run_cupti_external_correlation, 'run_cupti_external_correlation by %s' % run_cupti_external_correlation, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_external_correlation.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_2_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_2_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_2_PROFILING_PATH']
    mkdir(log_path)
    prepare_cupti()
    # prepare samples
    prepare_cmd = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD'] % (cupti_target_path, cupti_target_path)
    prepare_cmd1 = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD2']
    for cmd in [prepare_cmd, prepare_cmd1, prepare_cmd2]:
        print(cmd)
        run_loc_cmd(cmd)
    result1 = {}
    cmd1 = 'cd %s ; %s/rm *' % (log_path, cmder_bin_path)
    run_loc_cmd(cmd1)
    step1_file = '%s/step1_alignedTypes_cmd.log' % log_path
    step1_file1 = '%s/step1_pc_sampling_cmd1.log' % log_path
    step2_file = '%s/step2_alignedTypes.log' % log_path
    step2_file1 = '%s/step2_matrixMul.log' % log_path
    # run step1
    if cuda_short_version >= '12.1':
        step1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2_121'] % (cupti_target_path, cupti_target_path)
    elif cuda_short_version >= '11.7':
        step1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % (cupti_target_path, cupti_target_path)
    else:
        step1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD'] % (cupti_target_path, cupti_target_path)
    step1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1']['CMD1']
    print("====== step1_cmd is %s " % step1_cmd)
    print("====== step1_cmd1 is %s " % step1_cmd1)
    time.sleep(20)
    threads1 = []
    threads1.append(threading.Thread(target=check_result, args=(step1_cmd, 'run step1_cmd %s' % step1_cmd, step1_file, result1)))
    threads1.append(threading.Thread(target=check_result, args=(step1_cmd1, 'run step1_cmd1 %s' % step1_cmd1, step1_file1, result1)))
    for t1 in threads1:
        t1.start()
        time.sleep(2)
    time.sleep(30)
    # run step2
    if cuda_short_version >= '12.1':
        step2_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2_121'] % (cupti_target_path, cupti_target_path)
        step2_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2_121'] % (cupti_target_path, cupti_target_path)
    elif cuda_short_version >= '11.7':
        step2_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % (cupti_target_path, cupti_target_path)
        step2_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2'] % (cupti_target_path, cupti_target_path)
    else:
        step2_cmd = case_config['CUPTI_2_PROFILING']['STEP2']['CMD'] % (cupti_target_path, cupti_target_path)
        step2_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD1'] % (cupti_target_path, cupti_target_path)
    print("====== step2_cmd is %s " % step2_cmd)
    print("====== step2_cmd1 is %s " % step2_cmd1)
    time.sleep(10)
    threads2 = []
    threads2.append(threading.Thread(target=check_result, args=(step2_cmd, 'run step2_cmd %s' % step2_cmd, step2_file, result1)))
    threads2.append(threading.Thread(target=check_result, args=(step2_cmd1, 'run step2_cmd1 %s' % step2_cmd1, step2_file1, result1)))
    for t2 in threads2:
        t2.start()
    time.sleep(20)
    # run step1_1 and step2_1, assure single run the command can pass
    if cuda_short_version >= '12.1':
        step1_1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2_121'] % (cupti_target_path, cupti_target_path)
        step1_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD1']
        step2_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2_121'] % (cupti_target_path, cupti_target_path)
    elif cuda_short_version >= '11.7':
        step1_1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % (cupti_target_path, cupti_target_path)
        step1_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD1']
        step2_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2'] % (cupti_target_path, cupti_target_path)
    else:
        step1_1_cmd = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD'] % (cupti_target_path, cupti_target_path)
        step1_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD1']
        step2_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2_1']['CMD1'] % (cupti_target_path, cupti_target_path)
    for cmd in [step1_1_cmd, step1_1_cmd1, step2_1_cmd1]:
        time.sleep(15)
        print("====== %s" % cmd)
        check_result(cmd, 'verify single command can run pass by %s' % cmd, log_name, result)
    check_point = case_config['CUPTI_2_PROFILING']['CHECK_POINT']
    step1_cmd_status = search_keyword_in_file('%s/step1_alignedTypes_cmd.log' % log_path, check_point)
    step1_cmd1_status = search_keyword_in_file('%s/step1_pc_sampling_cmd1.log' % log_path, check_point)
    step2_cmd_status = search_keyword_in_file('%s/step2_alignedTypes.log' % log_path, check_point)
    step2_cmd1_status = search_keyword_in_file('%s/step2_matrixMul.log' % log_path, check_point)
    status_list = [step1_cmd_status, step1_cmd1_status, step2_cmd1_status, step2_cmd_status]
    cmd_list = [step1_cmd, step1_cmd1, step2_cmd1, step2_cmd]
    cmd_dict = dict(zip(cmd_list, status_list))
    print("====== %s " % cmd_dict)
    for key, value in cmd_dict.items():
        if value is True:
            logger.info('we can find the file by run command--- %s' % key)
        else:
            logger.info('we can find the file by run command--- %s' % key)
    # check step1 result
    if step1_cmd1_status ^ step1_cmd_status is True:
        logger.info('we run step1 successful')
        result['step1_check_point---%s' % check_point] = 'passed'
    else:
        logger.info('we run the step1 fail')
        result['step1_check_point---%s' % check_point] = 'failed'
    # check step2 result
    if step2_cmd1_status ^ step2_cmd_status is True:
        logger.info('we run step2 successful')
        result['step2_check_point---%s' % check_point] = 'passed'
    else:
        logger.info('we run the step2 fail')
        result['step2_check_point---%s' % check_point] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_2_profiling.json' % log_path)
    return result, passed, failed


@print_run_time
def cuda_memory_trace():
    if cuda_short_version > '11.5':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUDA_MEMORY_TRACE']['LOG_NAME']
        log_path = case_config['global']['env']['CUDA_MEMORY_TRACE_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['CUDA_MEMORY_TRACE']['PREPARE']['CMD']
        step1_cmd = case_config['CUDA_MEMORY_TRACE']['STEP1']['CMD']
        if cuda_short_version >= '12.1':
            check_point = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT121']
            check_point1 = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT1121']
        else:
            check_point = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT']
            check_point1 = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT1']
        check_point_tcc = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT_TCC']
        check_point_tcc2 = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT_TCC2']
        # build cupti_memory_trace sample
        check_result(prepare_cmd, 'we build cuda_memory_trace by %s' % prepare_cmd, log_name, result)
        # run cupti_memory_trace sample
        # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
        # The checkpoints have been changed since CUDA12.4, 2024.1.0
        # Updated on Dec 20, 2023
        # Refactored the code to call API check_cupti_output() on Apr 19, 2024
        mode = common_get_driver_mode()
        # fix bug: 4809462
        logger.info(f'mode is: {mode}')
        if mode in ['WDDM', 'MCDM']:
            # check_result(step1_cmd, 'we run cuda_memory_trace(WDDM) by %s' % step1_cmd, log_name, result, check_point, check_point1)
            out = run_loc_cmd(step1_cmd)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.succeeded and ret == 0 and check_point in out.output and check_point1 in out.output:
                result['run {}'.format(step1_cmd)] = 'passed'
                logger.info('run cuda_memory_trace (WDDM) by {} successful'.format(step1_cmd))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(step1_cmd)] = 'failed'
                logger.info('run cuda_memory_trace by {} fail'.format(step1_cmd))
            save_log(log_name, step1_cmd, 'run {}'.format(step1_cmd), out.output)
        # TCC or MCDM
        else:
            # check_result(step1_cmd, 'we run cuda_memory_trace(TCC) by %s' % step1_cmd, log_name, result, check_point_tcc)
            out = run_loc_cmd(step1_cmd)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.succeeded and ret == 0 and check_point_tcc in out.output and check_point_tcc2 in out.output:
                result['run {}'.format(step1_cmd)] = 'passed'
                logger.info('run cuda_memory_trace (TCC) by {} successful'.format(step1_cmd))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(step1_cmd)] = 'failed'
                logger.info('run cuda_memory_trace by {} fail'.format(step1_cmd))
            save_log(log_name, step1_cmd, 'run {}'.format(step1_cmd), out.output)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_memory_trace.json' % log_path)
        return result, passed, failed
    else:
        logger.info('no need run cuda_memory_trace if cuda version is less than 11.6')


@print_run_time
def pc_sampling_stop_api():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_STOP_API']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_STOP_API_PATH']
    mkdir(log_path)
    # prepare samples
    prepare_cmd1 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD3']
    prepare_cmd4 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD4']
    check_result(prepare_cmd1, 'create sample folder %s' % prepare_cmd1, log_name, result)
    check_result(prepare_cmd2, 'download .cu file by %s' % prepare_cmd2, log_name, result)
    check_result(prepare_cmd3, 'run prepare_cmd3 by %s' % prepare_cmd3, log_name, result)
    check_result(prepare_cmd4, 'run prepare_cmd4 by %s' % prepare_cmd4, log_name, result)
    # run the sample
    step1_cmd1 = case_config['PC_SAMPLING_STOP_API']['STEP1']['CMD1']
    out = run_loc_cmd(step1_cmd1)
    print(out['output'])
    list_1 = [i.split(' ')[-1].strip('s') for i in out['output'].split('\n') if 'PC sampling stop' in i]
    save_log(log_name, step1_cmd1, 'run step1 by %s' % step1_cmd1, out['output'])
    if out.succeeded and list_1[0] < '0.01' and list_1[1] < '0.01':
        print(out.succeeded and list_1[0] < '0.01' and list_1[1] < '0.01')
        result['run step1 by %s' % step1_cmd1] = 'passed'
        logger.info('we run step1 by %s successful' % step1_cmd1)
    else:
        result['run step1 by %s' % step1_cmd1] = 'failed'
        logger.info('we run step1 by %s failed' % step1_cmd1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_stop_api.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_query_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_QUERY_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_QUERY_SAMPLE_PATH']
    mkdir(log_path)
    prepare_cmd = case_config['PREPARE']['CMD1'] % (sample_1_path, device_sln)
    run_loc_cmd(prepare_cmd)
    # build the event_sampling sample
    cmd = case_config['CUPTI_QUERY_SAMPLE']['STEP1']['CMD']
    check_result(cmd, 'build_cupti_query', log_name, result)
    # run event_sampling sample
    run_event_sampling = case_config['CUPTI_QUERY_SAMPLE']['STEP2']['CMD']
    if SM < 7.2:
        check_result(run_event_sampling, 'run_cupti_query by %s' % run_event_sampling, log_name, result)
    else:
        check_point = case_config['CUPTI_QUERY_SAMPLE']['CHECK_POINT']
        check_result(run_event_sampling, 'run_cupti_query by %s' % run_event_sampling, log_name, result, check_point, flag=1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_query_sample.json' % case_config['global']['env']['CUPTI_QUERY_SAMPLE_PATH'])
    return result, passed, failed


@print_run_time
def cupti_profile_coverage():
    # Task 3960080: add several enhancements according to the changes of template 2924950 on Feb 15, 2023
    # 1. Not support -e application for Graph Samples now. (see DTCUPTI-2023).
    # 2. Option -o is removed since the new design in 12.1.
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_PROFILE_COVERAGE_LOG_PATH']
    mkdir(log_path)
    # get the driver mode on Windows
    mode = common_get_driver_mode()
    print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
    # prepare cupti smoke package
    prepare_cupti(arch=platform)
    # prepare sample
    cmd = case_config['CUPTI_PROFILE_COVERAGE']['CMD'] % cupti_target_path
    check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
    # profile sample list
    sample_list = case_config['CUPTI_PROFILE_COVERAGE']['SAMPLE_LIST'].split(',')
    # vectorAddMMAP can not run with TCC on Windows ===> added judgement in code
    if mode in ['TCC', 'MCDM']:
        sample_list.remove("vectorAddMMAP")
        print("vectorAddMMAP can not run with TCC or MCDM on Windows ===> removed vectorAddMMAP in the Samples List")
    print("sample_list is %s " % sample_list)
    # Get checkpoints
    checkpoint = case_config['CUPTI_PROFILE_COVERAGE']['CHECK_POINT']
    checkpoint_graph_step1 = case_config['CUPTI_PROFILE_COVERAGE']['CHECK_POINT_GRAPH_SAMPLES_STEP1']
    checkpoint_graph_step2 = case_config['CUPTI_PROFILE_COVERAGE']['CHECK_POINT_GRAPH_SAMPLES_STEP2']
    checkpoint_graph_step3 = case_config['CUPTI_PROFILE_COVERAGE']['CHECK_POINT_GRAPH_SAMPLES_STEP3']
    for sample in sample_list:
        if cuda_short_version >= '12.1':
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP1']['CMD1121'] % (cupti_target_path, sample, sample)
            step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP2']['CMD1121'] % (cupti_target_path, sample, sample)
            step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP3']['CMD1121'] % (cupti_target_path, sample, sample)
        else:
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
            step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
            step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)

        if sample in ['systemWideAtomics', 'UnifiedMemoryStreams']:
            if cuda_short_version >= '12.1':
                step2_cmd1 = r"cd %s ; .\profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -r auto -e application -j step2_%s.json -a %s.exe" % (cupti_target_path, sample, sample)
            else:
                step2_cmd1 = r"cd %s ; .\profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -o counterdata -r auto -e application -j step2_%s.json -a %s.exe" % (cupti_target_path, sample, sample)
        # Run profile injection
        if sample == 'simpleCudaGraphs':
            # https://nvbugs/4663598 [Auto]P1072_T2924950 -- need update checkpoint for user replay mode Updated on 2024.06.18
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, checkpoint_graph_step1)
            check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, checkpoint_graph_step2)
            check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, checkpoint_graph_step3)
        else:
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, checkpoint, flag=2)
            check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, checkpoint, flag=2)
            check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, checkpoint, flag=2)
    # run nvtx range
    if cuda_short_version >= '12.1':
        step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD1121'] % cupti_target_path
        step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD2121'] % cupti_target_path
    else:
        step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD1'] % cupti_target_path
        step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD2'] % cupti_target_path
    check_result(step4_cmd1, 'run step4_cmd1 by %s' % step4_cmd1, log_name, result, checkpoint, flag=2)
    check_result(step4_cmd2, 'run step4_cmd1 by %s' % step4_cmd2, log_name, result, checkpoint, flag=2)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_coverage.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_trace_injection():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_LOG_PATH']
    mkdir(log_path)
    prepare_cupti(arch=platform)
    # prepare 0
    parepare_detours_h = case_config['CUPTI_TRACE_INJECTION']['PREPARE0']['COPY_DETOURS_H']
    parepare_detours_lib = case_config['CUPTI_TRACE_INJECTION']['PREPARE0']['COPY_DETOURS_LIB']
    check_result(parepare_detours_h, 'prepare_%s' % parepare_detours_h, log_name, result)
    check_result(parepare_detours_lib, 'prepare_%s' % parepare_detours_lib, log_name, result)
    # prepare sample
    print(cupti_run_path)
    print(cupti_target_path)
    cmd1 = case_config['CUPTI_TRACE_INJECTION']['PREPARE']['CMD1'] % cupti_target_path
    cmd2 = case_config['CUPTI_TRACE_INJECTION']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_sample by %s' % cmd1, log_name, result)
    check_result(cmd2, 'build---cupti_trace_injection by %s' % cmd2, log_name, result)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    dict1["tests"].append({"name": "asyncAPI", "exe": "asyncAPI"})
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'asyncAPI.json'))
    # run sample
    step1_cmd1 = case_config['CUPTI_TRACE_INJECTION']['STEP1']['CMD1']
    print(step1_cmd1)
    out = run_loc_cmd(step1_cmd1)
    print(out['output'])
    # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
    # The checkpoints have been changed since CUDA12.4, 2024.1.0
    # Updated on Dec 20, 2023
    # Refactored the code to call API check_cupti_output() on Apr 19, 2024
    ret = check_cupti_output(out['output'], 'Calling CUPTI API')
    if out.succeeded and ret == 0:
        logger.info('run cupti_trace_injection by {} successful'.format(step1_cmd1))
        print('\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n')

        driver, runtime, memcpy, memset, kernel = 0, 0, 0, 0, 0
        '''
        Add the support of CUDA 12.1+ according to the output changes on Feb 15, 2021
        cudaMemcpyAsync_v3020, MEMCPY "HtoD", MEMCPY "DtoH" since CUDA 12.1
        MEMCPY "HtoD" [ 72294787181670, 72294808701786 ] duration 21520116, size 67108864, srcKind PINNED, dstKind DEVICE, correlationId 138    deviceId 0, contextId 1, streamId 7, graphId 0, graphNodeId 0, channelId 8, channelType ASYNC_MEMCPY
        RUNTIME [ 72294787132200, 72294787180300 ] duration 48100, "cudaMemcpyAsync_v3020", cbid 41, processId 11648, threadId 24340, correlationId 138
        MEMCPY "DtoH" [ 72294809184799, 72294830794707 ] duration 21609908, size 67108864, srcKind DEVICE, dstKind PINNED, correlationId 140    deviceId 0, contextId 1, streamId 7, graphId 0, graphNodeId 0, channelId 8, channelType ASYNC_MEMCPY
        RUNTIME [ 72294787222900, 72294787238600 ] duration 15700, "cudaMemcpyAsync_v3020", cbid 41, processId 11648, threadId 24340, correlationId 140
        '''
        MEMCPY_serach_keyword = r'MEMCPY "' if cuda_short_version >= '12.1' else r'MEMCPY'
        # Update for task https://nvbugs/4596515 [Auto]P1072_T2931930-cannot get the correct driver and runtime count on Apr 17, 2024
        for line in out['output'].split('\n'):
            if 'DRIVER' in line[:6]:
                driver += 1
            if 'RUNTIME' in line[:7]:
                runtime += 1
            if MEMCPY_serach_keyword in line:
                memcpy += 1
            if 'KERNEL' in line and 'Disabling' not in line and 'Enabling' not in line:
                kernel += 1
            if 'MEMSET' in line and 'Disabling' not in line and 'Enabling' not in line:
                memset += 1
        logger.info('we run cupti_trace_injection with asyncAPI successful')
        result_list = [driver, runtime, memcpy, kernel, memset]
        print(result_list)
        # run cupti trace injection by cupti dvs package
        cmd = 'cd %s; python CuptiSmoke.py --testconfig tracing-injection --testlist asyncAPI.json| tee asyncAPI_injection_temp.txt' % cupti_run_path
        check_result(cmd, 'run_cupti-trace-injection---%s' % cmd, log_name, result)

        # get the count of api
        def get_count(path, pattern1, pattern2, file1):
            cmd = r"cd %s ; sed -n '/%s/,/%s/p' %s | awk '{print $5}'" % (path, pattern1, pattern2, file1)
            out = run_loc_cmd(cmd)
            count_list = []
            print('the output of the cmd---%s is %s' % (cmd, out['output']))
            # Fix the "ValueError: invalid literal for int() with base 10: ''" problem on Dec 20, 2023
            # Added a common function: is_int_number()
            for i in out['output'].split('\n'):
                if i != 'Count' and i != ' ' and i is not None and is_int_number(i):
                    count_list.append(int(i))
            return sum(count_list)
        print('-------------------------------------------')
        # Convert the UTF-16 LE files to UTF-8 on Windows
        logger.info('====== Converting UTF-16 LE files to UTF-8 files on Windows ...')
        asyncAPI_injection_temp_file = '%s/asyncAPI_injection_temp.txt' % cupti_run_path
        asyncAPI_injection_file = '%s/asyncAPI_injection.txt' % cupti_run_path
        utf16_to_utf8(asyncAPI_injection_temp_file, asyncAPI_injection_file)
        logger.info('====== Converting UTF-16 LE files to UTF-8 files on Windows is DONE!')
        driver1 = get_count(cupti_run_path, 'Driver API', 'NVTX Range', asyncAPI_injection_file)
        runtime1 = get_count(cupti_run_path, 'Runtime API', 'Driver API', asyncAPI_injection_file)
        kernel1 = get_count(cupti_run_path, 'Kernel(s)', 'Memcopies', asyncAPI_injection_file)
        memcpy1 = get_count(cupti_run_path, 'Memcopies', 'Memsets', asyncAPI_injection_file)
        memset1 = get_count(cupti_run_path, 'Memsets', 'CUDA API', asyncAPI_injection_file)
        print('-------------------------------------------------------------------------------------------------------')
        print('use cupti_trace_injection run asyncAPI------the DriverTime: %s; Runtime: %s, Kernel: %s; Memcpy: %s; Memset: %s' % (driver, runtime, kernel, memcpy, memset))
        print('use cupti smoke run asyncAPI------the DriverTime: %s; Runtime: %s, Kernel: %s; Memcpy: %s; Memset: %s' % (driver1, runtime1, kernel1, memcpy1, memset1))
        print('-------------------------------------------------------------------------------------------------------')
        result_list1 = [driver1, runtime1, memcpy1, kernel1, memset1]
        if result_list1 == result_list:
            logger.info('we run cupti_trace_injection passed')
            result['run_cupti_trace_injection'] = 'passed'
        else:
            logger.info('we run cupti_trace_injection with asyncAPI failed')
            result['run_cupti_trace_injection'] = 'failed'
    else:
        logger.info('we run cupti_trace_injection with asyncAPI failed')
        result['run_cupti_trace_injection'] = 'failed'
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_trace_injection.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_correlation():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_CORRELATION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_CORRELATION_LOG_PATH']
    mkdir(log_path)
    # prepare
    cmd1 = case_config['CUPTI_CORRELATION']['PREPARE']['CMD1']
    check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
    # run the sample
    # https://nvbugs/4397181 [Require Change] Test Procedure on 2944933/Handling additional "Calling CUPTI API" for CUDA 12.4 cupti samples
    # The checkpoints have been changed since CUDA12.4, 2024.1.0
    # Updated on Dec 20, 2023
    # Refactored the code to call API check_cupti_output() on Apr 19, 2024
    step1_cmd1 = case_config['CUPTI_CORRELATION']['STEP1']['CMD1']
    # check_result(step1_cmd1, 'run_sample by %s' % step1_cmd1, log_name, result, 'ERROR', 'error', flag=2)
    out = run_loc_cmd(step1_cmd1)
    ret = check_cupti_output(out.output, 'Calling CUPTI API')
    if out.succeeded and ret == 0:
        result['run {}'.format(step1_cmd1)] = 'passed'
        logger.info('run cupti_correlation by {} successful'.format(step1_cmd1))
        print('\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n')
    else:
        result['run {}'.format(step1_cmd1)] = 'failed'
        logger.info('run cupti_correlation by {} fail'.format(step1_cmd1))
    save_log(log_name, step1_cmd1, 'run {}'.format(step1_cmd1), out.output)

    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_correlation.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_build_all_samples():
    # Rebuild all CUDA Samples in DEBUG mode

    build_all_samples(cuda_version=cuda_short_version, vs_devenv=devenv, vs_solution_file=solution_file, build_type=3, build_mode='DEBUG')


@print_run_time
def pc_sampling_coverage_2804521():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_COVERAGE_LOG_PATH']
    mkdir(log_path)
    # prepare
    parepare_detours_h = case_config['PC_SAMPLING_COVERAGE']['PREPARE']['COPY_DETOURS_H']
    parepare_detours_lib = case_config['PC_SAMPLING_COVERAGE']['PREPARE']['COPY_DETOURS_LIB']
    check_result(parepare_detours_h, 'prepare by %s' % parepare_detours_h, log_name, result)
    check_result(parepare_detours_lib, 'prepare by %s' % parepare_detours_lib, log_name, result)
    cmd1 = case_config['PC_SAMPLING_COVERAGE']['PREPARE']['CMD1']
    check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
    cmd = 'cd {}, {}/rm -fr *.dat'.format(sample_bin_path, cmder_bin_path)
    run_loc_cmd(cmd)
    # run test
    sample_list1 = case_config['PC_SAMPLING_COVERAGE']['SAMPLE_LIST'].split(',')
    prepare_cupti(arch=platform)
    print("The original samples list is %s " % sample_list1)
    if run_uvm_sample() is True:
        logger.info('we support UVM ---systemWideAtomics')
        sample_list = sample_list1
    else:
        logger.info('we need change sample list')
        sample_list = [i for i in sample_list1 if i != 'systemWideAtomics']
    # Updated on Dec 15, 2022 vectorAddMMAP can not run with TCC on Windows ===> added judgement in code
    driver_mode = common_get_driver_mode()
    print("====== The driver mode is %s" % driver_mode)
    if driver_mode in ['TCC', 'MCDM']:
        sample_list.remove("vectorAddMMAP")
        print("vectorAddMMAP can not run with TCC or MCDM on Windows ===> removed vectorAddMMAP in the Samples List")
    print("The final samples list is %s " % sample_list)
    for sample in sample_list:
        if os.path.exists('%s/%s.exe' % (sample_bin_path, sample)):
            # https://nvbugs/4688085 [Auto]P1072_T2804521 -- win: should update period value for some samples like reductionMultiBlockCG and cudaOpenMP. Updated on 2024.06.18
            for i in [5, 13]:
                period = i
                if i == 5 and sample == 'reductionMultiBlockCG':
                    period = 8  # if period<8, will not generate dat file
                if i == 13 and sample == 'cudaOpenMP':
                    period = 7  # if period>7, will not generate dat file
                if i == 5 and sample == 'p2pBandwidthLatencyTest':
                    period = 6  # if period<6, will not generate dat file
                if i == 13 and sample == 'vectorAddMMAP':
                    period = 9  # if period>9, will not generate dat file
                step1_cmd1 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD1'] % (sample_bin_path, period, sample, period, sample)
                check_result(step1_cmd1, 'run cmd by {}'.format(step1_cmd1), log_name, result)
                step1_cmd2 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD2'] % (sample_bin_path, cupti_target_path, cupti_target_path, sample, period)
                out = run_loc_cmd(step1_cmd2)
                if out.succeeded:
                    print(out['output'])
                    drop_num = out['output'].split('Total Dropped Samples')[1].split(',')[0].strip(':').strip(' ')
                    print('>>>>>>>>>>>>>>>{}<<<<<<<<<<<<'.format(drop_num))
                    print(type(drop_num))
                    if period in [5, 8, 6]:
                        if sample == 'simpleCooperativeGroups':
                            if drop_num == '0':
                                result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                                logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                            else:
                                result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                                logger.error('we run step1_cmd2 ---{} failed'.format(step1_cmd2))
                        else:
                            if drop_num != '0':
                                result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                                logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                            else:
                                result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                                logger.error('we run step1_cmd2 ---{} failed'.format(step1_cmd2))
                    elif period in [13, 7, 9] and drop_num == '0':
                        result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                        logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                    else:
                        result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                        logger.error('we run step1_cmd2 ---{} failed'.format(step1_cmd2))
        else:
            logger.warning('the sample --- {} does not exist'.format(sample))

    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    if result1['passed'] >= len(sample_list) * 4 - 7:
        logger.info('*************we get correct result, pass more than 80%**************')
    else:
        logger.info('/\n\n*************we get fail result or sample does not exist, please check the result!!!!!!!!!!!!!!!!')
        logger.info('the sample length is {}, so the result pass number should be more than {}'.format(len(sample_list), len(sample_list) * 4 - 7))
        logger.info('the actually result number is {}'.format(result1['passed'] + result1['failed']))
    dict_to_json(result1, '%s/pc_sampling_coverage.json' % log_path)
    return result, passed, failed


@print_run_time
def pc_sampling_graphic_coverage_2941255():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3]:
        GRAPHICS_SAMPLES_EXECUTION_DURATION = 15  # 15 seconds
        result = {}
        passed, failed = 0, 0
        driver_mode = common_get_driver_mode()
        logger.info("the driver mode is %s." % driver_mode)
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
        if driver_mode in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        log_name = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_GRAPHIC_COVERAGE_LOG_PATH']
        mkdir(log_path)
        # get samples list
        sample_list_str = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['SAMPLE_LIST']
        print("original sample list string is %s" % sample_list_str)
        sample_list_temp = sample_list_str.split(',')
        print("temporary sample list is %s" % sample_list_temp)
        # http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2941255 - So far, we don't support pc sampling for optix samples.
        # filter the optix samples
        logger.info("Template P1072_T2941255: So far, we don't support pc sampling for optix samples")
        graphic_sample_list = [item for item in sample_list_temp if 'optix' not in item]
        print("Real graphic sample list is %s" % graphic_sample_list)
        sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'], 'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                       'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                       'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                       'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                       'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                       'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                       }
        # prepare
        parepare_detours_h = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['PREPARE']['COPY_DETOURS_H']
        parepare_detours_lib = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['PREPARE']['COPY_DETOURS_LIB']
        check_result(parepare_detours_h, 'prepare by %s' % parepare_detours_h, log_name, result)
        check_result(parepare_detours_lib, 'prepare by %s' % parepare_detours_lib, log_name, result)
        cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['PREPARE']['CMD1']
        check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
        # remove old *.dat file in bin paths
        cmd = 'cd "{}"; {}/rm -fr *.dat'.format(sample_bin_path, cmder_bin_path)
        run_loc_cmd(cmd)
        logger.info("removed *.dat files in path %s" % sample_bin_path)
        # for OptiX samples
        optix_flag = case_config['global']['env']['OPTIX_FLAG']
        optix_bin_path = case_config['global']['env']['OPTIX_SAMPLE_BIN_PATH']
        if optix_flag == '1':
            cmd_rm = 'cd {}; {}/rm *.dat'.format(optix_bin_path, cmder_bin_path)
            run_loc_cmd(cmd_rm)
            logger.info("removed *.dat files in path %s" % optix_bin_path)
        prepare_cupti(arch=platform)
        sample_list = graphic_sample_list
        for sample in sample_list:
            if os.path.exists('%s/%s.exe' % (sample_bin_path, sample)) or os.path.exists('%s/%s.exe' % (optix_bin_path, sample)):
                # https://nvbugs/4688092 [Auto]P1072_T2941255 -- win: should update period value for some samples like postProcessGL Updated on 2024.06.18
                for i in [5, 13]:
                    period = i
                    if i == 5 and sample in ['postProcessGL', 'oceanFFT', 'simpleD3D11Texture']:
                        period = 6  # if period<6, will not generate dat file

                    if 'optix' in sample:
                        step1_cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (optix_bin_path, period, sample, period, sample)
                    else:
                        step1_cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (sample_bin_path, period, sample, period, sample)
                    # Class WindowManager is defined in common_utils.py
                    myWM = WindowManager()

                    def thread1():
                        check_result(step1_cmd1, 'run_cmd by %s' % step1_cmd1, log_name, result)

                    def thread2():
                        title_name = sample_dict[sample][0]
                        class_name = sample_dict[sample][1]
                        hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                        print("HWND is %s" % hwnd)
                        win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                        print("Closed window by HWND %s" % hwnd)
                    t1 = threading.Thread(target=thread1)
                    t2 = threading.Thread(target=thread2)
                    t1.start()
                    time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)
                    t2.start()
                    # Wait for 1 second
                    time.sleep(1)
                    if 'optix' in sample:
                        step1_cmd2 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD2'] % (optix_bin_path, cupti_target_path, cupti_target_path, sample, period)
                    else:
                        step1_cmd2 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD2'] % (sample_bin_path, cupti_target_path, cupti_target_path, sample, period)
                    out = run_loc_cmd(step1_cmd2)
                    if out.succeeded:
                        print('we run cmd {}'.format(step1_cmd2))
                        print(out['output'])
                        drop_num = out['output'].split('Total Dropped Samples')[1].split(',')[0].strip(':').strip(' ')
                        print('>>>>>>>>>>>drop_number:{}<<<<<<<'.format(drop_num))
                        if period in [5, 6] and drop_num != '0':
                            result['run step1_cmd2 ---- {}'.format(step1_cmd2)] = 'passed'
                            logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                        elif period in [13] and drop_num == '0':
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                            logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                        else:
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                            logger.error('we run step1_cmd2 ---{} failed'.format(step1_cmd2))
            else:
                logger.warning('the sample --- {} does not exist'.format(sample))
                result['run pc_sampling with graphic ----{}'.format(sample)] = 'failed'
        # Pay attention: Have to wait for several seconds, important !!!
        time.sleep(2)
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        if result1['passed'] >= len(sample_list) * 4 - 7:
            logger.info('*************we get correct result, pass more than 80%**************')
        else:
            logger.info('/\n\n*************we get fail result, please check the result!!!!!!!!!!!!!!!!')
            logger.info('the sample length is {}, so the result pass number should be more than {}'.format(len(sample_list), len(sample_list) * 4 - 7))
            logger.info('the actually result number is {}'.format(result1['passed'] + result1['failed']))
        dict_to_json(result1, '%s/pc_sampling_graphic_coverage.json' % log_path)
        return result, passed, failed
    else:
        print('not support this case on tesla GPU, Amere/Hopper/Blackwell')


@print_run_time
def profile_injection_graphic_coverage_3107112():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3]:
        # 1. Not support -e application for Graph Samples now. (see DTCUPTI-2023).
        # 2. Option -o is removed since the new design in 12.1.
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['LOG_NAME']
        log_path = case_config['global']['env']['PROFILE_INJECTION_GRAPHIC_COVERAGE_LOG_PATH']
        mkdir(log_path)
        # get the driver mode on Windows
        mode = common_get_driver_mode()
        print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
        if mode in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        # prepare cupti smoke package
        prepare_cupti(arch=platform)
        # prepare sample
        cmd = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['PREPARE']['CMD'] % (cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path)
        check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
        # profile sample list
        sample_list = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['SAMPLE_LIST'].split(',')
        print("sample_list is %s " % sample_list)
        sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'], 'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                       'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                       'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                       'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                       'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                       'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                       }
        # Get checkpoints
        checkpoint = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['CHECK_POINT']
        checkpoint_optix = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['CHECK_POINT_OPTIX']
        GRAPHICS_SAMPLES_EXECUTION_DURATION = 8

        def thread1(i_sample, i_step_cmd):
            if i_sample in ['optixPathTracer'] and '-e application --cblProfiling 1' in i_step_cmd:
                check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint_optix, flag=1)
            else:
                check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint, flag=2)

        def thread2(i_sample, i_step_cmd):
            # Class WindowManager is defined in common_utils.py
            myWM = WindowManager()
            title_name = sample_dict[i_sample][0]
            class_name = sample_dict[i_sample][1]
            hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
            print("HWND is %s" % hwnd)
            time.sleep(2)
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(2)
            print("Closed window by HWND %s ---- cmd: %s" % (hwnd, i_step_cmd))

        def run(i_sample, i_step_cmd):
            t1 = threading.Thread(target=thread1, args=(i_sample, i_step_cmd))
            t2 = threading.Thread(target=thread2, args=(i_sample, i_step_cmd))
            t1.start()
            time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)  # Set the graphics samples running x seconds
            t2.start()
            t1.join()
            t2.join()
        #
        for sample in sample_list:
            if cuda_short_version >= '12.1':
                step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1121'] % (cupti_target_path, sample, sample)
                if cuda_short_version >= '12.2':
                    if sample in ['optixPathTracer']:
                        step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1122_OPTIX'] % (cupti_target_path, sample, sample)
                    else:
                        step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1122'] % (cupti_target_path, sample, sample)
                if sample in ['optixPathTracer']:
                    step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP2']['CMD1121_OPTIX'] % (cupti_target_path, sample, sample)
                    step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP3']['CMD1121_OPTIX'] % (cupti_target_path, sample, sample)
                else:
                    step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP2']['CMD1121'] % (cupti_target_path, sample, sample)
                    step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP3']['CMD1121'] % (cupti_target_path, sample, sample)
            else:
                step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
                step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
                step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)

            # Run Step 1
            run(sample, step1_cmd1)
            time.sleep(2)
            # Run Step 2
            run(sample, step2_cmd1)
            time.sleep(2)
            # Run Step 3
            run(sample, step3_cmd1)
            time.sleep(2)

        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/profile_injection_graphic_coverage.json' % log_path)
        return result, passed, failed
    else:
        print('not support this case on tesla GPU, Ampere/Hopper/Blackwell')


@print_run_time
def profile_injection_graphic_coverage_old_api_3897007():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3]:
        # 1. Not support -e application for Graph Samples now. (see DTCUPTI-2023).
        # 2. Option -o is removed since the new design in 12.1.
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['LOG_NAME']
        log_path = case_config['global']['env']['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API_LOG_PATH']
        mkdir(log_path)
        # get the driver mode on Windows
        mode = common_get_driver_mode()
        print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
        if mode in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        else:
            # prepare cupti smoke package
            prepare_cupti(arch=platform)
            # prepare sample
            cmd = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['PREPARE']['CMD'] % (cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path)
            check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
            # profile sample list
            sample_list = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['SAMPLE_LIST'].split(',')
            print("sample_list is %s " % sample_list)
            sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'], 'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                           'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                           'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                           'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                           'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                           'optixPathTracer': ['optixPathTracer', 'GLFW30'],
                           }
            # Get checkpoints
            checkpoint = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['CHECK_POINT']
            checkpoint_optix = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['CHECK_POINT_OPTIX']
            GRAPHICS_SAMPLES_EXECUTION_DURATION = 10

            def thread1(i_sample, i_step_cmd):
                if i_sample in ['optixPathTracer'] and '-e application --cblProfiling 1' in i_step_cmd:
                    check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint_optix, flag=1)
                else:
                    check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint, flag=2)

            def thread2(i_sample, i_step_cmd):
                # Class WindowManager is defined in common_utils.py
                myWM = WindowManager()
                title_name = sample_dict[i_sample][0]
                class_name = sample_dict[i_sample][1]
                hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                print("HWND is %s" % hwnd)
                time.sleep(2)
                win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                time.sleep(6)
                print("Closed window by HWND %s ---- cmd: %s" % (hwnd, i_step_cmd))
                try:
                    if myWM.find_window_wildcard(class_name, "%s.*?" % title_name):
                        time.sleep(2)
                        win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                        time.sleep(6)
                        print("closed window again by HWND %s ---- cmd: %s" % (hwnd, i_step_cmd))
                except Exception as e:
                    print("occured error %s" % str(e))

            def run(i_sample, i_step_cmd):
                t1 = threading.Thread(target=thread1, args=(i_sample, i_step_cmd))
                t2 = threading.Thread(target=thread2, args=(i_sample, i_step_cmd))
                t1.start()
                time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)  # Set the graphics samples running x seconds
                t2.start()
                t1.join()
                t2.join()
            #
            for sample in sample_list:
                if cuda_short_version >= '12.1':
                    step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP1']['CMD1121'] % (cupti_target_path, sample, sample)
                    if cuda_short_version >= '12.2':
                        if sample in ['optixPathTracer']:
                            step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP1']['CMD1122_OPTIX'] % (cupti_target_path, sample, sample)
                        else:
                            step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP1']['CMD1122'] % (cupti_target_path, sample, sample)
                    if sample in ['optixPathTracer']:
                        step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP2']['CMD1121_OPTIX'] % (cupti_target_path, sample, sample)
                        step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP3']['CMD1121_OPTIX'] % (cupti_target_path, sample, sample)
                    else:
                        step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP2']['CMD1121'] % (cupti_target_path, sample, sample)
                        step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP3']['CMD1121'] % (cupti_target_path, sample, sample)
                else:
                    step1_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
                    step2_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
                    step3_cmd1 = case_config['PROFILE_INJECTION_GRAPHIC_COVERAGE_OLD_API']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)

                # Run Step 1
                run(sample, step1_cmd1)
                time.sleep(2)
                # Run Step 2
                run(sample, step2_cmd1)
                time.sleep(2)
                # Run Step 3
                run(sample, step3_cmd1)
                time.sleep(2)

            # calculate result
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '%s/profile_injection_graphic_coverage_old_api.json' % log_path)
            return result, passed, failed
    else:
        print('not support this case on tesla GPU, Ampere/Hopper/Blackwell')


@print_run_time
def trace_injection_graphic_coverage_3107566():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['LOG_NAME']
        log_path = case_config['global']['env']['TRACE_INJECTION_GRAPHIC_COVERAGE_LOG_PATH']
        mkdir(log_path)
        # get the driver mode on Windows
        mode = common_get_driver_mode()
        print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
        if mode in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        # prepare cupti dvs package
        prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower())
        # prepare sample
        cmd1 = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['PREPARE']['CMD1'] % (cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path)
        check_result(cmd1, 'prepare samples by %s' % cmd1, log_name, result)
        # prepare samples list
        sample_list = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['SAMPLE_LIST'].split(',')
        print("sample_list is %s " % sample_list)
        checkpoint = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['CHECKPOINT']
        GRAPHICS_SAMPLES_EXECUTION_DURATION = 8
        sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'], 'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                       'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                       'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                       'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                       'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                       'optixPathTracer': ['optixPathTracer', 'GLFW30']
                       }

        def run_trace_injection(i_sample):
            # prepare json file
            cmd2 = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['PREPARE']['CMD2'] % (i_sample, i_sample)
            check_result(cmd2, 'prepare test.json by %s' % cmd2, log_name, result)
            if cuda_short_version <= '11.4':
                dict1 = {"tests": []}
                dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
            else:
                dict1 = {"tests": []}
                dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            print('we will generate the run json file for graphic sample: %s' % i_sample)
            if 'cdp' in i_sample:
                dict1["tests"].append({"name": "%s" % i_sample, "CC": ['<70'], "exe": "%s.exe" % i_sample})
            else:
                dict1["tests"].append({"name": "%s" % i_sample, "exe": "%s.exe" % i_sample})
            # 11.4
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            print('the dict to generate the json file for graphic sample: %s ... \n%s' % (i_sample, str(dict2)))
            dict_to_json(dict2, '%s/test_%s.json' % (cupti_run_path, i_sample))
            #
            print('we will run trace injection for graphic sample: %s' % i_sample)
            if cuda_short_version > '11.4':
                i_step_cmd = cmd = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1_1'] % (i_sample, i_sample)
            else:
                i_step_cmd = cmd = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (i_sample, i_sample)

            print('running trace injection samples coverage for graphic sample: %s ... \n%s' % (i_sample, cmd))

            def thread1():
                check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint)

            def thread2():
                # Class WindowManager is defined in common_utils.py
                myWM = WindowManager()
                title_name = sample_dict[i_sample][0]
                class_name = sample_dict[i_sample][1]
                hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                print("HWND is %s" % hwnd)
                time.sleep(2)
                win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                time.sleep(2)
                print("Closed window by HWND %s ---- cmd: %s" % (hwnd, i_step_cmd))
            t1 = threading.Thread(target=thread1)
            t2 = threading.Thread(target=thread2)
            t1.start()
            time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)  # Set the graphics samples running x seconds
            time.sleep(5)
            t2.start()
            t1.join()
            time.sleep(2)
            t2.join()
            # run trace injection
        for sample in sample_list:
            run_trace_injection(sample)
            time.sleep(2)
        print("!!! A known open bug of Optix SDK samples: https://nvbugs/3926219 [CudaTools][12.1][Cupti]cupti trace fail with optix samples !!!")
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, output_flag)
        dict_to_json(result1, '%s/trace_injection_graphic_coverage.json' % log_path)
        return result, passed, failed
    else:
        print('not support this case on tesla GPU, Ampere/Hopper/Blackwell')


@print_run_time
def trace_injection_graphic_coverage_hes_5215367():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3] and cuda_short_version > '12.9' and sm > 9.0:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['LOG_NAME']
        log_path = case_config['global']['env']['TRACE_INJECTION_GRAPHIC_COVERAGE_HES_LOG_PATH']
        mkdir(log_path)
        # get the driver mode on Windows
        mode = common_get_driver_mode()
        print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
        # Not support Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
        if mode in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        # prepare cupti dvs package
        prepare_cupti(arch=case_config['global']['env']['PLATFORM'].lower())
        # prepare sample
        cmd1 = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['PREPARE']['CMD1'] % (cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path, cupti_target_path)
        check_result(cmd1, 'prepare samples by %s' % cmd1, log_name, result)
        # prepare samples list
        sample_list = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['SAMPLE_LIST'].split(',')
        print("sample_list is %s " % sample_list)
        checkpoint = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['CHECKPOINT']
        GRAPHICS_SAMPLES_EXECUTION_DURATION = 8
        sample_dict = {'simpleGL': ['Cuda GL Interop', 'FREEGLUT'], 'oceanFFT': ['CUDA FFT Ocean Simulation', 'FREEGLUT'],
                       'postProcessGL': ['CUDA GL Post Processing', 'FREEGLUT'],
                       'simpleD3D11': ['CUDA/D3D11 InterOP', 'CUDA SDK'],
                       'simpleD3D11Texture': ['CUDA/D3D11 Texture InterOP', 'CUDA SDK'],
                       'simpleD3D12': ['D3D12 CUDA Interop', 'DX12CudaSampleClass'],
                       'optixPathTracer': ['optixPathTracer', 'GLFW30']
                       }

        def run_trace_injection(i_sample):
            # prepare json file
            cmd2 = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['PREPARE']['CMD2'] % (i_sample, i_sample)
            check_result(cmd2, 'prepare test.json by %s' % cmd2, log_name, result)
            if cuda_short_version <= '11.4':
                dict1 = {"tests": []}
                dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
            else:
                dict1 = {"tests": []}
                dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            print('we will generate the run json file for graphic sample: %s' % i_sample)
            if 'cdp' in i_sample:
                dict1["tests"].append({"name": "%s" % i_sample, "CC": ['<70'], "exe": "%s.exe" % i_sample})
            else:
                dict1["tests"].append({"name": "%s" % i_sample, "exe": "%s.exe" % i_sample})
            # 11.4
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            print('the dict to generate the json file for graphic sample: %s ... \n%s' % (i_sample, str(dict2)))
            dict_to_json(dict2, '%s/test_%s.json' % (cupti_run_path, i_sample))
            #
            print('we will run trace injection for graphic sample: %s' % i_sample)
            if cuda_short_version > '11.4':
                i_step_cmd = cmd = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['STEP1']['CMD1_1'] % (i_sample, i_sample)
            else:
                i_step_cmd = cmd = case_config['TRACE_INJECTION_GRAPHIC_COVERAGE_HES']['STEP1']['CMD1'] % (i_sample, i_sample)

            print('running trace injection samples coverage for graphic sample: %s ... \n%s' % (i_sample, cmd))

            def thread1():
                check_result(i_step_cmd, 'run step1_%s by %s' % (i_sample, i_step_cmd), log_name, result, checkpoint)

            def thread2():
                # Class WindowManager is defined in common_utils.py
                myWM = WindowManager()
                title_name = sample_dict[i_sample][0]
                class_name = sample_dict[i_sample][1]
                hwnd = myWM.find_window_wildcard(class_name, "%s.*?" % title_name)
                print("HWND is %s" % hwnd)
                time.sleep(2)
                win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                time.sleep(2)
                print("Closed window by HWND %s ---- cmd: %s" % (hwnd, i_step_cmd))
            t1 = threading.Thread(target=thread1)
            t2 = threading.Thread(target=thread2)
            t1.start()
            time.sleep(GRAPHICS_SAMPLES_EXECUTION_DURATION)  # Set the graphics samples running x seconds
            time.sleep(5)
            t2.start()
            t1.join()
            time.sleep(2)
            t2.join()
        # run trace injection
        for sample in sample_list:
            run_trace_injection(sample)
            time.sleep(2)
        if search_keyword_in_file(log_name, 'Enabled HES tracing in CUPTI') and search_keyword_in_file(log_name, 'Requested HES to be enabled'):
            result['check_hes_information'] = 'passed'
        else:
            result['check_hes_information'] = 'failed'
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, output_flag)
        dict_to_json(result1, '%s/trace_injection_graphic_coverage_hes.json' % log_path)
        return result, passed, failed
    else:
        print('not support this case on tesla GPU, Ampere/Hopper/Blackwell and cuda version less than 13.0 and hopper--')


def optix_trace(sample, log_name, log_path, result):
    step_cmd1 = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['STEP']['CMD1'] % (cupti_run_path, sample, log_path, sample)
    step_cmd2 = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['STEP']['CMD2'] % (log_path, sample)
    check_result(step_cmd1, 'run_cupti trace optix by %s' % step_cmd1, log_name, result)
    check_result(step_cmd2, 'check trace optix result by %s' % step_cmd2, log_name, result, '1')
    return result


def close_optix_window(sample, graphic):
    if graphic:
        time.sleep(10)
        wm = WindowManager()
        hwnd = wm.find_window_wildcard('GLFW30', ".*?%s.*?" % sample)
        win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
    else:
        logger.info('sample %s no need to close window' % sample)


@print_run_time
def cupti_trace_optix_coverage_2828813():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0, 10.3]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_TRACE_OPTIX_COVERAGE_PATH']
        mkdir(log_path)
        if common_get_driver_mode() in ['TCC', 'MCDM']:
            logger.info('not support this case if the driver mode on Windows is TCC or MCDM.')
            return
        # prepare
        prepare_cupti(arch=platform)
        optix_bin_path = case_config['global']['env']['OPTIX_SAMPLE_BIN_PATH']
        prepare_cmd = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['PREPARE']['CMD1'] % (optix_bin_path, '*', cupti_target_path)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare optix bin file successful')
        else:
            logger.info('we prepare optix bin file fail, exit')
            exit(2)
        # fix bug: 4743974
        prepare_cmd2 = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['PREPARE']['CMD2'] % (cupti_target_path, user, password, base_url)
        check_result(prepare_cmd2, 'download synthetic.ptx  file by %s' % prepare_cmd2, log_name, result)
        prepare_cmd3 = case_config['CUPTI_TRACE_OPTIX_COVERAGE']['PREPARE']['CMD3'] % (cupti_target_path, user, password, base_url)
        check_result(prepare_cmd3, 'download synthetic.ptx  file by %s' % prepare_cmd3, log_name, result)
        sample_list = ['optixBoundValues', 'optixCallablePrograms', 'optixCompileWithTasks', 'optixConsole', 'optixCurves', 'optixCustomPrimitive', 'optixCutouts', 'optixDynamicGeometry',
                       'optixDynamicMaterials', 'optixHair', 'optixHello', 'optixMeshViewer', 'optixModuleCreateAbort', 'optixMotionGeometry', 'optixMultiGPU', 'optixNVLink', 'optixOpacityMicromap',
                       'optixOpticalFlow', 'optixPathTracer', 'optixRaycasting', 'optixSimpleMotionBlur', 'optixSphere', 'optixTriangle', 'optixVolumeViewer', 'optixWhitted']
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        for sample in sample_list:
            n2 = sample + '.exe'
            if sample in ['optixOpticalFlow', 'optixCompileWithTasks', 'optixDenoiser']:
                if sample == 'optixOpticalFlow':
                    n3 = "-o flow.exr soane-Beauty-001.exr soane-Beauty-002.exr"
                elif sample == 'optixCompileWithTasks':
                    n3 = "synthetic.ptx"
                else:
                    n3 = 'color.exr'
                dict1["tests"].append({"name": sample, "exe": n2, "exe-args": [n3]})
            else:
                dict1["tests"].append({"name": sample, "exe": n2})
        dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (cupti_run_path + '/optix.json'))
        # run the sample
        return_process = Manager().dict()
        for sample in sample_list:
            if sample in ['optixRaycasting']:
                graphic = False
            else:
                graphic = True
            step1 = Process(target=optix_trace, args=(sample, log_name, log_path, return_process))
            step2 = Process(target=close_optix_window, args=(sample, graphic))
            step1.start()
            step2.start()
            step1.join()
            step2.join()
            result.update(return_process)
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_trace_optix_coverage.json' % log_path)
        return result, passed, failed
    else:
        print('not support this case on tesla GPU, Ampere/Hopper/Blackwell')


@print_run_time
def sass_metrics_3306963():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['SASS_METRICS']['LOG_NAME']
    log_path = case_config['global']['env']['SASS_METRICS_PATH']
    mkdir(log_path)
    prepare_cmd = case_config['SASS_METRICS']['PREPARE']['CMD']
    check_result(prepare_cmd, 'build_sample by %s' % prepare_cmd, log_name, result)
    if SM < 7.0:
        step_cmd = case_config['SASS_METRICS']['STEP']['CMD']
        checkpoint = case_config['SASS_METRICS']['CHECK_POINT']
        check_result(step_cmd, 'run_sample by %s' % step_cmd, log_name, result, checkpoint, flag=1)
    else:
        step_cmd1 = case_config['SASS_METRICS']['STEP']['CMD1']
        step_cmd2 = case_config['SASS_METRICS']['STEP']['CMD2']
        step_cmd3 = case_config['SASS_METRICS']['STEP']['CMD3']
        step_cmd4 = case_config['SASS_METRICS']['STEP']['CMD4']
        checkpoint1_1 = case_config['SASS_METRICS']['CHECK_POINT1_1']
        checkpoint1_2 = case_config['SASS_METRICS']['CHECK_POINT1_2']
        checkpoint1_3 = case_config['SASS_METRICS']['CHECK_POINT1_3']
        checkpoint1_4 = case_config['SASS_METRICS']['CHECK_POINT1_4']
        checkpoint3_1 = case_config['SASS_METRICS']['CHECK_POINT3_1']
        checkpoint3_2 = case_config['SASS_METRICS']['CHECK_POINT3_2'] % SM
        checkpoint3_3 = case_config['SASS_METRICS']['CHECK_POINT3_3']
        checkpoint3_4 = case_config['SASS_METRICS']['CHECK_POINT3_4']
        check_result(step_cmd1, 'run_sample by %s' % step_cmd1, log_name, result, checkpoint1_1, checkpoint1_2, checkpoint1_3, checkpoint1_4)
        check_result(step_cmd2, 'run_sample by %s' % step_cmd2, log_name, result, 'ERROR', 'error', 'nan', flag=2)
        check_result(step_cmd3, 'run_sample by %s' % step_cmd3, log_name, result, checkpoint3_1, checkpoint3_2, checkpoint3_3)
        check_result(step_cmd4, 'run_sample by %s' % step_cmd4, log_name, result, checkpoint3_1, checkpoint3_2, checkpoint3_4)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sass_metrics.json' % log_path)
    return result, passed, failed


@print_run_time
def support_collect_sass_3481156():
    if cuda_short_version >= '12.2':
        sm = get_sm()
        if sm < 7.0:
            logger.info('we do not support this sample if sm less than 7.0')
        else:
            result = {}
            passed, failed = 0, 0
            log_name = case_config['SUPPORT_COLLECT_SASS']['LOG_NAME']
            log_path = case_config['global']['env']['SUPPORT_COLLECT_SASS_PATH']
            mkdir(log_path)
            prepare_cupti(arch=platform)
            # prepare samples
            prepare_cmd1 = case_config['SUPPORT_COLLECT_SASS']['PREPARE']['CMD1'].format(cupti_target_path, log_path)
            check_result(prepare_cmd1, 'prepare env  by  {}'.format(prepare_cmd1), log_name, result)
            # run the sample
            step1_cmd1 = case_config['SUPPORT_COLLECT_SASS']['STEP1']['CMD1']
            step1_cmd2 = case_config['SUPPORT_COLLECT_SASS']['STEP1']['CMD2']
            # os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']
            check_result(step1_cmd1, 'run the sample by {}'.format(step1_cmd1), log_name, result, 'error', 'ERROR', 'FAILED', flag=2)
            check_result(step1_cmd2, 'run the sample by {}'.format(step1_cmd2), log_name, result, 'error', 'ERROR', 'FAILED', flag=2)
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '{}/support_collect_sass.json'.format(log_path))
            return result, passed, failed
    else:
        logger.info('we do not support this case if cuda version less than 12.2')


@print_run_time
def pm_sampling_3767887():
    if cuda_short_version > '12.5':
        sm = get_sm()
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PM_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['PM_SAMPLING_PATH']
        mkdir(log_path)
        # build the checkpoint kernels sample
        step1_cmd = case_config['PM_SAMPLING']['STEP1']['CMD1']
        check_result(step1_cmd, 'build_pm_sampling-------%s' % step1_cmd, log_name, result)
        # run pm_sampling sample
        checkpoint1 = case_config['PM_SAMPLING']['CHECK_POINT1']
        checkpoint2 = case_config['PM_SAMPLING']['CHECK_POINT2']
        checkpoint3 = case_config['PM_SAMPLING']['CHECK_POINT3']
        checkpoint4 = case_config['PM_SAMPLING']['CHECK_POINT4']
        run_pm_sampling = case_config['PM_SAMPLING']['STEP2']['CMD1']
        if sm >= 7.5:
            check_result(run_pm_sampling, 'run_pm_sampling------%s' % run_pm_sampling, log_name, result, checkpoint1, checkpoint2, checkpoint3)
            check_result(run_pm_sampling, 'check result of pm_sampling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'error', 'n/a', 'fail', flag=2)
            pm_sampling_with_metric = case_config['PM_SAMPLING']['STEP3']['CMD1']
            check_result(pm_sampling_with_metric, 'run_pm_sampling_with_metric------%s' % pm_sampling_with_metric, log_name, result, checkpoint4, flag=1)
        else:
            check_result(run_pm_sampling, 'run_pm_sampling------%s' % run_pm_sampling, log_name, result, 'CUPTI_ERROR_NOT_SUPPORTED', flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pm_sampling.json' % log_path)
        return result, passed, failed
    else:
        logging.warning('support pm_sampling since 12.6 and turing++')


@print_run_time
def range_profiling_3860023():
    sm = get_sm()
    if cuda_short_version > '12.6' and sm >= 7.0:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['RANGE_PROFILING']['LOG_NAME']
        log_path = case_config['global']['env']['RANGE_PROFILING_PATH']
        mkdir(log_path)
        check_point1 = case_config['RANGE_PROFILING']['CHECK_POINT1']
        check_point2 = case_config['RANGE_PROFILING']['CHECK_POINT2']
        check_point3 = case_config['RANGE_PROFILING']['CHECK_POINT3']
        check_point4 = case_config['RANGE_PROFILING']['CHECK_POINT4']
        check_point5 = case_config['RANGE_PROFILING']['CHECK_POINT5']
        check_point6 = case_config['RANGE_PROFILING']['CHECK_POINT6']
        check_point7 = case_config['RANGE_PROFILING']['CHECK_POINT7']
        # build the checkpoint kernels sample
        step1_cmd = case_config['RANGE_PROFILING']['STEP1']['CMD1']
        check_result(step1_cmd, 'build_checkpoint_kernals-------%s' % step1_cmd, log_name, result)
        run_range_profiling1 = case_config['RANGE_PROFILING']['STEP2']['CMD1']
        run_range_profiling2 = case_config['RANGE_PROFILING']['STEP2']['CMD2']
        run_range_profiling3 = case_config['RANGE_PROFILING']['STEP2']['CMD3']
        run_range_profiling4 = case_config['RANGE_PROFILING']['STEP2']['CMD4']
        run_range_profiling5 = case_config['RANGE_PROFILING']['STEP2']['CMD5']
        run_range_profiling6 = case_config['RANGE_PROFILING']['STEP2']['CMD6']
        if sm >= 7.0:
            # run range_profiling
            check_result(run_range_profiling1, 'for step2_cmd1, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=2)
            check_result(run_range_profiling2, 'for step2_cmd2, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=2)
            check_result(run_range_profiling3, 'for step2_cmd3, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=2)
            # check_result(run_range_profiling4, 'for step2_cmd4, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling5, 'for step2_cmd5, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=2)
            check_result(run_range_profiling6, 'for step2_cmd6, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=2)
            check_result(run_range_profiling1, 'run step2_cmd1------{}'.format(run_range_profiling1), log_name, result, check_point1, check_point3)
            check_result(run_range_profiling2, 'run step2_cmd2------{}'.format(run_range_profiling2), log_name, result, check_point1, check_point4)
            check_result(run_range_profiling3, 'run step2_cmd3------{}'.format(run_range_profiling3), log_name, result, check_point2, check_point3)
            check_result(run_range_profiling4, 'run step2_cmd4------{}'.format(run_range_profiling4), log_name, result, check_point2, check_point4, 'CUPTI_ERROR_INVALID_OPERATION', flag=1)
            check_result(run_range_profiling5, 'run step2_cmd5------{}'.format(run_range_profiling5), log_name, result, check_point5, check_point6, check_point7)
            check_result(run_range_profiling6, 'run step2_cmd6------{}'.format(run_range_profiling6), log_name, result, check_point1, check_point3)
        else:
            check_result(run_range_profiling1, 'for step2_cmd7, run_range_profiling on gpu whose sm less than 70------%s' % run_range_profiling1, log_name, result, 'device architecture is not supported', flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/range_profiling.json' % log_path)
        return result, passed, failed
    else:
        logging.warning('support range_profiling since 12.7 and volta++')


@print_run_time
def cupti_profile_coverage_old_api_3896463():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_PROFILE_COVERAGE_OLD_API_PATH']
    mkdir(log_path)
    # get the driver mode on Windows
    mode = common_get_driver_mode()
    print(">>>>>>>>>>The driver mode is %s<<<<<<<<<<" % mode)
    # prepare cupti smoke package
    prepare_cupti(arch=platform)
    # prepare sample
    cmd = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CMD'] % cupti_target_path
    check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
    # profile sample list
    sample_list = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['SAMPLE_LIST'].split(',')
    # vectorAddMMAP can not run with TCC on Windows ===> added judgement in code
    if mode in ['TCC', 'MCDM']:
        sample_list.remove("vectorAddMMAP")
        print("vectorAddMMAP can not run with TCC or MCDM on Windows ===> removed vectorAddMMAP in the Samples List")
    print("sample_list is %s " % sample_list)
    # Get checkpoints
    checkpoint = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CHECK_POINT']
    checkpoint_graph_step1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CHECK_POINT_GRAPH_SAMPLES_STEP1']
    checkpoint_graph_step2 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CHECK_POINT_GRAPH_SAMPLES_STEP2']
    checkpoint_graph_step3 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CHECK_POINT_GRAPH_SAMPLES_STEP3']
    for sample in sample_list:
        if cuda_short_version >= '12.1':
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP1']['CMD1121'] % (cupti_target_path, sample, sample)
            step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP2']['CMD1121'] % (cupti_target_path, sample, sample)
            step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP3']['CMD1121'] % (cupti_target_path, sample, sample)
        else:
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
            step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
            step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)

        if sample in ['systemWideAtomics', 'UnifiedMemoryStreams']:
            if cuda_short_version >= '12.1':
                step2_cmd1 = r"cd %s ; .\profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -r auto -e application -j step2_%s.json -a %s.exe" % (cupti_target_path, sample, sample)
            else:
                step2_cmd1 = r"cd %s ; .\profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -o counterdata -r auto -e application -j step2_%s.json -a %s.exe" % (cupti_target_path, sample, sample)
        # Run profile injection
        if sample == 'simpleCudaGraphs':
            # https://nvbugs/4663598 [Auto]P1072_T2924950 -- need update checkpoint for user replay mode Updated on 2024.06.18
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, checkpoint_graph_step1)
            check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, checkpoint_graph_step2)
            check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, checkpoint_graph_step3)
        else:
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, checkpoint, flag=2)
            check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, checkpoint, flag=2)
            check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, checkpoint, flag=2)
    # run nvtx range
    if cuda_short_version >= '12.1':
        step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD1121'] % cupti_target_path
        step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD2121'] % cupti_target_path
    else:
        step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD1'] % cupti_target_path
        step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD2'] % cupti_target_path
    check_result(step4_cmd1, 'run step4_cmd1 by %s' % step4_cmd1, log_name, result, checkpoint, flag=2)
    check_result(step4_cmd2, 'run step4_cmd1 by %s' % step4_cmd2, log_name, result, checkpoint, flag=2)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_coverage_old_api.json' % log_path)
    return result, passed, failed


@print_run_time
def cupti_nvtx_ext_payload():
    if cuda_short_version > '12.9':
        result = {}
        log_name = case_config['CUPTI_NVTX_EXT_PAYLOAD']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_NVTX_EXT_PAYLOAD_PATH']
        mkdir(log_path)
        # prepare sample
        prepare_cmd1 = case_config['CUPTI_NVTX_EXT_PAYLOAD']['PREPARE']['CMD1']
        check_result(prepare_cmd1, 'prepare_sample by {}'.format(prepare_cmd1), log_name, result)
        if cuda_short_version == '13.0':
            cupti_lib = 'cupti64_2025.3.0.dll'
        step1_cmd1 = case_config['CUPTI_NVTX_EXT_PAYLOAD']['STEP1']['CMD1'].format(cupti_lib)
        check_result(step1_cmd1, 'run cmd to check no error information by {}'.format(step1_cmd1), log_name, result, 'fail', flag=2)
        check_result(step1_cmd1, 'run step1_cmd1 to check the expected message by {}'.format(step1_cmd1), log_name, result, 'Content of nvtx_payload_blob.txt', 'Parsing NVTX extended payload address', 'CUPTI_ACTIVITY_KIND_MARKER', 'CUPTI_ACTIVITY_KIND_MARKER_DATA', 'MARKER_DATA id')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_nvtx_ext_payload.json'.format(log_path))
        return result
    else:
        logger.info('support this case since cuda 13.0')


example_text = """
Example:
python run_cupti_case.py # run all the function
python run_cupti_case.py -e cupti_static # run single case
python run_cupti_case.py -el cupti_static,cupti_smoke # run multi cases
"""
reversion = '1.0'
check_list = ['cupti_injection', 'activity_trace', 'activity_trace_async', 'pc_sampling', 'concurrent_profiling', 'cupti_library', 'cupti_trace', 'cupti_guard', 'cupti_static',
              'event_multi_gpu', 'sass_source_map', 'cupti_sample', 'openacc_trace',
              'cupti_metric_properties', 'nvlink_bandwidth', 'unified_memory', 'cupti_trace_coverage', 'cupti_callback_event',
              'cupti_query', 'callback_metric', 'callback_event', 'callback_timestamp', 'cupti_event_sampling', 'cupti_callback_metric', 'cupti_extra_replay',
              'pc_sampling_continous', 'event_sampling', 'cupti_parallel_launch', 'pc_sampling_utility', 'cupti_nvtx_ext_payload',
              'pc_sampling_start_stop', 'cuda_graphs_trace', 'cupti_nvtx', 'userrange_profiling', 'callback_profiling', 'autorange_profiling', 'nested_range_profiling',
              'checkpoint_kernels', 'cupti_external_correlation', 'cupti_2_profiling', 'cupti_query_sample', 'cuda_memory_trace', 'pm_sampling_3767887', 'range_profiling_3860023',
              'cupti_profile_coverage', 'cupti_profile_coverage_old_api_3896463', 'cupti_trace_injection', 'cupti_correlation', 'cupti_build_all_samples', 'pc_sampling_coverage_2804521', 'pc_sampling_graphic_coverage_2941255',
              'profile_injection_graphic_coverage_3107112', 'profile_injection_graphic_coverage_old_api_3897007', 'trace_injection_graphic_coverage_3107566', 'cupti_trace_optix_coverage_2828813', 'sass_metrics_3306963',
              'support_collect_sass_3481156', 'trace_injection_graphic_coverage_hes_5215367', 'cupti_trace_coverage_hes_5215382']


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-c", "--config-file", dest="config_file", required=False,
        help='Specify test index file. e.g. cases')
    parser.add_argument(
        "-a", "--arch", dest="arch", required=False,
        help='Specify smoke test on P9 or x86 or ARM')
    parser.add_argument(
        "-e", "--excute", action='append', required=False,
        choices=check_list,
        help="Specify function to be run.")
    parser.add_argument(
        "-el", "--excute_list", action='store', required=False,
        help="Specify multi function to be run.")
    args = parser.parse_args()

    case_str = args.excute_list
    case_list_single = args.excute
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            getattr(mod, case)()
    elif case_list_single:
        mod = sys.modules["__main__"]
        getattr(mod, case_list_single[0])()
    else:
        pass
