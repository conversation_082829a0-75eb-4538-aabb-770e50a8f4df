global:
  env:
    CUDA_MAJOR: 13
    CUDA_MINOR: 0
    CUDA_REVISION: 4
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: 580
    DRIVER_VERSION: 580.39
    DEVICES: GPU-d14bec61-148c-234a-7772-fd20ad12f7bf
    DVS_BUILD: CUDA13.0
    PLATFORM: x86_win
    INSTALLER: exe
    HOST_HOME: "C:"
    RESULTS_HOME: ${HOST_HOME}/tesla_automation_results
    TOOLS_HOME: ${RESULTS_HOME}/external_device${DEVICES}
    DATE: 20250717171836
    DATE1: 20250717
    HOST_P4_PATH: ${HOST_HOME}/p4
    CQA_USER: Y3FhdXNlcg
    CQA_PASSWORD: Y3FhdGVzdA
    SAMPLE_PATH: ${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}
    SAMPLE_1_PATH: "${SAMPLE_PATH}/Samples/1_Utilities"
    SAMPLE_0_PATH: "${SAMPLE_PATH}/Samples/0_Introduction"
    SAMPLE_0_PATH1: "${SAMPLE_PATH}/0_Simple"
    SAMPLE_6_PATH1: "${SAMPLE_PATH}/6_Advanced"
    SAMPLE_6_PATH: "${SAMPLE_PATH}/Samples/6_Performance"
    SAMPLE_1_PATH1: "${SAMPLE_PATH}/1_Utilities"
    SAMPLE_2_PATH: "${SAMPLE_PATH}/Samples/2_Concepts_and_Techniques"
    SAMPLE_0_PATH2: "${SAMPLE_PATH}/build/Samples/0_Introduction"
    SAMPLE_1_PATH2: "${SAMPLE_PATH}/build/Samples/1_Utilities"
    SAMPLE_2_PATH2: "${SAMPLE_PATH}/build/Samples/2_Concepts_and_Techniques"
    SAMPLE_6_PATH2: "${SAMPLE_PATH}/build/Samples/6_Performance"
    BASE_PATH: "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v${CUDA_SHORT_VERSION}/bin"
    NCU_PATH: "C:/Program Files/NVIDIA Corporation/nsight-compute"
    DEVENV: "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv"
    CMEDER_PATH: "C:/CTT/cmder"
    CMDER_BIN_PATH: "${CMEDER_PATH}/vendor/git-for-windows/usr/bin"
    PYTHON: "C:/Python37/python.exe"
    SAMPLE_BIN_PATH: "${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}/bin/win64/Debug"
    SAMPLE_BIN_PATH_RELEASE: "${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}/bin/win64/Release"
    OPTIX_HOME: ${HOST_HOME}/tesla_automation_optix
    OPTIX_SDK_VERSION: "9.0" # Updated since CUDA 12.8
    OPTIX_BIN_PATH: ${OPTIX_HOME}/${OPTIX_SDK_VERSION}/bin/Debug
    HOST_PASSWORD: labuser
    OUTPUT_FLAG: 1
    SCPRIT_HOME: ${TOOLS_HOME}/scripts
    CUDA_APP_PATH: ${RESULTS_HOME}/cuda_app_${DATE1}
    CUDA_APP_RUN_PATH: ${CUDA_APP_PATH}/tests/runtime
    CUPTI_PATH: ${RESULTS_HOME}/cupti_${DATE1}
    CUPTI_HOST_PATH: ${CUPTI_PATH}/host/windows-desktop-win7-x64
    CUPTI_RUN_PATH: ${CUPTI_PATH}/target/windows-desktop-win7-x64
    REBEL_PATH: ${RESULTS_HOME}/rebel_${DATE1}
    REBEL_RUN_PATH: ${REBEL_PATH}/Rebel-Release-public-test/target/windows-desktop-win7-x64/cuda
    SANITIZER_PATH: ${RESULTS_HOME}/sanitizer_${DATE1}
    SANITIZER_RUN_PATH: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test
    TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE_PATH: ${TOOLS_HOME}/3570628_tools_support_vanadium_python_sample_result_${DATE}
    LOG_PATH: ${TOOLS_HOME}/%s_result_${DATE}
    LOG_NAME: ${LOG_PATH}/%s.log
    LOG_JSON: ${LOG_PATH}/%s.json


# get the SM value of GPU and name of GPU
PREPARE:
    CMD1: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"
    CMD2: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'Device 0'
    CMD3: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'
    CMD4: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'Device 0'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'
    CMD_TCC_WDDM: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'CUDA Device Driver Mode (TCC or WDDM)'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'

SAMPLE:
  CMD1: ${CMDER_BIN_PATH}/cp %s/%s %s
  #382 367 381 714 433
  ALLOCA: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T2667659/test/kernel.cu; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T2667659/test/common.h; cmd.exe /c "${BASE_PATH}/nvcc" -g -G -lcuda -lcudart -gencode arch=compute_%s,code=sm_%s -o alloca kernel.cu
  FB16-BASIC: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3061141/Windows/fp16-basic.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -arch sm_%s -o fp16-basic fp16-basic.cu
  RELATIVE-RETUREN: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/relative-return/relative-return-gdb.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -lcuda -lcudart -gencode arch=compute_%s,code=sm_%s -o relative-return relative-return-gdb.cu
  CUNVTXCONTEXTSTREAM: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3131437/nvtx_context_stream.cu; cmd.exe /c "${BASE_PATH}/nvcc" -g -G -lcuda -o CuNvtxContextStream nvtx_context_stream.cu
  VECTORADDDRV_1: cd "%s"; ${CMDER_BIN_PATH}/cp -r vectorAddDrv vectorAddDrv1; cd "%s/vectorAddDrv1"; ${CMDER_BIN_PATH}/rm vectorAddDrv.cpp; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3085429/vectorAddDrv.cpp; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
  VECTORADDDRV_2: ${CMDER_BIN_PATH}/cp "%s/vectorAddDrv1/data/vectorAdd_kernel64.fatbin" %s; ${CMDER_BIN_PATH}/cp "%s/%s" %s; cd "%s/vectorAddDrv"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
  PREPARE_CMD: cd "%s"; ${CMDER_BIN_PATH}/rm -rf vectorAddDrv_readonly; ${CMDER_BIN_PATH}/mkdir vectorAddDrv_readonly; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
  VECTORADDDRV_READONLY_CMD1: cd "%s/vectorAddDrv_readonly"; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3085433/%s
  VECTORADDDRV_READONLY_CMD2: cd "%s/vectorAddDrv_readonly"; cmd.exe /c "${BASE_PATH}/nvcc" -I ../Common -fatbin -g -G -o vectorAdd_kernel64.fatbin vectorAdd_kernel.cu; cmd.exe /c "${BASE_PATH}/nvcc" -I ../Common -lcuda -g -G -o vectorAddDrv_readonly.exe vectorAddDrv_readonly.cpp; cp -r * %s
  VECTORADDDRV_READONLY_CMD3: cp %s/vectorAddDrv_readonly/* %s
  STREANORDERED: cd "%s/streamOrderedAllocation"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "%s"; ${CMDER_BIN_PATH}/cp %s %s
  TEST: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/cta-reconfig-test/%s
  L2DESCRIPTOR: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3069379/kernel.cu; cmd.exe /c "${BASE_PATH}/nvcc" %s -arch sm_%s -o l2descriptor kernel.cu
  PIL: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3130396/Windows/pil-sample.exe;
  VECTORATOMIC: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_3192538/vector_atomics_globals.cu; cmd.exe /c "${BASE_PATH}/nvcc" -g -lineinfo -arch sm_%s -o vector_atomics_globals vector_atomics_globals.cu
  SUBTEST: multithread_single_graph_multi_stream multithread_single_stream_multi_graph multithread_multi_stream_multi_graph_fork_join multithread_multi_stream_multi_graph_straight_line multithread_null_blocking_stream_multi_graph
  SUB2_CLUSTER_TEST: -t DSMEM_AddressManipulationPrimitives_Immediate_CG DSMEM_AddressManipulationPrimitives_Immediate_PTX DSMEM_AddressManipulationPrimitives_StreamCap_PTX DSMEM_BasicDSMEMAccessibility_Immediate_Intrinsic DSMEM_BasicDSMEMAccessibility_StreamCap_PTX
  SUB_CLUSTER_TEST: -t Enumeration_ClusterDimension_FuncAttr_Immediate_CNP_CG Enumeration_ClusterDimension_FuncAttr_Immediate_CNP_Intrinsic Enumeration_ClusterDimension_FuncAttr_Immediate_CNP_PTX Enumeration_ClusterDimension_KernelSrcAttr_Immediate_CNP_CG Enumeration_ClusterDimension_KernelSrcAttr_Immediate_CNP_Intrinsic Enumeration_ClusterDimension_KernelSrcAttr_Immediate_CNP_PTX
  SUB_FPIFP: -t launch_function_pointer_non_unified launch_function_pointer_unified launch_virtual_function lazy_function_loading_udt loading_with_uft_and_udt loading_without_uft_or_udt
  SUB_UNIVERSAL_FUNCTION_POINTERS: -t launch_function_pointer_non_unified launch_function_pointer_unified launch_virtual_function
  SUB2_UNIVERSAL_FUNCTION_POINTERS: -t launch_function_pointer_non_unified launch_function_pointer_unified launch_virtual_function lazy_function_loading_udt
  SUB_KERNEL_LARGE_ARGS: -t test_32K_kernel_args
  SUB_CUDAIPCSANITY: -t cuda_ipc_sanity_sync_memops_memcpy cuda_ipc_sanity_sync_memops_memcpy_late_sync cuda_ipc_sanity_sync_memops_memset cuda_ipc_sanity_sync_memops_memset_late_sync
  SUB_MMAP_API: -t memmap_ipc_basic_sanity
  CTARECONFIG: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3134541/Linux/main.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -arch sm_90a -o ctaReconfig main.cu
  # CTARECONFIG_1: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3134541/Linux/main.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -gencode arch=compute_90a,code=sm_90a -o ctaReconfig main.cu
  CTARECONFIG_1: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3134541/Windows/ctaReconfig-test/ctaReconfig-test/kernel.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -gencode arch=compute_90a,code=sm_90a -o ctaReconfig kernel.cu
  PILDEFAULT: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3556126/Linux/kernel.cu; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -o pilDefault kernel.cu
  NVFATBIN:  cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T3564490/nvFatbin_basic/online.cpp; cmd.exe /c "${BASE_PATH}/nvcc" -lineinfo -arch sm_%s -g online.cpp -lnvrtc -lcuda -lnvfatbin -o nvFatbin
  VANADIUM_SAMPLE: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/vanadium_sample/vanadium_examples-main.zip; ${CMDER_BIN_PATH}/unzip vanadium_examples-main.zip; ${CMDER_BIN_PATH}/cp -r vanadium_examples-main/* .

SANITIZER:
  CMD1: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --report-api-errors no %s
  CMD2: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool initcheck %s
  CMD3: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool synccheck %s
  CMD4: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool racecheck %s
  CMD5: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --target-processes all %s -t %s
  multithread: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --target-processes all %s --no-tools-callbacks
  MMAP_CMD1: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --target-processes all %s -t memmap_ipc_basic_sanity
  MMAP_CMD2: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool initcheck --target-processes all %s -t memmap_ipc_basic_sanity
  MMAP_CMD3: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool synccheck --target-processes all %s -t memmap_ipc_basic_sanity
  MMAP_CMD4: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool racecheck --target-processes all %s -t memmap_ipc_basic_sanity
  SUB_CMD1: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --target-processes all %s %s
  SUB_CMD2: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool initcheck --target-processes all %s %s
  SUB_CMD3: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool synccheck --target-processes all %s %s
  SUB_CMD4: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool racecheck --target-processes all %s %s
  GRAP_CMD1: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" %s memcpy-memset
  GRAP_CMD2: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool initcheck %s memcpy-memset
  GRAP_CMD3: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool synccheck %s memcpy-memset
  GRAP_CMD4: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool racecheck %s memcpy-memset
  OPTIX_CMD1: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --leak-check full --check-optix-leaks yes %s
  OPTIX_CMD2: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 50 --check-optix yes %s
  CTA: cd %s; cmd.exe /c "${BASE_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace no %s
  CTA_CHECK: 'ERROR SUMMARY: 9 errors'
  CNP_CHECK: 'Warning: CUDA Dynamic Parallelism is not supported by the selected tool'
  CHECK_POINT1: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT2: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT3: 'ERROR SUMMARY: 1 error'
  CHECK_POINT4: 'ERROR SUMMARY: 2 errors'
  CHECK_POINT5: 'RACECHECK SUMMARY: 100 hazards displayed (161 errors, 0 warnings)'

MEMCHECK:
  CMD1: cd %s; cmd.exe /c "${BASE_PATH}/cuda-memcheck" %s
  CMD2: cd %s; cmd.exe /c "${BASE_PATH}/cuda-memcheck" --tool initcheck %s
  CMD3: cd %s; cmd.exe /c "${BASE_PATH}/cuda-memcheck" --tool synccheck %s
  CMD4: cd %s; cmd.exe /c "${BASE_PATH}/cuda-memcheck" --tool racecheck %s
  CHECK_POINT1: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT2: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT3: 'Internal Memcheck Warning: Detected use of unsupported CUDA memory pools.'

NCU:
  CMD1: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full %s
  CMD1_1: cd %s; cmd.exe /c "${NCU_PATH}/ncu" -c 5 --set full %s
  CMD2: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --target-processes all %s -t %s
  CMD3: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full %s --nocheck
  CMD4: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --replay range --set full %s
  multithread: cd %s; cmd.exe /c "${NCU_PATH}/ncu" -c 5 --target-processes all %s --no-tools-callbacks -t memset staged_memcpy
  mmap_api: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --target-processes all --set full %s -t memmap_ipc_basic_sanity
  CuGraphNodeDisable: cd %s; cmd.exe /c "${NCU_PATH}/ncu" %s memcpy-memset
  test: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full %s kernel.sm_90.cubin
  sibling_CMD1: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --graph-profiling graph %s
  sibling_CMD2: cd %s; cmd.exe /c "${NCU_PATH}/ncu" -c 5 %s
  sibling_WARN1: '==WARNING== Graph kernel nodes that can launch device graphs are not supported for profiling'
  sibling_WARN1_1: 'contains one or more device-side graph launches. Profiling result will not include those launches'
  sibling_WARN2: '==WARNING== No kernels were profiled'
  CMD2_GRAPH: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full --graph-profiling graph -f -o graph_profiling %s
  CMD1_GRAPH: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full -f -o node_profiling %s
  ConNode_WARN1: '==WARNING== Kernel nodes of a graph which can have conditional nodes are not supported for profiling'
  ConNode_WARN2: '==WARNING== No kernels were profiled'
  CNP_CMD1: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --metrics launch__uses_cdp -c 1 %s
  CNP_CMD2: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full --metrics inst_executed --page source --print-source sass %s -t cnp_simple_chain_launch
  CMD_SPECIAL1: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --metrics sm__ops_path_tensor_op_bgmma_src_int1,sm__ops_path_tensor_op_bmma_src_int1,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32_sparsity_off,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32_sparsity_on,sm__ops_path_tensor_op_hgmma_src_fp16,sm__ops_path_tensor_op_hgmma_src_fp16_sparsity_off,sm__ops_path_tensor_op_hgmma_src_fp16_sparsity_on,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32_sparsity_off,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32_sparsity_on,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32_sparsity_off,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32_sparsity_on,sm__ops_path_tensor_op_hmma_src_fp16,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16_sparsity_off,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16_sparsity_on,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32_sparsity_off,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32_sparsity_on,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32_sparsity_off,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32_sparsity_on,sm__ops_path_tensor_op_igmma_src_int8,sm__ops_path_tensor_op_igmma_src_int8_sparsity_off,sm__ops_path_tensor_op_igmma_src_int8_sparsity_on,sm__ops_path_tensor_op_imma_src_int8,sm__ops_path_tensor_op_imma_src_int8_sparsity_off,sm__ops_path_tensor_op_imma_src_int8_sparsity_on,sm__ops_path_tensor_src_bf16_dst_fp32,sm__ops_path_tensor_src_fp16,sm__ops_path_tensor_src_fp64,sm__ops_path_tensor_src_fp8,sm__ops_path_tensor_src_fp8_sparsity_off,sm__ops_path_tensor_src_fp8_sparsity_on,sm__ops_path_tensor_src_int1,sm__ops_path_tensor_src_int8,sm__ops_path_tensor_src_tf32_dst_fp32 %s
  CMD_SPECIAL2: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --metrics sm__sass_inst_executed.sum %s -l 4 -q 8
  CMD_SPECIAL3: cd %s; cmd.exe /c "${NCU_PATH}/ncu" --set full  --replay-mode app-range %s 1 1 1 0
  CNP_CHECK1: 'launch__uses_cdp                        1'
  CNP_CHECK2: 'stream_launcher()'
  CNP_CHECK3: 'spin()'
  CNP_CHECK4: 'cudaCDP2GetParameterBufferV2'
  CNP_CHECK5: 'cudaCDP2LaunchDeviceV2'
  CNP_CHECK6: 'cudaCDP2StreamCreateWithFlags'
  CHECK_POINT1: 'error'
  CHECK_POINT2: 'n/a'
  CHECK_POINT3: 'N/A'
  CHECK_POINT4: 'ERROR'
  CHECK_POINT5: 'nan'

CUPTI:
  CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -r auto -e kernel -j step1_output.json -a %s
  CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -n 5 -r auto -e application -j step2_output.json -a %s
  CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -n 5 -r user -e application -j step3_output.json -a %s
  CHECK_POINT: 'ERROR'
  CHECK_POINT2: 'nan'
  CHECK_POINT3: 'n/a'
  CMD4: cd %s; ${PYTHON} CuptiSmoke.py --testconfig tracing-injection --testlist %s |${CMDER_BIN_PATH}/tee %s/cupti_injection_%s.txt
  CMD5: cd %s; tail cupti_injection_%s.txt|grep PASSED|awk -F ' ' '{print $1}'
  FP16_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__inst_executed_pipe_fma_type_fp16.avg.pct_of_peak_sustained_active -n 5 -r auto -e kernel -j step1_output.json -a %s
  TMA_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__m_xbar2l1tex_read_sectors_mem_global_op_tma_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_red.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_st.sum,smsp__inst_executed_op_tma_ld.sum,smsp__inst_executed_op_tma_st.sum -n 5 -r auto -e kernel -j step1_output.json -a %s
  TMA_CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__m_xbar2l1tex_read_sectors_mem_global_op_tma_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_red.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_st.sum,smsp__inst_executed_op_tma_ld.sum,smsp__inst_executed_op_tma_st.sum -n 5 -r auto -e application -j step2_output.json -a %s
  TMA_CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__m_xbar2l1tex_read_sectors_mem_global_op_tma_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_red.sum,l1tex__m_l1tex2xbar_write_sectors_mem_global_op_tma_st.sum,smsp__inst_executed_op_tma_ld.sum,smsp__inst_executed_op_tma_st.sum -n 5 -r user -e application -j step3_output.json -a %s
  CLUSTER_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_inst_executed_op_dshared_ld.sum,smsp__sass_inst_executed_op_dshared_st.sum,smsp__sass_inst_executed_op_dshared_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_ld.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_st.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_atom.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_ld.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_atom.sum -n 5 -r auto -e kernel -j step1_output.json -a "%s -t DSMEM_AddressManipulationPrimitives_Immediate_CG"
  CLUSTER_CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_inst_executed_op_dshared_ld.sum,smsp__sass_inst_executed_op_dshared_st.sum,smsp__sass_inst_executed_op_dshared_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_ld.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_st.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_atom.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_ld.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_atom.sum -n 5 -r auto -e application -j step2_output.json -a "%s -t DSMEM_AddressManipulationPrimitives_Immediate_CG"
  CLUSTER_CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_inst_executed_op_dshared_ld.sum,smsp__sass_inst_executed_op_dshared_st.sum,smsp__sass_inst_executed_op_dshared_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_ld.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_atom.sum,l1tex__t_requests_pipe_lsu_mem_dshared_op_st.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_atom.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_ld.sum,l1tex__m_l1tex2xbar_write_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_ld.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_st.sum,l1tex__m_xbar2l1tex_read_sectors_mem_dshared_op_atom.sum -n 5 -r user -e application -j step3_output.json -a "%s -t DSMEM_AddressManipulationPrimitives_Immediate_CG"
  SUB_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__warps_launched.sum -n 5 -r auto -e kernel -j step1_output.json -a "%s %s"
  SUB_CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -n 5 -r auto -e application -j step2_output.json -a "%s %s"
  SUB_CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -n 5 -r user -e application -j step3_output.json -a "%s %s"
  VAG_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_l1tex_tags_mem_global.avg,smsp__sass_l1tex_tags_mem_global.max,smsp__sass_l1tex_tags_mem_global.min,smsp__sass_l1tex_tags_mem_global.sum,smsp__sass_sectors_mem_global.avg,smsp__sass_sectors_mem_global.max,smsp__sass_sectors_mem_global.min,smsp__sass_sectors_mem_global.sum -n 5 -r auto -e kernel -j step1_output.json -a %s
  VAG_CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_l1tex_tags_mem_global.avg,smsp__sass_l1tex_tags_mem_global.max,smsp__sass_l1tex_tags_mem_global.min,smsp__sass_l1tex_tags_mem_global.sum,smsp__sass_sectors_mem_global.avg,smsp__sass_sectors_mem_global.max,smsp__sass_sectors_mem_global.min,smsp__sass_sectors_mem_global.sum -n 5 -r auto -e application -j step2_output.json -a %s
  VAG_CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__sass_l1tex_tags_mem_global.avg,smsp__sass_l1tex_tags_mem_global.max,smsp__sass_l1tex_tags_mem_global.min,smsp__sass_l1tex_tags_mem_global.sum,smsp__sass_sectors_mem_global.avg,smsp__sass_sectors_mem_global.max,smsp__sass_sectors_mem_global.min,smsp__sass_sectors_mem_global.sum -n 5 -r user -e application -j step3_output.json -a %s
  GRAPH_CMD1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; %s
  GRAPH_CMD2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m smsp__warps_launched.sum -j step2_output.json -g 1 -a %s
  GRAPH_CMD3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -j step3_output.json -g 1 -a %s
  CMD1_1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__sass_inst_executed_op_global_ld.sum -r auto -e kernel -a %s
  CMD1_2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum -r auto -e kernel -a %s
  CMD1_3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -r auto -e kernel -a %s
  CMD1_4: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -r auto -e kernel -a %s
  CMD2_1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum -r auto -e application -a %s
  CMD2_2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__sass_inst_executed_op_global_ld.sum -r auto -e application -a %s
  CMD2_3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -r auto -e application -a %s
  CMD3_1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum -r user -e application -a %s
  CMD3_2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__sass_inst_executed_op_global_ld.sum -r user -e application -a %s
  CMD3_3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -r user -e application -a %s
  CMD_GRAPH1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__sass_inst_executed_op_global_ld.sum -g 1 -a %s
  CMD_GRAPH2: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum -g 1 -a %s
  CMD_GRAPH3: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ctas_launched.sum -g 1 -a %s
  CMD_GRAPH4: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -g 1 -a %s
  CMD_GRAPH5: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum -r auto -e application -a %s
  NORMAL_KERNERL_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -r auto -e kernel -a %s
  NORMAL_KERNEL_MULTI: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -r auto -e kernel -a %s
  NORMAL_APP_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -r auto -e application -a %s
  NORMAL_APP_MULTI: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -r auto -e application -a %s
  NORMAL_APP_USER_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -r user -e application -a %s
  NORMAL_APP_USER_MULTI: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -r user -e application -a %s
  GRAPH_NODE_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -g 0 -a %s
  GRAPH_NODE_MULTI: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -g 0 -a %s
  GRAPH_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -g 1 -a %s
  GRAPH_MULTI: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__mioc_inst_issued.sum -g 1 -a %s
  OPTIX_KERNAL_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -r auto -e kernel --cblProfiling 1 -a %s
  OPTIX_APP_SINGLE: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__ctas_launched.sum -r auto -e application --cblProfiling 1 -a %s
  PROFILE_SPECIAL1: cd %s; $ENV:PATH+=";${CUPTI_RUN_PATH}"; profiler_injection_test.exe -m sm__ops_path_tensor_op_bgmma_src_int1.avg,sm__ops_path_tensor_op_bmma_src_int1.avg,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32.avg,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32_sparsity_off.avg,sm__ops_path_tensor_op_hgmma_src_bf16_dst_fp32_sparsity_on.avg,sm__ops_path_tensor_op_hgmma_src_fp16.avg,sm__ops_path_tensor_op_hgmma_src_fp16_sparsity_off.avg,sm__ops_path_tensor_op_hgmma_src_fp16_sparsity_on.avg,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32.avg,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32_sparsity_off.avg,sm__ops_path_tensor_op_hgmma_src_tf32_dst_fp32_sparsity_on.avg,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32.avg,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32_sparsity_off.avg,sm__ops_path_tensor_op_hmma_src_bf16_dst_fp32_sparsity_on.avg,sm__ops_path_tensor_op_hmma_src_fp16.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16_sparsity_off.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp16_sparsity_on.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32_sparsity_off.avg,sm__ops_path_tensor_op_hmma_src_fp16_dst_fp32_sparsity_on.avg,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32.avg,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32_sparsity_off.avg,sm__ops_path_tensor_op_hmma_src_tf32_dst_fp32_sparsity_on.avg,sm__ops_path_tensor_op_igmma_src_int8.avg,sm__ops_path_tensor_op_igmma_src_int8_sparsity_off.avg,sm__ops_path_tensor_op_igmma_src_int8_sparsity_on.avg,sm__ops_path_tensor_op_imma_src_int8.avg,sm__ops_path_tensor_op_imma_src_int8_sparsity_off.avg,sm__ops_path_tensor_op_imma_src_int8_sparsity_on.avg,sm__ops_path_tensor_src_bf16_dst_fp32.avg,sm__ops_path_tensor_src_fp16.avg,sm__ops_path_tensor_src_fp64.avg,sm__ops_path_tensor_src_fp8.avg,sm__ops_path_tensor_src_fp8_sparsity_off.avg,sm__ops_path_tensor_src_fp8_sparsity_on.avg,sm__ops_path_tensor_src_int1.avg,sm__ops_path_tensor_src_int8.avg,sm__ops_path_tensor_src_tf32_dst_fp32.avg -a %s


#Test CLI Tools support for Vanadium Python sample 3570628
TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE:
  RUN_SANITIZER: "1"
  RUN_NCU: "1"
  RUN_CUDA_GDB: "1"
  RUN_CUPTI_TRACE: "1"
  RUN_CUPTI_PROFILE: "1"
  LOG_NAME: ${TOOLS_SUPPORT_VANADIUM_PYTHON_SAMPLE_PATH}/tools_support_vanadium_python_sample.txt
  SAMPLE_LIST: "python310 hello.py,python310 mandelbrot.py,python310 sobel.py data/nvidia_logo.png"
  PREPARE:
    CMD1: python310 -m pip install nvidia_cuda --index https://urm.nvidia.com/artifactory/api/pypi/sw-vanadium-pypi-local/simple; python310 -m pip install opencv-python; python310-m pip install numpy
    CMD2: cd %s; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/vanadium_sample/vanadium_examples-main.zip; ${CMDER_BIN_PATH}/unzip vanadium_examples-main.zip; ${CMDER_BIN_PATH}/cp -r vanadium_examples-main/* .
    CMD3: cd %s; ${CMDER_BIN_PATH}/rm %s.bat; ${CMDER_BIN_PATH}/touch %s.bat
    CMD4: ${CMDER_BIN_PATH}/cp -r %s/* %s