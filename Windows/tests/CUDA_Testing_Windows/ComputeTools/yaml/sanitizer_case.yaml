global:
  env:
    CUDA_MAJOR: 13
    CUDA_MINOR: 0
    CUDA_REVISION: 20
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: 580
    DRIVER_VERSION: 575.65
    DEVICES: 1
    DVS_BUILD: CUDA13.0
    INSTALLER: exe
    HOST_HOME: "C:"
    RESULTS_HOME: ${HOST_HOME}/tesla_automation_results
    TOOLS_HOME: ${RESULTS_HOME}/sanitizer_device${DEVICES}
    DATE: 20250415200108
    DATE1: 20250331
    HOST_P4_PATH: ${HOST_HOME}/p4
    PLATFORM: x86_win
    CQA_USER: Y3FhdXNlcg
    CQA_PASSWORD: Y3FhdGVzdA
    SAMPLE_PATH: "${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}"
    SAMPLE_BIN_PATH: "${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}/bin/win64/Debug"
    SAMPLE_BIN_PATH_RELEASE: "${HOST_HOME}/ProgramData/NVIDIA Corporation/CUDA Samples/v${CUDA_SHORT_VERSION}/bin/win64/Release"
    OPTIX_FLAG: 1
    OPTIX_HOME: ${HOST_HOME}/tesla_automation_optix
    OPTIX_SDK_VERSION: "9.0"   # Updated since CUDA 12.6
    OPTIX_SAMPLE_HOME: ${OPTIX_HOME}/${OPTIX_SDK_VERSION}
    OPTIX_HELLO_SAMPLE_PATH: "${HOST_HOME}/ProgramData/NVIDIA Corporation/OptiX SDK ${OPTIX_SDK_VERSION}.0/SDK/optixHello"
    OPTIX_HELLO_TEST_PATH: "C:/LMS/optix-${OPTIX_SDK_VERSION}/build/bin/Release"
    OPTIX_SAMPLE_BIN_PATH: ${OPTIX_SAMPLE_HOME}/bin/Release
    OPTIX_SAMPLE_BIN_DEBUG_PATH: ${OPTIX_SAMPLE_HOME}/bin/Debug
    OPTIX_SAMPLE_BIN_RELEASE_PATH: ${OPTIX_SAMPLE_HOME}/bin/Release
    SAMPLE_116_PATH0: "${SAMPLE_PATH}/Samples/0_Introduction"
    SAMPLE_115_PATH0: "${SAMPLE_PATH}/0_Simple"
    SAMPLE_SAMPLE0_PATH: "${SAMPLE_PATH}/build/Samples/0_Introduction"
    SAMPLE_116_PATH1: "${SAMPLE_PATH}/Samples/1_Utilities"
    SAMPLE_115_PATH1: "${SAMPLE_PATH}/1_Utilities"
    SAMPLE_SAMPLE1_PATH: "${SAMPLE_PATH}/build/Samples/1_Utilities"
    BASE_PATH: "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v${CUDA_SHORT_VERSION}"
    BASE_BIN_PATH: "${BASE_PATH}/bin"
    SANITIZER_BIN_PATH: "${BASE_PATH}/bin"
    SANITIZER_HOME_PATH: "${BASE_PATH}/compute-sanitizer"
    HOST_PASSWORD: labuser
    SCPRIT_HOME: ${TOOLS_HOME}/scripts
    BASE_LOG_PATH1: ${TOOLS_HOME}/2181029_sanitizer_sample_coverage_result_${DATE}
    BASE_LOG_PATH2: ${TOOLS_HOME}/2181021_sanitizer_check_option_result_${DATE}
    BASE_LOG_PATH3: ${TOOLS_HOME}/2179905_sanitizer_sanity_result_${DATE}
    BASE_LOG_PATH4: ${TOOLS_HOME}/2181022_sanitizer_check_demangle_result_${DATE}
    BASE_LOG_PATH6: ${TOOLS_HOME}/2181023_sanitizer_check_filter_result_${DATE}
    BASE_LOG_PATH7: ${TOOLS_HOME}/2181064_sanitizer_save_log_result_${DATE}
    BASE_LOG_PATH8: ${TOOLS_HOME}/2182162_sanitizer_launch_timeout_result_${DATE}
    NVTX_OPTION_PATH: ${TOOLS_HOME}/2679220_nvtx_option_result_${DATE}
    PADDING_OPTION_PATH: ${TOOLS_HOME}/2679219_padding_option_result_${DATE}
    LAUNCH_COUNT_SKIP_PATH: ${TOOLS_HOME}/2680934_launch_count_skip_result_${DATE}
    TARGET_PROCESS_OPTION_PATH: ${TOOLS_HOME}/2493063_target_process_option_result_${DATE}
    SANITIZER_NO_CRASH_PATH: ${TOOLS_HOME}/2635791_sanitizer_no_crash_result_${DATE}
    RACECHECK_DETECT_PRINT_OPTION_PATH: ${TOOLS_HOME}/2681761_racecheck_detect_print_option_result_${DATE}
    CUDA_COREDUMP_DISABLE_PATH: ${TOOLS_HOME}/2524649_cuda_coredump_disable_result_${DATE}
    CUDA_LEAK_CHECK_PATH: ${TOOLS_HOME}/2714083_cuda_leak_check_result_${DATE}
    DEVICE_STACK_FRAME_PATH: ${TOOLS_HOME}/2764359_device_stack_frame_result_${DATE}
    SANITIZER_CASE_LOG_PATH: ${BASE_LOG_PATH}
    SANITIZER_RESULT_PATH: ${BASE_LOG_PATH}
    CORRECT_LINE_NUM_PATH: ${TOOLS_HOME}/2716405_correct_line_num_result_${DATE}
    T744_PATH: ${RESULTS_HOME}/t744
    ENV_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin:$PATH
    CMEDER_PATH: "C:/CTT/cmder"
    CMDER_BIN_PATH: "${CMEDER_PATH}/vendor/git-for-windows/usr/bin"
    DEVENV: "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv"
    MSBUILD: "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/MSBuild"
    PYTHON: "C:/Python37/python.exe"
    PERL: "C:/Strawberry/perl/bin/perl.exe"
    SOLUTION_FILE: "Samples_VS2022.sln"
    SOLUTION_FILE1: "cuda-samples.sln"
    REBEL_SMOKE_TEST_PACKAGE_PATH: ${TOOLS_HOME}/sanitizer_rebel_smoke_test_package_${DATE1}
    REBEL_SMOKE_TEST_APP_PATH: ${REBEL_SMOKE_TEST_PACKAGE_PATH}/Rebel-Release-public-test/target/windows-desktop-win7-x64/cuda
    STOP_USING_WARPFULLMASK_PATH: ${TOOLS_HOME}/2759132_stop_using_warpfullmask_result_${DATE}
    MEMCHECK_WITH_FREE_PATH: ${TOOLS_HOME}/2788746_memcheck_with_free_result_${DATE}
    RELATIVE_RETURN_ADDRESS_PATH: ${TOOLS_HOME}/2800789_relative_return_address_result_${DATE}
    READ_ONLY_FLAG_PATH: ${TOOLS_HOME}/2818363_read_only_flag_result_${DATE}
    DETAIL_SESSION_OPTION_PATH: ${TOOLS_HOME}/2819761_detail_session_option_result_${DATE}
    SANITIZER_XML_OPTION_PATH: ${TOOLS_HOME}/2806646_sanitizer_xml_option_result_${DATE}
    OUTPUT_FLAG: 1
    CUDA_BARRIERS_OPTION_PATH: ${TOOLS_HOME}/2819793_cuda_barriers_option_result_${DATE}
    SAMPLE_0_PATH: "${SAMPLE_PATH}/Samples/0_Introduction"
    SAMPLE_0_PATH1: "${SAMPLE_PATH}/0_Simple"
    SAMPLE_6_PATH1: "${SAMPLE_PATH}/6_Advanced"
    SAMPLE_1_PATH: "${SAMPLE_PATH}/Samples/1_Utilities"
    SAMPLE_1_PATH1: "${SAMPLE_PATH}/1_Utilities"
    LEAK_CHECK_ALLOC_PATH: ${TOOLS_HOME}/2833990_leak_check_alloc_result_${DATE}
    TARGET_FILTER_OPTION_PATH: ${TOOLS_HOME}/2834633_target_filter_option_result_${DATE}
    STACK_OVERFLOW_CHECK_PATH: ${TOOLS_HOME}/2897883_stack_overflow_check_result_${DATE}
    SANITIZER_MEMCHECK_DEMO_PATH: ${TOOLS_HOME}/2931394_sanitizer_memcheck_demo_result_${DATE}
    SANITIZER_RACECHECK_WARP_PATH: ${TOOLS_HOME}/2931395_sanitizer_racecheck_warp_result_${DATE}
    SANITIZER_RACECHECK_BLOCK_PATH: ${TOOLS_HOME}/2931396_sanitizer_racecheck_block_result_${DATE}
    SANITIZER_INITCHECK_ERROR_PATH: ${TOOLS_HOME}/2931397_sanitizer_initcheck_error_result_${DATE}
    SANITIZER_LEAKCHECK_MEMCHECK_DEMO_PATH: ${TOOLS_HOME}/2931398_sanitizer_leakcheck_memcheck_demo_result_${DATE}
    SANITIZER_SYNCCHECK_DIVERGENT_PATH: ${TOOLS_HOME}/2931399_sanitizer_synccheck_divergent_result_${DATE}
    SANITIZER_SYNCCHECK_SYNCWARP_PATH: ${TOOLS_HOME}/2931400_sanitizer_synccheck_syncwarp_result_${DATE}
    LEAKCHECK_DEVICES_ALLOCATIONS_PATH: ${TOOLS_HOME}/2949009_leakcheck_devices_allocations_result_${DATE}
    MEMCHECK_OPTION_TRACK_STREAM_PATH: ${TOOLS_HOME}/2949010_memcheck_option_track_stream_result_${DATE}
    SYNCCHECK_OPTION_MISS_BARRIER_PATH: ${TOOLS_HOME}/2949013_synccheck_option_miss_barrier_result_${DATE}
    COREDUMP_GENERATION_SUPPORT_PATH: ${TOOLS_HOME}/2806977_coredump_generation_support_in_sanitizer_result_${DATE}
    CALL_HOST_DEVICE_PATH: ${TOOLS_HOME}/3021849_call_host_device_result_${DATE}
    DEPRECAT_CUDA_MEMCHECK_PATH: ${TOOLS_HOME}/2742928_deprecate_cuda_memcheck_result_${DATE}
    MEMCHECK_ORDERED_RACE_DETECTION_PATH: ${TOOLS_HOME}/3051676_memcheck_ordered_race_detection_result_${DATE}
    SANITIZER_GUARDWORD_CHECK_PATH: ${TOOLS_HOME}/2608117_sanitizer_guardword_check_result_${DATE}
    LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY_PATH: ${TOOLS_HOME}/2680933_launch_without_suffix_specified_explicitly_result_${DATE}
    SUPPORT_ALIGNED_DEVICE_MALLOC_PATH: ${TOOLS_HOME}/3071342_support_aligned_device_malloc_result_${DATE}
    SUPPORT_LDSM_PATH: ${TOOLS_HOME}/3079874_sanitizer_support_LDSM_result_${DATE}
    INITCHECK_TRACK_UNUSED_MEMORY_PATH: ${TOOLS_HOME}/3090527_initcheck_track_unused_memory_result_${DATE}
    SANITIZER_STEAM_CAPTURE_PATH: ${TOOLS_HOME}/3156524_sanitizer_steam_capture_result_${DATE}
    SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH: ${TOOLS_HOME}/3158301_sanitizer_racecheck_memcpy_async_result_${DATE}
    GRAPHIC_SAMPLE_COVERAGE_PATH: ${TOOLS_HOME}/3084216_graphic_sample_coverage_result_${DATE}
    SANITIZER_CHECK_READ_ACCESS_PATH: ${TOOLS_HOME}/3178884_sanitizer_check_read_access_result_${DATE}
    SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH: ${TOOLS_HOME}/3192559_sanitizer_memcheck_vector_atomics_result_${DATE}
    SANITIZER_SUPPORT_UNICODE_FILE_PATH: ${TOOLS_HOME}/3183138_sanitizer_support_unicode_file_result_${DATE}
    SANITIZER_MEMCHECK_CNP_SUPPORT_PATH: ${TOOLS_HOME}/3192566_sanitizer_memcheck_CNP_support_result_${DATE}
    SANITIZER_QUITE_CUDA_INIT_PATH: ${TOOLS_HOME}/3207945_sanitizer_quite_cuda_init_result_${DATE}
    SANITIZER_LAUNCH_ATTACH_PATH: ${TOOLS_HOME}/3207946_sanitizer_launch_attach_result_${DATE}
    SANITIZER_CAPTURE_CUDA_BARRIER_PATH: ${TOOLS_HOME}/3222551_sanitizer_capture_cuda_barrier_result_${DATE}
    INITCHECK_OPTIX_SAMPLES_COVERAGE_PATH: ${TOOLS_HOME}/3363004_initcheck_optix_samples_coverage_result_${DATE}
    OPTION_SUPPRESSIONS_PATH: ${TOOLS_HOME}/3568568_option_suppressions_result_${DATE}
    SANITIZER_3700598_RCCA_PATH: ${TOOLS_HOME}/3700598_sanitizer_rcca_result_${DATE}
    OPTION_PRELOAD_LIBRARY_PATH: ${TOOLS_HOME}/3532738_option_preload_library_result_${DATE}
    OPTIX_RTCORE_SOURCE_HIDING_PATH: ${TOOLS_HOME}/3730298_optix_rtcore_source_hiding_result_${DATE}
    SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH: ${TOOLS_HOME}/3831779_sanitizer_memcheck_backtrace_short_result_${DATE}
    SANITIZER_3797693_RCCA_PATH: ${TOOLS_HOME}/3797693_sanitizer_rcca_3797693_result_${DATE}
    MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH: ${TOOLS_HOME}/3504892_detect_missing_moudle_unload_result_${DATE}
    SANITIZER_COMPILER_PUBLIC_PTX_PATH: ${TOOLS_HOME}/4076330_sanitizer_compiler_public_ptx_result_${DATE}
    SANITIZER_TRACK_LDGSTS_PATH: ${TOOLS_HOME}/4085254_sanitizer_track_ldgsts_result_${DATE}
    SANITIZER_4085255_RCCA_PATH: ${TOOLS_HOME}/4085255_sanitizer_4085255_rcca_result_${DATE}

TOOLS_X86_WIN:
    SANITIZER_PATH: ${RESULTS_HOME}/sanitizer_${DATE1}
    SANITIZER_RUN_PATH: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test
    SANITIZER_PATH1: ${SANITIZER_PATH}_1
    SANITIZER_RUN_PATH1: ${SANITIZER_PATH1}/nvidia-compute-sanitizer-test
    SANITIZER_PATH2: ${SANITIZER_PATH}_2
    SANITIZER_RUN_PATH2: ${SANITIZER_PATH2}/nvidia-compute-sanitizer-test
    SANITIZER_PATH3: ${SANITIZER_PATH}_3
    SANITIZER_RUN_PATH3: ${SANITIZER_PATH3}/nvidia-compute-sanitizer-test
    SANITIZER_URL: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA${CUDA_SHORT_VERSION}_Release_Windows_Sanitizer_Public/
    NEW_SANITIZER_DVS_URL: http://ausdvs.nvidia.com/Query/PackageDetails?which_option=find_cl_for_package&package=Agora_Rel_DTC_${DVS_BUILD}%20Release%20Windows%20Sanitizer%20Public
    # SANITIZER_11_URL: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Windows_Sanitizer_Public/  http://dvstransfer.nvidia.com/dvsshare/dvs-binaries-vol1/Agora_Rel_DTC_C_Release_Windows_Sanitizer_Public/
    SANITIZER_11_URL: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries-vol1/Agora_Rel_DTC_${DVS_BUILD}_Release_Windows_Sanitizer_Public/

# get the SM value of GPU and name of GPU
PREPARE:
    CMD1: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"
    CMD2: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'Device 0'
    CMD3: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'
    CMD4: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'Device 0'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'
    CMD_TCC_WDDM: cd "%s/deviceQuery"; cmd.exe /c "${DEVENV}" deviceQuery_vs2022.sln /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ./deviceQuery | grep 'CUDA Device Driver Mode (TCC or WDDM)'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'

SANITIZER_NON_GRAPHICS_SAMPLE:
  LOG_NAME: ${SANITIZER_CASE_LOG_PATH}/sanitizer_non_graphics.log
  RUN_CASE: true
  # build  sample
  STEP1:
    CMD:   cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused yes ./%s
    SAMPLE_LIST: lineOfSight,matrixMul,vectorAdd,asyncAPI

SANITIZER_SAMPLE_COVERAGE:
  LOG_NAME: ${BASE_LOG_PATH1}/sanitizer_sample_coverage.log
  RUN_CASE: true
  # cuHook is Linux only.
  SAMPLE_LIST: "cdpAdvancedQuicksort,UnifiedMemoryStreams,reductionMultiBlockCG,simpleCudaGraphs,batchCUBLAS,simpleMultiGPU,cudaOpenMP,p2pBandwidthLatencyTest,vectorAddMMAP"
  STEP1:
    CMD:   cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full %s.exe
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
    CHECK_POINT1: 'LEAK SUMMARY: 0 bytes leaked in 0 allocations'
  STEP2:
    CMD:   cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck %s.exe
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  STEP3:
    CMD:   cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck %s.exe
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  STEP4:
    CMD:   cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck %s.exe
    CHECK_POINT: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT: 'Warning: CUDA Dynamic Parallelism is not supported by the selected tool'

SANITIZER_SANITY:
  LOG_NAME: ${BASE_LOG_PATH3}/sanitizer_sanity.log
  RUN_CASE: true
  STEP1:
    CHECK_OPTION: leak-check,initcheck,synccheck,racecheck
    CMD:
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool memcheck --leak-check full --save leak.log ./matrixMul; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read leak.log
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --save initcheck.log ./matrixMul
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck --log-file synccheck.log ./asyncAPI
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --log-file %p.log ./mergeSort
    CMD1:
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool memcheck --leak-check full --save leak.log ./matrixMul; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read leak.log
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --save initcheck.log ./matrixMul
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck --log-file synccheck.log ./asyncAPI
     -   cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --log-file %p.log ./mergeSort
    CHECK_POINT: '0 errors'
    CHECK_POINT1: '========= COMPUTE-SANITIZER'
    CHECK_POINT2: 'ERROR SUMMARY: 0 errors'
    CHECK_POINT3: 'LEAK SUMMARY: 0 bytes leaked in 0 allocations'
  STEP2:
    CMD: cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/ls.exe -l *.log

SANITIZER_SAVE_LOG:
  LOG_NAME: ${BASE_LOG_PATH7}/sanitizer_save_log.log
  RUN_CASE: true
  PREPARE:
    PREPARE_CMD: cd ${BASE_LOG_PATH7}; ${CMDER_BIN_PATH}/rm -rf vectorAdd_err; ${CMDER_BIN_PATH}/mkdir vectorAdd_err; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2181021/vectorAdd_err/vectorAdd_err.cu;
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -o vectorAdd_err.exe vectorAdd_err.cu
  STEP1:
    RUN_STEP1: true
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/rm.exe -fr *.log; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --save err.log ./vectorAdd_err.exe
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file err1.log ./vectorAdd_err.exe
    CMD2: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/ls.exe -l *.log
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"
    CHECK_POINT: "err.log"
    CHECK_POINT1: "err1.log"
  STEP2:
    RUN_STEP2: true
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read err.log
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/cat.exe err1.log
    CHECK_POINT: "ERROR SUMMARY: 2 errors"
  STEP3:
    RUN_STEP3: true
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/mv.exe -f *.log ${BASE_LOG_PATH7}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --save %p.log ./vectorAdd_err.exe; ${CMDER_BIN_PATH}/ls.exe *.log
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/mv.exe -f *.log ${BASE_LOG_PATH7}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file %p.log ./vectorAdd_err.exe; ${CMDER_BIN_PATH}/ls.exe *.log
    CMD2: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/ls.exe *.log
  STEP4:
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c 'set FOO=testing&&"${SANITIZER_BIN_PATH}/compute-sanitizer" --save %q{FOO}.log ./vectorAdd_err.exe'; ${CMDER_BIN_PATH}/ls.exe testing.log
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c 'set FOO1=testing1&&"${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file %q{FOO1}.log ./vectorAdd_err.exe'; ${CMDER_BIN_PATH}/ls.exe testing1.log
    CMD2: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/ls.exe *.log
    CHECK_POINT: "testing.log"
    CHECK_POINT1: "testing1.log"
  STEP5:
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/mv.exe -f *.log ${BASE_LOG_PATH7}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --save %%.log ./vectorAdd_err.exe; ${CMDER_BIN_PATH}/ls.exe %.log
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/mv.exe -f *.log ${BASE_LOG_PATH7}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file %%.log ./vectorAdd_err.exe; ${CMDER_BIN_PATH}/ls.exe %.log
    CMD2: cd "${BASE_LOG_PATH7}/vectorAdd_err"; ${CMDER_BIN_PATH}/ls.exe *.log
    CHECK_POINT: "ERROR SUMMARY: 2 errors"
  STEP6:
    CMD: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --save %x.log ./vectorAdd_err.exe
    CMD1: cd "${BASE_LOG_PATH7}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file %x.log ./vectorAdd_err.exe
    CHECK_POINT: "Unknown macro '%x'"

SANITIZER_LAUNCH_TIMEOUT:
  LOG_NAME: ${BASE_LOG_PATH8}/sanitizer_launch_timeout.log
  RUN_CASE: true
  PREPARE:
    PREPARE_CMD: cd ${BASE_LOG_PATH8}; ${CMDER_BIN_PATH}/rm -r vectorAdd_while;${CMDER_BIN_PATH}/mkdir vectorAdd_while; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
    CMD: cd ${BASE_LOG_PATH8}/vectorAdd_while; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2182162/vectorAdd_while/vectorAdd_while.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -o vectorAdd_while.exe vectorAdd_while.cu
  STEP1:
    CMD: cd "${BASE_LOG_PATH8}/vectorAdd_while"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --kill yes ./vectorAdd_while.exe
    CMD1: cd "${BASE_LOG_PATH8}/vectorAdd_while"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --kill ./vectorAdd_while.exe
    CHECK_POINT: "Error: No attachable process found"
  STEP2:
    CMD: cd "${BASE_LOG_PATH8}/vectorAdd_while"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --launch-timeout 30 --kill yes ./vectorAdd_while.exe
    CMD1: cd "${BASE_LOG_PATH8}/vectorAdd_while"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --launch-timeout 30 --kill ./vectorAdd_while.exe
    CHECK_POINT: "Error: No attachable process found"

SANITIZER_OPTION_CHECK:
  LOG_NAME: ${BASE_LOG_PATH2}/sanitizer_check_option.log
  RUN_CASE: true
  SAMPLE_LIST: vectorAdd, asyncAPI
  PREPARE:
    PREPARE_CMD: cd ${BASE_LOG_PATH2}; ${CMDER_BIN_PATH}/rm -rf vectorAdd_err; ${CMDER_BIN_PATH}/mkdir vectorAdd_err; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
    CMD: cd "${BASE_LOG_PATH2}/vectorAdd_err"; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2181021/vectorAdd_err/vectorAdd_err.cu;
    CMD1: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -o vectorAdd_err.exe vectorAdd_err.cu
  STEP1:
    RUN_STEP1: true
    CMD: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full ./vectorAdd_err.exe
    CHECK_POINT: "ERROR SUMMARY: 5 errors"
  STEP2:
    RUN_STEP2: true
    CMD1: cd "%s/asyncAPI"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./asyncAPI
    CHECK_POINT1: "ERROR SUMMARY: 0 errors"
    CMD2: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./vectorAdd_err.exe
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"
    CMD3: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --prefix "******" --error-exitcode 6 --print-limit 1 ./vectorAdd_err.exe
    CHECK_POINT3: "Program hit invalid configuration argument (error 9) on CUDA API call to cudaLaunchKernel"
    CHECK_POINT3_1: "LEAK SUMMARY: 600000 bytes leaked in 3 allocations"
    CHECK_POINT4: "ERROR SUMMARY: 5 errors"
  STEP3:
    RUN_STEP3: true
    CMD1: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./vectorAdd_err.exe
    CMD2: cd "${BASE_LOG_PATH2}/vectorAdd_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --check-exit-code no ./vectorAdd_err.exe
    CHECK_POINT: "ERROR SUMMARY: 2 errors"

CHECK_DEMANGLE:
  LOG_NAME: ${BASE_LOG_PATH4}/check_demangle.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r t744; mkdir t744; cd t744; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/t744/t744.cu
  STEP1:
    CMD: cd ${T744_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o t744 t744.cu; ls t744.exe
    CHECK_POINT: t744
  STEP2:
    CMD1: cd ${T744_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --print-limit 0 ./t744
    CMD2: cd ${T744_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --print-limit 0 --racecheck-report hazard ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT1_1: "void tkernel<int>(T1 *)"
    CHECK_POINT1_2: "void tkernel<int>(int*)"
    CHECK_POINT2: 'RACECHECK SUMMARY: 160 hazards displayed (0 errors, 160 warnings)'
    CHECK_POINT2_2: "void tkernel<int>(T1 *)"
    CHECK_POINT2_1: "void tkernel<int>(int*)"
  STEP3:
    CMD: cd ${T744_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --print-limit 0 --demangle simple ./t744
    CHECK_POINT: tkernel
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  STEP4:
    CMD: cd ${T744_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --print-limit 0 --demangle no ./t744
    CHECK_POINT: _Z7tkernelIiEvPT_
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"

CHECK_FILTER:
  LOG_NAME: ${BASE_LOG_PATH6}/check_filter.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r t744; mkdir t744; cd t744; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/t744/t744.cu
  STEP1:
    CMD: cd "${T744_PATH}"; rm t744.exe; nvcc -g -G -o t744 t744.cu
  STEP2:
    CMD: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck ./t744
    CHECK_POINT: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  STEP3:
    CMD1: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kernel_name=aaa ./t744
    CMD2: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kne=_Z7tkernelIiEvPT_ ./t744
    CMD3: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kne=tkernel  ./t744
    CHECK_POINT: "void tkernel<int>"
    CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
    CHECK_POINT2: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT2_1: "void tkernel<int>"
    CHECK_POINT3: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  STEP4:
    CMD1: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kns=tkernel ./t744
    CMD2: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kernel_substring=aaaa ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT1_1: "void tkernel<int>"
    CHECK_POINT2: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  # Added since CUDA 12.3
  STEP5:
    CMD1: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} regex=tke ./t744
    CMD2: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} regex=aaaa ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT1_1: "void tkernel<int>"
    CHECK_POINT1_2: "t744.cu:9 [32 hazards]"
    CHECK_POINT1_3: "t744.cu:11 [128 hazards]"
    CHECK_POINT1_4: "t744.cu:10"
    CHECK_POINT2: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  STEP6:
    CMD: cd "${T744_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --{} kne=_Z7tkernelIiEvPT_ ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

NVTX_OPTION:
  LOG_NAME: ${NVTX_OPTION_PATH}/nvtx_option.log
  STEP1:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./MemoryPool
    CMD_1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --nvtx no ./MemoryPool
    CHECK_POINT: "ERROR SUMMARY: 0 errors"
  STEP2:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace no ./MemoryPool
    CHECK_POINT: "ERROR SUMMARY: 3 errors"
  STEP3:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full ./Naming
    CHECK_POINT: "LEAK SUMMARY: 1 bytes leaked in 1 allocations"
    CHECK_POINT1: "ERROR SUMMARY: 1 error"
    CHECK_POINT2: "My allocation"

PADDING_OPTION:
  LOG_NAME: ${PADDING_OPTION_PATH}/padding_option.log
  STEP1:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace no --padding 16 ./Padding
    CHECK_POINT: "ERROR SUMMARY: 1 error"

LAUNCH_COUNT_SKIP:
  LOG_NAME: ${LAUNCH_COUNT_SKIP_PATH}/launch_count_skip.log
  STEP1:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 6 hazards displayed (6 errors, 0 warnings)"
  STEP2:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck -s 2 -c 2 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 2 hazards displayed (2 errors, 0 warnings)"
  STEP3:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck -c 4 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 4 hazards displayed (4 errors, 0 warnings)"
  STEP4:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck -s 2 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 4 hazards displayed (4 errors, 0 warnings)"

SANITIZER_NO_CRASH:
  LOG_NAME: ${SANITIZER_NO_CRASH_PATH}/sanitizer_not_crash.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r alloca; mkdir alloca; cd alloca; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/%s/%s
    CMD1: cd ${TOOLS_HOME}/../alloca; nvcc -g -G -o alloca Alloca1.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../alloca; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool memcheck ./alloca
  STEP2:
    CMD: cd ${TOOLS_HOME}/../alloca; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck ./alloca
  STEP3:
    CMD: cd ${TOOLS_HOME}/../alloca; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck ./alloca
  STEP4:
    CMD: cd ${TOOLS_HOME}/../alloca; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck ./alloca
  CHECK_POINT: "ERROR SUMMARY: 0 errors"
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"


TARGET_PROCESS_OPTION:
  LOG_NAME: ${TARGET_PROCESS_OPTION_PATH}/target_process_option.log
  PREPARE:
    CMD1: cd "%s/matrixMul"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD2: cd "%s/asyncAPI"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD3: cd "%s/mergeSort"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD1_1: cd "%s/matrixMul"; cmd.exe /c "${DEVENV}" %s /rebuild "Release"
    CMD2_1: cd "%s/asyncAPI"; cmd.exe /c "${DEVENV}" %s /rebuild "Release"
    CMD3_1: cd "%s/mergeSort"; cmd.exe /c "${DEVENV}" %s /rebuild "Release"
    CMD4: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm test.bat; ${CMDER_BIN_PATH}/touch test.bat
    CMD5: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm test.py; ${CMDER_BIN_PATH}/touch test.py
    CMD6: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm test.pl; ${CMDER_BIN_PATH}/touch test.pl
  STEP1:
    CMD1: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" C:\Windows\System32\cmd.exe /C test.bat
    CMD2: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all C:\Windows\System32\cmd.exe /C test.bat
    CMD3: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all ${PYTHON} test.py
    CMD4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all ${PERL} test.pl
    # Default to all for target-processes since 12.4
    CMD1_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes application-only C:\Windows\System32\cmd.exe /C test.bat
    CMD2_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" C:\Windows\System32\cmd.exe /C test.bat
    CMD3_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ${PYTHON} test.py
    CMD4_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ${PERL} test.pl
  STEP2:
    CMD1: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool memcheck -c 16 --target-processes all C:\Windows\System32\cmd.exe /C test.bat
    CMD2: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck -c 16 --target-processes all C:\Windows\System32\cmd.exe /C test.bat
    CMD3: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck -c 16 --target-processes all C:\Windows\System32\cmd.exe /C test.bat
    CMD4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck -c 16 --target-processes all C:\Windows\System32\cmd.exe /C test.bat
    # Default to all for target-processes since 12.4
    CMD1_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool memcheck -c 16 C:\Windows\System32\cmd.exe /C test.bat
    CMD2_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck -c 16 C:\Windows\System32\cmd.exe /C test.bat
    CMD3_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck -c 16 C:\Windows\System32\cmd.exe /C test.bat
    CMD4_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck -c 16 C:\Windows\System32\cmd.exe /C test.bat
  CHECK_POINT: "ERROR SUMMARY: 0 errors"
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  CHECK_POINT2: 'Error: Target application terminated before first instrumented API call'
  CHECK_POINT3: 'Tracking kernels launched by child processes requires the --target-processes all option'

# https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3591707 [AutoEnhance] Test Procedure on 2681761/Racecheck - Options - racecheck-detect-level/print-level
RACECHECK_DETECT_PRINT_OPTION:
  LOG_NAME: ${RACECHECK_DETECT_PRINT_OPTION_PATH}/racecheck_detect_print_option.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm -r 2681761; mkdir 2681761; cd 2681761; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/%s/%s
    CMD1: cd ${TOOLS_HOME}/../2681761; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o race race.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2681761; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --racecheck-report hazard --racecheck-detect-level info --print-level warn --save out.nvm ./race
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2681761; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --print-level info --read out.nvm
  STEP3:
    CMD: cd ${TOOLS_HOME}/../2681761; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --racecheck-report hazard --racecheck-detect-level warn ./race
  STEP4:
    CMD: cd ${TOOLS_HOME}/../2681761; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --racecheck-report hazard --racecheck-detect-level error ./race
  CHECK_POINT: "RACECHECK SUMMARY: 3 hazards displayed (1 error, 2 warnings)"
  CHECK_POINT1: "RACECHECK SUMMARY: 12 hazards displayed (1 error, 2 warnings)"
  CHECK_POINT1_1: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"
  CHECK_POINT2: "Error"
  CHECK_POINT3: "Info"
  CHECK_POINT4: "Warning"

# Get different numbers of errors with Volta and Turing GPUs + Confirmed with Jane , there is no need to judge the number of errors
# So modify the CHECK_POINT: "ERROR SUMMARY: 6 errors" to CHECK_POINT: "ERROR SUMMARY"
# http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2524649&isTemplate=true
# Added option --destroy-on-device-error kernel for compute-sanitizer on Feb 8, 2022
CUDA_COREDUMP_DISABLE:
  LOG_NAME: ${CUDA_COREDUMP_DISABLE_PATH}/cuda_coredump_disable.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm -rf 2524649; mkdir 2524649; cd 2524649; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD1: cd ${TOOLS_HOME}/../2524649; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -o memcheck_demo memcheck_demo.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2524649; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-error no ./memcheck_demo
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2524649; $ENV:CUDA_COREDUMP_FILE="corefile"; ls ENV:CUDA*; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-error no ./memcheck_demo
  STEP3:
    CMD: cd ${TOOLS_HOME}/../2524649; $ENV:CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1; ls ENV:CUDA*; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-error no ./memcheck_demo
  CHECK_POINT: "ERROR SUMMARY: 2 errors"

CUDA_LEAK_CHECK:
  LOG_NAME: ${CUDA_LEAK_CHECK_PATH}/cuda_leak_check.log
  PREPARE:
    CMD: cd ${CUDA_LEAK_CHECK_PATH}; ${CMDER_BIN_PATH}/rm -rf simpleCubemapTexture_memleak; ${CMDER_BIN_PATH}/mkdir simpleCubemapTexture_memleak; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"; cd simpleCubemapTexture_memleak; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2714083/simpleCubemapTexture_memleak/simpleCubemapTexture_memleak.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -I ../Common -o simpleCubemapTexture_memleak.exe simpleCubemapTexture_memleak.cu
  STEP1:
    CMD: cd "${CUDA_LEAK_CHECK_PATH}/simpleCubemapTexture_memleak"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full ./simpleCubemapTexture_memleak.exe
  CHECK_POINT: "LEAK SUMMARY: 24576 bytes leaked in 1 allocations"
  CHECK_POINT1: "ERROR SUMMARY: 1 error"

# Get different numbers of errors with Volta and Turing GPUs + Confirmed with Jane , there is no need to judge the number of errors
# So modify the CHECK_POINT: "ERROR SUMMARY: 3 errors" to CHECK_POINT: "ERROR SUMMARY"

# https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3537364 [AutoEnhance] Test Procedure on 2764359/[Sanitizer][RCCA] Device stack frame reported on double free
# Add option --destroy-on-device-error
DEVICE_STACK_FRAME:
  LOG_NAME: ${DEVICE_STACK_FRAME_PATH}/device_stack_frame.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm -r 2764359; mkdir 2764359; cd 2764359; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/2764359/test_double_free.cu; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -G -o test test_double_free.cu; ls
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2764359; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel ./test
  CHECK_POINT1: "ERROR SUMMARY"
  CHECK_POINT1_12_3: "ERROR SUMMARY: 1 error"
  CHECK_POINT2: "Malloc/Free Error encountered : Double free"
  CHECK_POINT2_12_3: "Malloc/Free Error encountered : Double free"
  CHECK_POINT2_13_0: "Malloc/Free Error encountered : Device-side double free"
  CHECK_POINT3: "test_double_free.cu:4:f()"
  CHECK_POINT3_12_3: "test_double_free.cu:4"
  CHECK_POINT4: "Device Frame:${RESULTS_HOME}/2764359/test_double_free.cu:8:kernel()"
  CHECK_POINT4_12_3: "test_double_free.cu:8"
  CHECK_POINT5: "f()"
  CHECK_POINT5_12_3: "f()"
  CHECK_POINT6: "kernel()"
  CHECK_POINT6_12_3: "Device Frame.*kernel()"



# Will get the different line no. in the output with different GPUs on Windows.
# The checkpoints here is base on Volta GV100.
# GV100 TITAN V =========     at 0x10 in C:/tesla_automation_results/2716405/testbt2-noinline.cu:14:local_func(long)
# TU104 RTX 5000 =========     at 0x0 in C:/tesla_automation_results/2716405/testbt2-noinline.cu:7:local_func(long)
# Remove the line no. in the CHECKPOINT, CHECKPOINT_1, just judge the function name  Mar 30, 2022
CORRECT_LINE_NUM:
  LOG_NAME: ${CORRECT_LINE_NUM_PATH}/correct_line_num.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm.exe -r 2716405; mkdir 2716405; cd 2716405; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/2716405/testbt2-noinline.cu
    CMD_1: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -o test-lineinfo testbt2-noinline.cu; ls *.exe
    CMD_2: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -rdc=true -o test-lineinfo-rdc testbt2-noinline.cu; ls *.exe
    CMD1: cd ${TOOLS_HOME}/../2716405; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/2716405/testbt2-forceinline.cu; ls *.exe
    CMD1_1: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -o test-lineinfo-forceinline testbt2-forceinline.cu; ls *.exe
    CMD1_2: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -rdc=true -o test-lineinfo-forceinline-rdc testbt2-forceinline.cu; ls *.exe
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --show-backtrace device ./test-lineinfo
    CMD1: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --show-backtrace device ./test-lineinfo-rdc
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --show-backtrace device ./test-lineinfo-forceinline
    CMD1: cd ${TOOLS_HOME}/../2716405; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --show-backtrace device ./test-lineinfo-forceinline-rdc
  CHECK_POINT1_1: ":local_func(long)"
  CHECK_POINT1_2: "testbt2-noinline.cu:23:local_access"
  CHECK_POINT1_3: "testbt2-noinline.cu:32:module_test"
  CHECK_POINT2_1: "testbt2-forceinline.cu:14:local_func"
  CHECK_POINT2_2: "testbt2-forceinline.cu:23:local_access"
  CHECK_POINT2_3: "testbt2-forceinline.cu:32:module_test"
  CHECK_POINT1_4: "testbt2-noinline.cu:14"
  CHECK_POINT1_5: "testbt2-noinline.cu:23"
  CHECK_POINT1_6: "testbt2-noinline.cu:32"
  CHECK_POINT1_7: "[clone module_test()] local_func(long)"
  CHECK_POINT1_8: "Device Frame:.*clone module_test()"
  CHECK_POINT1_9: "Device Frame:.*module_test()"
  CHECK_POINT2_4: "testbt2-forceinline.cu:14"
  CHECK_POINT2_5: "testbt2-forceinline.cu:23"
  CHECK_POINT2_6: "testbt2-forceinline.cu:32"
  CHECK_POINT2_7: "local_func(long)"
  CHECK_POINT2_8: "Device Frame:.*local_access()"
  CHECK_POINT2_9: "Device Frame:.*module_test()"

STOP_USING_WARPFULLMASK:
  LOG_NAME: ${STOP_USING_WARPFULLMASK_PATH}/stop_using_warpfullmask.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm.exe -r Bug200746055; mkdir Bug200746055; cd Bug200746055; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/Bug200746055/repro.cu
    CMD1: cd ${TOOLS_HOME}/../Bug200746055; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -lineinfo -lcuda -o repro repro.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../Bug200746055; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all ./repro
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'

MEMCHECK_WITH_FREE:
  LOG_NAME: ${MEMCHECK_WITH_FREE_PATH}/memcheck_with_free.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm.exe -r 2788746; mkdir 2788746; cd 2788746; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/2788746/test.cu
    CMD1: cd ${TOOLS_HOME}/../2788746; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -o test test.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2788746; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --show-backtrace=no --destroy-on-device-error=kernel ./test
    CHECK_POINT: "Malloc/Free Warning encountered : Empty malloc"
    CHECK_POINT_1: "Malloc/Free Warning encountered : Device-side malloc of size 0"
    CHECK_POINT1: "Malloc/Free Error encountered : Double free"
    CHECK_POINT1_1: "Malloc/Free Error encountered : Device-side double free"
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"

# Add option --destroy-on-device-error kernel on Apr 8, 2022
# https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3591869 [AutoEnhance] Test Procedure on 2800789/Good device frames reported with relative return address
# There is a bug on Linux: https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3594416
# Add option --show-backtrace device on Apr 11, 2022
RELATIVE_RETURN_ADDRESS:
  LOG_NAME: ${RELATIVE_RETURN_ADDRESS_PATH}/relative_return_address.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm.exe -r 2800789; mkdir 2800789; cd 2800789; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/P1072_T2800789/RelativeReturnAddress.cu
    # https://nvbugs/4566478 [Require change]Update the nvcc command in sanitizer case 2800789
    # Add -gencode arch=compute_90,code=sm_90 -gencode arch=compute_89,code=sm_89 for Hopper/Ada GPUs on Apr 22, 2024
    CMD1: cd ${TOOLS_HOME}/2800789; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -lcudart -o RelativeReturnAddress RelativeReturnAddress.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/2800789; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace device ./RelativeReturnAddress
    CMD1: cd ${TOOLS_HOME}/2800789; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace device --strip-paths no ./RelativeReturnAddress
    CHECK_POINT: "Invalid __global__ read of size 4 bytes"
    CHECK_POINT_12_3: "Invalid __global__ read of size 4 bytes"    # https://nvbugs/4283108 [Require Change] Test Procedure on 2800789/Good device frames reported with relative return address
    CHECK_POINT1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:21:device_kernel0(int *)"
    CHECK_POINT1_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:21"
    CHECK_POINT2: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:28:device_kernel0(int *)"
    CHECK_POINT2_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:28"
    CHECK_POINT3: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:31:device_kernel0(int *)"
    CHECK_POINT3_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:31"
    CHECK_POINT4: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:41:device_kernel0(int *)"
    CHECK_POINT4_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:41"
    CHECK_POINT5: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:53:device_kernel0(int *)"
    CHECK_POINT5_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:53"
    CHECK_POINT6: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:61:device_kernel0(int *)"
    CHECK_POINT6_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:61"
    CHECK_POINT7: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:70:device_kernel0(int *)"
    CHECK_POINT7_12_3: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:70"
    CHECK_POINT8: "Address 0x3 is misaligned"
    CHECK_POINT8_12_3: "Access at 0x3 is misaligned"
    CHECK_POINT8_13_0: "Access to 0x3 is misaligned"
    CHECK_POINT9: "device_kernel0(int *)"
    CHECK_POINT9_12_3: "device_kernel0(int *)"


READ_ONLY_FLAG:
  LOG_NAME: ${READ_ONLY_FLAG_PATH}/read_only_flag.log
  STEP1:
    CMD: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --show-backtrace no memmapBasic.exe read-only
  CHECK_POINT: 'ERROR SUMMARY: 1 error'

DETAIL_SESSION_OPTION:
  LOG_NAME: ${DETAIL_SESSION_OPTION_PATH}/detail_session_option.log
  STEP1:
    CMD: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --print-session-details yes ./asyncAPI
    CMD1: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --print-session-details ./asyncAPI
  STEP2:
    CMD: cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe out.nvm; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --print-session-details yes --save-session-details yes --save out.nvm ./asyncAPI
    CMD1: cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/rm.exe out.nvm; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --print-session-details --save-session-details --save out.nvm ./asyncAPI
  STEP3:
    CMD: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read out.nvm --print-session-details yes
    CMD1: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read out.nvm --print-session-details
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT1: 'Process ID'
  CHECK_POINT2: 'Created'
  CHECK_POINT3: 'System OS'
  CHECK_POINT4: 'OS build'
  CHECK_POINT5: 'System OS'
  CHECK_POINT6: 'OS build'
  CHECK_POINT7: 'System CPU'
  CHECK_POINT8: 'CPU architecture'
  CHECK_POINT9: 'Computer Name'
  CHECK_POINT10: 'CUDA version'
  CHECK_POINT11: 'Display Driver version'

SANITIZER_XML_OPTION:
  LOG_NAME: ${SANITIZER_XML_OPTION_PATH}/sanitizer_xml_option.log
  STEP1:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r 2806646; mkdir 2806646; cd 2806646; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2806646/RelativeReturnAddress.cu
    CMD2: cd ${TOOLS_HOME}/2806646; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -lineinfo -lcudart -gencode arch=compute_%s,code=sm_%s -o RelativeReturnAddress RelativeReturnAddress.cu
  STEP2:
    CMD1: cd ${TOOLS_HOME}/2806646; ${CMDER_BIN_PATH}/rm out.xml; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer"  --xml yes --save out.xml ./RelativeReturnAddress | ${CMDER_BIN_PATH}/tee out.txt
    CMD1_1: cd ${TOOLS_HOME}/2806646; ${CMDER_BIN_PATH}/rm out.xml; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" %s --xml --save out.xml ./RelativeReturnAddress | ${CMDER_BIN_PATH}/tee out.txt
    CMD2: cd ${TOOLS_HOME}/2806646; ${CMDER_BIN_PATH}/rm out.xml; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --launch-timeout 600 --xml yes --save out.xml ./RelativeReturnAddress | ${CMDER_BIN_PATH}/tee out.txt

# Only Work for GPU SM greater than 8.0
# C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.6\bin\win64\Debug\simpleAWBarrier.exe starting...
# GPU Device 0: "Ampere" with compute capability 8.6
#
# Launching normVecByDotProductAWBarrier kernel with numBlocks = 46 blockSize = 1024
# ========= Warning: Detected overflow of tracked cuda::barrier structures. Results might be incorrect. Try using --num-cuda-barriers to fix the issue
# =========
# CUDA error at C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.6\Samples\0_Introduction\simpleAWBarrier\simpleAWBarrier.cu:239 code=999(cudaErrorUnknown) "cudaStreamSynchronize(stream)"
# ========= Target application returned an error
# ========= ERROR SUMMARY: 1 error
CUDA_BARRIERS_OPTION:
  LOG_NAME: ${CUDA_BARRIERS_OPTION_PATH}/cuda_barriers_option.log
  PREPARE:
    PREPARE_CMD: cd ${CUDA_BARRIERS_OPTION_PATH}; ${CMDER_BIN_PATH}/rm -rf simpleAWBarrier_mod; ${CMDER_BIN_PATH}/mkdir simpleAWBarrier_mod; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
    CMD1: cd ${CUDA_BARRIERS_OPTION_PATH}/simpleAWBarrier_mod; ${CMDER_BIN_PATH}/wget --user {} --password {} {}/P1072_T2819793/simpleAWBarrier_mod.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc"  -arch sm_{} -std=c++17 -I ../Common -o simpleAWBarrier_mod.exe simpleAWBarrier_mod.cu
    # https://nvbugs/4613469 [Require Change] Test Procedure on 2819793/Option - num-cuda-barriers
  # Updated for Step 5 Repeat above steps with racecheck and we should get the similar results.
  STEP1:
    CMD: cd "${CUDA_BARRIERS_OPTION_PATH}/simpleAWBarrier_mod"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool %s ./simpleAWBarrier_mod.exe
  STEP2:
    CMD: cd "${CUDA_BARRIERS_OPTION_PATH}/simpleAWBarrier_mod"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool %s --num-cuda-barriers 0 ./simpleAWBarrier_mod.exe
  STEP3:
    CMD: cd "${CUDA_BARRIERS_OPTION_PATH}/simpleAWBarrier_mod"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool %s --num-cuda-barriers 2 ./simpleAWBarrier_mod.exe
  STEP4:
    CMD: cd "${CUDA_BARRIERS_OPTION_PATH}/simpleAWBarrier_mod"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool %s --num-cuda-barriers 1 ./simpleAWBarrier_mod.exe
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT1: 'ERROR SUMMARY: 1 error'
  CHECK_POINT_RACECHECK: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT_RACECHECK1: 'RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)'
  CHECKPOINT2: 'Warning: Detected overflow of tracked cuda::barrier structures. Results might be incorrect. Try using --num-cuda-barriers to fix the issue'

# Remove CHECK_POINT2 from 'Leaked 65536 bytes at' to 'Leaked 65,536 bytes at' on Mar 1, 2022
# 11.6 The output on Windows show '========= Leaked 65,536 bytes at 0xb14610000'
# 11.7 The output on Windows show '========= Leaked 65536 bytes at 0x7f48a5610000'
# ==> Remove CHECK_POINT2. No need to judge it anymore.
LEAK_CHECK_ALLOC:
  LOG_NAME: ${LEAK_CHECK_ALLOC_PATH}/leak_check_alloc.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r 2833990; mkdir 2833990; cd 2833990; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/2833990/nvcc_sanitizers.cu
    CMD1: cd ${TOOLS_HOME}/2833990; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -o nvcc_sanitizers nvcc_sanitizers.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/2833990; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full ./nvcc_sanitizers
  CHECK_POINT: 'ERROR SUMMARY: 3 errors'
  CHECK_POINT1: 'LEAK SUMMARY: 196608 bytes leaked in 3 allocations'

# Default to all for target-processes since 12.4 DTCS-1373
TARGET_FILTER_OPTION:
  LOG_NAME: ${TARGET_FILTER_OPTION_PATH}/target_filter_option.log
  PREPARE:
    CMD1: cd "%s/matrixMul"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD2: cd "%s/asyncAPI"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD3: cd "%s/mergeSort"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
    CMD4: cd ${TOOLS_HOME}/../; rm run.bat; touch run.bat
  STEP1:
    CMD1: cd ${TARGET_FILTER_OPTION_PATH}; ${CMDER_BIN_PATH}/rm -r vectorAdd_memleak; ${CMDER_BIN_PATH}/mkdir vectorAdd_memleak; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"; cd vectorAdd_memleak; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2834633/vectorAdd_memLeak/vectorAdd_memleak.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -o vectorAdd_memleak.exe vectorAdd_memleak.cu
    CMD2: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm -r test.bat; ${CMDER_BIN_PATH}/touch test.bat
  STEP2:
    CMD1: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes all cmd.exe /c test.bat
    CMD2: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes all --target-processes-filter mergeSort.exe cmd.exe /c test.bat
    CMD3: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes all --target-processes-filter regex:.*merge cmd.exe /c test.bat
    CMD4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes all --target-processes-filter regex:.*vector cmd.exe /c test.bat
    CMD1_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full cmd.exe /c test.bat
    CMD2_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes-filter mergeSort.exe cmd.exe /c test.bat
    CMD3_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes-filter regex:.*merge cmd.exe /c test.bat
    CMD4_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --target-processes-filter regex:.*vector cmd.exe /c test.bat
  STEP3:
    CMD1: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes-filter matrixMul cmd.exe /c test.bat
    CMD2: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all --target-processes-filter Matrix cmd.exe /c test.bat
    CMD3: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all --target-processes-filter regex:*Mul cmd.exe /c test.bat
    CMD1_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes application-only --target-processes-filter matrixMul cmd.exe /c test.bat
    CMD2_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes-filter Matrix cmd.exe /c test.bat
    CMD3_12_4: cd ${TOOLS_HOME}/../; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes-filter regex:*Mul cmd.exe /c test.bat

  RESTORE:
    CMD1: cd "%s/vectorAdd"; ${CMDER_BIN_PATH}/rm.exe -r vectorAdd.cu; ${CMDER_BIN_PATH}/mv.exe  vectorAdd.cu_bak vectorAdd.cu; cd "%s/vectorAdd"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
  CHECK_POINT1: "LEAK SUMMARY: 200000 bytes leaked in 1 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 1 error"
  CHECK_POINT3: "LEAK SUMMARY: 0 bytes leaked in 0 allocations"
  CHECK_POINT4: "ERROR SUMMARY: 0 errors"
  CHECK_POINT5: "Error: Target application terminated before first instrumented API call"
  CHECK_POINT6: 'Invalid regex provided to --target-processes-filter option: The repeat operator "*" cannot start a regular expression.'
  CHECK_POINT6_12_5: 'Invalid regex provided to --target-processes-filter option.'
  CHECK_POINT7: "The error occurred while parsing the regular expression: '>>>HERE>>>*Mul'"
  CHECK_POINT7_12_5: "Invalid regex. regex_error(error_badrepeat): One of *?+{ was not preceded by a valid regular expression."

# https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3537434 [AutoEnhance] Test Procedure on 2897883/[Sanitizer][RCCA] Stack overflow should be detected in recursive function
# Added option --destroy-on-device-error kernel for compute-sanitizer on Feb 15, 2022
STACK_OVERFLOW_CHECK:
  LOG_NAME: ${STACK_OVERFLOW_CHECK_PATH}/stack_overflow_check.log
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; ${CMDER_BIN_PATH}/rm -r 2897883; mkdir 2897883; cd 2897883; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T2897883/test.cu
    CMD1: cd ${TOOLS_HOME}/../2897883; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -lineinfo -arch=sm_%s -o test test.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2897883; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel ./test
  CHECK_POINT1: 'Stack overflow'
  CHECK_POINT1_12_3: 'Stack overflow'
  CHECK_POINT2: 'test.cu:3:test(int)'
  CHECK_POINT2_12_3: 'test.cu:3'
  CHECK_POINT3: 'test.cu:9:test(int)'
  CHECK_POINT3_12_3: 'test.cu:9'
  CHECK_POINT4: 'ERROR SUMMARY: 1 error'
  CHECK_POINT4_12_3: 'ERROR SUMMARY: 1 error'
  CHECK_POINT5: 'test(int)'
  CHECK_POINT5_12_3: 'test(int)'


# Can not use make or cumake on Windows, use nvcc instead of make or cumake.
# https://nvbugswb.nvidia.com/NVBugs5/redir.aspx?url=/3572868 [CudaTools][11.7][sanitizer][2022.2.0]Get different outputs of memcheck_demo on Turing and Ampere/Volta/Pascal
SANITIZER_MEMCHECK_DEMO:
  LOG_NAME: ${SANITIZER_MEMCHECK_DEMO_PATH}/sanitizer_memcheck_demo.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r memcheck; mkdir memcheck; cd memcheck;  ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/memcheck;  ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD3: cd ${TOOLS_HOME}/memcheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -o memcheck_demo memcheck_demo.cu; ls memcheck_demo.exe
    CMD4: cd ${TOOLS_HOME}/memcheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o memcheck_demo memcheck_demo.cu; ls memcheck_demo.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/memcheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-error no ./memcheck_demo
  STEP2:
    CMD1: cd ${TOOLS_HOME}/memcheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-error no ./memcheck_demo
  CHECK_POINT1: "Invalid __global__ write of size 4 bytes"
  CHECK_POINT1_1: "Invalid __global__ write of size 4 bytes"
  CHECK_POINT2: "unaligned_kernel()"
  CHECK_POINT2_1: "unaligned_kernel()"
  CHECK_POINT3: "misaligned"
  CHECK_POINT3_1: "misaligned"
  CHECK_POINT4: "out_of_bounds_kernel()"
  CHECK_POINT4_1: "out_of_bounds_kernel()"
  CHECK_POINT5: "is out of bounds"
  CHECK_POINT5_1: "is out of bounds"
  CHECK_POINT6: "ERROR SUMMARY: 2 errors"
  CHECK_POINT6_1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT7: "memcheck_demo.cu:34:unaligned_kernel()"
  CHECK_POINT7_1: "memcheck_demo.cu:34"
  CHECK_POINT8: "memcheck_demo.cu:39:out_of_bounds_function()"
  CHECK_POINT8_1: "memcheck_demo.cu:39"

# Can not use make or cumake on Windows, use nvcc instead of make or cumake.
SANITIZER_RACECHECK_WARP:
  LOG_NAME: ${SANITIZER_RACECHECK_WARP_PATH}/sanitizer_racecheck_warp.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r racecheck; mkdir racecheck;cd racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/Makefile
    CMD2: cd ${TOOLS_HOME}/racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/warp_error.cu
    CMD3: cd ${TOOLS_HOME}/racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/block_error.cu
    CMD4: cd ${TOOLS_HOME}/racecheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o warp_error warp_error.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o block_error block_error.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/racecheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --racecheck-report hazard --print-limit 0 ./warp_error
  CHECK_POINT1: "Warning: (Warp Level Programming) Potential RAW hazard detected"
  CHECK_POINT1_1: "Warning: (Warp Level Programming) Potential RAW hazard detected"
  CHECK_POINT2: "Write Thread"
  CHECK_POINT2_1: "Write Thread"
  CHECK_POINT3: "warp_error.cu:44:sumKernel"
  CHECK_POINT3_1: "warp_error.cu:44"
  CHECK_POINT4: "Read Thread"
  CHECK_POINT4_1: "Read Thread"
  CHECK_POINT5: "warp_error.cu:56:sumKernel"
  CHECK_POINT5_1: "warp_error.cu:56"
  CHECK_POINT6: "RACECHECK SUMMARY: 248 hazards displayed (0 errors, 248 warnings)"
  CHECK_POINT6_1: "RACECHECK SUMMARY: 248 hazards displayed (0 errors, 248 warnings)"
  CHECK_POINT7: "sumKernel"
  CHECK_POINT7_1: "sumKernel"


# Can not use make or cumake on Windows, use nvcc instead of make or cumake.
SANITIZER_RACECHECK_BLOCK:
  LOG_NAME: ${SANITIZER_RACECHECK_BLOCK_PATH}/sanitizer_racecheck_block.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r racecheck; mkdir racecheck; cd racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/Makefile
    CMD2: cd ${TOOLS_HOME}/racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/warp_error.cu
    CMD3: cd ${TOOLS_HOME}/racecheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/block_error.cu
    CMD4: cd ${TOOLS_HOME}/racecheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o warp_error warp_error.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o block_error block_error.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/racecheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --racecheck-report analysis ./block_error
  # https://nvbugs/4281624 [Require Change] Test Procedure on 2931396/Racecheck - Block-level Hazards
  CHECK_POINT1: "Error: Race reported between Write access at "
  CHECK_POINT1_1: "Error: Race reported between Write access at sumKernel"
  CHECK_POINT2: "block_error.cu:41:sumKernel"
  CHECK_POINT2_1: "block_error.cu:41"
  CHECK_POINT3: "Read access at"
  CHECK_POINT3_1: "Read access at sumKernel"
  CHECK_POINT4: "block_error.cu:51:sumKernel"
  CHECK_POINT4_1: "block_error.cu:51"
  CHECK_POINT5: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"
  CHECK_POINT5_1: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"
  CHECK_POINT6: "sumKernel"
  CHECK_POINT6_1: "sumKernel"


# Can not use make or cumake on Windows, use nvcc instead of make or cumake.
SANITIZER_INITCHECK_ERROR:
  LOG_NAME: ${SANITIZER_INITCHECK_ERROR_PATH}/sanitizer_initcheck_error.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r initcheck; mkdir initcheck; cd initcheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Initcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/initcheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Initcheck/memset_error.cu
    CMD3: cd ${TOOLS_HOME}/initcheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o memset_error memset_error.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/initcheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck  ./memset_error
  CHECK_POINT1: "Uninitialized __global__ memory read of size 4 bytes"
  CHECK_POINT1_1: "Uninitialized __global__ memory read of size 4 bytes"
  CHECK_POINT2: "memset_error.cu:41:vectorAdd"
  CHECK_POINT2_1: "memset_error.cu:41"
  CHECK_POINT3: " ERROR SUMMARY: 48 errors"
  CHECK_POINT3_1: " ERROR SUMMARY: 48 errors"
  CHECK_POINT4: " vectorAdd"
  CHECK_POINT4_1: "vectorAdd"

SANITIZER_LEAKCHECK_MEMCHECK_DEMO:
  LOG_NAME: ${SANITIZER_LEAKCHECK_MEMCHECK_DEMO_PATH}/sanitizer_leakcheck_memcheck_demo.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r memcheck; mkdir memcheck; cd memcheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/memcheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD3: cd ${TOOLS_HOME}/memcheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o memcheck_demo memcheck_demo.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/memcheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error=kernel --leak-check=full --report-api-error no ./memcheck_demo
  CHECK_POINT1: "Invalid __global__ write of size 4 bytes"
  CHECK_POINT2: "is misaligned"
  CHECK_POINT3: "Saved host backtrace up to driver entry point at"
  CHECK_POINT4: "LEAK SUMMARY: 1024 bytes leaked in 1 allocations"
  CHECK_POINT5: "ERROR SUMMARY: 3 errors"

SANITIZER_SYNCCHECK_DIVERGENT:
  LOG_NAME: ${SANITIZER_SYNCCHECK_DIVERGENT_PATH}/sanitizer_synccheck_divergent.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r synccheck; mkdir synccheck; cd synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/Makefile
    CMD2: cd ${TOOLS_HOME}/synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/divergent_threads.cu
    CMD3: cd ${TOOLS_HOME}/synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/illegal_syncwarp.cu
    CMD4: cd ${TOOLS_HOME}/synccheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o divergent_threads divergent_threads.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o illegal_syncwarp illegal_syncwarp.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/synccheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck ./divergent_threads
  CHECK_POINT1: "Barrier error detected. Divergent thread(s) in warp"
  CHECK_POINT2: "ERROR SUMMARY: 16 errors"

SANITIZER_SYNCCHECK_SYNCWARP:
  LOG_NAME: ${SANITIZER_SYNCCHECK_SYNCWARP_PATH}/sanitizer_synccheck_syncwarp.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r synccheck; mkdir synccheck; cd synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/Makefile
    CMD2: cd ${TOOLS_HOME}/synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/divergent_threads.cu
    CMD3: cd ${TOOLS_HOME}/synccheck; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/illegal_syncwarp.cu
    CMD4: cd ${TOOLS_HOME}/synccheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o divergent_threads_debug divergent_threads.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o illegal_syncwarp_debug illegal_syncwarp.cu; ls *.exe
    CMD5: cd ${TOOLS_HOME}/synccheck; cumake clean; cmd.exe /c "${BASE_PATH}/bin/nvcc" -o divergent_threads_release divergent_threads.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -o illegal_syncwarp_release illegal_syncwarp.cu; ls *.exe
  STEP1:
    CMD1: cd ${TOOLS_HOME}/synccheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck illegal_syncwarp_debug.exe
    CMD2: cd ${TOOLS_HOME}/synccheck; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck illegal_syncwarp_release.exe
  CHECK_POINT1: "Barrier error detected. Invalid arguments"
  CHECK_POINT2: "ERROR SUMMARY: 17 errors"

# http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2594371
# Memory pools is not supported on Windows with TCC.
# PS C:\tesla_automation_results\sanitizer_20220418\nvidia-compute-sanitizer-test> .\MallocAsync.exe
#    At C:\dvs\p4\build\sw\devtools\Agora\Rel\DTC_H\Shared\ComputeSanitizer\Tests\Common\Src\TestUtilsCuda.cpp:136:
#    Memory pools is not supported
# Not support vGPU/Windows TCC for now, run the sample before testing to confirm if it returns as supported or not
LEAKCHECK_DEVICES_ALLOCATIONS:
  LOG_NAME: ${LEAKCHECK_DEVICES_ALLOCATIONS_PATH}/leakcheck_devices_allocations.log
  STEP1:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --leak-check full ./MallocAsync
  CHECK_POINT1: "LEAK SUMMARY: 4 bytes leaked in 1 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 3 errors"
  CHECK_POINT_TCC1: "Memory pools is not supported"
  CHECK_POINT_TCC2: "Target application returned an error"

# Memory pools is not supported on Windows with TCC.
# PS C:\tesla_automation_results\sanitizer_20220418\nvidia-compute-sanitizer-test> .\MallocAsyncRace.exe
# At C:\dvs\p4\build\sw\devtools\Agora\Rel\DTC_H\Shared\ComputeSanitizer\Tests\Common\Src\TestUtilsCuda.cpp:136:
# Memory pools is not supported
# Updated the test steps based on CUDA 11.8 Changes on May 6, 2022
# Modified case 2949010 based on Note: step1 39 errors, step 2 15 errors, step3 24 errors in 12.1 as DTCS-801 addressed on Feb 15, 2023
MEMCHECK_OPTION_TRACK_STREAM:
  LOG_NAME: ${MEMCHECK_OPTION_TRACK_STREAM_PATH}/memcheck_option_track_stream.log
  STEP1:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no .\MallocAsyncRace.exe kernel
  STEP2:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races use-before-alloc --show-backtrace no .\MallocAsyncRace.exe kernel
  STEP3:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races use-after-free --show-backtrace no .\MallocAsyncRace.exe kernel
  STEP4:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races no --show-backtrace no .\MallocAsyncRace.exe kernel
  CHECK_POINT1: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2: "Free-before-alloc on allocation of size 1 bytes"
  CHECK_POINT3: "Use-after-free on allocation of size 1 bytes"
  CHECK_POINT4: "ERROR SUMMARY: 39 errors"
  CHECK_POINT5: "ERROR SUMMARY: 15 errors"
  CHECK_POINT6: "ERROR SUMMARY: 24 errors"
  CHECK_POINT7: "ERROR SUMMARY: 0 errors"

SYNCCHECK_OPTION_MISS_BARRIER:
  LOG_NAME: ${SYNCCHECK_OPTION_MISS_BARRIER_PATH}/synccheck_option_miss_barrier.log
  # Known bug: https://nvbugs/3552592 [CudaTools][11.7][Sanitizer][2022.2.0]It will hang when testing "--missing-barrier-init-is-fatal no"
  STEP1:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --tool synccheck --missing-barrier-init-is-fatal yes ./SyncCudaBarrier 6
  CHECK_POINT1: "Barrier error detected. Missing init"
  CHECK_POINT2: "ERROR SUMMARY: 32 errors"

COREDUMP_GENERATION_SUPPORT:
  LOG_NAME: ${COREDUMP_GENERATION_SUPPORT_PATH}/coredump_generation_support.log
  STEP1:
    CMD1: cd ${RESULTS_HOME}; ${CMDER_BIN_PATH}/rm -rf 2806977-coredump; mkdir 2806977-coredump; cd 2806977-coredump; ${CMDER_BIN_PATH}/wget *******************************/Automation/CUDA_Linux/Sources/ComputeTools/coredump/coredump.cu;
          cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o coredump coredump.cu; ls coredump.exe
  STEP2:
    CMD1: cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump=yes --destroy-on-device-error kernel coredump.exe
    CMD1_1: cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump --destroy-on-device-error kernel coredump.exe
    CMD2: cd ${RESULTS_HOME}/2806977-coredump; ${CMDER_BIN_PATH}/ls *.nvcudmp
  STEP3:
    CMD1: cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump=yes --coredump-name a.nvcudmp --destroy-on-device-error kernel coredump.exe
    CMD1_1: cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump --coredump-name a.nvcudmp --destroy-on-device-error kernel coredump.exe
    CMD2: cd ${RESULTS_HOME}/2806977-coredump; ${CMDER_BIN_PATH}/ls a.nvcudmp
    CMD3: $ENV:CUDA_COREDUMP_SHOW_PROGRESS=1; cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump=yes --coredump-name a.nvcudmp --destroy-on-device-error kernel coredump.exe
    CMD3_1: $ENV:CUDA_COREDUMP_SHOW_PROGRESS=1; cd ${RESULTS_HOME}/2806977-coredump; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --generate-coredump --coredump-name a.nvcudmp --destroy-on-device-error kernel coredump.exe
  CHECK_POINT1: "coredump.exe"
  CHECK_POINT2: "ERROR SUMMARY: 16 errors"
  CHECK_POINT3: ".nvcudmp"
  CHECK_POINT4: "a.nvcudmp"
  CHECK_POINT5: "coredump: Writing ELF file to a.nvcudmp"
  CHECK_POINT6: "coredump: Writing out global memory"
  CHECK_POINT7: "coredump: 100%..."


# localRecursive On Linux : 7 Host Frames
# localRecursive on Windows: 9 Host Frames
CALL_HOST_DEVICE:
  LOG_NAME: ${CALL_HOST_DEVICE_PATH}/call_host_device.log
  STEP1:
    CMD1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel localRecursive.exe
  STEP2:
    CMD1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --num-callers-device 0 --num-callers-host 0 localRecursive.exe
  STEP3:
    CMD1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --num-callers-device 1 --num-callers-host 1 localRecursive.exe
  STEP4:
    CMD1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --num-callers-device 2 --num-callers-host 2 localRecursive.exe
  STEP5:
    CMD1: cd %s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --num-callers-device 2 --num-callers-host 2 --show-backtrace no localRecursive.exe
  CHECK_POINT1: "ERROR SUMMARY: 1 error"

DEPRECAT_CUDA_MEMCHECK:
  LOG_NAME: ${DEPRECAT_CUDA_MEMCHECK_PATH}/deprecat_cuda_memcheck.log
  STEP1:
    CMD1: cd "%s/asyncAPI"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${BASE_BIN_PATH}/cuda-memcheck" asyncAPI.exe
  CHECK_POINT1: "This tool is deprecated and will be removed in a future release of the CUDA toolkit"
  CHECK_POINT2: "Please use the compute-sanitizer tool as a drop-in replacement"

# Modified case 3051676 according to Step 1/2/3/4 all change to 39 errors in 12.1 as DTCS-801 addressed on Feb 15, 2023
MEMCHECK_ORDERED_RACE_DETECTION:
  LOG_NAME: ${MEMCHECK_ORDERED_RACE_DETECTION_PATH}/memcheck_ordered_race_detection.log
  STEP1:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace kernel
  STEP2:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memset
  STEP3:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memcpy-host-to-device
  STEP4:
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memcpy-device-to-device
  CHECK_POINT1: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2: "Free-before-alloc"
  CHECK_POINT3: "Use-after-free"
  CHECK_POINT4: "ERROR SUMMARY: 39 errors"
  CHECK_POINT5: "Use-before-alloc"
  CHECK_POINT6: "ERROR SUMMARY: 39 errors"

SANITIZER_GUARDWORD_CHECK:
  LOG_NAME: ${SANITIZER_GUARDWORD_CHECK_PATH}/sanitizer_guardword_check.log
  STEP1:
    CMD: cd "${SANITIZER_HOME_PATH}"; dir -Recurse |Select-String -pattern "GA107" | tee ga107Strings.txt
    CHECKPOINT: "GA107"
  STEP2:
    CMD1: cd "%s/GuardwordCheck"; ${PYTHON} RunGuardwordCheck.py --config ComputeSanitizer/guardword_config.json --features . ../../nvidia-compute-sanitizer
    CMD2: cd "%s/GuardwordCheck"; ${PYTHON} RunGuardwordCheck.py --config ComputeSanitizer/guardword_config.json --features . "${SANITIZER_HOME_PATH}"
    CHECKPOINT: "0 guardword violations found"

LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY:
  LOG_NAME: ${LAUNCH_WITHOUT_SUFFIX_SPECIFIED_EXPLICITLY_PATH}/launch_without_suffix_specified_explicitly.log
  PREPARE:
       CMD: cd "%s\4_CUDA_Libraries\oceanFFT"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"; cd "${SAMPLE_BIN_PATH}"; ${CMDER_BIN_PATH}/ls -la oceanFFT.*
       CHECKPOINT: "oceanFFT.exe"
  STEP1:
       CMD1: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" oceanFFT.exe
       CMD2: Stop-Process -name oceanFFT -Force
       CHECKPOINT: "ERROR SUMMARY: 0 errors"

# run All checkers - add support __nv_aligned_device_malloc()   3071342
SUPPORT_ALIGNED_DEVICE_MALLOC:
  LOG_NAME: ${SUPPORT_ALIGNED_DEVICE_MALLOC_PATH}/support_aligned_device_malloc.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r P1072_T3071342; mkdir P1072_T3071342; cd P1072_T3071342; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T3071342/test.cu
    CMD2: cd ${TOOLS_HOME}/P1072_T3071342; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -arch=sm_%s -o test test.cu
  STEP1:
    CMD1: cd ${TOOLS_HOME}/P1072_T3071342; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./test
    CMD2: cd ${TOOLS_HOME}/P1072_T3071342; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck ./test
    CMD3: cd ${TOOLS_HOME}/P1072_T3071342; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck ./test
    CMD4: cd ${TOOLS_HOME}/P1072_T3071342; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck ./test
  CHECK_POINT1: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT2: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'

SUPPORT_LDSM:
  LOG_NAME:  ${SUPPORT_LDSM_PATH}/sanitizer_support_LDSM.log
  PREPARE:
    CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -r P1072_T3079874; mkdir P1072_T3079874; cd P1072_T3079874; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T3079874/CuLdSm.cu
    CMD2: cd ${TOOLS_HOME}/P1072_T3079874; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -arch=sm_%s -lineinfo -o CuLdSm CuLdSm.cu
  STEP1:
    CMD1: cd ${TOOLS_HOME}/P1072_T3079874; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full ./CuLdSm
    CHECK_POINT1: 'LEAK SUMMARY: 2052 bytes leaked in 2 allocations'
    CHECK_POINT2: 'ERROR SUMMARY: 2 errors'
  STEP2:
    CMD1: cd ${TOOLS_HOME}/P1072_T3079874; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck  ./CuLdSm
    CHECK_POINT1: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'

# 3090527
INITCHECK_TRACK_UNUSED_MEMORY:
  LOG_NAME: ${INITCHECK_TRACK_UNUSED_MEMORY_PATH}/initcheck_track_unused_memory.log
  PREPARE:
     CMD1: cd ${TOOLS_HOME}; ${CMDER_BIN_PATH}/rm -rf 3090527; mkdir 3090527; cd 3090527; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/Unused/unused.cu
     CMD2: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${BASE_BIN_PATH}/nvcc" -g -G -o unused unused.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes unused.exe 80
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory unused.exe 80
    CHECKPOINT1: "20% of allocation were unused."
    CHECKPOINT2: "ERROR SUMMARY: 1 error"
  STEP2:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 75 unused.exe 20
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --unused-memory-threshold 75 unused.exe 20
    CHECKPOINT1: "80% of allocation were unused."
    CHECKPOINT2: "ERROR SUMMARY: 1 error"
  STEP3:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 80 unused.exe 20
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --unused-memory-threshold 80 unused.exe 20
    CHECKPOINT1: "80% of allocation were unused."
    CHECKPOINT2: "ERROR SUMMARY: 1 error"
  STEP4:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 81 unused.exe 20
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --unused-memory-threshold 81 unused.exe 20
    CHECKPOINT1: "COMPUTE-SANITIZER"
    CHECKPOINT2: "ERROR SUMMARY: 0 errors"
  STEP5:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 120 unused.exe
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --unused-memory-threshold 120 unused.exe
    CHECKPOINT: "the argument for option 'unused-memory-threshold' is invalid"
  STEP6:
    CMD: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --unused-memory-threshold 75 --xml yes --save out.xml unused.exe 20
    CMD_12_3: cd ${TOOLS_HOME}/3090527; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --unused-memory-threshold 75 --xml --save out.xml unused.exe 20
    CHECKPOINT1: "80% of allocation were unused."
    CHECKPOINT2: "ERROR SUMMARY: 1 error"
    CHECKPOINT3: "UnusedMemory"
    CHECKPOINT4: "80"


# [Sanitizer][RCCA]Initcheck for steam capture 3156524
SANITIZER_STEAM_CAPTURE:
  LOG_NAME: ${SANITIZER_STEAM_CAPTURE_PATH}/sanitizer_steam_capture.log
  PREPARE:
    CMD1: cd ${SANITIZER_STEAM_CAPTURE_PATH}/; ${CMDER_BIN_PATH}/rm -r 3156524; mkdir 3156524; cd 3156524; ${CMDER_BIN_PATH}/wget --user {} --password {} {}/3156524/capture_memset.cu
    CMD2: cd ${SANITIZER_STEAM_CAPTURE_PATH}/3156524; cmd.exe /c "${BASE_PATH}/bin/nvcc" -arch=compute_{} -g -lineinfo capture_memset.cu -o capture_memset
  STEP1:
    CMD1: cd ${SANITIZER_STEAM_CAPTURE_PATH}/3156524; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck capture_memset.exe
  CHECK_POINT1: "ERROR SUMMARY: 0 error"

# Racechek option --racecheck-memcpy-async 3158301
SANITIZER_RACECHECK_MEMCPY_ASYNC:
  LOG_NAME: ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}/sanitizer_racecheck_memcpy_async.log
  PREPARE:
    CMD1: cp {}/raceMemcpyAsync.exe ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}
  STEP1:
    CMD1: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool=racecheck raceMemcpyAsync.exe 1
    CMD2: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool=racecheck raceMemcpyAsync.exe 2
  STEP2:
    CMD1: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool=racecheck --racecheck-memcpy-async no raceMemcpyAsync.exe 1
    CMD2: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool=racecheck --racecheck-memcpy-async no raceMemcpyAsync.exe 2
  CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  CHECK_POINT2: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"
  CHECK_POINT3: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

GRAPHIC_SAMPLE_COVERAGE_3084216:
  LOG_NAME: ${GRAPHIC_SAMPLE_COVERAGE_PATH}/graphic_sample_coverage.log
  SAMPLE_LIST: "simpleGL,oceanFFT,simpleD3D11,simpleD3D11Texture,simpleD3D12,postProcessGL,optixPathTracer"
  STEP1:  #  compute-sanitizer --leak-check full ./sample
    CMD: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full %s.exe
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --check-optix-leaks yes %s.exe                  # For optix samples
    CMD1_1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --check-optix-leaks %s.exe                    # For optix samples
  STEP2:  # compute-sanitizer --tool initcheck yes ./sample
    CMD: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck %s.exe
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --check-optix=yes %s.exe         # For optix samples
    CMD1_1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --check-optix %s.exe       # For optix samples
  STEP3:  # compute-sanitizer --tool synccheck ./sample
    CMD: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck %s.exe
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck %s.exe --qatest                # For oceanFFT.exe
  STEP4: # compute-sanitizer --tool racecheck ./sample
    CMD: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck %s.exe
    CMD1: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck %s.exe --qatest                # For oceanFFT.exe


# Memcheck- stream ordered race check for read access 3178884
SANITIZER_CHECK_READ_ACCESS:
  LOG_NAME: ${SANITIZER_CHECK_READ_ACCESS_PATH}/sanitizer_check_read_access.log
  PREPARE:
    CMD1: cd ${SANITIZER_CHECK_READ_ACCESS_PATH}; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/ReadAccess/read_access.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -o read_access read_access.cu
  STEP1:
    CMD1: cd ${SANITIZER_CHECK_READ_ACCESS_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --track-stream-ordered-races=all --show-backtrace=no read_access.exe
  CHECK_POINT1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2: "Use-before-alloc"
  CHECK_POINT3: "Use-after-free"

# Sanitizer supports Unicode files and paths 3183138
SANITIZER_SUPPORT_UNICODE_FILE:
  LOG_NAME: ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/sanitizer_support_unicode_file.log
  PREPARE:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}; ${CMDER_BIN_PATH}/rm -fr %s; ${CMDER_BIN_PATH}/mkdir %s
    CMD2: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_3183138/coredump.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o coredump.exe coredump.cu
    CMD3: ${CMDER_BIN_PATH}/cp -r "${BASE_PATH}/compute-sanitizer" ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s
  STEP1:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --report-api-error no coredump.exe
  STEP2:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --report-api-error no --save %s-save.log coredump.exe
    CMD2: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --read %s-save.log
  STEP3:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --log-file %s-logfile.log coredump.exe; ${CMDER_BIN_PATH}/cat %s-logfile.log
  STEP4:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --xml yes --save %s-save.xml coredump.exe; ${CMDER_BIN_PATH}/cat %s-save.xml
    CMD_12_3: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --report-api-error no --xml --save %s-save.xml coredump.exe; ${CMDER_BIN_PATH}/cat %s-save.xml
  STEP5:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --injection-path=${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/%s/compute-sanitizer coredump.exe
  CHECK_POINT1: "ERROR SUMMARY: 16 errors"
  CHECK_POINT2: "Invalid __global__ read of size 4 bytes"
  CHECK_POINT3: "ERROR SUMMARY: 18 errors"


# Memcheck - vector atomics 3192559
SANITIZER_MEMCHECK_VECTOR_ATOMICS:
  LOG_NAME: ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}/sanitizer_memcheck_vector_atomics.log
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_3192559/vector_atomics_globals-4sanitizer.cu
    CMD2: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -lineinfo -arch sm_%s -o vector_atomics-nullF vector_atomics_globals-4sanitizer.cu
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" vector_atomics-nullF.exe
  CHECK_POINT1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2: "Invalid __global__ write of size 16 bytes"
  CHECK_POINT3: "vector_atomics_globals-4sanitizer.cu:85:call_atomic_v4_system"
  CHECK_POINT4: "call_atomic_v4_system"
  CHECK_POINT5: "Copy output data from the CUDA device to the host memory"
  CHECK_POINT1_12_3: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2_12_3: "Invalid __global__ write of size 16 bytes"
  CHECK_POINT3_12_3: "vector_atomics_globals-4sanitizer.cu:85"
  CHECK_POINT4_12_3: "call_atomic_v4_system"
  CHECK_POINT5_12_3: "Copy output data from the CUDA device to the host memory"



# Memcheck - CNP support for cluster kernel attributes 3192566
SANITIZER_MEMCHECK_CNP_SUPPORT:
  LOG_NAME: ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}/sanitizer_memcheck_CNP_support.log
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_3192566/cnp_v2_static_cluster-4sanitizer.cu
    CMD2: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -lineinfo -arch=sm_%s -rdc=true -o cnp_v2_static_cluster-4sanitizer cnp_v2_static_cluster-4sanitizer.cu
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" cnp_v2_static_cluster-4sanitizer.exe
  CHECK_POINT1: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2: "cnp_v2_static_cluster-4sanitizer.cu:40:clusterKernel"
  CHECK_POINT3: "clusterKernel"
  CHECK_POINT1_12_3: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2_12_3: "cnp_v2_static_cluster-4sanitizer.cu:40"
  CHECK_POINT3_12_3: "clusterKernel"



# Option - quite/require-cuda-init 3207945
SANITIZER_QUITE_CUDA_INIT:
  LOG_NAME: ${SANITIZER_QUITE_CUDA_INIT_PATH}/sanitizer_quite_cuda_init.log
  RUN_CASE: true
  PREPARE:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}; ${CMDER_BIN_PATH}/rm -r vectorAdd_memleak; ${CMDER_BIN_PATH}/mkdir vectorAdd_memleak; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"; cd vectorAdd_memleak; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T3207945/vectorAdd_memLeak/vectorAdd_memleak.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -o vectorAdd_memleak.exe vectorAdd_memleak.cu
    CMD2: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/helloworld/helloworld.exe
  STEP1:
    CMD1: cd "${SANITIZER_QUITE_CUDA_INIT_PATH}/vectorAdd_memleak"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full vectorAdd_memleak.exe
  STEP2:
    CMD1: cd "${SANITIZER_QUITE_CUDA_INIT_PATH}/vectorAdd_memleak"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full -q vectorAdd_memleak.exe
  STEP3:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --mode attach 1234
  STEP4:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" -q --mode attach 1234
  STEP5:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --require-cuda-init yes helloworld.exe
    CMD2: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --require-cuda-init no helloworld.exe
    CMD3: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --require-cuda-init no -q helloworld.exe
  # restore the sample
  STEP6:
    CMD1: cd "%s/vectorAdd"; ${CMDER_BIN_PATH}/rm.exe -r vectorAdd.cu; ${CMDER_BIN_PATH}/mv.exe  vectorAdd.cu_bak vectorAdd.cu;cd "%s/vectorAdd"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
  CHECK_POINT1: "LEAK SUMMARY: 200000 bytes leaked in 1 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 1 error"
  CHECK_POINT3: "Finding attachable process on host 127.0.0.1"
  CHECK_POINT4: "No processes found on 127.0.0.1"
  CHECK_POINT5: "Target application terminated before first instrumented API call"

# 3207946
SANITIZER_LAUNCH_ATTACH:
  LOG_NAME: ${SANITIZER_LAUNCH_ATTACH_PATH}/sanitizer_launch_attach.log
  PREPARE:
    CMD1: cd "%s/vectorAdd"; cmd.exe /c "${DEVENV}" %s /rebuild "Debug"
  STEP:
    CMD1: cd "${SAMPLE_BIN_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --mode launch vectorAdd.exe
    CMD2: sleep 6; tasklist | ${CMDER_BIN_PATH}/grep %s
    CMD3: cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --mode attach %s
  CHECK_POINT1: "Finding attachable process on host 127.0.0.1"
  CHECK_POINT2: "ERROR SUMMARY: 0 errors"

# 3222551
SANITIZER_CAPTURE_CUDA_BARRIER:
  LOG_NAME: ${SANITIZER_CAPTURE_CUDA_BARRIER_PATH}/sanitizer_capture_cuda_barrier.log
  PREPARE:
    CMD1: cd ${SANITIZER_CAPTURE_CUDA_BARRIER_PATH}; ${CMDER_BIN_PATH}/wget --user %s --password %s %s/P1072_T3222551/test-cuda-barrier.cu
    CMD2: cd ${SANITIZER_CAPTURE_CUDA_BARRIER_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -lcuda %s -arch sm_%s -o test-cuda-barrier test-cuda-barrier.cu
  STEP:
    CMD1: cd ${SANITIZER_CAPTURE_CUDA_BARRIER_PATH}/; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck test-cuda-barrier.exe
  CHECK_POINT: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"


INITCHECK_OPTIX_SAMPLES_COVERAGE_3363004:
  LOG_NAME: ${INITCHECK_OPTIX_SAMPLES_COVERAGE_PATH}/initcheck_optix_samples_coverage.log
  SAMPLE_LIST: "optixVolumeViewer,optixWhitted,optixHair,optixPathTracer,optixCallablePrograms"
  STEP1:  # compute-sanitizer --tool initcheck yes ./sample
    CMD: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck %s.exe
    CMD_12_2: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory yes --check-optix=yes %s.exe         # For optix samples
    CMD_12_3: cd "%s"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --track-unused-memory --check-optix %s.exe             # For optix samples

OPTION_SUPPRESSIONS_3568568:
  LOG_NAME: ${OPTION_SUPPRESSIONS_PATH}/option_suppressions.log
  PREPARE:
    CMD1: cd ${OPTION_SUPPRESSIONS_PATH}; ${CMDER_BIN_PATH}/rm -fr compute-sanitizer-samples; git clone https://github.com/NVIDIA/compute-sanitizer-samples.git;  ${CMDER_BIN_PATH}/ls -la;
    CMD2: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; ${CMDER_BIN_PATH}/ls -la;
    CMD3: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; nvcc -g -G -lcuda -o suppressions_demo suppressions_demo.cu;  ${CMDER_BIN_PATH}/ls -la suppressions_demo.exe
    CMD4: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; nvcc -g -G -lcuda -o suppressions_initcheck_demo suppressions_initcheck_demo.cu; ${CMDER_BIN_PATH}/ls -la suppressions_initcheck_demo.exe
    CHECK_POINT1: "compute-sanitizer-samples"
    CHECK_POINT2_1: "suppressions_initcheck_demo.cu"
    CHECK_POINT2_2: "suppressions_demo.cu"
    CHECK_POINT3: "suppressions_demo.exe"
    CHECK_POINT4: "suppressions_initcheck_demo.exe"
  STEP1:  # suppressions_demo
    CMD1: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --save suppa.xml --xml suppressions_demo.exe
    CMD2: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; ${CMDER_BIN_PATH}/ls -la suppa.xml; ${CMDER_BIN_PATH}/cat suppa.xml
    CMD3: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --suppressions suppa.xml suppressions_demo.exe
    CHECK_POINT1: "Program hit cudaErrorMemoryAllocation (error 2) due to \"out of memory\" on CUDA API call to cudaMalloc."
    CHECK_POINT2: "<text>Program hit cudaErrorMemoryAllocation (error 2) due to &quot;out of memory&quot; on CUDA API call to cudaMalloc.</text>"
    CHECK_POINT3: "ERROR SUMMARY: 0 errors"
  STEP2: # uppressions_initcheck_demo
    CMD1: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --save suppb.xml --xml suppressions_initcheck_demo.exe
    CMD2: cd ${OPTION_SUPPRESSIONS_PATH}/compute-sanitizer-samples/Suppressions; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --suppressions suppb.xml suppressions_initcheck_demo.exe
    CHECK_POINT1: "Uninitialized __global__ memory read of size 4 bytes"
    CHECK_POINT2: "ERROR SUMMARY: 0 errors"

# [Sanitizer][RCCA]No false positive when vector loads are not 32-bits per vector element 3700598
SANITIZER_3700598_RCCA:
  LOG_NAME: ${SANITIZER_3700598_RCCA_PATH}/sanitizer_rcca_3700598.txt
  PREPARE:
    CMD1: cd ${SANITIZER_3700598_RCCA_PATH}; ${CMDER_BIN_PATH}/wget.exe --user %s --password %s %s/3700598/cs16-fail.cu
    CMD2: cd ${SANITIZER_3700598_RCCA_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -std=c++17 cs16-fail.cu -o cs16-fail
  STEP1:
    CMD1: cd ${SANITIZER_3700598_RCCA_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" cs16-fail

OPTION_PRELOAD_LIBRARY:
  LOG_NAME: ${OPTION_PRELOAD_LIBRARY_PATH}/option_preload_library_3532738.log
  PREPARE:
    # Use donwloaded Rebel smoke test pacakge
    CMD1: cd ${REBEL_SMOKE_TEST_APP_PATH}; ${CMDER_BIN_PATH}/ls.exe -la
    CMD2: cd ${REBEL_SMOKE_TEST_APP_PATH}/mimalloc; ${CMDER_BIN_PATH}/ls.exe -la
    CHECKPOINT_1: "mimalloc"
    CHECKPOINT_2: "mimalloc-override-test.exe"
  STEP1:
    # compute-sanitizer --target-processes all --preload-library mimalloc-redirect.dll --preload-library mimalloc.dll mimalloc-override-test.exe
    CMD1: cd ${REBEL_SMOKE_TEST_APP_PATH}/mimalloc; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --target-processes all --preload-library mimalloc-redirect.dll --preload-library mimalloc.dll mimalloc-override-test.exe
    CHECKPOINT_1: "process: user:"
    CHECKPOINT_2: "Error: Target application terminated before first instrumented API call"

# Test sanitizer initcheck no errors when reading from memory allocated by cudaMalloc3D
SANITIZER_3683951_RCCA:
  LOG_NAME: ${SANITIZER_3683951_RCCA_PATH}/sanitizer_rcca_3683951.txt
  PREPARE:
    CMD1: cd ${SANITIZER_3683951_RCCA_PATH}; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/3683951/DotNetEntryPoint.exe; cmd.exe /c "${BASE_PATH}/bin/nvcc" -lcuda -o main main.cpp
  STEP1:
    CMD1: cd ${SANITIZER_3683951_RCCA_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./DotNetEntryPoint.exe
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# Test sanitizer initcheck no errors when reading from memory allocated by cudaMalloc3D
SANITIZER_3797693_RCCA:
  LOG_NAME: ${SANITIZER_3797693_RCCA_PATH}/sanitizer_rcca_3797693.txt
  PREPARE:
    CMD1: cd ${SANITIZER_3797693_RCCA_PATH}; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/3797693/main.cpp; cmd.exe /c "${BASE_PATH}/bin/nvcc" -lcuda -o main main.cpp
  STEP1:
    CMD1: cd ${SANITIZER_3797693_RCCA_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" ./main.exe
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

OPTIX_RTCORE_SOURCE_HIDING:
  LOG_NAME: ${OPTIX_RTCORE_SOURCE_HIDING_PATH}/optix_rtcore_source_hiding_3730298.log
  PREPARE:
    CMD1: cd "${OPTIX_HELLO_SAMPLE_PATH}"; ${CMDER_BIN_PATH}/mv.exe draw_solid_color.cu draw_solid_color.cu_bak; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/P1072_T3730298/draw_solid_color.cu
    CMD2: cmake -G "Visual Studio 17 2022" -S "C:\ProgramData\NVIDIA Corporation\OptiX SDK ${OPTIX_SDK_VERSION}.0\SDK" -B "C:\LMS\optix-${OPTIX_SDK_VERSION}\build"; cmd.exe /c "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv" "C:/LMS/optix-${OPTIX_SDK_VERSION}/build/OptiX-Samples.sln" /rebuild "Release"
    CMD3: cd "${OPTIX_HELLO_TEST_PATH}"; ${CMDER_BIN_PATH}/ls.exe -la
    CMD4: cd "${OPTIX_HELLO_SAMPLE_PATH}"; ${CMDER_BIN_PATH}/rm.exe draw_solid_color.cu;${CMDER_BIN_PATH}/mv.exe draw_solid_color.cu_bak draw_solid_color.cu
    CHECKPOINT_1: "optixHello.exe"
  STEP1:
    # Run with CBL2 by default since R555: compute-sanitizer --leak-check full --check-optix-leaks optixHello.
    # 1) No kernel functions with a name containing the string "ttu" or "rtcore" are visible
    # 2) No kernel function with a name starting with the string "megakernel_simple" is visible.
    # 3) function with a name that contains both the string "raygen__" and the name of the user-defined entrypoint function is visible.
    # 4) Can see Device Frame: NVIDIA internal (note: don't see it on ARM SBSA with release build, can see it with debug build)
    CMD1: cd "${OPTIX_HELLO_TEST_PATH}"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --check-optix-leaks optixHello.exe
    CHECKPOINT_1: "ttu"
    CHECKPOINT_2: "rtcore"
    CHECKPOINT_3: "megakernel_simple"
    CHECKPOINT_4: "Invalid __global__ write of size 4 bytes"
    CHECKPOINT_5: "at __raygen__draw_solid_color"
    CHECKPOINT_6: "Device Frame:NVIDIA internal"
  STEP2:
    # Run with CBL2 by default since R555: compute-sanitizer --leak-check full --check-optix-leaks optixHello.
    # 1) No kernel functions with a name containing the string "ttu" or "rtcore" are visible
    # 2) No kernel function with a name starting with the string "megakernel_simple" is visible.
    # 3) function with a name that contains both the string "raygen__" and the name of the user-defined entrypoint function is visible.
    # 4) Can see Device Frame: NVIDIA internal (note: don't see it on ARM SBSA with release build, can see it with debug build)
    CMD1: cd "${OPTIX_SAMPLE_BIN_PATH}"; $ENV:OPTIX_FORCE_DEPRECATED_LAUNCHER="CBL1"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --check-optix-leaks optixHello.exe
    CHECKPOINT_1: "ttu"
    CHECKPOINT_2: "rtcore"
    CHECKPOINT_3: "megakernel_simple"
    CHECKPOINT_4: "Invalid __global__ write of size 4 bytes"
    CHECKPOINT_5: "at __raygen__draw_solid_color"
    CHECKPOINT_6: "Device Frame:NVIDIA internal"
    CHECKPOINT_6_1: "Device Frame: NVIDIA internal"
  STEP3:
    # Run with CBL2 by default since R555: compute-sanitizer --leak-check full --check-optix-leaks optixHello.
    # 1) No kernel functions with a name containing the string "ttu" or "rtcore" are visible
    # 2) No kernel function with a name starting with the string "megakernel_simple" is visible.
    # 3) function with a name that contains both the string "raygen__" and the name of the user-defined entrypoint function is visible.
    # 4) Can see Device Frame: NVIDIA internal (note: don't see it on ARM SBSA with release build, can see it with debug build)
    CMD1: cd "${OPTIX_SAMPLE_BIN_PATH}"; $ENV:OPTIX_FORCE_DEPRECATED_LAUNCHER="1"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --leak-check full --check-optix-leaks optixHello.exe
    CHECKPOINT_1: "ttu"
    CHECKPOINT_2: "rtcore"
    CHECKPOINT_3: "megakernel_simple"
    CHECKPOINT_4: "Invalid __global__ write of size 4 bytes"
    CHECKPOINT_5: "at __raygen__draw_solid_color"
    CHECKPOINT_6: "Device Frame:NVIDIA internal"
    CHECKPOINT_6_1: "Device Frame: NVIDIA internal"


# Memcheck - detect-missing-module-unload 3504892
MEMCHECK_DETECT_MISSING_MODULE_UNLOAD:
  LOG_NAME: ${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/detect_missing_module_unload.txt
  PREPARE:
    PREPARE_CMD: cd ${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}; ${CMDER_BIN_PATH}/rm -r vectorAddDrv_err; ${CMDER_BIN_PATH}/mkdir vectorAddDrv_err; cmd.exe /c mklink /d "Common" "${SAMPLE_PATH}/Common"
    CMD1: cd "${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/vectorAddDrv_err"; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/P1072_T3504892/vectorAddDrv_Err/{}
    CMD2: cd "${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/vectorAddDrv_err"; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -fatbin -o vectorAdd_kernel64.fatbin vectorAdd_kernel.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -I ../Common -lcuda -o vectorAddDrv_err.exe vectorAddDrv_Err.cpp
  STEP1:
    CMD1: cd "${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/vectorAddDrv_err"; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --detect-missing-module-unload --leak-check full ./vectorAddDrv_err.exe
  CHECK_POINT1: "Leaked .*bytes from CUDA module.*"
  CHECK_POINT2: "ERROR SUMMARY: 1000 errors"


# Memcheck - Option -backtrace-short  3831779
SANITIZER_MEMCHECK_BACKTRACE_SHORT:
  LOG_NAME: ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}/sanitizer_memcheck_backtrace_short.txt
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/compute-sanitizer-samples/Memcheck/{}
    CMD2: cd ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -g -G -o memcheck_demo memcheck_demo.cu
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-errors no ./memcheck_demo
  STEP2:
    CMD1: cd ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel --report-api-errors no --strip-paths no --backtrace-short no ./memcheck_demo
  CHECK_POINT1: "at unaligned_kernel.* in memcheck_demo.cu"
  CHECK_POINT2: "Host Frame: run_unaligned in memcheck_demo.cu.*in memcheck_demo"
  CHECK_POINT3: "Host Frame: main in memcheck_demo.cu"
  CHECK_POINT4: "Device Frame: out_of_bounds_kernel.* in memcheck_demo.cu"
  CHECK_POINT5: "Host Frame: run_out_of_bounds.* in memcheck_demo.cu.*in memcheck_demo"
  CHECK_POINT6: "Host Frame: main in memcheck_demo.cu.*in memcheck_demo"
  CHECK_POINT7: "at unaligned_kernel.* in ${SANITIZER_MEMCHECK_BACKTRACE_SHORT_PATH}/memcheck_demo.cu"
  CHECK_POINT8: "Host Frame: cudart.*"
  CHECK_POINT9: "Host Frame: __device_stub__Z16unaligned_kernelv"
  CHECK_POINT: "ERROR SUMMARY: 2 errors"

# Test Sanitizer support for Compiler - Public ptx exposure for LDG/STG.256 4076330
SANITIZER_COMPILER_PUBLIC_PTX:
  LOG_NAME: ${SANITIZER_COMPILER_PUBLIC_PTX_PATH}/sanitizer_compiler_public_ptx.txt
  PREPARE:
    CMD1: cd ${SANITIZER_COMPILER_PUBLIC_PTX_PATH}; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/P1072_T4076330/ldgstg256memcheck.cu; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/P1072_T4076330/ldgstg256initcheck.cu
    CMD2: cd ${SANITIZER_COMPILER_PUBLIC_PTX_PATH}; cmd.exe /c "${BASE_PATH}/bin/nvcc" -arch sm_{} -o ldgstg256memcheck ldgstg256memcheck.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -arch sm_{} -o ldgstg256initcheck ldgstg256initcheck.cu;
  STEP1:
    CMD1: cd ${SANITIZER_COMPILER_PUBLIC_PTX_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --destroy-on-device-error kernel ./ldgstg256memcheck
    CHECK_POINT1: "Invalid __global__ read of size 28 bytes"
    CHECK_POINT2: "Invalid __global__ read of size 32 bytes"
    CHECK_POINT3: "at ldgstg256OutOfBounds(const unsigned int *)+0x30"
    CHECK_POINT4: "at ldgstg256missaligned(const unsigned int *)+0x30"
  STEP2:
    CMD1: cd ${SANITIZER_COMPILER_PUBLIC_PTX_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool initcheck --destroy-on-device-error kernel ./ldgstg256initcheck
    CHECK_POINT1: "Uninitialized __global__ memory read of size 32 bytes"
    CHECK_POINT2: "at ldgstg256Uninit(const unsigned int *)+0xc0"

# Track LDGSTS resolution through ARRIVES.LDGSTSBAR independently from CUDA Barrier tracking 4085254
SANITIZER_TRACK_LDGSTS:
  LOG_NAME: ${SANITIZER_TRACK_LDGSTS_PATH}/sanitizer_track_ldgsts.txt
  PREPARE:
    CMD1: cd ${SANITIZER_TRACK_LDGSTS_PATH}; ${CMDER_BIN_PATH}/wget.exe  --user {} --password {} {}/P1072_T4085254/async4.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -arch sm_{} -o async4 async4.cu
  STEP1:
    CMD1: cd ${SANITIZER_TRACK_LDGSTS_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool racecheck --destroy-on-device-error kernel ./async4
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

# Test sanitizer initcheck no errors when reading from memory allocated by cudaMalloc3D
SANITIZER_4085255_RCCA:
  LOG_NAME: ${SANITIZER_4085255_RCCA_PATH}/sanitizer_4085255_rcca.txt
  PREPARE:
    CMD1: cd ${SANITIZER_4085255_RCCA_PATH}; ${CMDER_BIN_PATH}/wget.exe --user {} --password {} {}/P1072_T4085255/test.cu; cmd.exe /c "${BASE_PATH}/bin/nvcc" -gencode arch=compute_100a,code=sm_100a -o test test.cu
  STEP1:
    CMD1: cd ${SANITIZER_4085255_RCCA_PATH}; cmd.exe /c "${SANITIZER_BIN_PATH}/compute-sanitizer" --tool synccheck ./test
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"