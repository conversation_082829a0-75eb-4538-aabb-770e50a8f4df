2025-01-16 00:43:26.065159 -0800 [INFO] sanitizer_common_modify_config.steps[1] [36m    ------ Running step 1/5 (Test: suite.yaml - sanitizer_common_modify_config)> ------[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m Visual Studio Version        = <>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m Visual Studio Suite          = <>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m MOFED                        = <>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m IB Firmware                  = <>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m====---------------------------------------------------------------====[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:43:26.065159 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:43:26.080786 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:43:26.080786 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        TEMPLATE_ID           = <9999995>[m
2025-01-16 00:43:26.080786 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:43:26.080786 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m====---------------------------------------------------------------====[m
2025-01-16 00:43:26.080786 -0800 [WARNING] sanitizer_common_modify_config.steps[1] [93m      - step 1/5 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:43:26.080786 -0800 [INFO] sanitizer_common_modify_config.steps[1] [93m      - step 1/5 timeout = None seconds (None)[m
2025-01-16 00:43:26.080786 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m       ==== CMD ====: 
                                                                                       echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml"
                                                                                       echo "12.9.0"
                                                                                       
                                                                                       $major=(echo 12.9.0|awk -F "." '{print $1}')
                                                                                       $minor=(echo 12.9.0|awk -F "." '{print $2}')
                                                                                       $revision=(echo 12.9.0|awk -F "." '{print $3}')
                                                                                       echo $major, $minor, $revision
                                                                                       $CUDA_MAJOR=(grep 'CUDA_MAJOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_MINOR=(grep 'CUDA_MINOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_REVISION=(grep 'CUDA_REVISION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       echo "OLD: $CUDA_MAJOR, $CUDA_MINOR, $CUDA_REVISION"
                                                                                       $cuda_short_version=$major + "." + $minor
                                                                                       echo $cuda_short_version
                                                                                       sed -i "s/$CUDA_MAJOR/    CUDA_MAJOR: $major/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       sed -i "s/$CUDA_MINOR/    CUDA_MINOR: $minor/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       sed -i "s/$CUDA_REVISION/    CUDA_REVISION: $revision/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       $CUDA_MAJOR=(grep 'CUDA_MAJOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_MINOR=(grep 'CUDA_MINOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_REVISION=(grep 'CUDA_REVISION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       echo "NEW: $CUDA_MAJOR, $CUDA_MINOR, $CUDA_REVISION"
                                                                                     ==============================[m
2025-01-16 00:43:26.080786 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:43:26.315968 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml[m
2025-01-16 00:43:26.315968 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| 12.9.0[m
2025-01-16 00:43:26.394472 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| 12[m
2025-01-16 00:43:26.394472 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| 9[m
2025-01-16 00:43:26.394472 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| 0[m
2025-01-16 00:43:26.473007 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| OLD:     CUDA_MAJOR: 12,     CUDA_MINOR: 8,     CUDA_REVISION: 20[m
2025-01-16 00:43:26.488683 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| 12.9[m
2025-01-16 00:43:26.693213 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	| NEW:     CUDA_MAJOR: 12,     CUDA_MINOR: 9,     CUDA_REVISION: 0[m
2025-01-16 00:43:26.708840 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:43:26.708840 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 00:43:26.708840 -0800 [INFO] sanitizer_common_modify_config.steps[1] [92m	step 1/5 <expected_returncode> GOOD: <0>[m
2025-01-16 00:43:26.708840 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	[96mR400 - step 1/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 00:43:26.708840 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m	[1;37;42mstep 1/5 PASS[0m[m
2025-01-16 00:43:29.205781 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[36m    ------ Running step 1/5 (Test: sanitizer_common_modify_config)> ------[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m====----------------- Current Environment Details -----------------====[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m Visual Studio Version        = <>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m Visual Studio Suite          = <>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m CUDA Driver Version          = <575.40 CL 35390623>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m OS Verion                    = <Windows 11>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m MOFED                        = <>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m IB Firmware                  = <>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m GPU Name                     = <RTX-3060>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m====---------------- Current Environment Variables ----------------====[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        P4ROOT                = <Undefined>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        LD_LIBRARY_PATH       = <Undefined>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        GPGPU_COMPILER_EXPORT = <Undefined>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        NV_TOOLS              = <Undefined>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        DRIVER_ROOT           = <Undefined>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        DEVICE_ID             = <0>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        TEMPLATE_ID           = <9999995>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m        VISIBLE_DEVICE_SM     = <86>[m[m
2025-01-16 00:43:29.205781 -0800 [DEBUG] sanitizer_common_modify_config.steps[1] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 00:43:29.205781 -0800 [WARNING] sanitizer_common_modify_config.steps[1] [93m[93m      - step 1/5 timeout = None - invalid settings! Timeout ignored ... [m[m
2025-01-16 00:43:29.205781 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[93m      - step 1/5 timeout = None seconds (None)[m[m
2025-01-16 00:43:29.205781 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m       ==== CMD ====: 
                                                                                       echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml"
                                                                                       echo "12.9.0"
                                                                                       
                                                                                       $major=(echo 12.9.0|awk -F "." '{print $1}')
                                                                                       $minor=(echo 12.9.0|awk -F "." '{print $2}')
                                                                                       $revision=(echo 12.9.0|awk -F "." '{print $3}')
                                                                                       echo $major, $minor, $revision
                                                                                       $CUDA_MAJOR=(grep 'CUDA_MAJOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_MINOR=(grep 'CUDA_MINOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_REVISION=(grep 'CUDA_REVISION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       echo "OLD: $CUDA_MAJOR, $CUDA_MINOR, $CUDA_REVISION"
                                                                                       $cuda_short_version=$major + "." + $minor
                                                                                       echo $cuda_short_version
                                                                                       sed -i "s/$CUDA_MAJOR/    CUDA_MAJOR: $major/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       sed -i "s/$CUDA_MINOR/    CUDA_MINOR: $minor/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       sed -i "s/$CUDA_REVISION/    CUDA_REVISION: $revision/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
                                                                                       $CUDA_MAJOR=(grep 'CUDA_MAJOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_MINOR=(grep 'CUDA_MINOR:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       $CUDA_REVISION=(grep 'CUDA_REVISION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
                                                                                       echo "NEW: $CUDA_MAJOR, $CUDA_MINOR, $CUDA_REVISION"
                                                                                     ==============================[m[m
2025-01-16 00:43:29.205781 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	[44m[^RUN_CMD_START^][0m[m[m
2025-01-16 00:43:29.451032 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml[m[m
2025-01-16 00:43:29.451032 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| 12.9.0[m[m
2025-01-16 00:43:29.520157 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| 12[m[m
2025-01-16 00:43:29.520157 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| 9[m[m
2025-01-16 00:43:29.520157 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| 0[m[m
2025-01-16 00:43:29.598136 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| OLD:     CUDA_MAJOR: 12,     CUDA_MINOR: 9,     CUDA_REVISION: 0[m[m
2025-01-16 00:43:29.613762 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| 12.9[m[m
2025-01-16 00:43:29.818155 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	| NEW:     CUDA_MAJOR: 12,     CUDA_MINOR: 9,     CUDA_REVISION: 0[m[m
2025-01-16 00:43:29.833779 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	[44m[_RUN_CMD_END_][0m[m[m
2025-01-16 00:43:29.833779 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	[7m[ CMD.returncode = 0 ][0m[m[m
2025-01-16 00:43:29.849406 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[92m	step 1/5 <expected_returncode> GOOD: <0>[m[m
2025-01-16 00:43:29.849406 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	[96mR400 - step 1/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m[m
2025-01-16 00:43:29.849406 -0800 [INFO] sanitizer_common_modify_config.steps[1] [m[m	[1;37;42mstep 1/5 PASS[0m[m[m
