2025-01-16 01:16:02.810280 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [36m    ------ Running step 2/3 (Test: 3491667-tools_support_publicPTX)> ------[m
2025-01-16 01:16:02.810280 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 01:16:02.825291 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m Visual Studio Version        = <>[m
2025-01-16 01:16:02.825291 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m Visual Studio Suite          = <>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m OS Verion                    = <Windows 11>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m MOFED                        = <>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m IB Firmware                  = <>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        P4ROOT                = <Undefined>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        DEVICE_ID             = <0>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        TEMPLATE_ID           = <3491667>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 01:16:02.826298 -0800 [DEBUG] 3491667-tools_support_publicPTX.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 01:16:02.826298 -0800 [WARNING] 3491667-tools_support_publicPTX.steps[2] [93m      - step 2/3 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 01:16:02.826298 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [93m      - step 2/3 timeout = None seconds (None)[m
2025-01-16 01:16:02.826298 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m       ==== CMD ====: 
                                                                                        C:/Python37/python.exe C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_publicPTX_3491667
                                                                                      ==============================[m
2025-01-16 01:16:02.826298 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 01:16:10.268696 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	| cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/Samples/1_Utilities/deviceQuery"; cmd.exe /c "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv" deviceQuery_vs2022.sln /rebuild "Debug"; cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/bin/win64/Debug"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'[m
2025-01-16 01:16:10.268696 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	| sm_temp is 8.6[m
2025-01-16 01:16:10.268696 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	| sm_temp(encoding with UTF-8-sig) is b'\xef\xbb\xbf8.6'[m
2025-01-16 01:16:10.268696 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	| sm is 8.6[m
2025-01-16 01:16:10.268696 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	| 2025/01/16 01:16:10 - __main__ - INFO - not support this case if cuda version less than 12.3 or sm less than 9.0[m
2025-01-16 01:16:10.268696 -0800 [WARNING] 3491667-tools_support_publicPTX.steps[2] [93m	step 2/3 <na_if_contains> WARN: <not support this case> found in <2025/01/16 01:16:10 - __main__ - INFO - not support this case if cuda version less than 12.3 or sm less than 9.0> (triggered by - <not support this case> contained)[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [92m	step 2/3 <expected_returncode> GOOD: <0>[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[96mR231 - step 2/3 <na_if_contains> result: [0m[0;93mTRIGGERED[0m[96m (triggered by - <not support this case> contained)[0m
                                                                                   		[93m| 'not support this case' - found[0m[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[96mR400 - step 2/3 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 01:16:10.315986 -0800 [INFO] 3491667-tools_support_publicPTX.steps[2] [m	[1;37;43mstep 2/3 NA[0m[m
