
2025-01-16 01:16:00.439109 Pacific Standard Time:		step 2/3 <expected_returncode> BAD: <1> (expected - 0)
                                             ==== CMD ====: 
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_ipc_3069391
                                            ==============================
2025-01-16 01:16:00.439109 Pacific Standard Time:		[96mR231 - step 2/3 <na_if_contains> result: [0m[0;92mGOOD[0m[96m (triggered by - <not support> contained)[0m
                                                                             		[92m| 'not support' - not found[0m
                                             ==== CMD ====: 
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_ipc_3069391
                                            ==============================
2025-01-16 01:16:00.439109 Pacific Standard Time:		[Err.S] [96mR400 - step 2/3 <expected_returncode> result: [0m[0;41mBAD[0m[96m (expected - <0>)[0m [Err.E]
                                             ==== CMD ====: 
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_ipc_3069391
                                            ==============================
2025-01-16 01:16:00.439109 Pacific Standard Time:		[1;37;41mstep 2/3 FAIL[0m
                                             ==== CMD ====: 
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_ipc_3069391
                                            ==============================
