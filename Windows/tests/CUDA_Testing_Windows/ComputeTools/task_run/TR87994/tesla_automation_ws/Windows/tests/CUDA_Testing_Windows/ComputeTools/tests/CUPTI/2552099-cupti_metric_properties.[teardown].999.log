2025-01-16 01:37:18.448113 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [36m    ------ Running <Teardown> step 999/1 (Test: 2552099-cupti_metric_properties)> ------[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m Visual Studio Version        = <>[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m Visual Studio Suite          = <>[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 01:37:18.448113 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m OS Verion                    = <Windows 11>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m MOFED                        = <>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m IB Firmware                  = <>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m====---------------------------------------------------------------====[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        P4ROOT                = <Undefined>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        PATH                  = <c:\Python311;c:\Python311\Scripts;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Microsoft MPI\Bin\;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;c:\Strawberry\c\bin;c:\Strawberry\perl\bin;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;c:\Strawberry\perl\site\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Tesseract-OCR;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\CTT\cmder\vendor\git-for-windows\usr\bin;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        DEVICE_ID             = <0>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        TEMPLATE_ID           = <2552099>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 01:37:18.463736 -0800 [DEBUG] 2552099-cupti_metric_properties.<teardown>_steps[999] [44m====---------------------------------------------------------------====[m
2025-01-16 01:37:18.463736 -0800 [WARNING] 2552099-cupti_metric_properties.<teardown>_steps[999] [93m      - step 999/1 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 01:37:18.463736 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [93m      - step 999/1 timeout = None seconds (None)[m
2025-01-16 01:37:18.463736 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m       ==== CMD ====: 
                                                                                        echo "Teardown"
                                                                                      ==============================[m
2025-01-16 01:37:18.463736 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 01:37:18.699333 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	| Teardown[m
2025-01-16 01:37:18.715356 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 01:37:18.715356 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 01:37:18.715356 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [92m	step 999/1 <expected_returncode> GOOD: <0>[m
2025-01-16 01:37:18.715356 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	[96mR400 - step 999/1 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 01:37:18.715356 -0800 [INFO] 2552099-cupti_metric_properties.<teardown>_steps[999] [m	[1;37;42mstep 999/1 PASS[0m[m
