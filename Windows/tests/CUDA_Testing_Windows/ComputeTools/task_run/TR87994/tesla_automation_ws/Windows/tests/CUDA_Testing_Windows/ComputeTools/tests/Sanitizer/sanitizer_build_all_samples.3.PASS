
2025-01-16 01:09:27.561224 Pacific Standard Time:		step 3/3 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              echo "^^^^^^^^^^ Build all CUDA Samples in Debug mode Start ..."
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_build_all_samples
                                              echo "@@@@@@@@@@ Build all CUDA Samples in Debug mode End."
                                            ==============================
2025-01-16 01:09:27.561224 Pacific Standard Time:		[96mR400 - step 3/3 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              echo "^^^^^^^^^^ Build all CUDA Samples in Debug mode Start ..."
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_build_all_samples
                                              echo "@@@@@@@@@@ Build all CUDA Samples in Debug mode End."
                                            ==============================
2025-01-16 01:09:27.561224 Pacific Standard Time:		[1;37;42mstep 3/3 PASS[0m
                                             ==== CMD ====: 
                                              echo "^^^^^^^^^^ Build all CUDA Samples in Debug mode Start ..."
                                              ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_build_all_samples
                                              echo "@@@@@@@@@@ Build all CUDA Samples in Debug mode End."
                                            ==============================
