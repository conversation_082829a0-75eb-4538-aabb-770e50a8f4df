2025-01-16 01:14:31.878533 -0800 [INFO] setup_common_modify_config.steps[4] [36m    ------ Running step 4/5 (Test: suite.yaml - setup_common_modify_config)> ------[m
2025-01-16 01:14:31.878533 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 01:14:31.878533 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m Visual Studio Version        = <>[m
2025-01-16 01:14:31.878533 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m Visual Studio Suite          = <>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m OS Verion                    = <Windows 11>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m MOFED                        = <>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m IB Firmware                  = <>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m====---------------------------------------------------------------====[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        P4ROOT                = <Undefined>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        DEVICE_ID             = <0>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        TEMPLATE_ID           = <9999996>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 01:14:31.894154 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m====---------------------------------------------------------------====[m
2025-01-16 01:14:31.894154 -0800 [WARNING] setup_common_modify_config.steps[4] [93m      - step 4/5 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 01:14:31.894154 -0800 [INFO] setup_common_modify_config.steps[4] [93m      - step 4/5 timeout = None seconds (None)[m
2025-01-16 01:14:31.894154 -0800 [INFO] setup_common_modify_config.steps[4] [m       ==== CMD ====: 
                                                                                   echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml"
                                                                                   echo "12.9"
                                                                                   
                                                                                   $OLD=(grep -i 'DVS_BUILD:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml)
                                                                                   echo "OLD: $OLD"
                                                                                   $BRANCH="12.9"
                                                                                   If ( "11.7" -eq $BRANCH) {
                                                                                       $NEW="H"
                                                                                   } ElseIf ( "11.6" -eq $BRANCH ) {
                                                                                       $NEW="G"
                                                                                   } ElseIf ( "11.5" -eq $BRANCH ) {
                                                                                       $NEW="F"
                                                                                   } ElseIf ( "11.8" -eq $BRANCH ) {
                                                                                       $NEW="C"
                                                                                   } ElseIf ( "12.0" -eq $BRANCH ) {
                                                                                       $NEW="D"
                                                                                   } ElseIf ( "12.1" -eq $BRANCH ) {
                                                                                       $NEW="F"
                                                                                   } ElseIf ( "12.2" -eq $BRANCH ) {
                                                                                       $NEW="I"
                                                                                   } ElseIf ( "12.3" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.3"
                                                                                   } ElseIf ( "12.4" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.4"
                                                                                   } ElseIf ( "12.5" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.5"
                                                                                   } ElseIf ( "12.6" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.6"
                                                                                   } ElseIf ( "12.7" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.7"
                                                                                   } ElseIf ( "12.8" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.8"
                                                                                   } Else {
                                                                                      $NEW="ERROR"
                                                                                   }
                                                                                   sed -i "s/$OLD/    DVS_BUILD: $NEW/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml
                                                                                   $NEW=(grep -i 'DVS_BUILD:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml)
                                                                                   echo "NEW: $NEW"
                                                                                 ==============================[m
2025-01-16 01:14:31.894154 -0800 [INFO] setup_common_modify_config.steps[4] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 01:14:32.130105 -0800 [INFO] setup_common_modify_config.steps[4] [m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml[m
2025-01-16 01:14:32.130105 -0800 [INFO] setup_common_modify_config.steps[4] [m	| 12.9[m
2025-01-16 01:14:32.161367 -0800 [INFO] setup_common_modify_config.steps[4] [m	| OLD:     DVS_BUILD: CUDA12.6[m
2025-01-16 01:14:32.239976 -0800 [INFO] setup_common_modify_config.steps[4] [m	| NEW:     DVS_BUILD: ERROR[m
2025-01-16 01:14:32.255654 -0800 [INFO] setup_common_modify_config.steps[4] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 01:14:32.255654 -0800 [INFO] setup_common_modify_config.steps[4] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 01:14:32.255654 -0800 [INFO] setup_common_modify_config.steps[4] [92m	step 4/5 <expected_returncode> GOOD: <0>[m
2025-01-16 01:14:32.255654 -0800 [INFO] setup_common_modify_config.steps[4] [m	[96mR400 - step 4/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 01:14:32.255654 -0800 [INFO] setup_common_modify_config.steps[4] [m	[1;37;42mstep 4/5 PASS[0m[m
2025-01-16 01:14:34.815166 -0800 [INFO] setup_common_modify_config.steps[4] [m[36m    ------ Running step 4/5 (Test: setup_common_modify_config)> ------[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m====----------------- Current Environment Details -----------------====[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m Visual Studio Version        = <>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m Visual Studio Suite          = <>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m CUDA Driver Version          = <575.40 CL 35390623>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m OS Verion                    = <Windows 11>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m MOFED                        = <>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m IB Firmware                  = <>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m GPU Name                     = <RTX-3060>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m====---------------- Current Environment Variables ----------------====[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        P4ROOT                = <Undefined>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        LD_LIBRARY_PATH       = <Undefined>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        GPGPU_COMPILER_EXPORT = <Undefined>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        NV_TOOLS              = <Undefined>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        DRIVER_ROOT           = <Undefined>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        DEVICE_ID             = <0>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        TEMPLATE_ID           = <9999996>[m[m
2025-01-16 01:14:34.815166 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m        VISIBLE_DEVICE_SM     = <86>[m[m
2025-01-16 01:14:34.831175 -0800 [DEBUG] setup_common_modify_config.steps[4] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 01:14:34.831175 -0800 [WARNING] setup_common_modify_config.steps[4] [93m[93m      - step 4/5 timeout = None - invalid settings! Timeout ignored ... [m[m
2025-01-16 01:14:34.831175 -0800 [INFO] setup_common_modify_config.steps[4] [m[93m      - step 4/5 timeout = None seconds (None)[m[m
2025-01-16 01:14:34.831175 -0800 [INFO] setup_common_modify_config.steps[4] [m[m       ==== CMD ====: 
                                                                                   echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml"
                                                                                   echo "12.9"
                                                                                   
                                                                                   $OLD=(grep -i 'DVS_BUILD:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml)
                                                                                   echo "OLD: $OLD"
                                                                                   $BRANCH="12.9"
                                                                                   If ( "11.7" -eq $BRANCH) {
                                                                                       $NEW="H"
                                                                                   } ElseIf ( "11.6" -eq $BRANCH ) {
                                                                                       $NEW="G"
                                                                                   } ElseIf ( "11.5" -eq $BRANCH ) {
                                                                                       $NEW="F"
                                                                                   } ElseIf ( "11.8" -eq $BRANCH ) {
                                                                                       $NEW="C"
                                                                                   } ElseIf ( "12.0" -eq $BRANCH ) {
                                                                                       $NEW="D"
                                                                                   } ElseIf ( "12.1" -eq $BRANCH ) {
                                                                                       $NEW="F"
                                                                                   } ElseIf ( "12.2" -eq $BRANCH ) {
                                                                                       $NEW="I"
                                                                                   } ElseIf ( "12.3" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.3"
                                                                                   } ElseIf ( "12.4" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.4"
                                                                                   } ElseIf ( "12.5" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.5"
                                                                                   } ElseIf ( "12.6" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.6"
                                                                                   } ElseIf ( "12.7" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.7"
                                                                                   } ElseIf ( "12.8" -eq $BRANCH ) {
                                                                                       $NEW="CUDA12.8"
                                                                                   } Else {
                                                                                      $NEW="ERROR"
                                                                                   }
                                                                                   sed -i "s/$OLD/    DVS_BUILD: $NEW/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml
                                                                                   $NEW=(grep -i 'DVS_BUILD:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml)
                                                                                   echo "NEW: $NEW"
                                                                                 ==============================[m[m
2025-01-16 01:14:34.831175 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	[44m[^RUN_CMD_START^][0m[m[m
2025-01-16 01:14:35.035218 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/setup_case.yaml[m[m
2025-01-16 01:14:35.035218 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	| 12.9[m[m
2025-01-16 01:14:35.066529 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	| OLD:     DVS_BUILD: ERROR[m[m
2025-01-16 01:14:35.131988 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	| NEW:     DVS_BUILD: ERROR[m[m
2025-01-16 01:14:35.145003 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	[44m[_RUN_CMD_END_][0m[m[m
2025-01-16 01:14:35.145003 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	[7m[ CMD.returncode = 0 ][0m[m[m
2025-01-16 01:14:35.145003 -0800 [INFO] setup_common_modify_config.steps[4] [m[92m	step 4/5 <expected_returncode> GOOD: <0>[m[m
2025-01-16 01:14:35.145003 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	[96mR400 - step 4/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m[m
2025-01-16 01:14:35.145003 -0800 [INFO] setup_common_modify_config.steps[4] [m[m	[1;37;42mstep 4/5 PASS[0m[m[m
