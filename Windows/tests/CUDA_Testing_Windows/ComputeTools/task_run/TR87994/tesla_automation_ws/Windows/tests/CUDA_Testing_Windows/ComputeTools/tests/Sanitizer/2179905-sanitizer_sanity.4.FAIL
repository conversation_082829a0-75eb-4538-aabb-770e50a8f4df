
2025-01-16 01:10:10.072493 Pacific Standard Time:		step 4/4 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/sanitizer_sanity_result_$date1/sanitizer_sanity.json
                                            ==============================
2025-01-16 01:10:10.072493 Pacific Standard Time:		[Err.S] [96mR301 - step 4/4 <expected_contains> result: [0m[0;41mBAD[0m[96m (expected - <"failed": 0> contained)[0m
                                                                            		[41m| '"failed": 0' - not found[0m [Err.E]
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/sanitizer_sanity_result_$date1/sanitizer_sanity.json
                                            ==============================
2025-01-16 01:10:10.072493 Pacific Standard Time:		[96mR400 - step 4/4 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/sanitizer_sanity_result_$date1/sanitizer_sanity.json
                                            ==============================
2025-01-16 01:10:10.073511 Pacific Standard Time:		[1;37;41mstep 4/4 FAIL[0m
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/sanitizer_sanity_result_$date1/sanitizer_sanity.json
                                            ==============================
