
2025-01-16 01:37:18.432488 Pacific Standard Time:		step 4/4 <expected_contains> GOOD: <"failed": 0> found in <    "failed": 0> (expected - <"failed": 0> contained)
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${RESULTS_HOME}/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/2552099_cupti_metric_properties_result_$date1/cupti_metric_properties.json
                                            ==============================
2025-01-16 01:37:18.448113 Pacific Standard Time:		step 4/4 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${RESULTS_HOME}/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/2552099_cupti_metric_properties_result_$date1/cupti_metric_properties.json
                                            ==============================
2025-01-16 01:37:18.448113 Pacific Standard Time:		[96mR301 - step 4/4 <expected_contains> result: [0m[0;92mGOOD[0m[96m (expected - <"failed": 0> contained)[0m
                                                                                   		[92m| '"failed": 0' - found[0m
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${RESULTS_HOME}/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/2552099_cupti_metric_properties_result_$date1/cupti_metric_properties.json
                                            ==============================
2025-01-16 01:37:18.448113 Pacific Standard Time:		[96mR400 - step 4/4 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${RESULTS_HOME}/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/2552099_cupti_metric_properties_result_$date1/cupti_metric_properties.json
                                            ==============================
2025-01-16 01:37:18.448113 Pacific Standard Time:		[1;37;42mstep 4/4 PASS[0m
                                             ==== CMD ====: 
                                              $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                              echo $date1
                                              cat ${RESULTS_HOME}/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/2552099_cupti_metric_properties_result_$date1/cupti_metric_properties.json
                                            ==============================
