
2025-01-16 01:47:17.460536 Pacific Standard Time:		step 4/5 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $<PERSON>ANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $<PERSON>ANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $<PERSON>ANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $<PERSON>ANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================
2025-01-16 01:47:17.460536 Pacific Standard Time:		[96mR400 - step 4/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $BRANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $BRANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $BRANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $BRANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================
2025-01-16 01:47:17.460536 Pacific Standard Time:		[1;37;42mstep 4/5 PASS[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $BRANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $BRANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $BRANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $BRANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================

2025-01-16 01:47:20.286212 Pacific Standard Time:		step 4/5 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $BRANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $BRANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $BRANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $BRANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================
2025-01-16 01:47:20.286212 Pacific Standard Time:		[96mR400 - step 4/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $BRANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $BRANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $BRANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $BRANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================
2025-01-16 01:47:20.286212 Pacific Standard Time:		[1;37;42mstep 4/5 PASS[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              echo "12.9"
                                              
                                              $OLD=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "OLD: $OLD"
                                              $BRANCH="12.9"
                                              If ( "11.7" -eq $BRANCH) {
                                                  $NEW="H"
                                              } ElseIf ( "11.6" -eq $BRANCH ) {
                                                  $NEW="G"
                                              } ElseIf ( "11.5" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "11.4" -eq $BRANCH ) {
                                                  $NEW="E"
                                              } ElseIf ( "11.8" -eq $BRANCH ) {
                                                  $NEW="C"
                                              } ElseIf ( "12.0" -eq $BRANCH ) {
                                                  $NEW="D"
                                              } ElseIf ( "12.1" -eq $BRANCH ) {
                                                  $NEW="F"
                                              } ElseIf ( "12.2" -eq $BRANCH ) {
                                                  $NEW="I"
                                              } ElseIf ( "12.3" -eq $BRANCH ) {
                                                  $NEW="CUDA12.3"
                                              } ElseIf ( "12.4" -eq $BRANCH ) {
                                                  $NEW="CUDA12.4"
                                              } ElseIf ( "12.5" -eq $BRANCH ) {
                                                  $NEW="CUDA12.5"
                                              } ElseIf ( "12.6" -eq $BRANCH ) {
                                                  $NEW="CUDA12.6"
                                              } ElseIf ( "12.7" -eq $BRANCH ) {
                                                  $NEW="CUDA12.7"
                                              } ElseIf ( "12.8" -eq $BRANCH ) {
                                                  $NEW="CUDA12.8"
                                              } ElseIf ( "12.9" -eq $BRANCH ) {
                                                  $NEW="CUDA12.9"
                                              } Else {
                                                  $NEW="ERROR"
                                              }
                                              sed -i "s/$OLD/    DVS_BUILD: $NEW/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DVS_BUILD:' ${CUPTI_YAML})
                                              echo "NEW: $NEW"
                                            ==============================
