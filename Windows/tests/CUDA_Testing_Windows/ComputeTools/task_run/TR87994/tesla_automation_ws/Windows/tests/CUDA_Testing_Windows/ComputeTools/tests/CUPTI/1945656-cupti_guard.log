2025-01-16 00:42:37.452547 -0800 [INFO] 1945656-cupti_guard [m	++++++ <Running Test Rerun-0: [1945656-cupti guard] (parent: CUPTI)> ++++++[m
2025-01-16 00:42:37.939725 -0800 [INFO] 1945656-cupti_guard [m	====== < ${WS_ROOT}: [ C:/tesla_linux_auto/task_run/TR87994/tesla_automation_ws/Windows ] ======[m
2025-01-16 00:42:37.939725 -0800 [INFO] 1945656-cupti_guard [m	====== < ${TEST_WS}: [ C:/tesla_linux_auto/task_run/TR87994/tesla_automation_ws/Windows/tests/CUDA_Testing_Windows/ComputeTools/tests/CUPTI ] ======[m
2025-01-16 00:42:37.939725 -0800 [INFO] 1945656-cupti_guard [m	====== < ${TEST_FILE_DIR}: [ C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\tests\CUPTI ] ======[m
2025-01-16 00:42:37.955455 -0800 [INFO] 1945656-cupti_guard.steps[1] [36m    ------ Running step 1/3 (Test: 1945656-cupti_guard)> ------[m
2025-01-16 00:42:37.955455 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:42:37.955455 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m Visual Studio Version        = <>[m
2025-01-16 00:42:37.955455 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m Visual Studio Suite          = <>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m MOFED                        = <>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m IB Firmware                  = <>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        TEMPLATE_ID           = <1945656>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:42:37.971040 -0800 [DEBUG] 1945656-cupti_guard.steps[1] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:37.986616 -0800 [WARNING] 1945656-cupti_guard.steps[1] [93m      - step 1/3 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:42:37.986616 -0800 [INFO] 1945656-cupti_guard.steps[1] [93m      - step 1/3 timeout = None seconds (None)[m
2025-01-16 00:42:37.986616 -0800 [INFO] 1945656-cupti_guard.steps[1] [m       ==== CMD ====: 
                                                                            $script_path=C: ; echo $script_path
                                                                            grep 'cupti_yaml = '  C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py
                                                                          ==============================[m
2025-01-16 00:42:37.986616 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:42:38.300555 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	| cupti_yaml = 'C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml'[m
2025-01-16 00:42:38.316110 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:42:38.316110 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 00:42:38.316110 -0800 [INFO] 1945656-cupti_guard.steps[1] [92m	step 1/3 <expected_returncode> GOOD: <0>[m
2025-01-16 00:42:38.316110 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	[96mR400 - step 1/3 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 00:42:38.316110 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	[1;37;42mstep 1/3 PASS[0m[m
2025-01-16 00:42:38.331735 -0800 [INFO] 1945656-cupti_guard.steps[1] [m	Saving bridge file for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994/TR87994-1945656-cupti_guard-1.yaml>[m
2025-01-16 00:42:38.331735 -0800 [INFO] 1945656-cupti_guard.steps[2] [36m    ------ Running step 2/3 (Test: 1945656-cupti_guard)> ------[m
2025-01-16 00:42:38.331735 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:42:38.335247 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m Visual Studio Version        = <>[m
2025-01-16 00:42:38.335247 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m Visual Studio Suite          = <>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m MOFED                        = <>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m IB Firmware                  = <>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        TEMPLATE_ID           = <1945656>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:42:38.347761 -0800 [DEBUG] 1945656-cupti_guard.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:38.347761 -0800 [WARNING] 1945656-cupti_guard.steps[2] [93m      - step 2/3 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:42:38.347761 -0800 [INFO] 1945656-cupti_guard.steps[2] [93m      - step 2/3 timeout = None seconds (None)[m
2025-01-16 00:42:38.347761 -0800 [INFO] 1945656-cupti_guard.steps[2] [m       ==== CMD ====: 
                                                                            C:/Python37/python.exe C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py -e cupti_guard
                                                                          ==============================[m
2025-01-16 00:42:38.347761 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| the logs folder exist[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/Samples/1_Utilities/deviceQuery"; cmd.exe /c "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv" deviceQuery_vs2022.sln /rebuild "Debug"; cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/bin/win64/Debug"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| sm_temp is 8.6[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| sm_temp(encoding with UTF-8-sig) is b'\xef\xbb\xbf8.6'[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| sm is 8.6[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| Traceback (most recent call last):[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 3405, in <module>[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|     getattr(mod, case_list_single[0])()[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\common_utils.py", line 100, in wrapper[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|     func(*args, **kw)[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 393, in cupti_guard[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|     prepare_cupti(arch=platform)[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 227, in prepare_cupti[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|     download_path = get_dvs_package('cupti', 'windows', cuda_short_version)[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\common_utils.py", line 1024, in get_dvs_package[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|     url = "{}Agora_Rel_{}%20Release%20{}".format(original_url, Agora[cuda_version], url_component[tool][arch])[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	|                                                                ~~~~~^^^^^^^^^^^^^^[m
2025-01-16 00:42:46.387098 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	| KeyError: '12.9'[m
2025-01-16 00:42:46.449650 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:42:46.449650 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	[7m[ CMD.returncode = 1 ][0m[m
2025-01-16 00:42:46.449650 -0800 [ERROR] 1945656-cupti_guard.steps[2] [0m	[37;41m[Err.S] step 2/3 <expected_returncode> BAD: <1> (expected - 0) [Err.E][0m[m
2025-01-16 00:42:46.449650 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	[Err.S] [96mR400 - step 2/3 <expected_returncode> result: [0m[0;41mBAD[0m[96m (expected - <0>)[0m [Err.E][m
2025-01-16 00:42:46.452660 -0800 [ERROR] 1945656-cupti_guard.steps[2] [m	[Step.Fail.S] [1;37;41mstep 2/3 FAIL[0m [Step.Fail.E][m
2025-01-16 00:42:46.452660 -0800 [INFO] 1945656-cupti_guard.steps[2] [m	Saving bridge file for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994/TR87994-1945656-cupti_guard-2.yaml>[m
2025-01-16 00:42:46.452660 -0800 [INFO] 1945656-cupti_guard.steps[3] [36m    ------ Running step 3/3 (Test: 1945656-cupti_guard)> ------[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m Visual Studio Version        = <>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m Visual Studio Suite          = <>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m MOFED                        = <>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m IB Firmware                  = <>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        TEMPLATE_ID           = <1945656>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:42:46.452660 -0800 [DEBUG] 1945656-cupti_guard.steps[3] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:46.452660 -0800 [WARNING] 1945656-cupti_guard.steps[3] [93m      - step 3/3 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:42:46.465669 -0800 [INFO] 1945656-cupti_guard.steps[3] [93m      - step 3/3 timeout = None seconds (None)[m
2025-01-16 00:42:46.465669 -0800 [INFO] 1945656-cupti_guard.steps[3] [m       ==== CMD ====: 
                                                                            $date1=(grep 'DATE:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml | awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
                                                                            echo $date1
                                                                            cat ${SCRIPT_DIR}/tesla_automation_results/cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90/cupti_guard_result_$date1/cupti_guard.json
                                                                          ==============================[m
2025-01-16 00:42:46.465669 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:42:46.763045 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| 20250116001705[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| cat : Cannot find path 'C:\tesla_automation_results\cupti_deviceGPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90\cupti_guard_re[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| sult_20250116001705\cupti_guard.json' because it does not exist.[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| At line:1 char:183[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| + ... echo $date1;cat ${SCRIPT_DIR}/tesla_automation_results/cupti_deviceGP ...[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| +                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	|     + CategoryInfo          : ObjectNotFound: (C:\tesla_automa...upti_guard.json:String) [Get-Content], ItemNotFoundEx[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	|    ception[m
2025-01-16 00:42:46.873031 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	|     + FullyQualifiedErrorId : PathNotFound,Microsoft.PowerShell.Commands.GetContentCommand[m
2025-01-16 00:42:46.888657 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	| [m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	[7m[ CMD.returncode = 1 ][0m[m
2025-01-16 00:42:46.904288 -0800 [ERROR] 1945656-cupti_guard.steps[3] [0m	[37;41m[Err.S] step 3/3 <expected_returncode> BAD: <1> (expected - 0) [Err.E][0m[m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	[Err.S] [96mR301 - step 3/3 <expected_contains> result: [0m[0;41mBAD[0m[96m (expected - <"failed": 0> contained)[0m
                                                                       		[41m| '"failed": 0' - not found[0m [Err.E][m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	[Err.S] [96mR400 - step 3/3 <expected_returncode> result: [0m[0;41mBAD[0m[96m (expected - <0>)[0m [Err.E][m
2025-01-16 00:42:46.904288 -0800 [ERROR] 1945656-cupti_guard.steps[3] [m	[Step.Fail.S] [1;37;41mstep 3/3 FAIL[0m [Step.Fail.E][m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.steps[3] [m	Saving bridge file for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994/TR87994-1945656-cupti_guard-3.yaml>[m
2025-01-16 00:42:46.904288 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [36m    ------ Running <Teardown> step 999/1 (Test: 1945656-cupti_guard)> ------[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m Visual Studio Version        = <>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m Visual Studio Suite          = <>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m MOFED                        = <>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m IB Firmware                  = <>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        TEMPLATE_ID           = <1945656>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:42:46.904288 -0800 [DEBUG] 1945656-cupti_guard.<teardown>_steps[999] [44m====---------------------------------------------------------------====[m
2025-01-16 00:42:46.919861 -0800 [WARNING] 1945656-cupti_guard.<teardown>_steps[999] [93m      - step 999/1 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:42:46.919861 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [93m      - step 999/1 timeout = None seconds (None)[m
2025-01-16 00:42:46.919861 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m       ==== CMD ====: 
                                                                            echo "Teardown"
                                                                          ==============================[m
2025-01-16 00:42:46.919861 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:42:47.124057 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	| Teardown[m
2025-01-16 00:42:47.154639 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [92m	step 999/1 <expected_returncode> GOOD: <0>[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	[96mR400 - step 999/1 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	[1;37;42mstep 999/1 PASS[0m[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	Saving bridge file for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994/TR87994-1945656-cupti_guard-999.yaml>[m
2025-01-16 00:42:47.155648 -0800 [INFO] 1945656-cupti_guard.<teardown>_steps[999] [m	Saving bridge file for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994/TR87994-1945656-cupti_guard-999.yaml>[m
2025-01-16 00:42:47.171285 -0800 [ERROR] 1945656-cupti_guard [m	[1;37;41m==== FAIL ==== Test - <1945656-cupti guard>[0m[m
2025-01-16 00:42:47.171285 -0800 [INFO] 1945656-cupti_guard [m	Saving bridge file of Test for uploading to CQASystem <C:/tesla_linux_auto/upload/Ongoing/TR87994//TR87994-1945656-cupti_guard.yaml>[m
2025-01-16 00:42:48.019003 -0800 [INFO] 1945656-cupti_guard [mSince result is FAIL, it is required to show latest PASS configs[m
2025-01-16 00:42:48.019003 -0800 [INFO] 1945656-cupti_guard [m{'task_run_tc_id': 6441857, 'cuda_ver': '12.8.45', 'driver_ver': '571.39', 'owner_name': 'Liqiang Jiang', 'result': 'PASS', 'result_time': '2025-01-10T05:18:41.377678Z', 'log_file': '<a href="https://cqasystem.nvidia.com/task_log/TR87471/3/tests/CUDA_Testing_Windows/ComputeTools/tests/CUPTI/1945656-cupti_guard.log">1945656-cupti_guard.log</a>', 'bugs': None, 'task_tc_id': 66504602, 'user_mode_driver_ver': '', 'taskrun_id': 87471, 'task_id': 80810, 'devtest_env': 'GB203_Win11', 'log_path': 'https://cqasystem.nvidia.com/task_log/TR87471/3/tests/CUDA_Testing_Windows/ComputeTools/tests/CUPTI/1945656-cupti_guard.log'}[m
