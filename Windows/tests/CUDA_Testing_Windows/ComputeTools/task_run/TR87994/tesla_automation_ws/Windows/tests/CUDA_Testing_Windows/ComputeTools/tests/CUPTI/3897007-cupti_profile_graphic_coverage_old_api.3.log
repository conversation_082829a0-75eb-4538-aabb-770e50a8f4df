2025-01-16 00:43:10.041741 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [36m    ------ Running step 3/4 (Test: 3897007-cupti_profile_graphic_coverage_old_api)> ------[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m Visual Studio Version        = <>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m Visual Studio Suite          = <>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m OS Verion                    = <Windows 11>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m MOFED                        = <>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m IB Firmware                  = <>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m====---------------------------------------------------------------====[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        P4ROOT                = <Undefined>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        DEVICE_ID             = <0>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        TEMPLATE_ID           = <3897007>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 00:43:10.041741 -0800 [DEBUG] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [44m====---------------------------------------------------------------====[m
2025-01-16 00:43:10.041741 -0800 [WARNING] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [93m      - step 3/4 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 00:43:10.041741 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [93m      - step 3/4 timeout = None seconds (None)[m
2025-01-16 00:43:10.041741 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m       ==== CMD ====: 
                                                                                                       C:/Python37/python.exe C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py -e profile_injection_graphic_coverage_old_api_3897007
                                                                                                     ==============================[m
2025-01-16 00:43:10.041741 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| the logs folder exist[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/Samples/1_Utilities/deviceQuery"; cmd.exe /c "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv" deviceQuery_vs2022.sln /rebuild "Debug"; cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/bin/win64/Debug"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm_temp is 8.6[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm_temp(encoding with UTF-8-sig) is b'\xef\xbb\xbf8.6'[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm is 8.6[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/Samples/1_Utilities/deviceQuery"; cmd.exe /c "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv" deviceQuery_vs2022.sln /rebuild "Debug"; cd "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v12.9/bin/win64/Debug"; ./deviceQuery | grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm_temp is 8.6[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm_temp(encoding with UTF-8-sig) is b'\xef\xbb\xbf8.6'[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| sm is 8.6[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| >>>>>> The command is nvidia-smi --query-gpu=driver_model.current --format=csv,noheader[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| >>>>>> We get the driver mode on Windows, Driver mode list is ['WDDM'].[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| >>>>>> We get the driver mode on Windows successfully. Driver mode is WDDM.[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| >>>>>>>>>>The driver mode is WDDM<<<<<<<<<<[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| Traceback (most recent call last):[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 3405, in <module>[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|     getattr(mod, case_list_single[0])()[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\common_utils.py", line 100, in wrapper[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|     func(*args, **kw)[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 2866, in profile_injection_graphic_coverage_old_api_3897007[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|     prepare_cupti(arch=platform)[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\run_cupti_case.py", line 227, in prepare_cupti[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|     download_path = get_dvs_package('cupti', 'windows', cuda_short_version)[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[m
2025-01-16 00:43:24.352743 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|   File "C:\tesla_automation_test\Windows\tests\CUDA_Testing_Windows\ComputeTools\scripts\common_utils.py", line 1024, in get_dvs_package[m
2025-01-16 00:43:24.368429 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|     url = "{}Agora_Rel_{}%20Release%20{}".format(original_url, Agora[cuda_version], url_component[tool][arch])[m
2025-01-16 00:43:24.368429 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	|                                                                ~~~~~^^^^^^^^^^^^^^[m
2025-01-16 00:43:24.368429 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	| KeyError: '12.9'[m
2025-01-16 00:43:24.415820 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 00:43:24.415820 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[7m[ CMD.returncode = 1 ][0m[m
2025-01-16 00:43:24.415820 -0800 [ERROR] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [0m	[37;41m[Err.S] step 3/4 <expected_returncode> BAD: <1> (expected - 0) [Err.E][0m[m
2025-01-16 00:43:24.415820 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[96mR231 - step 3/4 <na_if_contains> result: [0m[0;92mGOOD[0m[96m (triggered by - <not support this case> contained)[0m
                                                                                                  		[92m| 'not support this case' - not found[0m[m
2025-01-16 00:43:24.415820 -0800 [INFO] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[Err.S] [96mR400 - step 3/4 <expected_returncode> result: [0m[0;41mBAD[0m[96m (expected - <0>)[0m [Err.E][m
2025-01-16 00:43:24.415820 -0800 [ERROR] 3897007-cupti_profile_graphic_coverage_old_api.steps[3] [m	[Step.Fail.S] [1;37;41mstep 3/4 FAIL[0m [Step.Fail.E][m
