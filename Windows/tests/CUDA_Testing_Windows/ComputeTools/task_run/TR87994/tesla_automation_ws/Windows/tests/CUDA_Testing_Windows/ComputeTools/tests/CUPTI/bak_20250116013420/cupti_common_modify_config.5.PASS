
2025-01-16 01:01:34.049087 Pacific Standard Time:		step 5/5 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================
2025-01-16 01:01:34.049087 Pacific Standard Time:		[96mR400 - step 5/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================
2025-01-16 01:01:34.049087 Pacific Standard Time:		[1;37;42mstep 5/5 PASS[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================

2025-01-16 01:01:36.977545 Pacific Standard Time:		step 5/5 <expected_returncode> GOOD: <0>
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================
2025-01-16 01:01:36.977545 Pacific Standard Time:		[96mR400 - step 5/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================
2025-01-16 01:01:36.977545 Pacific Standard Time:		[1;37;42mstep 5/5 PASS[0m
                                             ==== CMD ====: 
                                              echo "${CUPTI_YAML}"
                                              $DATE=Get-Date -Format 'yyyyMMddHHmmss'
                                              $DATE1=Get-Date -Format 'yyyyMMdd'
                                              echo "$DATE, $DATE1"
                                              
                                              $OLD=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $OLD1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "OLD: $OLD, $OLD1"
                                              $NEW=$DATE
                                              $NEW1=$DATE1
                                              sed -i "s/$OLD/    DATE: $NEW/" ${CUPTI_YAML}
                                              sed -i "s/$OLD1/    DATE1: $NEW1/" ${CUPTI_YAML}
                                              $NEW=(grep -i 'DATE:' ${CUPTI_YAML})
                                              $NEW1=(grep -i 'DATE1:' ${CUPTI_YAML})
                                              echo "NEW: $NEW, $NEW1"
                                            ==============================
