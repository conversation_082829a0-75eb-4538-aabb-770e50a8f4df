2025-01-16 01:14:45.658828 -0800 [INFO] external_common_modify_config.steps[2] [36m    ------ Running step 2/5 (Test: suite.yaml - external_common_modify_config)> ------[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m====----------------- Current Environment Details -----------------====[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m Visual Studio Version        = <>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m Visual Studio Suite          = <>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m CUDA Driver Version          = <575.40 CL 35390623>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m OS Verion                    = <Windows 11>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m MOFED                        = <>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m IB Firmware                  = <>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m GPU Name                     = <RTX-3060>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m====---------------- Current Environment Variables ----------------====[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        P4ROOT                = <Undefined>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        LD_LIBRARY_PATH       = <Undefined>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        GPGPU_COMPILER_EXPORT = <Undefined>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        NV_TOOLS              = <Undefined>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        DRIVER_ROOT           = <Undefined>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        DEVICE_ID             = <0>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        TEMPLATE_ID           = <9999993>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m        VISIBLE_DEVICE_SM     = <86>[m
2025-01-16 01:14:45.665838 -0800 [DEBUG] external_common_modify_config.steps[2] [44m====---------------------------------------------------------------====[m
2025-01-16 01:14:45.665838 -0800 [WARNING] external_common_modify_config.steps[2] [93m      - step 2/5 timeout = None - invalid settings! Timeout ignored ... [m
2025-01-16 01:14:45.665838 -0800 [INFO] external_common_modify_config.steps[2] [93m      - step 2/5 timeout = None seconds (None)[m
2025-01-16 01:14:45.665838 -0800 [INFO] external_common_modify_config.steps[2] [m       ==== CMD ====: 
                                                                                      echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml"
                                                                                      echo "575, 575.40"
                                                                                      
                                                                                      $OLD=(grep -i 'DRV_BRANCH:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      $OLD1=(grep -i 'DRIVER_VERSION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      echo "OLD: $OLD, $OLD1"
                                                                                      $NEW="575"
                                                                                      $NEW1="575.40"
                                                                                      sed -i "s/$OLD/    DRV_BRANCH: $NEW/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml
                                                                                      sed -i "s/$OLD1/    DRIVER_VERSION: $NEW1/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml
                                                                                      $NEW=(grep -i 'DRV_BRANCH:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      $NEW1=(grep -i 'DRIVER_VERSION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      echo "NEW: $NEW, $NEW1"
                                                                                    ==============================[m
2025-01-16 01:14:45.665838 -0800 [INFO] external_common_modify_config.steps[2] [m	[44m[^RUN_CMD_START^][0m[m
2025-01-16 01:14:45.901556 -0800 [INFO] external_common_modify_config.steps[2] [m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml[m
2025-01-16 01:14:45.901556 -0800 [INFO] external_common_modify_config.steps[2] [m	| 575, 575.40[m
2025-01-16 01:14:45.964404 -0800 [INFO] external_common_modify_config.steps[2] [m	| OLD:     DRV_BRANCH: 570,     DRIVER_VERSION: 570.39[m
2025-01-16 01:14:46.089872 -0800 [INFO] external_common_modify_config.steps[2] [m	| NEW:     DRV_BRANCH: 575,     DRIVER_VERSION: 575.40[m
2025-01-16 01:14:46.105497 -0800 [INFO] external_common_modify_config.steps[2] [m	[44m[_RUN_CMD_END_][0m[m
2025-01-16 01:14:46.105497 -0800 [INFO] external_common_modify_config.steps[2] [m	[7m[ CMD.returncode = 0 ][0m[m
2025-01-16 01:14:46.105497 -0800 [INFO] external_common_modify_config.steps[2] [92m	step 2/5 <expected_returncode> GOOD: <0>[m
2025-01-16 01:14:46.105497 -0800 [INFO] external_common_modify_config.steps[2] [m	[96mR400 - step 2/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m
2025-01-16 01:14:46.105497 -0800 [INFO] external_common_modify_config.steps[2] [m	[1;37;42mstep 2/5 PASS[0m[m
2025-01-16 01:14:48.696278 -0800 [INFO] external_common_modify_config.steps[2] [m[36m    ------ Running step 2/5 (Test: external_common_modify_config)> ------[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m====----------------- Current Environment Details -----------------====[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m Visual Studio Version        = <>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m Visual Studio Suite          = <>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m CUDA Toolkit Version         = <12.9.0 CL 35281822>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m CUDA Driver Version          = <575.40 CL 35390623>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m OS Verion                    = <Windows 11>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m VBIOS                        = <VBIOS Version                         : 94.02.42.80.77>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m MOFED                        = <>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m IB Firmware                  = <>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m GPU Name                     = <RTX-3060>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m====---------------- Current Environment Variables ----------------====[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        P4ROOT                = <Undefined>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        LD_LIBRARY_PATH       = <Undefined>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        GPGPU_COMPILER_EXPORT = <Undefined>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        NV_TOOLS              = <Undefined>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        DRIVER_ROOT           = <Undefined>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        PATH                  = <C:\Program Files\Microsoft MPI\Bin;C:\Windows\System32\Wbem;c:\Python311;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0;C:\Python310;C:\Program Files\OpenSSH-Win64;C:\Windows\system32;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\ProgramData\chocolatey\bin;C:\Windows;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tesseract-OCR;c:\Python311\Scripts;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\bin\Hostx64\x64;C:\CTT\cmder\vendor\git-for-windows\usr\bin;c:\Strawberry\perl\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Microsoft VS Code\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;c:\Strawberry\perl\site\bin;C:\Program Files\CMake\bin;C:\Python310\Scripts;c:\Strawberry\c\bin;C:\Windows\System;>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        CUDA_VISIBLE_DEVICES  = <GPU-23ce5fb8-ac2b-4f1a-0f7d-6b3f1e7c0f90>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        DEVICE_ID             = <0>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        TEMPLATE_ID           = <9999993>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m        VISIBLE_DEVICE_SM     = <86>[m[m
2025-01-16 01:14:48.696278 -0800 [DEBUG] external_common_modify_config.steps[2] [44m[44m====---------------------------------------------------------------====[m[m
2025-01-16 01:14:48.696278 -0800 [WARNING] external_common_modify_config.steps[2] [93m[93m      - step 2/5 timeout = None - invalid settings! Timeout ignored ... [m[m
2025-01-16 01:14:48.696278 -0800 [INFO] external_common_modify_config.steps[2] [m[93m      - step 2/5 timeout = None seconds (None)[m[m
2025-01-16 01:14:48.696278 -0800 [INFO] external_common_modify_config.steps[2] [m[m       ==== CMD ====: 
                                                                                      echo "C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml"
                                                                                      echo "575, 575.40"
                                                                                      
                                                                                      $OLD=(grep -i 'DRV_BRANCH:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      $OLD1=(grep -i 'DRIVER_VERSION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      echo "OLD: $OLD, $OLD1"
                                                                                      $NEW="575"
                                                                                      $NEW1="575.40"
                                                                                      sed -i "s/$OLD/    DRV_BRANCH: $NEW/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml
                                                                                      sed -i "s/$OLD1/    DRIVER_VERSION: $NEW1/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml
                                                                                      $NEW=(grep -i 'DRV_BRANCH:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      $NEW1=(grep -i 'DRIVER_VERSION:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml)
                                                                                      echo "NEW: $NEW, $NEW1"
                                                                                    ==============================[m[m
2025-01-16 01:14:48.696278 -0800 [INFO] external_common_modify_config.steps[2] [m[m	[44m[^RUN_CMD_START^][0m[m[m
2025-01-16 01:14:48.916329 -0800 [INFO] external_common_modify_config.steps[2] [m[m	| C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml[m[m
2025-01-16 01:14:48.916329 -0800 [INFO] external_common_modify_config.steps[2] [m[m	| 575, 575.40[m[m
2025-01-16 01:14:48.979276 -0800 [INFO] external_common_modify_config.steps[2] [m[m	| OLD:     DRV_BRANCH: 575,     DRIVER_VERSION: 575.40[m[m
2025-01-16 01:14:49.104754 -0800 [INFO] external_common_modify_config.steps[2] [m[m	| NEW:     DRV_BRANCH: 575,     DRIVER_VERSION: 575.40[m[m
2025-01-16 01:14:49.120427 -0800 [INFO] external_common_modify_config.steps[2] [m[m	[44m[_RUN_CMD_END_][0m[m[m
2025-01-16 01:14:49.120427 -0800 [INFO] external_common_modify_config.steps[2] [m[m	[7m[ CMD.returncode = 0 ][0m[m[m
2025-01-16 01:14:49.120427 -0800 [INFO] external_common_modify_config.steps[2] [m[92m	step 2/5 <expected_returncode> GOOD: <0>[m[m
2025-01-16 01:14:49.120427 -0800 [INFO] external_common_modify_config.steps[2] [m[m	[96mR400 - step 2/5 <expected_returncode> result: [0m[0;92mGOOD[0m[96m (expected - <0>)[0m[m[m
2025-01-16 01:14:49.120427 -0800 [INFO] external_common_modify_config.steps[2] [m[m	[1;37;42mstep 2/5 PASS[0m[m[m
