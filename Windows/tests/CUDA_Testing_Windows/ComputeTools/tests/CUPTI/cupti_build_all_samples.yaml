Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - SCRIPT_DIR: "C:"
    - DEVENV: "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/devenv"
    - MSBUILD: "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/MSBuild"
    - SAMPLE_PATH: "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v${TK_BRANCH}"
    - SAMPLE_BIN_PATH: "C:/ProgramData/NVIDIA Corporation/CUDA Samples/v${TK_BRANCH}/bin/win64/Debug"
    - SOLUTION: "Samples_VS2022.sln"
    - PYTHON: "C:/Python37/python.exe"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 9999991
  setup: ~
  shell: powershell
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            grep 'cupti_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py

    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}"
            echo $script_path
            echo "${DEVENV}, ${MSBUILD}, ${SAMPLE_PATH}, ${SAMPLE_BIN_PATH}"
            nvidia-smi.exe -L

    # rebuild all CUDA Samples in Debug mode
    - expected_returncode: 0
      cmd: |
            echo "^^^^^^^^^^ Build all CUDA Samples in Debug mode Start ..."
            ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py -e cupti_build_all_samples
            echo "@@@@@@@@@@ Build all CUDA Samples in Debug mode End."

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
        echo "Teardown"