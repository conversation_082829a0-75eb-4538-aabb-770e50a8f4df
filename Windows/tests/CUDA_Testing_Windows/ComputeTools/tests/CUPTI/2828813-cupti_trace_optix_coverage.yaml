Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
    - CMDER_PATH: "C:/CTT/cmder"
    - CMDER_BIN_PATH: "${CMDER_PATH}/vendor/git-for-windows/usr/bin"
    - PYTHON: "C:/Python37/python.exe"
    - RESULTS_HOME: ${SCRIPT_DIR}/tesla_automation_results
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2828813
  setup: ~
  shell: powershell
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            $script_path=${SCRIPT_DIR} ; echo $script_path
            grep 'cupti_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py

    - expected_returncode: 0
      na_if_contains: "not support this case"
      cmd: |
            ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_cupti_case.py -e cupti_trace_optix_coverage_2828813

    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/cupti_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            cat ${RESULTS_HOME}/cupti_device${CUDA_VISIBLE_DEVICES}/2828813_trace_optix_coverage_result_$date1/cupti_trace_optix_coverage.json

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"
