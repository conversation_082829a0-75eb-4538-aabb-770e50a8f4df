Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
    - CMDER_PATH: "C:/CTT/cmder"
    - CMDER_BIN_PATH: "${CMDER_PATH}/vendor/git-for-windows/usr/bin"
    - PYTHON: "C:/Python37/python.exe"
    - RESULTS_HOME: ${SCRIPT_DIR}/tesla_automation_results
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2806646
  setup: ~
  shell: powershell
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            grep 'sanitizer_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py

    # Modify the 11.5 to 11.6 according to the case on Linux, Jan 13, 2022
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            $toolkit=(echo "${TK_BRANCH}")
            echo $toolkit
            $toolkit1="11.6"

            if($toolkit -lt $toolkit1) {
               echo "++++++++ This case is available from CUDA 11.6. ++++++++"
               echo "&&&& NA"
            }

    - expected_returncode: 0
      cmd: |
            ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_xml_option

    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            echo "CUDA_VISIBLE_DEVICES: ${CUDA_VISIBLE_DEVICES}"
            cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_device${CUDA_VISIBLE_DEVICES}/2806646_sanitizer_xml_option_result_$date1/sanitizer_xml_option.json

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"
