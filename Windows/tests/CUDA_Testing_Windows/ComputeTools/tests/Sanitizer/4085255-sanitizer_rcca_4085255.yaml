Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
    - CMDER_PATH: "C:/CTT/cmder"
    - CMDER_BIN_PATH: "${CMDER_PATH}/vendor/git-for-windows/usr/bin"
    - PYTHON: "C:/Python37/python.exe"
    - RESULTS_HOME: ${SCRIPT_DIR}/tesla_automation_results
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 4085255
  setup: ~
  shell: powershell
  steps: !!seq

    - expected_returncode: 0
      na_if_contains: "we only support this case since cuda 12.8 and only on GB100/GB10b"
      cmd: |
            ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_rcca_4085255

    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            echo "CUDA_VISIBLE_DEVICES: ${CUDA_VISIBLE_DEVICES}"
            cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_device${CUDA_VISIBLE_DEVICES}/4085255_sanitizer_4085255_rcca_result_$date1/sanitizer_rcca_4085255.json

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"