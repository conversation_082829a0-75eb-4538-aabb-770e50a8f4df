
Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 7777777
  setup: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              dir
              echo "Alex-Setup"

  shell: powershell
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
         cd C:\tesla_automation_test\Windows\test_code
         dir
    - expected_returncode: 0
      cmd: |
         compute-sanitizer "C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.5\bin\win64\Debug\matrixMul.exe"
    - expected_returncode: 0
      cmd: |
         compute-sanitizer "C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.5\bin\win64\Debug\mergeSort.exe"
    - expected_returncode: 0
      cmd: |
         $name="NVIDIA" ; echo $name
         echo $name
         echo "Alex<PERSON><PERSON><PERSON>"

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              dir
              echo "Alex-Teardown"




