Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2819761
  setup: ~
  shell: powershell
  steps: !!seq
    # GPU_PCI_BUS_ID = GPU UUID : GPU-a3c3e9df-21a2-33f4-4e98-677d8db93822 ==> CUDA_VISIBLE_DEVICES: "GPU-a3c3e9df-21a2-33f4-4e98-677d8db93822"
    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            echo "bash ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml"
            $DEVICES=(grep -i 'DEVICES:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
            echo "OLD DEVICES: $DEVICES"
            $NEW_DEVICES="${CUDA_VISIBLE_DEVICES}"
            sed -i "s/$DEVICES/    DEVICES: ${NEW_DEVICES}/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
            $DEVICES=(grep -i 'DEVICES:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
            echo "NEW DEVICES: $DEVICES"
    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            grep 'sanitizer_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
             $toolkit=(echo "${TK_BRANCH}")
             echo $toolkit
             $toolkit1="11.5"

             if($toolkit -lt $toolkit1) {
                 echo "&&&& NA"
             }
    - expected_returncode: 0
      cmd: |
            C:/Python37/python.exe ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e detail_session_option
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            echo "CUDA_VISIBLE_DEVICES: ${CUDA_VISIBLE_DEVICES}"
            cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_device${CUDA_VISIBLE_DEVICES}/2819761_detail_session_option_result_$date1/detail_session_option.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"
