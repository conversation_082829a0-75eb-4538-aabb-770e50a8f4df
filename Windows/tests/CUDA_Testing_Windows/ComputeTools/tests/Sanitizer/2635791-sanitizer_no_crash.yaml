Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2635791
  setup: ~
  shell: powershell
  steps: !!seq
    # GPU_PCI_BUS_ID = GPU UUID : GPU-a3c3e9df-21a2-33f4-4e98-677d8db93822 ==> CUDA_VISIBLE_DEVICES: "GPU-a3c3e9df-21a2-33f4-4e98-677d8db93822"
    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            echo "bash ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml"
            $DEVICES=(grep -i 'DEVICES:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
            echo "OLD DEVICES: $DEVICES"
            $NEW_DEVICES="${CUDA_VISIBLE_DEVICES}"
            sed -i "s/$DEVICES/    DEVICES: ${NEW_DEVICES}/" C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml
            $DEVICES=(grep -i 'DEVICES:' C:/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml)
            echo "NEW DEVICES: $DEVICES"

    - expected_returncode: 0
      cmd: |
            $script_path="${SCRIPT_DIR}" ; echo $script_path
            echo "/bin/bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml"
            grep 'sanitizer_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            $toolkit=(echo "$TK_BRANCH")
            $toolkit="11.5"
            echo $toolkit
            $toolkit1="11.3"
            if ( $toolkit -lt $toolkit1 ){
                echo "&&&& NA"
            }
    - expected_returncode: 0
      timeout: 1200s
      cmd: |
            C:/Python37/python.exe ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_no_crash
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            cat ${SCRIPT_DIR}/tesla_automation_results/sanitizer_device${CUDA_VISIBLE_DEVICES}/2635791_sanitizer_no_crash_result_$date1/sanitizer_no_crash.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"
