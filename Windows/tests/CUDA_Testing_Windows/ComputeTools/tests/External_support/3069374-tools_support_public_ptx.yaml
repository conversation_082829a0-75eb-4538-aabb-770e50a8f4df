Settings:
  env: !!seq
    - SCRIPT_DIR: "C:"
    - PLATFORM: x86_win
    - CMDER_PATH: "C:/CTT/cmder"
    - CMDER_BIN_PATH: "${CMDER_PATH}/vendor/git-for-windows/usr/bin"
    - PYTHON: "C:/Python37/python.exe"
    - RESULTS_HOME: ${SCRIPT_DIR}/tesla_automation_results
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3069374
  setup: ~
  shell: powershell
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            $script_path=${SCRIPT_DIR} ; echo $script_path
            grep 'common_case_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py

    - expected_returncode: 0
      na_if_contains: 'not support'
      cmd: |
            ${PYTHON} ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/scripts/run_common_case.py -e tools_support_public_ptx_3069374
  
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            $date1=(grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/Windows/tests/CUDA_Testing_Windows/ComputeTools/yaml/common_case.yaml |awk -F ':' '{print $2}' | sed 's/^[ \t]*//g').trim()
            echo $date1
            echo "CUDA_VISIBLE_DEVICES: ${CUDA_VISIBLE_DEVICES}"
            cat ${SCRIPT_DIR}/tesla_automation_results/external_device${CUDA_VISIBLE_DEVICES}/tools_support_public_ptx_3069374_result_$date1/tools_support_public_ptx_3069374.json

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
            echo "Teardown"
