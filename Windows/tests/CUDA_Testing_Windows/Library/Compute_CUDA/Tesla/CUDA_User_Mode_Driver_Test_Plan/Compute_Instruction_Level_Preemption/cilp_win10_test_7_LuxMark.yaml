Settings:
  env: !!seq
    - GPU_FILTERS: "gpu_with_display;driver_model_WDDM;single_gpu"
    - compatible_SM: "6.0"
    - compatible_driver: "396"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  automation_owner: cxuan
  devtest_id: 1596486
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\CILP\test_CILP.py::TestCILP::test_setup -v --log-file=details_1_setup.log

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\common\test_common.py::test_set_wddm_model -v --log-file=details_2_set_wddm_model.log

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\common\test_common.py::test_disable_unselected_gpu -v --log-file=details_3_disable_unselected_gpu.log

    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\CILP\test_CILP.py::TestCILP::test_luxMark -v --log-file=details_4_test_luxMark.log

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\common\test_common.py::test_enable_all_gpus -v --log-file=details_5_enable_unselected_gpu.log

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details*.log *.csv
