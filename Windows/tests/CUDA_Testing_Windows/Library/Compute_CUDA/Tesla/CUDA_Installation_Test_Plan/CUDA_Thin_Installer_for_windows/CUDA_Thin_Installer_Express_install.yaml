Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 872530
  automation_owner: jorbinm
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe common\test_common.py::test_cleanup -v --log-file=details_1_cleanup.log

    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      reboot_if_contains: "* Need reboot: True"
      exit_test_if_failed: True
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_thin_Installer_express_install.py::test_thin_installer_express -v --log-file=details_2_express_install.log --timeout=10800
              
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe common\test_common.py::test_reboot -v --log-file=details_3_reboot.log
              
    - expected_returncode: 0              
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_thin_Installer_express_install.py::test_checks_after_installation -v --log-file=details_4_checks_after_installation.log

    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_thin_Installer_express_install.py::test_build_and_run_nvjitlink_samples -v --log-file=details_5_build_and_run_nvjitlink_samples.log
   
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log  *.txt
