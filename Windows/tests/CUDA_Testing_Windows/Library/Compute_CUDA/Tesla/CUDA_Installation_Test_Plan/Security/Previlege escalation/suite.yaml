Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: CUDA_Security_Load_Image_Path_verification_after_adding_Malicious_DLL
    call: ${yaml_dir_path}/${name}.yaml
  - name: Driver_Security_Load_Image_Path_verification_after_adding_Malicious_DLL
    call: ${yaml_dir_path}/${name}.yaml
  - name: CUDA_Security_CUDA_Installation_uninstallation_after_adding_Malicious_DLL
    call: ${yaml_dir_path}/${name}.yaml    
  - name: Driver_Security_Driver_Installation_uninstallation_after_adding_Malicious_DLL
    call: ${yaml_dir_path}/${name}.yaml       
    