Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3532093
  automation_owner: lqian
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe common\test_common.py::test_cleanup -v --log-file=details_1_cleanup.log
    
    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_load_image_path_verification_after_adding_malicious_dll.py::test_load_image_path_verification -v --log-file=details_2_load_image_path_verification.log 
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log cleanup.txt Logfile.CSV
