Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1094623
  automation_owner: lqian
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe common\test_common.py::test_cleanup -v --log-file=details_1_cleanup.log
              
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      reboot_if_contains: "* Need reboot: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_custom_install.py::test_custom_install -v --log-file=details_2_cuda_custom_install.log
    
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_custom_install.py::test_checks_after_installation -v --log-file=details_3_checks_after_installation.log  

    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_custom_install.py::test_build_and_run_nvjitlink_samples -v --log-file=details_4_build_and_run_nvjitlink_samples.log  

    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_custom_install.py::test_uninstall_components_without_reboot -v --log-file=details_5_uninstall_components_without_reboot.log  

    - expected_returncode: 0              
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_uninstall_driver_components.py::test_uninstall_graphic_driver -v --log-file=details_6_uninstall_graphic_driver.log
 
    - expected_returncode: 0              
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_uninstall_driver_components.py::test_uninstall_HD_driver -v --log-file=details_7_uninstall_HD_driver.log
 
    - expected_returncode: 0              
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_uninstall_driver_components.py::test_uninstall_USBC_Driver -v --log-file=details_8_uninstall_USBC_Driver.log
 
    - expected_returncode: 0              
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe installation_tests\test_cuda_custom_install.py::test_check_after_uninstallation -v --log-file=details_9_check_after_uninstallation.log 

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log