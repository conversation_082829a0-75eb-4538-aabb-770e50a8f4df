Settings:
  env: !!seq
   - compatible_CUDA: "11.6"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2897706
  automation_owner: jorbinm
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\ms_visual_stuido\test_build_project_solution_SDK_samples.py::test_vs_install_dependency -v --log-file=details_1_install_dependency.log

    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\ms_visual_stuido\test_build_project_solution_SDK_samples.py -k "2022-Enterprise" -v --log-file=details_2_2022_enterprise.log --timeout=14400

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log
