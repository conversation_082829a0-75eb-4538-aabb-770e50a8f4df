Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2897704
  automation_owner: lqian
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      exit_test_if_failed: True 
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\ms_visual_stuido\test_vs_new_project_creation_with_previous_cuda_version.py::test_install_cuda -v -k "pre_version" --log-file=details_install_pre_cuda.log 

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      exit_test_if_failed: True 
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\ms_visual_stuido\test_vs_new_project_creation_with_previous_cuda_version.py::test_install_cuda -v -k "current" --log-file=details_install_current_cuda.log 
    
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\ms_visual_stuido\test_vs_new_project_creation_with_previous_cuda_version.py -v -k "2022-Community" --log-file=details_2022_community_new_project_with_previous_cuda_version.log

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log