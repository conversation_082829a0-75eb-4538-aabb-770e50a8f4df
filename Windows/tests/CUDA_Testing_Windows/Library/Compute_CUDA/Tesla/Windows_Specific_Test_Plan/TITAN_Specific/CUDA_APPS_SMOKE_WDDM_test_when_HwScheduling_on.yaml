Settings:
  env: !!seq
    - GPU_FILTERS: "driver_model_WDDM"
    - compatible_SM: "6.0"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  automation_owner: cxuan
  devtest_id: 2492868
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\common\test_common.py::test_set_wddm_model -v --log-file=details_1_set_WDDM_model.log

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\titan_specific\test_general_functions.py::test_set_TDR_recover_and_HWSCHED_on -v --log-file=details_2_set_TDR_recover_and_HWSCHED_on.log

    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\cuda_drvrt\test_cuda_apps_smoke.py::test_run_cuda_apps -v --log-file=details_3_run_cuda_apps.log --timeout=10800

    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\cuda_drvrt\test_cuda_apps_smoke.py::test_check_all_cuda_apps_results -v --log-file=details_4_test_check_all_cuda_apps_results.log 

    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\titan_specific\test_general_functions.py::test_set_TDR_bugcheck -v --log-file=details_5_set_TDR_bugcheck.log

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details*.log *.txt
