Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: CUDA_sanity_test
    call: ${yaml_dir_path}/${name}.yaml
  - name: NVML_test_with_normal_user
    call: ${yaml_dir_path}/${name}.yaml
  - name: NVML_test_with_sudo_user
    call: ${yaml_dir_path}/${name}.yaml
  - name: nvidia_smi_sanity_check_on_wsl
    call: ${yaml_dir_path}/${name}.yaml
  - name: CUDA_sanity_on_EFLOW_containers
    call: ${yaml_dir_path}/${name}.yaml
  - name: Document_verify_CUDA_installation_instructions
    call: ${yaml_dir_path}/${name}.yaml
  - name: Static_lib_check_for_compilation
    call: ${yaml_dir_path}/${name}.yaml
  - name: CUDA_Samples_Sanity_test
    call: ${yaml_dir_path}/${name}.yaml