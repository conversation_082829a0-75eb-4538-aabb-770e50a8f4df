Settings:
  env: ~
  enabled: True

Suites: !!seq
  - name: SDK_Samples
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Math_Library_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CU<PERSON>_Kernel_Mode_Driver_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_User_Mode_Driver_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Tesla_Stress_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Windows_Specific_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_HPC_Applications_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Compiler
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_CPP_Core_Library
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: One_Time_Test
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Installation_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Python_Low_Level_Binding
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: enhanced_compatibility_test_plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: eGPU_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Download_Page_test
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Release_Compability_Testing
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: HGX_Test_Plan
    call: ${yaml_dir_path}/${name}/suite.yaml  