Settings:
  env: ~
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2941931
  automation_owner: lqian
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cmd
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\one_time_test\test_verify_nvbandwidth.py::test_prepare_env -v --log-file=details_prepare_env.log --timeout=3600
              
    - expected_returncode: 0
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\one_time_test\test_verify_nvbandwidth.py::test_verify_nvbandwidth_functionalities -v --log-file=details_verify_nvbandwidth_functionalities.log --timeout=3600

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log *.txt