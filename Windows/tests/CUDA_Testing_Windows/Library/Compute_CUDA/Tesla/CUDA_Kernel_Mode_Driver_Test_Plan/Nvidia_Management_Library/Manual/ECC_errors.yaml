Settings:
  env: !!seq
    - GPU_FILTERS: "gpu_brand_Tesla;gpu_brand_Quadro"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1871587
  automation_owner: k<PERSON>a
  log_parser_type: Windows
  setup: !!seq
    - expected_returncode: 0
      reboot_if_contains: "Reboot required"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_setup.py
  shell: cd .
  steps: !!seq
    - expected_returncode: 0
      reboot_if_contains: "* Need reboot: True"
      na_if_contains: "* Test NA: True"
      waive_if_contains: "* Test waived: True"
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\nvml\test_reset_ecc_errors.py::test_enable_ecc -v --log-file=details_1_enable_ecc.log

    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\nvml\test_reset_ecc_errors.py::test_ecc_function -v --log-file=details_2_test_ecc_function.log

    - expected_returncode: 0
      fail_if_contains: "* Dump file detected: True"
      reboot_if_contains: "* Need reboot: True"
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code
              C:\PythonLinkTest\Scripts\pytest.exe normal_tests\nvml\test_reset_ecc_errors.py::test_disable_ecc -v --log-file=details_3_test_disable_ecc.log


  teardown: !!seq
    - expected_returncode: 0
      cmd: |
              cd C:\tesla_automation_test\Windows\test_code\common
              C:\PythonLinkTest\python.exe case_teardown.py --upload-logs details_*.log