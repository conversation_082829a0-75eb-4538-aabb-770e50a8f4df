Settings:
  env: ~
  enabled: True

Suites: !!seq
  - name: CUDA_Samples_Sanity_test_for_Windows
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Graphic_samples_specific
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_Samples_variants
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Memory_Check_for_CUDA_Samples
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: New_Samples_check
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: Static_lib_check
    call: ${yaml_dir_path}/${name}/suite.yaml
