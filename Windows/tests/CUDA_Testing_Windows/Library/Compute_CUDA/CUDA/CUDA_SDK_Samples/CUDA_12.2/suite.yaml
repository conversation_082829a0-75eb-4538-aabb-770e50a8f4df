Settings:
  env: ~
  enabled: True
  print_instance: False

Suites: !!seq
  - name: 0_Introduction
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 1_Utilities
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 2_Concepts_and_Techniques
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 3_CUDA_Features
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 4_CUDA_Libraries
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 5_Domain_Specific
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 6_Performance
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: 7_libNVVM
    call: ${yaml_dir_path}/${name}/suite.yaml
