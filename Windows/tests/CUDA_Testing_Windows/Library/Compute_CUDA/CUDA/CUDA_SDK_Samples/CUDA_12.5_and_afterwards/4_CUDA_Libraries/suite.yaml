
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: matrixMulCUBLAS
    call: ${yaml_dir_path}/${name}.yaml
  - name: oceanFFT
    call: ${yaml_dir_path}/${name}.yaml
  - name: lineOfSight
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientMultiBlockCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientMultiDeviceCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: randomFog
    call: ${yaml_dir_path}/${name}.yaml
  - name: MersenneTwisterGP11213
    call: ${yaml_dir_path}/${name}.yaml
  - name: boxFilterNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradient
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientPrecond
    call: ${yaml_dir_path}/${name}.yaml
  - name: freeImageInteropNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: histEqualizationNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUBLAS
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUFFT
    call: ${yaml_dir_path}/${name}.yaml
  - name: batchCUBLAS
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientUM
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUFFT_2d_MGPU
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUFFT_MGPU
    call: ${yaml_dir_path}/${name}.yaml
  - name: cuSolverDn_LinearSolver
    call: ${yaml_dir_path}/${name}.yaml
  - name: cuSolverSp_LinearSolver
    call: ${yaml_dir_path}/${name}.yaml
  - name: cuSolverRf
    call: ${yaml_dir_path}/${name}.yaml
  - name: cuSolverSp_LowlevelQR
    call: ${yaml_dir_path}/${name}.yaml
  - name: cuSolverSp_LowlevelCholesky
    call: ${yaml_dir_path}/${name}.yaml
  - name: cannyEdgeDetectorNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: FilterBorderControlNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUBLASXT
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientCudaGraphs
    call: ${yaml_dir_path}/${name}.yaml
  - name: nvJPEG
    call: ${yaml_dir_path}/${name}.yaml
  - name: nvJPEG_encoder
    call: ${yaml_dir_path}/${name}.yaml
  - name: watershedSegmentationNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: batchedLabelMarkersAndLabelCompressionNPP
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUBLAS_LU
    call: ${yaml_dir_path}/${name}.yaml
  - name: jitLto
    call: ${yaml_dir_path}/${name}.yaml
