Settings:
  env: ~
  enabled: True
  print_instance: False

Suites: !!seq
  - name: CUDA_10.1
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_10.2
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.0
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.1
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.2
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.3
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.4
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.5
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.6
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.7
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_11.8
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.0
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.1
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.2
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.3
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.4
    call: ${yaml_dir_path}/${name}/suite.yaml
  - name: CUDA_12.5_and_afterwards
    call: ${yaml_dir_path}/${name}/suite.yaml
