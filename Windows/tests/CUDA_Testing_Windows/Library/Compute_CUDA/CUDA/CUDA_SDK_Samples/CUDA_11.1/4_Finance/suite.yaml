
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: quasirandomGenerator
    call: ${yaml_dir_path}/${name}.yaml
  - name: binomialoptions
    call: ${yaml_dir_path}/${name}.yaml
  - name: Blackscholes
    call: ${yaml_dir_path}/${name}.yaml
  - name: MonteCarloMultiGPU
    call: ${yaml_dir_path}/${name}.yaml
  - name: SobolQRNG
    call: ${yaml_dir_path}/${name}.yaml
  - name: binomialOptions_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: Blackscholes_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: quasirandomGenerator_nvrtc
    call: ${yaml_dir_path}/${name}.yaml