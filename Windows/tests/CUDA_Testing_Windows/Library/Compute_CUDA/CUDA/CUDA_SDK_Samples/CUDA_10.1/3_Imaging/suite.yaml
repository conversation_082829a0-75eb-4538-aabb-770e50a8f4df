
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: boxFilter
    call: ${yaml_dir_path}/${name}.yaml
  - name: convolutionFFT2D
    call: ${yaml_dir_path}/${name}.yaml
  - name: convolutionSeparable
    call: ${yaml_dir_path}/${name}.yaml
  - name: convolutionTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: dct8x8
    call: ${yaml_dir_path}/${name}.yaml
  - name: dwtHaar1D
    call: ${yaml_dir_path}/${name}.yaml
  - name: dxtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: histogram
    call: ${yaml_dir_path}/${name}.yaml
  - name: imageDenoising
    call: ${yaml_dir_path}/${name}.yaml
  - name: postProcessGL
    call: ${yaml_dir_path}/${name}.yaml
  - name: recursiveGaussian
    call: ${yaml_dir_path}/${name}.yaml
  - name: bilateralFilter
    call: ${yaml_dir_path}/${name}.yaml
  - name: bicubicTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: SobelFilter
    call: ${yaml_dir_path}/${name}.yaml
  - name: HSOpticalFlow
    call: ${yaml_dir_path}/${name}.yaml
  - name: stereoDisparity
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUDA2GL
    call: ${yaml_dir_path}/${name}.yaml
  - name: NV12toBGRandResize
    call: ${yaml_dir_path}/${name}.yaml
  - name: vulkanImageCUDA
    call: ${yaml_dir_path}/${name}.yaml