
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: cdpSimplePrint
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpSimpleQuicksort
    call: ${yaml_dir_path}/${name}.yaml
  - name: cudaTensorCoreGemm
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCudaGraphs
    call: ${yaml_dir_path}/${name}.yaml
  - name: immaTensorCoreGemm
    call: ${yaml_dir_path}/${name}.yaml
  - name: memMapIPCDrv
    call: ${yaml_dir_path}/${name}.yaml
  - name: globalToShmemAsyncCopy
    call: ${yaml_dir_path}/${name}.yaml
  - name: dmmaTensorCoreGemm
    call: ${yaml_dir_path}/${name}.yaml
  - name: bf16TensorCoreGemm
    call: ${yaml_dir_path}/${name}.yaml
  - name: tf32TensorCoreGemm
    call: ${yaml_dir_path}/${name}.yaml
  - name: binaryPartitionCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: bindlessTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: ptxjit
    call: ${yaml_dir_path}/${name}.yaml
  - name: newdelete
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpAdvancedQuicksort
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpQuadtree
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpBezierTessellation
    call: ${yaml_dir_path}/${name}.yaml
  - name: warpAggregatedAtomicsCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: jacobiCudaGraphs
    call: ${yaml_dir_path}/${name}.yaml
  - name: cudaCompressibleMemory
    call: ${yaml_dir_path}/${name}.yaml
  - name: graphMemoryNodes
    call: ${yaml_dir_path}/${name}.yaml
  - name: graphMemoryFootprint
    call: ${yaml_dir_path}/${name}.yaml

