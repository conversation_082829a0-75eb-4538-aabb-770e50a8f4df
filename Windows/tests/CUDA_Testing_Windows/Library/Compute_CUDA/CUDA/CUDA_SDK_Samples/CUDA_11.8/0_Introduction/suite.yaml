
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: cppIntegration
    call: ${yaml_dir_path}/${name}.yaml
  - name: matrixMul
    call: ${yaml_dir_path}/${name}.yaml
  - name: matrixMulDrv
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAtomicIntrinsics
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleMultiGPU
    call: ${yaml_dir_path}/${name}.yaml
  - name: simplePitchLinearTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleStreams
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleTemplates
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleTextureDrv
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleVoteIntrinsics
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleZeroCopy
    call: ${yaml_dir_path}/${name}.yaml
  - name: template
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleLayeredTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleP2P
    call: ${yaml_dir_path}/${name}.yaml
  - name: SimpleSurfaceWrite
    call: ${yaml_dir_path}/${name}.yaml
  - name: SimpleMultiCopy
    call: ${yaml_dir_path}/${name}.yaml
  - name: asyncAPI
    call: ${yaml_dir_path}/${name}.yaml
  - name: simplePrintf
    call: ${yaml_dir_path}/${name}.yaml
  - name: vectorAdd
    call: ${yaml_dir_path}/${name}.yaml
  - name: vectorAddDrv
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleMPI
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCubemapTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAssert
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCallback
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleSeparateCompilation
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleIPC
    call: ${yaml_dir_path}/${name}.yaml
  - name: cudaOpenMP
    call: ${yaml_dir_path}/${name}.yaml
  - name: cppOverload
    call: ${yaml_dir_path}/${name}.yaml
  - name: UnifiedMemoryStreams
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleOccupancy
    call: ${yaml_dir_path}/${name}.yaml
  - name: clock_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: matrixMul_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAssert_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAtomicIntrinsics_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleTemplates_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: vectorAdd_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleVoteIntrinsics_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: fp16ScalarProduct
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCooperativeGroups
    call: ${yaml_dir_path}/${name}.yaml
  - name: vectorAddMMAP
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleDrvRuntime
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAttributes
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleAWBarrier
    call: ${yaml_dir_path}/${name}.yaml
  - name: clock
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleTexture3D
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleCUDA2GL
    call: ${yaml_dir_path}/${name}.yaml
  - name: c++11_cuda
    call: ${yaml_dir_path}/${name}.yaml
  - name: concurrentKernels
    call: ${yaml_dir_path}/${name}.yaml
  - name: mergeSort
    call: ${yaml_dir_path}/${name}.yaml
  - name: matrixMulDynlinkJIT
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleHyperQ
    call: ${yaml_dir_path}/${name}.yaml
