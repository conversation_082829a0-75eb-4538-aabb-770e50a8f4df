
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: p2pBandwidthLatencyTest
    call: ${yaml_dir_path}/${name}.yaml
  - name: marchingCubes
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleGL
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D10
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D10Texture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D9Texture
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D9
    call: ${yaml_dir_path}/${name}.yaml
  - name: volumeRender
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D11Texture
    call: ${yaml_dir_path}/${name}.yaml
  - name: Mandelbrot
    call: ${yaml_dir_path}/${name}.yaml
  - name: SLID3D10Texture
    call: ${yaml_dir_path}/${name}.yaml
  - name: volumeFiltering
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D10RenderTarget
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleVulkan
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D12
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleVulkanMMAP
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleD3D11
    call: ${yaml_dir_path}/${name}.yaml
  - name: convolutionFFT2D
    call: ${yaml_dir_path}/${name}.yaml
  - name: dwtHaar1D
    call: ${yaml_dir_path}/${name}.yaml
  - name: dxtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: postProcessGL
    call: ${yaml_dir_path}/${name}.yaml
  - name: recursiveGaussian
    call: ${yaml_dir_path}/${name}.yaml
  - name: bilateralFilter
    call: ${yaml_dir_path}/${name}.yaml
  - name: bicubicTexture
    call: ${yaml_dir_path}/${name}.yaml
  - name: SobelFilter
    call: ${yaml_dir_path}/${name}.yaml
  - name: HSOpticalFlow
    call: ${yaml_dir_path}/${name}.yaml
  - name: stereoDisparity
    call: ${yaml_dir_path}/${name}.yaml
  - name: NV12toBGRandResize
    call: ${yaml_dir_path}/${name}.yaml
  - name: vulkanImageCUDA
    call: ${yaml_dir_path}/${name}.yaml
  - name: quasirandomGenerator
    call: ${yaml_dir_path}/${name}.yaml
  - name: binomialOptions
    call: ${yaml_dir_path}/${name}.yaml
  - name: BlackScholes
    call: ${yaml_dir_path}/${name}.yaml
  - name: MonteCarloMultiGPU
    call: ${yaml_dir_path}/${name}.yaml
  - name: SobolQRNG
    call: ${yaml_dir_path}/${name}.yaml
  - name: binomialOptions_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: BlackScholes_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: quasirandomGenerator_nvrtc
    call: ${yaml_dir_path}/${name}.yaml
  - name: fluidsGL
    call: ${yaml_dir_path}/${name}.yaml
  - name: fluidsD3D9
    call: ${yaml_dir_path}/${name}.yaml
  - name: smokeParticles
    call: ${yaml_dir_path}/${name}.yaml
  - name: nbody
    call: ${yaml_dir_path}/${name}.yaml
  - name: VFlockingD3D10
    call: ${yaml_dir_path}/${name}.yaml
  - name: fastWalshTransform
    call: ${yaml_dir_path}/${name}.yaml
  - name: FDTD3d
    call: ${yaml_dir_path}/${name}.yaml

