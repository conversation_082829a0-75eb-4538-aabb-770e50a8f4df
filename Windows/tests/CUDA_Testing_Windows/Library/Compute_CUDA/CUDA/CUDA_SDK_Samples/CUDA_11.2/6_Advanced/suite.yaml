
Settings:
  env: ~
  enabled: True

Tests: !!seq
  - name: c++11_cuda
    call: ${yaml_dir_path}/${name}.yaml
  - name: matrixMuldynlinkJIT
    call: ${yaml_dir_path}/${name}.yaml
  - name: eigenvalues
    call: ${yaml_dir_path}/${name}.yaml
  - name: FastWalshTransform
    call: ${yaml_dir_path}/${name}.yaml
  - name: LineOfSight
    call: ${yaml_dir_path}/${name}.yaml
  - name: ptxjit
    call: ${yaml_dir_path}/${name}.yaml
  - name: radixSortThrust
    call: ${yaml_dir_path}/${name}.yaml
  - name: reduction
    call: ${yaml_dir_path}/${name}.yaml
  - name: scalarProd
    call: ${yaml_dir_path}/${name}.yaml
  - name: scan
    call: ${yaml_dir_path}/${name}.yaml
  - name: SortingNetworks
    call: ${yaml_dir_path}/${name}.yaml
  - name: threadFenceReduction
    call: ${yaml_dir_path}/${name}.yaml
  - name: threadMigration
    call: ${yaml_dir_path}/${name}.yaml
  - name: transpose
    call: ${yaml_dir_path}/${name}.yaml
  - name: FunctionPointers
    call: ${yaml_dir_path}/${name}.yaml
  - name: ConcurrentKernels
    call: ${yaml_dir_path}/${name}.yaml
  - name: alignedTypes
    call: ${yaml_dir_path}/${name}.yaml
  - name: FDTD3d
    call: ${yaml_dir_path}/${name}.yaml
  - name: Interval
    call: ${yaml_dir_path}/${name}.yaml
  - name: Newdelete
    call: ${yaml_dir_path}/${name}.yaml
  - name: mergeSort
    call: ${yaml_dir_path}/${name}.yaml
  - name: SegmentationTreeThrust
    call: ${yaml_dir_path}/${name}.yaml
  - name: shfl_scan
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpAdvancedQuicksort
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpQuadtree
    call: ${yaml_dir_path}/${name}.yaml
  - name: simpleHyperQ
    call: ${yaml_dir_path}/${name}.yaml
  - name: cdpBezierTessellation
    call: ${yaml_dir_path}/${name}.yaml
  - name: warpAggregatedAtomicsCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: reductionMultiBlockCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientMultiBlockCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: conjugateGradientMultiDeviceCG
    call: ${yaml_dir_path}/${name}.yaml
  - name: jacobiCudaGraphs
    call: ${yaml_dir_path}/${name}.yaml
  - name: cudaCompressibleMemory
    call: ${yaml_dir_path}/${name}.yaml