import re
import os
import logging
import subprocess
from collections import Counter

from windows_test_helper.steps import Step
from windows_test_helper.gpu_info import GPUsInSUT
from windows_test_helper.ui_app_wrapper.device_manager import DeviceManager
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.cuda_samples import CUDASamples
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.gpu_management import GPUManagement
from windows_test_helper.nvapi import get_all_nv_GPU_names


logger = logging.getLogger(__name__)
tk_branch = os.environ['TK_BRANCH']


class TestDriverLoadUnload:

    def test_section_1(self):
        with Step(1, 'Make sure all nvidia GPUs are working'):
            gm = GPUManagement()
            for gpu_device in gm.gpu_devices:
                device_id = gpu_device['device_id']
                gpu_name = gpu_device['name']
                status = gm.get_gpu_status_by_device_id(device_id)
                assert status == 'enabled', 'GPU {}:{} is not enabled: {}'.format(device_id, gpu_name, status)

        with Step(2, 'Make sure all GPUs are detected and enumerate under device manager'):
            device_manager = DeviceManager()
            device_manager.open()
            all_display_adptors = device_manager.cache_adapters_name
            logger.debug('All Display Adaptors in device manager: {}'.format(all_display_adptors))
            nvidia_display_adapters_names = [adapter_name for adapter_id, adapter_name in all_display_adptors.items() if 'NVIDIA' in adapter_name]
            logger.debug('Nvidia Display Adaptors in device manager: {}'.format(nvidia_display_adapters_names))

            gpus = GPUsInSUT().gpus
            gpu_names = [gpu.product_name for gpu in gpus]
            logger.debug('All gpus name in SUT: {}'.format(gpu_names))

            assert Counter(nvidia_display_adapters_names) == Counter(gpu_names), 'GPU listed in Device manager not same with GPU listed in nvidia-smi'

        with Step(3, 'Run Nbody on gpus'):
            cuda_samples = CUDASamples(tk_branch)
            nbody = cuda_samples.get_sample('nbody')
            nbody.build_sample()

            cmd = '{} -benchmark -numbodies=2000000'.format(nbody.get_sample_binary_path())
            logger.debug('Run command: {}'.format(cmd))
            subprocess.Popen(cmd)

        with Step(4, 'Leave the sample running, disable the gpus'):
            gm.disable_all()

        with Step(5, 'enable the gpus'):
            gm.enable_all()

            logger.warning('2482634, WAR for devcon with TCC config: Reboot system now then check device')
            set_reboot_flag()

    def test_section_2(self):
        with Step(1, 'Check target gpus, make sure they enumerates under NV control panel, and that you can query them with devicequery or nvSMI.'):
            nvcpl_gpu_names = get_all_nv_GPU_names()
            logger.debug('Nvidia Display Adaptors in nvcpl/NVAPI: {}'.format(nvcpl_gpu_names))

            gpus = GPUsInSUT().gpus
            gpu_names = [gpu.product_name for gpu in gpus]
            logger.debug('All gpus name in SUT: {}'.format(gpu_names))

            assert Counter(nvcpl_gpu_names) == Counter(gpu_names), 'GPU listed in NVCPL/NVAPI not same with GPU listed in nvidia-smi'

            cuda_samples = CUDASamples(tk_branch)
            devicequery = cuda_samples.get_sample('deviceQuery')
            devicequery.build_sample()

            cmd = devicequery.get_sample_binary_path()
            res = run_loc_cmd(cmd)
            assert res.succeeded, 'Run command {} failed'.format(cmd)

            device_names = []
            for line in res.stdout.split('\n'):
                matched = re.match(r'Device \d: "(.*)"', line)
                if matched is not None:
                    device_names.append(matched.group(1))

            assert Counter(device_names) == Counter(gpu_names), 'GPU listed in devicequery not same with GPU listed in nvidia-smi'

        with Step(2, 'Run nbody again after enable gpu'):
            cuda_samples = CUDASamples(tk_branch)
            nbody = cuda_samples.get_sample('nbody')
            nbody.build_sample()

            cmd = '{} -benchmark -numbodies=1000000'.format(nbody.get_sample_binary_path())
            res = run_loc_cmd(cmd)
            assert res.succeeded, 'Run command {} failed'.format(cmd)
