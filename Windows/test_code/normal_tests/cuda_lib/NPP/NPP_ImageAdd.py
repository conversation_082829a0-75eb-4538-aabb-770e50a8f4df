import os
import time
import filecmp
from windows_test_helper.steps import Step
from windows_test_helper import logger
from windows_test_helper.constants import constants
from windows_test_helper.share_folder import copy_from_share_folder
from windows_test_helper.interactive_shell import InteractiveShell
from windows_test_helper.vs_info import get_vcvarsall
from windows_test_helper.gpu_info import get_gpu_compute_capability
from windows_test_helper.run_cmd import run_loc_cmd


def test_NPP_ImageAdd(filter_GPU_indexes):
    with Step(1, 'Copy source file to local system'):
        share_folder_username = constants['share_folder_username']
        share_folder_password = constants['share_folder_password']
        remote_path = constants['NPP_ImageAdd']['remote_path']
        local_path = constants['NPP_ImageAdd']['local_path']
        copy_from_share_folder(remote_path, local_path, share_folder_username, share_folder_password)

    with Step(2, 'Modify .cu source file'):
        target_file = os.path.join(local_path, '1042673_NppLutTest.cu')
        with open(target_file) as f:
            target_str = f.read()
        src_1 = r'D:\\customerbug\\1042673\\guest.bmp'
        src_2 = r'D:\\customerbug\\1042673\\out.bmp'
        dst_1 = os.path.join(local_path, 'guest.bmp').replace('\\', '\\\\')
        dst_2 = os.path.join(local_path, 'out.bmp').replace('\\', '\\\\')
        new_str = target_str.replace(src_1, dst_1).replace(src_2, dst_2)

        with open(target_file, 'w') as f:
            f.write(new_str)

    with Step(3, 'Build binary test.exe'):
        sm_ver = get_gpu_compute_capability(filter_GPU_indexes[0][0])
        print(sm_ver)
        sm_ver = sm_ver.replace('.', '')
        logger.debug('Get SM : {}'.format(sm_ver))
        ishell = InteractiveShell()
        ishell.open()
        vcvarsall = get_vcvarsall()
        vcvarsall_path = os.path.dirname(vcvarsall)
        vcvarsall_path_escaped = vcvarsall_path.replace('(', '{(}').replace(')', '{)}')
        ishell.send_command('cd {}'.format(vcvarsall_path_escaped))
        ishell.send_command('vcvarsall.bat amd64')
        ishell.send_command('cd {}'.format(local_path))
        ishell.send_command('nvcc -arch=sm_{} -o test.exe 1042673_NppLutTest.cu -lcuda -lnppial -lnppicc -lnppidei -lnppisu'.format(sm_ver))

        exe_path = os.path.join(local_path, 'test.exe')
        timeout = 60
        start = current = time.time()
        while current - start < timeout:
            time.sleep(5)
            if os.path.isfile(exe_path):
                break
            current = time.time()
        assert os.path.isfile(exe_path), 'test.exe does not exist: {}'.format(exe_path)
        logger.info('test.exe has been compiled successsfully')
        logger.debug(ishell.get_text())
        ishell.exit()

    with Step(4, 'Run App and compare result'):
        res = run_loc_cmd(exe_path)
        assert res.succeeded, 'Fail to run command: {}'.format(exe_path)
        assert os.path.exists(os.path.join(local_path, 'out.bmp')), 'NOT Found out.bmp under {}, please check'.format(local_path)
        logger.debug('Compare Result via golden_out.bmp')
        res = filecmp.cmp(os.path.join(local_path, 'out.bmp'), os.path.join(local_path, 'golden_out.bmp'))
        assert res, 'Two iamges has difference, please check if image crashes!'
