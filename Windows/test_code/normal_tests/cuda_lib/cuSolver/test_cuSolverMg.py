import os
import re
from windows_test_helper.steps import Step
from windows_test_helper.dvs import DVSPackage, DVSPackageName
from windows_test_helper.files_ops import copy_cuda_lib_dll
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper import logger
from windows_test_helper.cuda_components import CUDAComponents

drv_branch = 'r{}_00'.format(os.environ['DRV_BRANCH'])
tk_branch = os.getenv('TK_BRANCH')


def test_cuSolverMg():
    """
    template id: 2075486
    """

    with Step(1, 'Get changelist from builds.json and download cuSolver package from DVS'):
        lib_component = CUDAComponents()
        CL = lib_component.get_changelist('libcusolver')
        logger.debug('From builds.json, the CL of libcusolver is {}'.format(CL))
        cuSolver_pkg = DVSPackage(DVSPackageName.cuSolver, drv_branch)
        res = cuSolver_pkg.check_cl_existence(CL)
        if (not res):
            logger.debug('No matching cuSolver package for CL: {}, get TOT DVS package.'.format(CL))
            CL = cuSolver_pkg.get_tot_cl()
        cuSolver_folder = cuSolver_pkg.prepare_local_package(CL)

    with Step(2, 'Copy libraries/dll from local install path to cuSolver DVS package bin path'):
        dst_binPath = os.path.join(cuSolver_folder, 'CUDA-cusolver-package', 'bin', 'x86_64_win32_release')
        logger.debug('copy libraries/dll to {}'.format(dst_binPath))
        copy_cuda_lib_dll(tk_branch, dst_binPath)

    with Step(3, 'Run cucuSolverMg test'):
        cuSolver_test_path = os.path.join(cuSolver_folder, 'CUDA-cusolver-package', 'cuSolver', 'script', 'mg')
        os.chdir(cuSolver_test_path)
        logger.info('Working dir : {}'.format(cuSolver_test_path))
        test_cmd = r'perl.exe cusolverMg_nightly.pl -binpath={} -libpath={}'.format(dst_binPath, dst_binPath)
        res = run_loc_cmd(test_cmd)
        assert res.succeeded, 'Retun code is not 0, please check'
        assert re.search('CUDA DVS BASIC SANITY SCORE: 100.0', res.output), 'The cuSolverMg score is not 100, please check !'
