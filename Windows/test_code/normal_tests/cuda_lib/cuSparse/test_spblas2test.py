import os
import logging
from windows_test_helper.dvs import D<PERSON><PERSON>ack<PERSON>, DVSPackageName
from windows_test_helper.files_ops import copy_cuda_lib_dll
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.cuda_components import CUDAComponents
from windows_test_helper.steps import Step

drv_branch = 'r{}_00'.format(os.environ['DRV_BRANCH'])
cudaVer = os.getenv('TK_BRANCH')
logger = logging.getLogger(__name__)


def test_spblas2test():
    """
    template id: 2200659
    """
    with Step(1, 'Get libcusparse CL'):
        cuda_components = CUDAComponents()
        cusparse_cl = cuda_components.get_changelist('libcusparse')
        logger.debug('libcusparse change list is: {}'.format(cusparse_cl))

    with Step(2, 'Download and extract cusparse dvs package'):
        cusparse_pkg = DVSPackage(DVSPackageName.cuSparse, drv_branch)
        if cusparse_pkg.check_cl_existence(cusparse_cl):
            cusparse_pkg_folder = cusparse_pkg.prepare_local_package(cusparse_cl)
        else:
            logger.debug('No matching dvs cusparse package for CL: {}, use tot'.format(cusparse_cl))
            cusparse_pkg_folder = cusparse_pkg.prepare_local_package()

    with Step(3, 'Copying dlls'):
        dest_dir = cusparse_pkg_folder + r'\CUDA-cusparse-package\bin\x86_64_win32_release'
        copy_cuda_lib_dll(cudaVer, dest_dir)

    with Step(4, 'Run cusparse smoke tests'):
        os.chdir(cusparse_pkg_folder + r'\CUDA-cusparse-package\bin\x86_64_win32_release')
        cmd = 'spblas2test -Pd -Rgemvi'
        res = run_loc_cmd(cmd)
        assert res.succeeded
        assert 'FAIL' not in res.stdout
