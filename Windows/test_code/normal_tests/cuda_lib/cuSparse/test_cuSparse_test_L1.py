import os
import logging
from windows_test_helper.files_ops import copy_cuda_lib_dll
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.constants import constants
from windows_test_helper.dvs import DVSPackage, DVSPackageName
from windows_test_helper.cuda_components import CUDAComponents

logger = logging.getLogger(__name__)
drv_branch = 'r{}_00'.format(os.environ['DRV_BRANCH'])
cudaVer = os.getenv('TK_BRANCH')


def test_cuSparse_test_L1():
    """
    template id: 2200664
    """
    with Step(1, 'Get libcusparse CL'):
        cuda_components = CUDAComponents()
        cusparse_cl = cuda_components.get_changelist('libcusparse')
        logger.debug('libcusparse change list is: {}'.format(cusparse_cl))

    with Step(2, 'Download and extract cusparse dvs package'):
        cusparse_pkg = DVSPackage(DVSPackageName.cuSparse, drv_branch)
        if cusparse_pkg.check_cl_existence(cusparse_cl):
            cusparse_pkg_folder = cusparse_pkg.prepare_local_package(cusparse_cl)
        else:
            logger.debug('No matching dvs cusparse package for CL: {}, use tot'.format(cusparse_cl))
            cusparse_pkg_folder = cusparse_pkg.prepare_local_package()

    with Step(3, 'Copying dlls'):
        dest_dir = cusparse_pkg_folder + r'\CUDA-cusparse-package\bin\x86_64_win32_release'
        copy_cuda_lib_dll(cudaVer, dest_dir)

    with Step(4, 'Run cusparse smoke tests'):
        sparse_test_path = os.path.join(cusparse_pkg_folder, 'CUDA-cusparse-package', 'cusparse')
        os.chdir(sparse_test_path)
        if float(cudaVer) >= 11.1:
            Testsparse_cmd = r'perl .\sparse_nightly.pl -build=release -config=nightly -mkl'
        else:
            matrices_remote_path = constants['matrices']['remote_path']
            cmd = r'net use z: {} {} /user:{}'.format(matrices_remote_path, constants['share_folder_password'], constants['share_folder_username'])
            run_loc_cmd(cmd)
            Testsparse_cmd = r'perl .\sparse_nightly.pl -build=release -config=nightly -mkl -matrix_path="z:\p4matrices"'
        res = run_loc_cmd(Testsparse_cmd)
        assert res.succeeded
        assert '&&&& FAIL' not in res.stdout
