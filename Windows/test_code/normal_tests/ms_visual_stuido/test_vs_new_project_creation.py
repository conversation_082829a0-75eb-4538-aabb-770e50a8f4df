import os
import time
import pytest
from windows_test_helper.steps import Step
from windows_test_helper.vs_info import get_devenv
from windows_test_helper.ui_app_wrapper.vs import VS2017, VS2019, VS2022


@pytest.mark.parametrize('vs_ver, vs_suite',
                         [('2017', 'Community'), ('2019', 'Community'), ('2022', 'Community'),
                          ('2017', 'Enterprise'), ('2019', 'Enterprise'), ('2022', 'Enterprise')])
def test_vs_new_project_creation(vs_ver, vs_suite):
    '''
    template id: 1556630, 1557968, 2021845, 2022666, 2897703, 2897709
    '''

    with Step(1, 'Select VS via version and then start it'):
        # wait some time for winsrv system or sytem with ATP finishing starting up
        time.sleep(180)
        _, _, devenv_path = get_devenv(vs_ver, vs_suite)
        if vs_ver == '2017':
            vs = VS2017(devenv_path)
        if vs_ver == '2019':
            vs = VS2019(devenv_path)
        if vs_ver == '2022':
            vs = VS2022(devenv_path)

        vs.start()

    with Step(2, 'New a project, add a new item and batch build it'):
        tk_branch = os.getenv('TK_BRANCH')
        cuda = 'CUDA {}'.format(tk_branch)
        cuda_runtime = 'CUDA {} Runtime'.format(tk_branch)
        file_name = 'file1'
        proj_name = 'test1'
        proj_location = r'C:\tmp\MSVS'
        if not os.path.exists(proj_location):
            os.mkdir(proj_location)
        vs.new_proj(cuda_runtime, proj_name, proj_location)
        vs.add_new_item(cuda, proj_name, proj_location, file_name)
        vs.batch_build()
        time.sleep(2)
        vs.close()
