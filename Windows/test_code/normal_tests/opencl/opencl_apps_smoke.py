import os

import logging
import winreg
import shutil
import json

from windows_test_helper.dvs import D<PERSON><PERSON><PERSON><PERSON>, DVSPackageName
from windows_test_helper.steps import Step
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.globals import GlobalsInFile
from windows_test_helper.cqa_globals import set_detailed_result

logger = logging.getLogger(__name__)
drv_branch = 'r{}_00'.format(os.environ['DRV_BRANCH'])


class TestOpenCLAppsSmoke:

    def test_disable_intel_opencl(self):
        with Step(1, 'Disable intel opencl if needed'):
            keys = [r'SOFTWARE\Khronos\OpenCL\Vendors', r'SOFTWARE\Wow6432Node\Khronos\OpenCL\Vendors']
            reboot_needed = False

            for key in keys:
                try:
                    opencl_vender_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key)
                except FileNotFoundError:
                    logger.debug('{} not exists, continue')
                    continue
                else:
                    i = 0
                    while True:
                        try:
                            value_name, _, _ = winreg.EnumValue(opencl_vender_key, i)
                        except OSError:
                            break
                        else:
                            if 'intelopencl' in value_name.lower():
                                logger.debug('Delete value name {} under key {}'.format(value_name, key))
                                winreg.DeleteValue(opencl_vender_key, value_name)
                                reboot_needed = True

                            i += 1

            if reboot_needed:
                set_reboot_flag()

    def test_run_opencl_apps_smoke(self):
        with Step(1, 'Download and extract latest cuda_a branch opencl apps testing package from dvs'):
            opencl_package = DVSPackage(DVSPackageName.openCL, drv_branch)
            opencl_folder = opencl_package.prepare_local_package(cl=None)

        cmd = 'perl.exe smoke.pl -config=release'
        with Step(2, 'Run command {} '.format(cmd)):
            test_folder = os.path.join(opencl_folder, 'CUDA-opencl-apps-package', 'opencl', 'apps')
            os.chdir(test_folder)

            summary_log = os.path.join(test_folder, 'summary.txt')
            if os.path.exists(summary_log):
                os.remove(summary_log)
            res = run_loc_cmd(cmd)
            with open(summary_log, 'w') as fw:
                for line in res.output.splitlines():
                    fw.write(line + '\n')

        with Step(3, 'Check opencl apps results'):
            g = GlobalsInFile()
            logging_dir = g.get('logging_dir')

            logger.debug('copy summary.txt to logging dir: {}'.format(logging_dir))
            if os.path.isfile(summary_log):
                shutil.copyfile(summary_log, os.path.join(logging_dir, 'summary.txt'))
            err_msg_json = []
            if not os.path.isfile(summary_log):
                logger.error('No summary.txt found, please check')
                # summary_file_exist = 'FAILED'
            else:
                # for detailed result
                total = 0
                passed = 0
                failed = 0
                waived = 0
                with open(summary_log) as summary_file:
                    lines = summary_file.readlines()

                i = 0
                while i < len(lines):
                    line = lines[i].strip()
                    if line.startswith('&&&& RUNNING'):
                        app_name = line.split()[-1]
                        error_info = ''
                        i += 1
                        while i < len(lines) and not lines[i].strip().startswith('&&&&'):
                            error_info += lines[i].strip() + '\n'
                            i += 1
                        if i < len(lines) and 'FAILED' in lines[i]:
                            err_msg_json.append((app_name, error_info.strip()))
                            failed += 1
                        elif i < len(lines) and 'WAIVED' in lines[i]:
                            waived += 1
                        elif i < len(lines) and 'PASSED' in lines[i]:
                            passed += 1
                    else:
                        i += 1
                total = passed + failed + waived
            # set detailed result
            set_detailed_result(total, passed, failed, waived)

            assert not err_msg_json, json.dumps(err_msg_json)
            assert res.succeeded, 'Run {} failed'.format(cmd)
            assert 'PASSED' in res.output, 'Command outputs does not have PASSED item'
