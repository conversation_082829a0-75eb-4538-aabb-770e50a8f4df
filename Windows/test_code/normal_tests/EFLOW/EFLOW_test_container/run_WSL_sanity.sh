#!/usr/bin/bash

echo Download WSL sanity package to /tmp folder: $1
cd /tmp
wget $1 -O cuda_sanity_package.zip

echo Unzip WSL sanity package
unzip cuda_sanity_package.zip

echo Go to tests folder and run the tests
cd /tmp/tests
# backup some tests not needed for EFLOW
mv cuda_apps_3020 _cuda_apps_3020
mv performance _performance
#mkdir backup
#mv runtime/coredump backup || echo 0
#mv runtime/coredumpapi_global backup || echo 0
#mv runtime/coredumpapi_perctx backup || echo 0
#mv runtime/coredumpapi_sticky backup || echo 0

sh run_all_tests.sh Linux
