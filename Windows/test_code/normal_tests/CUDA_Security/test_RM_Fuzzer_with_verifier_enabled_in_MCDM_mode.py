import os
import json
import pytest
import logging
import platform

from windows_test_helper.dvs import DVS<PERSON>ack<PERSON>, DVSPackageName
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.ui_app_wrapper.driver_verifier import DriverVerifier
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.run_external_binary import run_loc_cmd_as_other_user
from windows_test_helper.constants import constants
from windows_test_helper.share_folder import copy_from_share_folder
from windows_test_helper.steps import Step, init_results, check_results
from windows_test_helper.gpu_info import get_nvsmi


logger = logging.getLogger(__name__)

drv_branch = 'r{}_00'.format(os.environ['DRV_BRANCH'])
drv_cl = os.getenv('DRV_CL')
DRV_BRANCH = os.getenv('DRV_BRANCH')
cuda_version = os.getenv('TK_BRANCH')


def set_driver_mode(str):
    # Download tool "rmeg"
    remote_path = os.path.join(constants['binaries']['remote_path'], 'rmreg.exe')
    local_path = os.path.join(constants['binaries']['local_path'], 'rmreg.exe')
    share_folder_username = constants['share_folder_username']
    share_folder_password = constants['share_folder_password']
    copy_from_share_folder(remote_path, local_path, share_folder_username, share_folder_password)

    cmd_510_MCDM = r'rmreg.exe set PreferMCDMOverTCC 1'
    cmd_510_TCC = r'rmreg.exe set PreferMCDMOverTCC 0'
    cmd_515_MCDM = r'rmreg.exe set AdapterType 4'
    cmd_515_TCC = r'rmreg.exe set AdapterType 2'
    if str == 'MCDM':
        if int(DRV_BRANCH) == 510:
            cmd = cmd_510_MCDM
        else:
            cmd = cmd_515_MCDM
    elif str == 'TCC':
        if int(DRV_BRANCH) == 510:
            cmd = cmd_510_TCC
        else:
            cmd = cmd_515_TCC

    os.chdir(r'C:\tmp\binaries')
    res = run_loc_cmd(cmd)
    assert res.succeeded, 'Failed to set driver mode {} via {}'.format(str, cmd)
    res = run_loc_cmd('rmreg.exe restart')
    assert res.succeeded, 'Driver mode {} failed to take effect via {}'.format(str, cmd)


@pytest.mark.gpu_brand_Tesla
@pytest.mark.CUDA(11.6)
def test_check_OS_and_set_MCDM_mode():
    with Step(1, 'Check OS build version'):
        version = platform.version()
        build_number = version.split('.')[2]
        assert not int(build_number) < 21275, 'Current OS version does not meet the requirement!'

    with Step(2, 'Set driver mode to MCDM'):
        if int(DRV_BRANCH) < 555:
            set_driver_mode('MCDM')
        else:
            logger.info('nvidia-smi supports for MCDM from R555.')
            nvsmi = get_nvsmi()
            cmd = '{} -dm 2'.format(nvsmi)
            res = run_loc_cmd(cmd)
            assert res.succeeded, 'Run command {} failed'.format(cmd)
            if 'Reboot required' in res.stdout:
                set_reboot_flag()


@pytest.mark.driver_model_TCC
def test_enable_driver_verifier():
    """
    template id: 2897726
    """

    with Step(1, 'enable driver verifier with driver "nvlddmkm.sys"'):
        driver_verifier = DriverVerifier()
        driver_verifier.select_driver('nvlddmkm.sys')
        set_reboot_flag()


def test_RM_Fuzzer_in_MCDM_mode(create_normal_test_user):
    """
    template id: 2897726
    """
    with Step(1, 'Run deviceQuery_drv.exe to check driver is under MCDM mode'):
        cuda_apps_pkg = DVSPackage(DVSPackageName.cudaApps, drv_branch)
        cl = cuda_apps_pkg.get_smaller_cl(drv_cl)
        cuda_apps_folder = cuda_apps_pkg.prepare_local_package(cl)
        deviceQuery_bin_path = os.path.join(cuda_apps_folder, 'tests', 'driver', 'deviceQuery_drv')
        os.chdir(deviceQuery_bin_path)
        res = run_loc_cmd('deviceQuery_drv.exe')
        assert res.succeeded, 'Run deviceQuery_drv.exe failed'
        assert 'MCDM' in res.stdout, 'GPU is not in MCDM mode'

    with Step(2, 'verify driver "nvlddmkm.sys" is selected'):
        driver_verifier = DriverVerifier()
        drivers_selected = driver_verifier.drivers_selected()

        assert len(drivers_selected) == 1, 'Should have only one driver selected'
        assert drivers_selected[0] == 'nvlddmkm.sys', '"nvlddmkm.sys" should be selected'

    with Step(3, 'Prepare Fuzzer.exe ......'):
        RM_Fuzzer_package = DVSPackage(name=DVSPackageName.StandaloneRMTest, driver_branch=drv_branch)
        cl = RM_Fuzzer_package.get_smaller_cl(drv_cl)
        RM_Fuzzer_folder = RM_Fuzzer_package.prepare_local_package(cl)

    with Step(4, 'Run Fuzzer.exe with Admin User ......'):
        os.chdir(RM_Fuzzer_folder)
        results = init_results()
        cmds = [
            'fuzzer.exe --seed 123 --count 100 --gtest_shuffle',
            'fuzzer.exe --count 100 --gtest_shuffle --gtest_random_seed=0',
        ]
        for index, cmd in enumerate(cmds):
            with Step('6.{}'.format(index), 'Run command: {}'.format(cmd), results=results, suppress_excp=True):
                res = run_loc_cmd(cmd)
                assert res.succeeded, 'Return code is not 0,run fuzzer with Admin User failed: {}'.format(cmd)
        err_msg_json = check_results(results)
        assert not err_msg_json, json.dumps(err_msg_json)

    with Step(5, 'Run Fuzzer.exe with Standard User ......'):
        normal_username, normal_password = create_normal_test_user
        os.chdir(RM_Fuzzer_folder)
        cmd = 'fuzzer.exe'
        results = init_results()
        all_options = [
            '--seed 123 --count 100 --gtest_shuffle',
            '--count 100 --gtest_shuffle --gtest_random_seed=0',
        ]
        for index, options in enumerate(all_options):
            with Step('7.{}'.format(index), 'Run command: {}'.format(options), results=results, suppress_excp=True):
                res = run_loc_cmd_as_other_user(
                    cmd=cmd,
                    user=normal_username,
                    password=normal_password,
                    options=options
                )
                assert res.succeeded, 'Return code is not 0,run fuzzer with Standard User failed: {}'.format(cmd)
        err_msg_json = check_results(results)
        assert not err_msg_json, json.dumps(err_msg_json)


def test_revert_TCC_mode():
    logger.info('Revert driver mode from MCDM to TCC')
    if int(DRV_BRANCH) < 555:
        set_driver_mode('TCC')
    else:
        nvsmi = get_nvsmi()
        cmd = '{} -dm 1'.format(nvsmi)
        res = run_loc_cmd(cmd)
        assert res.succeeded, 'Run command {} failed'.format(cmd)
        if 'Reboot required' in res.stdout:
            set_reboot_flag()


def test_delete_existing_settings():
    with Step(1, 'Delete driver verifier settings'):
        driver_verifier = DriverVerifier()
        driver_verifier.delete_existing_settings()
        set_reboot_flag()


def test_verify_driver_not_selected():
    with Step(1, 'verify driver "nvlddmkm.sys" is not selected'):
        driver_verifier = DriverVerifier()
        drivers_selected = driver_verifier.drivers_selected()
        assert len(drivers_selected) == 0, 'Should not have any driver selected'
