import os
import logging
import pytest
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.constants import constants
from windows_test_helper.share_folder import copy_from_share_folder
logger = logging.getLogger(__name__)


@pytest.mark.gpu_with_display
def test_cuda_context_test_with_vkCreateDevice():
    """
    template id: 2682061
    """
    with Step(1, 'copy rcca_bug3265219 to local...'):
        share_folder_username = constants['share_folder_username']
        share_folder_password = constants['share_folder_password']
        copy_from_share_folder(r'\\10.23.137.1\CQA_Tools\Windows\rcca_bug3265219', r'c:\tmp\rcca_bug3265219',
                               share_folder_username, share_folder_password)
        cblCtxRepro = r'c:\tmp\rcca_bug3265219\cblCtxRepro.exe'
        assert os.path.isfile(cblCtxRepro), 'Copied files: {} does not exist'.format(cblCtxRepro)
        res = run_loc_cmd(cblCtxRepro)
        assert res.succeeded, 'Run cblCtxRepro.exe fail'
        assert 'Fix is successful' in res.stdout
