import winreg
import logging

from windows_test_helper.ui_app_wrapper.nvcpl import NVCPL
from windows_test_helper.ui_app_wrapper.windows_desktop import WindowsDesktop
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.globals import GlobalsInFile
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.gpu_info import GPUsInSUT, is_dch_driver
from windows_test_helper.na import set_na_flag


logger = logging.getLogger(__name__)


def test_nvcpl_desktop_tab_functions():
    with Step(1, 'Check if DCH driver'):
        if not is_dch_driver():
            logger.warning('Current test is only for DCH driver, skip')
            set_na_flag()

    with Step(2, 'In Desktop menu, check "Enable Developer Settings", "Add Desktop Context Menu", "Show Notification Tray Icon", "Display GPU Activity Icon in Notification Area"'):
        nvcpl = NVCPL()
        nvcpl.open()
        nvcpl.change_desktop_menu_states(
            enable_developer_settings=True,
            add_desktop_context_menu=True,
            show_notification_tray_icon=True,
            display_gpu_activity_icon_in_notification_area=True)

    with Step(3, 'Verifying'):
        logger.debug('Verify Developer option will appear on the left panel in NVCPL')
        all_items_in_left_panel = nvcpl.list_all_tree_items()
        logger.debug(all_items_in_left_panel)
        assert 'Developer' in all_items_in_left_panel

        logger.debug('Verify nvcpl shortcut will appear in right click menu')
        windows_desktop = WindowsDesktop()
        all_items_in_context_menu = windows_desktop.list_items_in_context_menu()
        logger.debug(all_items_in_context_menu)
        assert 'NVIDIA Control Panel' in all_items_in_context_menu

        logger.debug('Verify nvcpl shortcut will appear in Notification tray')
        all_items_in_notification_tray = windows_desktop.list_items_in_notification_tray()
        logger.debug(all_items_in_notification_tray)
        assert 'NVIDIA Settings' in all_items_in_notification_tray

        logger.debug('Verify GPU activity Icon appear in Notification tray')
        gpus = GPUsInSUT().gpus
        gpu_activity_icon_exist = False
        for gpu in gpus:
            gpu_name = gpu.product_name
            for item_name in all_items_in_notification_tray:
                if gpu_name in item_name:
                    gpu_activity_icon_exist = True
                    break
        assert gpu_activity_icon_exist, 'GPU activity Icon does not appear in Notification tray'

    with Step(4, 'In Desktop menu, uncheck "Enable Developer Settings", "Add Desktop Context Menu", "Show Notification Tray Icon", "Display GPU Activity Icon in Notification Area"'):
        nvcpl.change_desktop_menu_states(
            enable_developer_settings=False,
            add_desktop_context_menu=False,
            show_notification_tray_icon=False,
            display_gpu_activity_icon_in_notification_area=False)

    with Step(5, 'Verifying'):
        logger.debug('Verify Developer option does not appear on the left panel in NVCPL')
        all_items_in_left_panel = nvcpl.list_all_tree_items()
        logger.debug(all_items_in_left_panel)
        assert 'Developer' not in all_items_in_left_panel

        logger.debug('Verify nvcpl shortcut does not appear in right click menu')
        windows_desktop = WindowsDesktop()
        all_items_in_context_menu = windows_desktop.list_items_in_context_menu()
        logger.debug(all_items_in_context_menu)
        assert 'NVIDIA Control Panel' not in all_items_in_context_menu

        logger.debug('Verify nvcpl shortcut does not appear in Notification tray')
        all_items_in_notification_tray = windows_desktop.list_items_in_notification_tray()
        logger.debug(all_items_in_notification_tray)
        assert 'NVIDIA Settings' not in all_items_in_notification_tray

        logger.debug('Verify GPU activity Icon does not appear in Notification tray')
        gpus = GPUsInSUT().gpus
        gpu_activity_icon_exist = False
        for gpu in gpus:
            gpu_name = gpu.product_name
            for item_name in all_items_in_notification_tray:
                if gpu_name in item_name:
                    gpu_activity_icon_exist = True
                    break
        assert not gpu_activity_icon_exist, 'GPU activity Icon appears in Notification tray'
        nvcpl.close()


def test_cache_current_admin_credentials_and_login_with_normal_user(create_normal_test_user):
    win_logon_key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, r'SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon')

    try:
        default_username = winreg.QueryValueEx(win_logon_key, 'DefaultUserName')[0]
    except FileNotFoundError:
        raise AssertionError('Registry value "DefaultUserName" not exists')

    try:
        default_password = winreg.QueryValueEx(win_logon_key, 'DefaultPassword')[0]
    except FileNotFoundError:
        raise AssertionError('Registry value "DefaultPassword" not exists')

    logger.debug('check if default_username is admin')
    res = run_loc_cmd('net user {}'.format(default_username))
    if 'Local Group Memberships      *Administrators' not in res.output:
        raise AssertionError(r'Current DefaultUserName: {} in registery SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon is not admin'.format(default_username))

    logger.debug('store default_username: {} to global file'.format(default_username))
    g = GlobalsInFile()
    g.update(DefaultUserName=default_username, DefaultPassword=default_password)

    normal_user_username, normal_user_password = create_normal_test_user
    logger.debug('update registery with normal user: {}'.format(normal_user_username))
    winreg.SetValueEx(win_logon_key, 'DefaultUserName', 0, winreg.REG_SZ, normal_user_username)
    winreg.SetValueEx(win_logon_key, 'DefaultPassword', 0, winreg.REG_SZ, normal_user_password)

    set_reboot_flag()


def test_restore_cached_admin_user():
    g = GlobalsInFile()
    default_username = g.get('DefaultUserName')
    default_password = g.get('DefaultPassword')
    logger.debug('get default_username: {} from global file'.format(default_username))

    logger.debug('update registery with cached admin user: {}'.format(default_username))
    win_logon_key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, r'SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon')
    winreg.SetValueEx(win_logon_key, 'DefaultUserName', 0, winreg.REG_SZ, default_username)
    winreg.SetValueEx(win_logon_key, 'DefaultPassword', 0, winreg.REG_SZ, default_password)

    set_reboot_flag()
