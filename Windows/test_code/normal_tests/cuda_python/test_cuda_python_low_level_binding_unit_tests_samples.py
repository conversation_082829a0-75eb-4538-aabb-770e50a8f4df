import os
import stat
import shutil
import winreg
import re
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.web_access import download_file
from windows_test_helper.cqa_globals import set_detailed_result
from windows_test_helper import logger


def test_cuda_python_low_level_binding_unit_tests_samples(customized_configs):
    """
    template id : unit_tests 2736425, samples 2741268
    """
    repo = customized_configs['git_repo']
    assert repo is not None, 'Please specify git repo'
    branch = customized_configs['git_branch']
    assert branch is not None, 'Please specify git branch'
    env_name = customized_configs['env_name']
    assert env_name is not None, 'Please specify env name'
    python_versions = customized_configs['python_versions']
    assert python_versions is not None, 'Please specify python versions'
    python_download_links = customized_configs['python_download_links']
    assert python_download_links is not None, 'Please specify python download links'
    token = os.environ[env_name]
    gpu_brand = os.getenv('PRODUCT_BRAND')

    with Step(1, 'Install python.'):
        min_supported_python = [x for x in python_versions.split(',')][0].strip()
        max_supported_python = [x for x in python_versions.split(',')][-1].strip()
        min_supported_python_download_link = [x for x in python_download_links.split(',')][0].strip()
        max_supported_python_download_link = [x for x in python_download_links.split(',')][-1].strip()
        assert min_supported_python in min_supported_python_download_link, 'Please set correct python version and download link'
        assert max_supported_python in max_supported_python_download_link, 'Please set correct python version and download link'

        if gpu_brand == 'Tesla':
            testing_python = max_supported_python
            python_link = max_supported_python_download_link
        else:
            testing_python = min_supported_python
            python_link = min_supported_python_download_link

        # Check if the testing python was installed. If not, install it
        if not get_python_installation_path(testing_python):
            logger.info('Install testing python: {} using {}'.format(testing_python, python_link))
            download_file(python_link, r'c:\tmp')
            local_python = os.path.join(r'c:\tmp', os.path.basename(python_link))
            assert os.path.exists(local_python), 'Python installer does not exist.'
            cmd = r'{} /quiet InstallAllUsers=1 TargetDir="C:\Python{}"'.format(local_python, testing_python.replace('.', ''))
            res = run_loc_cmd(cmd)
            assert res.succeeded, 'Install python failed.'
            python_path = r'C:\python{}'.format(testing_python.replace('.', ''))
        else:
            python_path = get_python_installation_path(testing_python).rstrip('\\')
            logger.info('Testing python {} already installed in {}'.format(testing_python, python_path))

    with Step(2, 'Use git bash to clone cuda-python repo.'):
        if os.path.exists(r'C:\tmp\cuda-python-public'):
            shutil.rmtree(r'C:\tmp\cuda-python-public', onerror=readonly_handler)
            if os.path.exists(r'C:\tmp\cuda-python-public'):
                shutil.rmtree(r'C:\tmp\cuda-python-public')
        git_exe = r'"C:\Program Files\Git\cmd\git.exe"'
        assert not os.path.isfile(git_exe), 'git bash not exists: {}'.format(git_exe)
        url = repo.format(token)
        cmd = r'{} clone {} -b {} "C:\tmp\cuda-python-public"'.format(git_exe, url, branch)
        res = run_loc_cmd(cmd)
        assert res.succeeded, 'Clone repo failed.'

    with Step(3, 'Install dependencies of the CUDA-Python bindings.'):
        dependencies = ['pywin32', 'pycparser', 'autopxd2']
        for i in dependencies:
            cmd = r'{}\Scripts\pip.exe install '.format(python_path) + i
            res = run_loc_cmd(cmd)
            assert res.succeeded, '{} installation failed.'.format(i)
        os.chdir(r'C:\tmp\cuda-python-public\cuda_bindings')
        cmd = r'{}\Scripts\pip.exe install -r requirements.txt'.format(python_path)
        res = run_loc_cmd(cmd)
        assert res.succeeded, 'Installing requirements failed.'

    with Step(4, 'Build bindings.'):
        cuda_version = os.getenv('TK_BRANCH')
        cmd = r'{}\Scripts\pip.exe install -e .'.format(python_path)
        if cuda_version < '12.9':
            res = run_loc_cmd(cmd)
        # Per https://nvbugspro.nvidia.com/bug/5137800?commentNumber=2,
        # Need LIB env before building bindings
        else:
            lib_env = os.path.join(os.getenv('CUDA_PATH'), 'lib', 'x64')
            res = run_loc_cmd(cmd, user_env={'LIB': lib_env})
        assert res.succeeded and 'failed' not in res.output

    with Step(5, 'Run tests.'):
        devtest_id = os.environ.get('TEMPLATE_ID')
        if devtest_id == '2736425':
            # Run Python unit-tests
            cmd = r'{}\python.exe -m pytest tests\\'.format(python_path)
            res1 = run_loc_cmd(cmd, user_env={'CUDA_HOME': os.getenv('CUDA_PATH')})

            if os.getenv('TK_BRANCH') >= '11.6':
                modified_cmd = r'set CUDA_PYTHON_CUDA_PER_THREAD_DEFAULT_STREAM=1 && {}\python.exe -m pytest tests\ && set CUDA_PYTHON_CUDA_PER_THREAD_DEFAULT_STREAM='.format(python_path)
                res2 = run_loc_cmd(modified_cmd, user_env={'CUDA_HOME': os.getenv('CUDA_PATH')}, shell=True)

            # Remove python related path from system path temporarily
            path = os.getenv('PATH')
            new_path_list = []
            for p in path.split(':'):
                if 'python' not in p.lower():
                    new_path_list.append(p)
            new_path = ':'.join(new_path_list)
            os.environ['PATH'] = new_path

            # Build Cython unit-tests
            cmd = r'tests\cython\build_tests.bat'
            res = run_loc_cmd(cmd, user_env={'CUDA_HOME': os.getenv('CUDA_PATH'), 'PATH': r'{0};{0}\Scripts'.format(python_path)})
            assert res.succeeded and 'failed' not in res.output and 'error' not in res.output

            # Run Cython unit-tests
            cmd = r'{}\python.exe -m pytest tests\cython\\'.format(python_path)
            res3 = run_loc_cmd(cmd, user_env={'CUDA_HOME': os.getenv('CUDA_PATH')})

        if devtest_id == '2741268':
            cmd = r'{}\python.exe -m pytest examples'.format(python_path)
            res = run_loc_cmd(cmd, user_env={'CUDA_HOME': os.getenv('CUDA_PATH')})

        # Extract detailed result
        total = 0
        passed = 0
        failed = 0
        skipped = 0
        if devtest_id == '2736425':
            logger.info('Extract CUDA python detailed result')
            for i in range(1, 4):
                res = locals()[f'res{i}']
                match_collected = re.search(r'collected (\d+) items', res.output)
                if match_collected:
                    total += int(match_collected.group(1))
                match_passed = re.search(r'(\d+) passed', res.output)
                if match_passed:
                    passed += int(match_passed.group(1))
                match_failed = re.search(r'(\d+) failed', res.output)
                if match_failed:
                    failed += int(match_failed.group(1))
                match_skipped = re.search(r'(\d+) skipped', res.output)
                if match_skipped:
                    skipped += int(match_skipped.group(1))

            logger.info('Detailed result: total {} items, {} passed, {} failed, {} skipped'.format(total, passed, failed, skipped))
            # Save detailed result
            set_detailed_result(total, passed, failed, skipped)
            assert res1.succeeded and res2.succeeded and res3.succeeded and not failed

        if devtest_id == '2741268':
            logger.info('Extract CUDA python detailed result')
            match_collected = re.search(r'collected (\d+) items', res.output)
            if match_collected:
                total = int(match_collected.group(1))
            match_passed = re.search(r'(\d+) passed', res.output)
            if match_passed:
                passed = int(match_passed.group(1))
            match_failed = re.search(r'(\d+) failed', res.output)
            if match_failed:
                failed = int(match_failed.group(1))
            match_skipped = re.search(r'(\d+) skipped', res.output)
            if match_skipped:
                skipped = int(match_skipped.group(1))

            logger.info('Detailed result: total {} items, {} passed, {} failed, {} skipped'.format(total, passed, failed, skipped))
            # Save detailed result
            set_detailed_result(total, passed, failed, skipped)
            assert res.succeeded and 'failed' not in res.output


def readonly_handler(func, path, execinfo):
    os.chmod(path, stat.S_IWUSR)


def get_python_installation_path(version):
    try:
        reg_path = fr'SOFTWARE\Python\PythonCore\{version}\InstallPath'
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
            install_path = winreg.QueryValue(key, '')
            winreg.CloseKey(key)
            return install_path
        except OSError:
            pass

    except OSError:
        pass

    return None
