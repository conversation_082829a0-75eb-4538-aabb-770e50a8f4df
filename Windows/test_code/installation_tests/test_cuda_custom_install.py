import os
import re
import logging
import time
import shutil
import subprocess
import pyperclip
import pytest

from windows_test_helper.steps import Step, UIStep
from windows_test_helper.ui_app_wrapper.control_panel_programs import Control_Panel_Programs
from windows_test_helper.ui_app_wrapper.cuda_installer import find_button_by_name_and_invoke
from windows_test_helper.cuda_samples import CUDASamples, gen_vs_project_file
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.share_folder import copy_from_share_folder
from windows_test_helper.constants import constants
from windows_ui_automation.uia import uia_wrapper, iuia_object, find_first, find_all
from windows_ui_automation.mouse import click
from windows_ui_automation.uia import get_clickable_point
from windows_test_helper.reboot import set_reboot_flag
from windows_ui_automation.keyboard import send_keys
from windows_test_helper.globals import GlobalsInFile


logger = logging.getLogger(__name__)
cuda_version = os.getenv('TK_BRANCH')
driver_version = os.getenv('DRV_VER')
gpu_brand = os.getenv('PRODUCT_BRAND')
cp_programs = Control_Panel_Programs()
doc_location = r'C:\cuda_custom_installation\cuda_doc'
sample_location = r'C:\cuda_custom_installation\samples'
dev_location = r'C:\cuda_custom_installation\cuda_dev'
cuda_location = r'C:\cuda_custom_installation\cuda_toolkit'
folders_files_in_dev_location = ['bin', 'compute-sanitizer', 'extras', 'include', 'lib', 'libnvvp', 'nvml', 'nvvm', 'src', 'version.json']
folders_files_in_doc_location = ['tools', 'CUDA_Toolkit_Release_Notes.txt', 'DOCS', 'EULA.txt', 'README']
folders_files_in_sample_location = ['README_CUDA_Samples.txt']


def find_component(install_window, component):
    c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'tree item')
    c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, '{}'.format(component))
    c = iuia_object.CreateAndCondition(c1, c2)
    return find_first(install_window, uia_wrapper.TreeScope_Descendants, c, timeout=180)


@pytest.mark.usefixtures('prepare_cuda_library_samples_repo')
def test_custom_install(customized_configs):
    """
    template id: 1094623
    """
    testing_cuda = customized_configs['cuda_package_path']
    local_cuda_pkg = os.path.join(r'c:\tmp\cuda', os.path.basename(testing_cuda))
    dev_index = 1
    if os.path.exists(r'C:\cuda_custom_installation'):
        shutil.rmtree(r'C:\cuda_custom_installation')
    if cuda_version < '12.3':
        os.makedirs(doc_location)
        if cuda_version < '11.7':
            os.mkdir(sample_location)
        os.mkdir(dev_location)
    else:
        os.makedirs(cuda_location)

    with Step(1, 'Copy cuda package to local'):
        src = r'\\10.23.137.1\Automation\CUDA_Windows\Template_NVIDIA software license agreement.txt'
        dst = r'C:\tmp\Template_NVIDIA software license agreement.txt'
        share_folder_username = constants['share_folder_username']
        share_folder_password = constants['share_folder_password']
        copy_from_share_folder(src, dst, share_folder_username, share_folder_password)
        copy_from_share_folder(testing_cuda, local_cuda_pkg, share_folder_username, share_folder_password)

    with UIStep(2, 'Launch cuda package: {}'.format(local_cuda_pkg), screenshot_label='2_launch_cuda_package'):
        p = subprocess.Popen(local_cuda_pkg)
        desktop = iuia_object.GetRootElement()
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_ProcessIdPropertyId, p.pid)
        c = iuia_object.CreateAndCondition(c1, c2)
        installer_dialog = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)
        # invoke OK button
        find_button_by_name_and_invoke('OK', installer_dialog)
        # wait until installer window show up
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer')
        c = iuia_object.CreateAndCondition(c1, c2)
        install_window = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)

    with UIStep(3, 'Wait until nvidia software license agreement showed', screenshot_label='3_nvidia_software_license_agreement'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA software license agreement')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(install_window, uia_wrapper.TreeScope_Descendants, c, timeout=180)

    with UIStep(4, 'Read NVIDIA software license agreement'):
        c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_AutomationIdPropertyId, '1016')
        agreement = find_first(install_window, uia_wrapper.TreeScope_Children, c)
        agreement.SetFocus()
        send_keys('^A')
        time.sleep(2)
        send_keys('^C')
        time.sleep(2)

    with Step(4.1, 'Save agreenment content to log file and compare it with template'):
        g = GlobalsInFile()
        logging_dir = g.get('logging_dir')
        log_file = os.path.join(logging_dir, 'NVIDIA software license agreement.txt')
        content = pyperclip.paste()
        content = content.replace('\r\n', '\n')
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(content)
        with open(log_file, encoding='utf-8') as f:
            content = f.read()
        with open(dst, encoding='utf-8') as f:
            template = f.read()
        assert content == template, 'Agreement content is not same with template, please check.'

    with UIStep(5, 'Choose custom install', screenshot_label='5_custom_install'):
        # invoke agree and continue
        find_button_by_name_and_invoke('AGREE AND CONTINUE', install_window)
        time.sleep(1)
        # invoke custom button
        find_button_by_name_and_invoke('Custom (Advanced)', install_window)
        time.sleep(1)
        find_button_by_name_and_invoke('NEXT', install_window)
        time.sleep(1)

    with UIStep(6, 'Uncheck CUDA box', screenshot_label='6_uncheck_cuda_box'):
        cuda_item = find_component(install_window, 'CUDA')
        click(coords=get_clickable_point(cuda_item))
        time.sleep(5)

    with UIStep(7, 'Expand CUDA tree then verify all options are unchecked', screenshot_label='7_verify_cuda_compoments_unchecked'):
        click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
        time.sleep(1)
        c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'tree item')
        all_items = find_all(install_window, uia_wrapper.TreeScope_Descendants, c)
        all_components = []
        all_components_with_version = []
        for item in all_items:
            all_components.append(re.findall(r'[a-zA-Z\s]+', item.CurrentName)[0])
            all_components_with_version.append(item.CurrentName)
        logger.info('All actual components are as follows:\n{}'.format(all_components_with_version))
        expected_all_components = ['CUDA', 'Runtime', 'Demo Suite', 'Libraries', 'NVJPEG', 'CCCL', 'CUSPARSE', 'CUBLAS', 'CUDART', 'NPP', 'CUFFT', 'CURAND',
                                   'CUSOLVER', 'NVRTC', 'Development', 'Compiler', 'nvcc', 'Libraries', 'NPP', 'NVJPEG', 'NVML', 'NVTX', 'CURAND', 'CUSOLVER',
                                   'NVRTC', 'CUSPARSE', 'CUBLAS', 'CUFFT', 'cuxxfilt', 'nvprune', 'cuobjdump', 'Tools', 'Visual Profiler', 'Disassembler',
                                   'Profiling Tools', 'Compute Sanitizer', 'CUPTI', 'Visual Studio Integration', 'Nsight Systems', 'Nsight Compute',
                                   'Documentation', 'General Documentation', 'Occupancy Calculator', 'Nsight VSE', 'Other components', 'RTX Desktop Manager',
                                   'PhysX', 'Driver components', 'Display Driver']
        if cuda_version < '11.7':
            expected_all_components.append('Samples', 'General Samples')
        # Per https://jirasw.nvidia.com/browse/CTK-3929， CUDA Nsight NVTX was removed from CUDA 12.0
        # Per https://jirasw.nvidia.com/browse/CTK-3085 and 3751968, Development/Tools/MEMCHECK was removed from CUDA 12.0
        if cuda_version < '12.0':
            expected_all_components.extend(['MEMCHECK', 'Nsight NVTX'])
        # Per bug 3563027 and 3567351, add Development/Compiler/Libraries/Profiling API from CUDA11.8
        if cuda_version > '11.7':
            expected_all_components.append('Profiling API')
        logger.info('All expected components are as follows:\n{}'.format(expected_all_components))
        # Per CUDAINST-786, CUDAINST-820 and CUDAINST-1439, add Runtime/Libraries/OPENCL, Runtime/Libraries/NVJITLINK
        # and Development/Compiler/NVVM Samples from CUDA 12.0
        if cuda_version >= '12.0':
            expected_all_components.extend(['OPENCL', 'NVJITLINK'])
        if cuda_version in ['12.0', '12.1']:
            expected_all_components.append('NVVM Samples')
        # Per CUDAINST-2528 add Runtime/Libraries/NVFATBIN from CUDA 12.4
        if cuda_version >= '12.4':
            expected_all_components.append('NVFATBIN')

        new_components = []
        missing_components = []
        cnt_all = len(all_components)
        for i in range(cnt_all):
            if all_components[i] not in expected_all_components:
                new_components.append(all_components[i])
        for i in range(len(expected_all_components)):
            if expected_all_components[i] not in all_components:
                missing_components.append(expected_all_components[i])
        assert not len(new_components), 'components: {} may be newly added or get name changed, please check.'.format(new_components)
        assert not len(missing_components), 'components: {} may be removed, please check.'.format(missing_components)

        all_cuda = []
        for i in range(cnt_all):
            all_cuda.append(all_components_with_version[i])
            if all_components_with_version[i] == 'Nsight Compute|{}|'.format(cuda_version):
                break
        cnt_cuda = len(all_cuda)
        for i in range(1, cnt_cuda):
            select_pattern = all_items[i].GetCurrentPattern(uia_wrapper.UIA_SelectionItemPatternId).QueryInterface(uia_wrapper.IUIAutomationSelectionItemPattern)
            assert select_pattern.CurrentIsSelected == 0, 'CUDA is unchecked, but its component "{}" still got checked, please check.'.format(all_components[i + 1])
            version = re.findall(r'\d+.\d+', all_components_with_version[i])
            if version:
                assert version[0] == cuda_version, 'Component version is not correct, please check.'
        time.sleep(2)

    with UIStep(8, 'Uncheck other components', screenshot_label='8_uncheck_other_components'):
        # collapse cuda component
        click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
        time.sleep(1)

        other_component_item = find_component(install_window, 'Other components')
        click(coords=get_clickable_point(other_component_item))
        time.sleep(1)

        driver_component_item = find_component(install_window, 'Driver components')
        click(coords=get_clickable_point(driver_component_item))
        time.sleep(1)

        if gpu_brand in ['Titan', 'GeForce', 'Quadro']:
            geforce_component_item = find_component(install_window, 'NVIDIA GeForce Experience components')
            click(coords=get_clickable_point(geforce_component_item))
            time.sleep(1)

    # Per 4193236, from CUDA 12.3, there's only one installation location.
    # So from CUDA 12.3, it needn't check location for sub-components "CUDA Development", "CUDA Documentation", "CUDA Runtime"
    if cuda_version < '12.3':
        logger.info('Per bug 4193236, before CUDA 12.2.1, there are different installation locations for CUDA Development, Documentation.\
                     So step 9 - 16 are location checks for CUDA before 12.2.1')
        with UIStep(9, 'Select Runtime to install only', screenshot_label='9_select_runtime_install_only'):
            # expand cuda component
            cuda_item = find_component(install_window, 'CUDA')
            click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
            time.sleep(1)

            runtime_item = find_component(install_window, 'Runtime')
            click(coords=get_clickable_point(runtime_item))
            time.sleep(1)

            find_button_by_name_and_invoke('NEXT', install_window)
            time.sleep(1)

        with UIStep(10, 'Check "Select installation location" screen only show install location for CUDA Development', screenshot_label='10_only_cuda_development_location_showed'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            assert len(all_list_items) == 1, 'Not only location for CUDA Development, please check.'
            time.sleep(1)

            find_button_by_name_and_invoke('BACK', install_window)
            time.sleep(1)

        with UIStep(11, 'Select Development to install only', screenshot_label='11_select_development_install_only'):
            # expand cuda component
            cuda_item = find_component(install_window, 'CUDA')
            click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
            time.sleep(1)

            # uncheck runtime
            runtime_item = find_component(install_window, 'Runtime')
            click(coords=get_clickable_point(runtime_item))
            time.sleep(1)

            # check development
            development_item = find_component(install_window, 'Development')
            click(coords=get_clickable_point(development_item))
            time.sleep(1)

            find_button_by_name_and_invoke('NEXT', install_window)
            time.sleep(1)

        with UIStep(12, '"Select installation location" screen only show install location for CUDA Development', screenshot_label='12_only_cuda_development_location_showed'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            assert len(all_list_items) == 1, 'Not only location for CUDA Development, please check.'

            find_button_by_name_and_invoke('BACK', install_window)
            time.sleep(1)

        if cuda_version < '11.7':
            with UIStep(13, 'Select Samples to install only', screenshot_label='13_select_samples_install_only'):
                # expand cuda component
                cuda_item = find_component(install_window, 'CUDA')
                click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
                time.sleep(1)

                # uncheck development
                runtime_item = find_component(install_window, 'Development')
                click(coords=get_clickable_point(runtime_item))
                time.sleep(1)

                # check samples
                samples_item = find_component(install_window, 'Samples')
                click(coords=get_clickable_point(samples_item))
                time.sleep(1)

                find_button_by_name_and_invoke('NEXT', install_window)
                time.sleep(1)

            with UIStep(14, '"Select installation location" screen only show install location for CUDA Samples', screenshot_label='14_only_cuda_samples_location_showed'):
                c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
                list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
                c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
                all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
                assert len(all_list_items) == 1, 'Not only location for CUDA Samples, please check.'

                find_button_by_name_and_invoke('BACK', install_window)
                time.sleep(1)

        with UIStep(15, 'Select Documentation to install only', screenshot_label='15_select_documentation_install_only'):
            # expand cuda component
            cuda_item = find_component(install_window, 'CUDA')
            click(coords=(get_clickable_point(cuda_item)[0] - 47, get_clickable_point(cuda_item)[1]))
            time.sleep(1)

            if cuda_version < '11.7':
                # uncheck samples
                runtime_item = find_component(install_window, 'Samples')
                click(coords=get_clickable_point(runtime_item))
                time.sleep(1)
            else:
                # uncheck development
                runtime_item = find_component(install_window, 'Development')
                click(coords=get_clickable_point(runtime_item))
                time.sleep(1)

            # check documentation
            documentation_item = find_component(install_window, 'Documentation')
            click(coords=get_clickable_point(documentation_item))
            time.sleep(1)

            find_button_by_name_and_invoke('NEXT', install_window)
            time.sleep(1)

        with UIStep(16, '"Select installation location" screen only show install location for CUDA Documentation', screenshot_label='16_only_cuda_documentation_location_showed'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            assert len(all_list_items) == 1, 'Not only location for CUDA Development, please check.'

            find_button_by_name_and_invoke('BACK', install_window)
            time.sleep(1)

            next_step = 17
    else:
        next_step = 9

    with UIStep(int('{}'.format(next_step)), 'Select all components', screenshot_label='select_all_components'):
        driver_component_item = find_component(install_window, 'Driver components')
        click(coords=get_clickable_point(driver_component_item))
        time.sleep(1)

        other_component_item = find_component(install_window, 'Other components')
        click(coords=get_clickable_point(other_component_item))
        time.sleep(1)

        cuda_item = find_component(install_window, 'CUDA')
        click(coords=(get_clickable_point(cuda_item)))
        time.sleep(1)

        if gpu_brand in ['Titan', 'GeForce', 'Quadro']:
            geforce_component_item = find_component(install_window, 'NVIDIA GeForce Experience components')
            click(coords=get_clickable_point(geforce_component_item))
            time.sleep(1)

        # invoke next button
        find_button_by_name_and_invoke('NEXT', install_window)

    if cuda_version < '12.3':
        with UIStep(18, 'Install cuda documentation to custom location', screenshot_label='18_install_cuda_documentation_to_custom_location'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            if cuda_version < '12.0':
                click(coords=(get_clickable_point(all_list_items[0])[0] + 129, get_clickable_point(all_list_items[0])[1] - 9))
            else:
                click(coords=(get_clickable_point(all_list_items[1])[0] + 129, get_clickable_point(all_list_items[1])[1] - 9))
            time.sleep(1)

            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'edit')
            location_edit = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)

            value_pattern = location_edit.GetCurrentPattern(uia_wrapper.UIA_ValuePatternId).QueryInterface(uia_wrapper.IUIAutomationValuePattern)
            value_pattern.SetValue(doc_location)
            time.sleep(5)

            find_button_by_name_and_invoke('OK', install_window)
            time.sleep(5)

        with UIStep(19, 'Install cuda samples to custom location', screenshot_label='19_install_cuda_samples_to_custom_location'):
            if cuda_version < '11.7':
                c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
                list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
                c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
                all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
                click(coords=(get_clickable_point(all_list_items[1])[0] + 129, get_clickable_point(all_list_items[1])[1] - 9))
                time.sleep(5)

                c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'edit')
                location_edit = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)

                value_pattern = location_edit.GetCurrentPattern(uia_wrapper.UIA_ValuePatternId).QueryInterface(uia_wrapper.IUIAutomationValuePattern)
                value_pattern.SetValue(sample_location)
                time.sleep(5)

                find_button_by_name_and_invoke('OK', install_window)
                dev_index = 2

        with UIStep(20, 'Install cuda development to custom location', screenshot_label='21_install_cuda_development_to_custom_location'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            if cuda_version < '12.0':
                click(coords=(get_clickable_point(all_list_items[dev_index])[0] + 129, get_clickable_point(all_list_items[dev_index])[1] - 9))
            else:
                click(coords=(get_clickable_point(all_list_items[0])[0] + 129, get_clickable_point(all_list_items[0])[1] - 9))
            time.sleep(5)

            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'edit')
            location_edit = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)

            value_pattern = location_edit.GetCurrentPattern(uia_wrapper.UIA_ValuePatternId).QueryInterface(uia_wrapper.IUIAutomationValuePattern)
            value_pattern.SetValue(dev_location)
            time.sleep(5)

            find_button_by_name_and_invoke('OK', install_window)
            time.sleep(1)

            # invoke next button
            find_button_by_name_and_invoke('NEXT', install_window)

            next_step = 21
    else:
        logger.info("Per bug 4193236, there's only one installation location.")
        with UIStep(10, 'Install cuda to custom location', screenshot_label='10_install_cuda_to_custom_location'):
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            list = find_first(install_window, uia_wrapper.TreeScope_Children, c)
            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_list_items = find_all(list, uia_wrapper.TreeScope_Children, c)
            click(coords=(get_clickable_point(all_list_items[0])[0] + 129, get_clickable_point(all_list_items[0])[1] - 9))
            time.sleep(1)

            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'edit')
            location_edit = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)

            value_pattern = location_edit.GetCurrentPattern(uia_wrapper.UIA_ValuePatternId).QueryInterface(uia_wrapper.IUIAutomationValuePattern)
            value_pattern.SetValue(cuda_location)
            time.sleep(5)

            find_button_by_name_and_invoke('OK', install_window)
            time.sleep(5)

            # invoke next button
            find_button_by_name_and_invoke('NEXT', install_window)

            next_step = 11

    with UIStep(int('{}'.format(next_step)), 'Install - finish', screenshot_label='install_finish'):
        # check install finished
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Nsight Visual Studio Edition Summary')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(install_window, uia_wrapper.TreeScope_Children, c, timeout=72000)

        # get nsight summary
        nsight_summary = ''
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'pane')
        panes = find_all(install_window, uia_wrapper.TreeScope_Descendants, c1)
        for pane in panes:
            if re.search('Installed:', pane.CurrentName):
                nsight_summary = pane.CurrentName
        logger.info('Nsight Visual Studio Edition Summary is as follows:\n {}'.format(nsight_summary))

        # invoke next button
        find_button_by_name_and_invoke('NEXT', install_window)
        time.sleep(1)

    with UIStep(int('{}'.format(next_step + 1)), 'Install - close', screenshot_label='close_window'):
        if gpu_brand in ['Titan', 'GeForce', 'Quadro']:
            c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Create desktop shortcut for NVIDIA GeForce Experience')
            c = iuia_object.CreateAndCondition(c1, c2)
            geforce_experience_shortcut = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)
            click(coords=get_clickable_point(geforce_experience_shortcut))
            time.sleep(1)

            c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Launch NVIDIA GeForce Experience')
            c = iuia_object.CreateAndCondition(c1, c2)
            geforce_experience_launch = find_first(install_window, uia_wrapper.TreeScope_Descendants, c)
            click(coords=get_clickable_point(geforce_experience_launch))
            time.sleep(1)
        # invoke close button
        find_button_by_name_and_invoke('CLOSE', install_window)

    # reboot after install cuda packages
    set_reboot_flag()


def close_display_window(window_name):
    desktop = iuia_object.GetRootElement()
    c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
    c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, window_name)
    c = iuia_object.CreateAndCondition(c1, c2)
    display_window = find_first(desktop, uia_wrapper.TreeScope_Descendants, c)

    find_button_by_name_and_invoke('Close', display_window)


def check_demo_samples_exists_and_run_samples(demo_location):
    demo_samples = ['bandwidthTest', 'busGrind', 'deviceQuery', 'nbody', 'randomFog', 'OceanFFT', 'vectorAdd']
    demo_samples_without_display = ['bandwidthTest', 'busGrind', 'deviceQuery', 'vectorAdd']
    demo_samples_with_display = ['nbody', 'randomFog', 'OceanFFT']
    display_window = ['CUDA N-Body ', 'Random Fog', 'CUDA FFT Oce']
    gpu_brand = os.getenv('PRODUCT_BRAND')
    bin_path = os.path.join(os.path.abspath(os.path.dirname(os.path.dirname(demo_location))), 'bin')

    with Step(1, 'Check if all the demo suite samples all exist'):
        for sample in demo_samples:
            sample_path = os.path.join(demo_location, sample)
            assert os.path.exists(sample_path + '.exe')

    with Step(2, 'Run demo suite samples'):
        logger.info('Demo suite samples location: {}'.format(demo_location))
        os.chdir(demo_location)
        for sample in demo_samples_without_display:
            logger.info('Run demo suite sample: {}'.format(sample))
            res = run_loc_cmd(sample)
            assert res.succeeded, 'Run demo suite sample: {} failed.'.format(sample)

        if gpu_brand in ['Titan', 'GeForce', 'Quadro']:
            i = 0
            for sample in demo_samples_with_display:
                logger.info('Run demo suite sample: {}'.format(sample))
                p = subprocess.Popen(sample, env={'PATH': '$PATH;{}'.format(bin_path)})
                time.sleep(2)
                close_display_window(display_window[i])
                assert not p.returncode, 'Run demo suite sample: {} failed'.format(sample)
                i += 1


def test_checks_after_installation():
    # check if CUDA was installed with correct version
    if cuda_version < '12.3':
        nvcc_location = dev_location
    else:
        nvcc_location = cuda_location
    assert os.path.exists(r'{}\bin\nvcc.exe'.format(nvcc_location)), 'Failed to install CUDA'
    res = run_loc_cmd(r'{}\bin\nvcc.exe -V'.format(nvcc_location))
    assert 'cuda_' + cuda_version in res.stdout, 'Installed CUDA version wass incorrect'
    missing_components = []
    missing_envs = []

    with Step(1, 'Check all components are installed'):
        # check components with cuda version
        logger.info('check components in WCPL')
        nvidia_components = cp_programs.get_nvidia_components()
        # Per CTK-4743 and 4193236, from CUDA 12.3, sub-components "CUDA Development", "CUDA Documentation",
        # "CUDA Runtime", "CUDA Visual Studio Integration" are changed into one component named "Nvidia CUDA Toolkit"
        if cuda_version < '12.3':
            installed_components_1 = ['NVIDIA CUDA Development ', 'NVIDIA CUDA Documentation ', 'NVIDIA CUDA Runtime ', 'NVIDIA CUDA Visual Studio Integration ']
            if cuda_version < '11.7':
                installed_components_1.append('NVIDIA CUDA Samples ')
            # Per https://jirasw.nvidia.com/browse/CTK-3929， CUDA Nsight NVTX was removed from CUDA 12.0
            if cuda_version < '12.0':
                installed_components_1.append('NVIDIA CUDA Nsight NVTX ')
            for installed_component in installed_components_1:
                component_name = installed_component + cuda_version
                if component_name not in nvidia_components:
                    missing_components.append(component_name)
            #    assert component_name in nvidia_components, '{} is not installed'.format(component_name)
        else:
            if 'NVIDIA CUDA Toolkit ' + cuda_version not in nvidia_components:
                missing_components.append('NVIDIA CUDA Toolkit ' + cuda_version)

        driver_name = 'NVIDIA Graphics Driver {}'.format(driver_version)
        if driver_name not in nvidia_components:
            missing_components.append(component_name)
        # assert driver_name in nvidia_components, 'Graphic driver {} should not be installed without nvidia gpu'.format(
        #     driver_name)

        # check other components
        installed_components_2 = ['NVIDIA PhysX System Software ', 'NVIDIA Nsight Compute ', 'NVIDIA Nsight Systems ',
                                  'NVIDIA Nsight Visual Studio Edition ']
        # Per bug 3750423, From 12.0, NVIDIA Tools Extension SDK (NVTX) was removed from CUDA 12.0
        if cuda_version < '12.0':
            installed_components_2.append('NVIDIA Tools Extension SDK ')
        for installed_component in installed_components_2:
            if installed_component not in ' '.join(nvidia_components):
                missing_components.append(installed_component)
        #    assert installed_component in ' '.join(nvidia_components), '{} is not installed'.format(installed_component)

        # other components depends on gpu
        if gpu_brand in ['Tesla', 'NVIDIA']:
            installed_components_by_gpu = ['NVIDIA RTX Desktop Manager']
        else:
            installed_components_by_gpu = ['NVIDIA HD Audio Driver', 'NVIDIA GeForce Experience', 'NVIDIA USBC Driver']
        for installed_component in installed_components_by_gpu:
            if installed_component not in ' '.join(nvidia_components):
                missing_components.append(installed_component)
        #    assert installed_component in ' '.join(nvidia_components), '{} is not installed'.format(installed_component)

        logger.info('Missing components: {}'.format(missing_components))
        assert len(missing_components) == 0, '{} missing, please check it.'.format(missing_components)

    with Step(2, 'Check CUDA environment paths in PATH'):
        logger.info('CUDA environment paths')
        envs = ['CUDA_PATH', 'CUDA_PATH_V{}'.format(cuda_version.replace('.', '_'))]
        if cuda_version < '11.6':
            envs.extend(['NVCUDASAMPLES_ROOT', 'NVCUDASAMPLES{}_ROOT'.format(cuda_version.replace('.', '_'))])
        for env in envs:
            if os.getenv(env) is None:
                missing_envs.append(env)
        #    assert os.getenv(env) is not None, '{} is not in environment variables'.format(env)

        # Per bug 3750423, From 12.0, NVIDIA Tools Extension SDK (NVTX) was removed from CUDA 12.0
        # check 'NVTOOLSEXT_PATH'
        if cuda_version < '12.0':
            if r'C:\Program Files\NVIDIA Corporation\NvToolsExt' not in os.getenv('NVTOOLSEXT_PATH'):
                missing_envs.append('NVTOOLSEXT_PATH')
            # assert os.getenv('NVTOOLSEXT_PATH') is not None, 'NVTOOLSEXT_PATH is not in environment variables'
            # assert r'C:\Program Files\NVIDIA Corporation\NvToolsExt' in os.getenv('NVTOOLSEXT_PATH'), 'NVTOOLSEXT_PATH is incorrect'

        # check path
        if cuda_version >= '12.3':
            path_location = cuda_location
        else:
            path_location = dev_location
        assert os.getenv('path'), 'path is not in environment variables'
        cuda_lib = r'{}\libnvvp'.format(path_location)
        cuda_bin = r'{}\bin'.format(path_location)
        if cuda_lib not in os.getenv('path'):
            missing_envs.append(cuda_lib)
        if cuda_bin not in os.getenv('path'):
            missing_envs.append(cuda_bin)
        # assert cuda_lib in os.getenv('path'), 'error: cuda lib path: {} is not in path value'.format(cuda_lib)
        # assert cuda_bin in os.getenv('path'), 'error: cuda bin path: {} is not in path value'.format(cuda_bin)

    with Step(3, 'Check related folders and files after installation'):
        logger.info('Verify installed folders')
        if cuda_version < '12.3':
            for f in folders_files_in_dev_location:
                assert os.path.exists(r'{}\{}'.format(dev_location, f)), 'cannot find {} in {}'.format(f, dev_location)
            for f in folders_files_in_doc_location:
                assert os.path.exists(r'{}\{}'.format(doc_location, f)), 'cannot find {} in {}'.format(f, doc_location)
        else:
            for f in folders_files_in_dev_location:
                assert os.path.exists(r'{}\{}'.format(cuda_location, f)), 'cannot find {} in {}'.format(f, dev_location)
            for f in folders_files_in_doc_location:
                assert os.path.exists(r'{}\{}'.format(cuda_location, f)), 'cannot find {} in {}'.format(f, doc_location)

        if cuda_version == '11.6':
            for f in folders_files_in_sample_location:
                assert os.path.exists(r'{}\{}'.format(sample_location, f)), 'cannot find {} in {}'.format(f, sample_location)

        # No-Go on SOLID for CUDA 12.4, https://jirasw.nvidia.com/browse/CTK-5447
        if False:
            logger.info('Per CTK-5447 for CUDA 12.4 and later, do an additional check for NVVM-SOLID, NVRTC and NVJITLINK related directory and files')
            # For NVVM-SOLID, directory ‘nvvm-next’ is present in custom location and all the files exist.
            nvvm_solid_files = [r'{}\nvvm-next\bin\cicc.exe'.format(cuda_location),
                                r'{}\nvvm-next\bin\nvvm64_40_0.dll'.format(cuda_location),
                                r'{}\nvvm-next\include\nvvm.h'.format(cuda_location),
                                r'{}\nvvm-next\lib\x64\nvvm.lib'.format(cuda_location),
                                r'{}\nvvm-next\libdevice\libdevice.10.bc'.format(cuda_location)
                                ]
            nvrtc_files = [r'{}\lib\x64\nvrtc-next.lib'.format(cuda_location),
                           r'{}\lib\x64\nvrtc-next_static.lib'.format(cuda_location),
                           r'{}\lib\x64\nvrtc-builtins-next_static.lib'.format(cuda_location),
                           r'{}\bin\nvrtc-builtins-next64_{}.dll'.format(cuda_location, cuda_version.replace('.', '')),
                           r'{}\bin\nvrtc-next64_{}_0.dll'.format(cuda_location, cuda_version.split('.')[0] + '0')
                           ]
            nvjitlink_files = [r'{}\lib\x64\nvJitLink-next.lib'.format(cuda_location),
                               r'{}\lib\x64\nvJitLink-next_static.lib'.format(cuda_location),
                               r'{}\bin\nvJitLink-next_{}_0.dll'.format(cuda_location, cuda_version.split('.')[0] + '0')
                               ]
            missing_files = []
            for f in nvvm_solid_files:
                if not os.path.exists(f):
                    missing_files.append(f)
            for f in nvrtc_files:
                if not os.path.exists(f):
                    missing_files.append(f)
            for f in nvjitlink_files:
                if not os.path.exists(f):
                    missing_files.append(f)
            assert not missing_files, 'Detected missing files: {}'.format(missing_files)

    with Step(4, 'Build and run samples'):
        # cuda samples repo has been prepared in the common step 'prepare cuda samples repo'
        # Here we need to get cuda samples path and call cmake
        # Major and update releases have the same root 'C:\tmp\cuda-samples'
        git_root = constants['cuda_samples_internal']['git_root']
        logger.info('Run cmake to generate VS files')
        gen_vs_project_file(git_root, cuda_version)

        cuda_samples = CUDASamples(cuda_version)
        sample_common = 'devicequery'
        sample_display = 'nbody'

        sample_instance = cuda_samples.get_sample(sample_common)
        sample_instance.build_sample()
        cmd = sample_instance.get_sample_binary_path()
        os.chdir(os.path.dirname(cmd))
        logger.debug('Run cuda sample {} with command {}'.format(sample_common, cmd))
        res = run_loc_cmd(cmd)
        assert res.succeeded, 'Run command failed: {}'.format(sample_common)

        if gpu_brand in ['Titan', 'GeForce', 'Quadro']:
            sample_instance = cuda_samples.get_sample(sample_display)
            sample_instance.build_sample()
            cmd = sample_instance.get_sample_binary_path()
            os.chdir(os.path.dirname(cmd))
            logger.debug('Run cuda sample {} with command {}'.format(sample_display, cmd))
            p = subprocess.Popen(sample_display, env={'PATH': '$PATH;{}'.format(os.path.dirname(cmd))})
            time.sleep(2)
            close_display_window('CUDA N-Body ')
            assert not p.returncode, 'Run demo suite sample: {} failed'.format(sample_display)

    with Step(5, 'Run demo samples'):
        if cuda_version < '12.3':
            location = r'{}\extras\demo_suite'.format(dev_location)
        else:
            location = r'{}\extras\demo_suite'.format(cuda_location)
        check_demo_samples_exists_and_run_samples(location)


def test_build_and_run_nvjitlink_samples(build_and_run_nvjitlink_samples):
    # Per bug 3874968, add some samples to verify nvjitlink component
    failed_samples, waived_samples = build_and_run_nvjitlink_samples
    assert len(failed_samples) == 0, 'Running nvjitlink samples failed, please check it.'


def test_uninstall_components_without_reboot():
    nvidia_components = cp_programs.get_nvidia_components()
    with Step(1, 'uninstall cuda components without reboot'):
        components_without_driver = [component for component in nvidia_components if 'Driver' not in component]
        logger.info('need to uninstall components {}'.format(components_without_driver))
        # Per CTK-4743 and 4193236, from CUDA 12.3, sub components "CUDA Development", "CUDA Documentation", "CUDA Runtime", "CUDA Visual Studio Integration" are changed into one component named "Nvidia CUDA Toolkit"
        # uninstall cuda development will also uninstall runtime, so no need to uninstall runtime
        if cuda_version < '12.3':
            if 'NVIDIA CUDA Runtime {}'.format(cuda_version) in nvidia_components and 'NVIDIA CUDA Development {}'.format(
                    cuda_version) in components_without_driver:
                components_without_driver.remove('NVIDIA CUDA Runtime {}'.format(cuda_version))
        for component in components_without_driver:
            cp_programs.uninstall_nvidia_component(component)


def test_check_after_uninstallation():
    logger.info('Check if any component is uninstalled.')
    nvidia_components = cp_programs.get_nvidia_components()
    assert not nvidia_components, 'Fail to uninstall {} '.format(nvidia_components)
