import os
import re
import logging
import time
import requests

from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.interactive_shell import InteractiveShell
from windows_test_helper.web_access import download_file
from windows_test_helper.ui_app_wrapper.driver_install import Driver
from windows_test_helper.gpu_info import get_nvsmi
from bs4 import BeautifulSoup
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.globals import GlobalsInFile
from windows_test_helper.windows import get_caption


logger = logging.getLogger(__name__)
cuda_version = os.getenv('TK_BRANCH')
ishell = InteractiveShell()
os.environ['NVCUDASAMPLES_ROOT'] = r'C:\ProgramData\Miniconda3\cuda-samples\{}'.format(cuda_version)
logger.info('Got env RELEASE_LABEL: {}'.format(os.getenv('RELEASE_LABEL')))
if os.getenv('RELEASE_LABEL').strip() == '0':
    channel_url = r'https://kitmaker-web.nvidia.com/kitbundles/r{}/{}/conda/'.format(cuda_version, os.getenv('BUILD_DATE'))
    logger.info('get channel_url for nightly: {}'.format(channel_url))
else:
    channel_url = r'https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}/{}/{}/conda/'.format(cuda_version.replace('.', '-'), os.getenv('RELEASE_LABEL'), os.getenv('BUILD_ID'))
    logger.info('get channel_url for rc: {}'.format(channel_url))
g = GlobalsInFile()
logging_dir = g.get('logging_dir')
os_name = get_caption()


def uninstall_conda(conda_path=r'C:\ProgramData\Miniconda3', uninstaller='Uninstall-Miniconda3.exe'):
    conda_uninstaller = os.path.join(conda_path, uninstaller)
    if os.path.exists(conda_uninstaller):
        logger.debug('conda uninstaller {} exists, begin to uninstall'.format(conda_uninstaller))
        run_loc_cmd(r'{} /S'.format(conda_uninstaller))
        # use an evaluated time for uninstallation of conda, as conda installer spawned sub-process will return immidiately and hard to track its completion
        time.sleep(30)
        assert not os.path.exists(conda_path), 'conda folder {} still exists after uninstall'.format(conda_path)
    logger.debug('No previously installed conda on path {}, it is clean for new conda'.format(conda_path))


def test_install_driver(customized_configs):
    with Step(1, 'Install driver'):
        path = customized_configs['conda_driver_path']
        driver_name = path.split('\\')[-1]
        match = re.match(r'(\d+\.\d+)_', driver_name)
        required_driver = match.group(1)
        logger.debug('Need install driver version: {}'.format(required_driver))
        driver = Driver(path)
        restart = False
        restart = driver.driver_express_install()
        if restart:
            set_reboot_flag()


def test_conda_install(customized_configs):
    """
    template id: 2761197
    """
    with Step(1, 'Verify driver'):
        path = customized_configs['conda_driver_path']
        driver_name = path.split('\\')[-1]
        match = re.match(r'(\d+\.\d+)_', driver_name)
        required_driver = match.group(1)

        nvsmi = get_nvsmi()
        res = run_loc_cmd(nvsmi)
        assert res.succeeded, 'Run command nvidia-smi failed'

        lines = res.stdout.split('\n')
        line = lines[2].strip()
        match = re.match(r'\| NVIDIA-SMI ([\d\.]*)\s+Driver Version: ([\d\.]+)\s+CUDA Version: ([\d\.]+)\s+\|', line)
        assert match, 'unexpect output for nvidia-smi output line {}: "{}"'.format(2, line)
        parsed_driver_version = match.group(2)
        logger.debug('Get Driver Version = {} '.format(parsed_driver_version))

        assert required_driver == parsed_driver_version, 'Installed Driver does not match required Driver'

    with Step(2, 'Uninstall, download and install conda'):
        uninstall_conda()
        conda_path = r'C:\ProgramData\Miniconda3\Scripts\activate.bat'
        download_file('https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe', r'c:\tmp\conda')
        install_cmd = r'c:\tmp\conda\Miniconda3-latest-Windows-x86_64.exe /S /D=C:\ProgramData\Miniconda3'
        res = run_loc_cmd(install_cmd)
        assert res.succeeded, 'Run install cmd fail'
        time.sleep(30)
        assert os.path.exists(conda_path), 'Failed to install conda'

    with Step(3, 'conda install cuda'):
        flag_2016 = False
        if re.search('Windows Server 2016', os_name, re.I):
            flag_2016 = True

        ishell.open()
        ishell.send_command(r'C:\ProgramData\Miniconda3\Scripts\activate.bat C:\ProgramData\Miniconda3')
        time.sleep(5)
        ishell.send_command(r'conda create --name cuda{} --yes'.format(cuda_version))
        time.sleep(15)
        ishell.send_command(r'conda activate cuda{}'.format(cuda_version))
        time.sleep(5)
        ishell.send_command(r'conda config --remove-key channels')
        time.sleep(2)
        ishell.send_command(r'conda config --add channels {}'.format(channel_url))
        time.sleep(2)
        ishell.send_command(r'conda config --show channels')
        time.sleep(2)

        finish_flag = False
        if not flag_2016:
            ishell.send_command(r'conda install cuda --yes')
            for _ in range(0, 60):
                time.sleep(300)
                if 'Executing transaction: done' in ishell.get_text():
                    finish_flag = True
                    logger.info(ishell.get_text())
                    break
        else:
            install_log_file = os.path.join(logging_dir, 'install.txt')
            ishell.send_command(r'conda install cuda --yes >{} 2>&1'.format(install_log_file))
            for _ in range(0, 60):
                time.sleep(300)
                with open(install_log_file) as f:
                    context = f.read()
                res = re.search(r'Executing transaction:.* done', context)
                if res:
                    finish_flag = True
                    logger.debug(context)
                    break
        ishell.exit()
        assert finish_flag, 'Failed to run conda install'

    with Step(4, 'check nvcc bin '):
        res = run_loc_cmd(r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\bin\nvcc.exe -V'.format(cuda_version))
        assert res.succeeded, 'Run nnvcc.exe failed'
        assert 'V{}'.format(cuda_version) in res.stdout

    with Step(5, 'Check conda install cuda pkgs'):
        # get all pkgs from url
        html_doc = requests.get(channel_url).text
        soup = BeautifulSoup(html_doc, 'html.parser')
        """['name','version','doc','dev','license','linux_64','windows_64','norch','summary']"""
        packages = []
        for tr in soup.findAll('table')[0].findAll('tr'):
            temp = []
            for td in tr.findAll('td'):
                temp.append(td.get_text())
            if temp:
                if temp[0].strip() and temp[6] == 'X':
                    packages.append(temp)
        # check pkg and pkg version
        logger.debug(packages)
        ishell.open()
        ishell.send_command(r'C:\ProgramData\Miniconda3\Scripts\activate.bat C:\ProgramData\Miniconda3')
        time.sleep(5)
        for package in packages:
            ishell.send_command('cls')
            if not flag_2016:
                ishell.send_command(r'conda search {}'.format(package[0].strip()))
                for _ in range(0, 6):
                    time.sleep(10)
                    if 'Loading channels: done' in ishell.get_text():
                        logger.info(ishell.get_text())
                        break
                assert package[1] in ishell.get_text()
            else:
                package_log_file = os.path.join(logging_dir, 'package.txt')
                ishell.send_command(r'conda search {} >{} 2>&1'.format(package[0].strip(), package_log_file))
                for _ in range(0, 6):
                    time.sleep(10)
                    with open(package_log_file) as f:
                        context = f.read()
                    res = re.search(r'Loading channels:.* done', context)
                    if res:
                        logger.debug(context)
                        break
                assert package[1] in context
        ishell.exit()

    with Step(7, 'the dynamic and static libraries list in proper folder'):
        LibDir = r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\lib'.format(cuda_version)
        DllDir = r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\bin'.format(cuda_version)
        filepath_list = []
        for filename in os.listdir(DllDir):
            filepath = os.path.join(DllDir, filename)
            if os.path.isfile(filepath):
                if filepath.endswith('lib'):
                    filepath_list.append(filepath)
        for filename in os.listdir(LibDir):
            filepath = os.path.join(LibDir, filename)
            if os.path.isfile(filepath):
                if filepath.endswith('dll'):
                    filepath_list.append(filepath)
        assert len(filepath_list) == 0, '{} exist in improper folder, pls check'.format(filepath_list)

    with Step(8, 'Run all demo samples in demo_suite folder between 11.7 and 12.5.'):
        if float(cuda_version) > 11.6 and float(cuda_version) < 12.5:
            conda_bin = r'C:\ProgramData\Miniconda3\envs\cuda{}\bin'.format(cuda_version)
            conda_lib = r'C:\ProgramData\Miniconda3\envs\cuda{}\lib\x64'.format(cuda_version)
            os.environ['PATH'] = conda_bin + ';' + conda_lib + ';' + os.environ['PATH']
            demo_samples_cmds = ['bandwidthTest.exe', 'busGrind.exe', 'deviceQuery.exe', 'nbody.exe --qatest', 'randomFog.exe --qatest', 'OceanFFT.exe --qatest', 'vectorAdd.exe']
            demo_location = r'C:\ProgramData\Miniconda3\envs\cuda{}\demo_suite'.format(cuda_version)
            logger.info('Demo suite samples location: {}'.format(demo_location))
            os.chdir(demo_location)
            for sample in demo_samples_cmds:
                logger.info('Run demo suite sample: {}'.format(sample))
                res = run_loc_cmd(sample)
                assert res.succeeded, 'Run demo suite sample: {} failed.'.format(sample)

    with Step(9, 'Check necessary installed files.'):
        if float(cuda_version) < 12.5:
            install_paths = [
                r'C:\ProgramData\Miniconda3\envs\cuda{}\bin'.format(cuda_version),
                r'C:\ProgramData\Miniconda3\envs\cuda{}\lib\x64'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\lib\Win32'.format(cuda_version),
                r'C:\ProgramData\Miniconda3\envs\cuda{}\src'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\compute-sanitizer'.format(cuda_version),
                r'C:\ProgramData\Miniconda3\envs\cuda{}\nvvm'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\nsight-compute'.format(cuda_version),
            ]
        else:
            install_paths = [
                r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\lib\x64'.format(cuda_version),
                r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\compute-sanitizer'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\nvvm'.format(cuda_version),
                r'C:\ProgramData\Miniconda3\envs\cuda{}\libnvvp'.format(cuda_version),
            ]
        doc_base_dir = r'C:\ProgramData\Miniconda3\envs\cuda{}'.format(cuda_version)
        install_paths_extend = [r'C:\ProgramData\Miniconda3\envs\cuda{}\demo_suite'.format(cuda_version)]
        if float(cuda_version) >= 12.0 and float(cuda_version) < 12.5:
            doc_base_dir = r'C:\ProgramData\Miniconda3\envs\cuda{}\cuda-doc'.format(cuda_version)
            docs = ['CUDA_Toolkit_Release_Notes.txt', 'DOCS', 'EULA.txt', 'LICENSE', 'README']
            full_path_docs = list(map(lambda x: os.path.join(doc_base_dir, x), docs))
            install_paths_extend.extend(full_path_docs)
            install_paths.extend(install_paths_extend)
        if float(cuda_version) > 11.6 and float(cuda_version) < 12.5:
            install_paths.extend(install_paths_extend)
        logger.info('Verify installed folders and files')
        for install_path in install_paths:
            assert os.path.exists(r'{}'.format(install_path)), 'cannot find {}'.format(install_path)


def test_conda_uninstall():
    with Step(1, 'conda uninstall cuda'):
        flag_2016 = False
        if re.search('Windows Server 2016', os_name, re.I):
            flag_2016 = True

        # remove copied samples
        if os.environ['NVCUDASAMPLES_ROOT']:
            os.system('rd /s /q {}'.format(os.environ['NVCUDASAMPLES_ROOT']))
        time.sleep(2)
        # uninstall cuda
        ishell.open()
        ishell.send_command(r'C:\ProgramData\Miniconda3\Scripts\activate.bat C:\ProgramData\Miniconda3')
        time.sleep(5)
        finish_flag = False
        if not flag_2016:
            ishell.send_command(r'conda activate cuda{}'.format(cuda_version))
            time.sleep(5)
            ishell.send_command(r'conda uninstall cuda --yes')
            for _ in range(0, 20):
                time.sleep(30)
                if 'Executing transaction: done' in ishell.get_text():
                    finish_flag = True
                    break
        else:
            uninstall_log_file = os.path.join(logging_dir, 'uninstall.txt')
            ishell.send_command(r'conda uninstall cuda --yes >{} 2>&1'.format(uninstall_log_file))
            for _ in range(0, 20):
                time.sleep(30)
                with open(uninstall_log_file) as f:
                    context = f.read()
                res = re.search(r'Executing transaction:.* done', context)
                if res:
                    finish_flag = True
                    break
        ishell.exit()
        assert finish_flag, 'conda uninstall cuda failed'

        # check if related folders deleted
        time.sleep(30)
        rm_paths = [
            r'C:\ProgramData\Miniconda3\envs\cuda{}\bin'.format(cuda_version),
            r'C:\ProgramData\Miniconda3\envs\cuda{}\Lib\x64'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\Lib\Win32'.format(cuda_version),
            r'C:\ProgramData\Miniconda3\envs\cuda{}\src'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\compute-sanitizer'.format(cuda_version),
            r'C:\ProgramData\Miniconda3\envs\cuda{}\nvvm'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\nsight-compute'.format(cuda_version),
        ]
        rm_paths_12_5 = [
            r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\lib\x64'.format(cuda_version),
            r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\compute-sanitizer'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\Library\nvvm'.format(cuda_version),
            r'C:\ProgramData\Miniconda3\envs\cuda{}\libnvvp'.format(cuda_version),
        ]
        rm_paths_extend = [r'C:\ProgramData\Miniconda3\envs\cuda{}\EULA.txt'.format(cuda_version), r'C:\ProgramData\Miniconda3\envs\cuda{}\CUDA_Toolkit_Release_Notes.txt'.format(cuda_version)]
        if float(cuda_version) > 11.6 and float(cuda_version) < 12.5:
            rm_paths.extend(rm_paths_extend)
            for rm_path in rm_paths:
                assert not os.path.exists(rm_path), '{} folder is not deleted after uninstallation'.format(rm_path)
        if float(cuda_version) >= 12.5:
            for rm_path in rm_paths_12_5:
                assert not os.path.exists(rm_path), '{} folder is not deleted after uninstallation'.format(rm_path)
