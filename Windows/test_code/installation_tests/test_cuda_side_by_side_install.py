import os
import re
import logging
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.steps import Step
from windows_test_helper.cuda_samples import CUDASamples
from windows_test_helper.ui_app_wrapper.cuda_installer import CUDAInstaller
from windows_test_helper.ui_app_wrapper.control_panel_programs import Control_Panel_Programs
from windows_test_helper.globals import GlobalsInFile
import time
import math
logger = logging.getLogger(__name__)
g = GlobalsInFile()
logging_dir = g.get('logging_dir')

tk_branch = os.getenv('TK_BRANCH')
driver_version = os.getenv('DRV_VER')
cuda_samples = CUDASamples(tk_branch)


def test_install_precuda(customized_configs):
    with Step(1, 'check cuda related paths and files'):
        cuda_test_path = customized_configs['cuda_package_path']
        pre_cuda_path = customized_configs['pre_cuda_package_path']
        release_cuda_branch = re.search(r'cuda_(\d+.\d+)', pre_cuda_path).group(1)
        assert os.path.isfile(cuda_test_path), 'cuda path in test {} does not exist'.format(cuda_test_path)
        assert os.path.isfile(pre_cuda_path), 'pre_cuda path  {} does not exist'.format(pre_cuda_path)
        assert tk_branch in cuda_test_path, 'cuda path is match with {}'.format(tk_branch)

    with Step(2, 'Install pre cuda'):
        cuda_release = CUDAInstaller(pre_cuda_path)
        cuda_release.cuda_install_express()
        # check cuda version
        res = run_loc_cmd(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin\nvcc.exe -V'.format(release_cuda_branch))
        assert 'cuda_' + release_cuda_branch in res.stdout, 'installed cuda version is incorrect'


def test_install_test_cuda(customized_configs):
    """
    template id: 1103465
    """
    cuda_test_path = customized_configs['cuda_package_path']

    with Step(1, 'Install testing cuda'):
        cuda_intest = CUDAInstaller(cuda_test_path)
        cuda_intest.cuda_install_express()
        # check cuda version
        res = run_loc_cmd(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin\nvcc.exe -V'.format(tk_branch))
        assert 'cuda_' + tk_branch in res.stdout, 'installed cuda version is incorrect'


def test_check_components_and_environments(customized_configs):
    pre_cuda_path = customized_configs['pre_cuda_package_path']
    release_cuda_branch = re.search(r'cuda_(\d+.\d+)', pre_cuda_path).group(1)

    with Step(1, 'Check if both version of CUDA are installed'):
        res = run_loc_cmd(
            r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin\nvcc.exe -V'.format(release_cuda_branch))
        assert 'cuda_' + release_cuda_branch in res.stdout, 'installed cuda version is incorrect'
        res = run_loc_cmd(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin\nvcc.exe -V'.format(tk_branch))
        assert 'cuda_' + tk_branch in res.stdout, 'installed cuda version is incorrect'

    with Step(2, 'Check nvidia components by WCPL'):
        cp_programs = Control_Panel_Programs()
        nvidia_components = cp_programs.get_nvidia_components()
        time.sleep(5)
        # check sample component
        if float(release_cuda_branch) < 11.6:
            assert 'NVIDIA CUDA Samples {}'.format(
                release_cuda_branch) in nvidia_components, 'CUDA Samples{} is not installed'.format(release_cuda_branch)
        if float(tk_branch) < 11.6:
            assert 'NVIDIA CUDA Samples {}'.format(
                tk_branch) in nvidia_components, 'CUDA Samples {} is not installed'.format(tk_branch)

        # check driver component
        driver_name = 'NVIDIA Graphics Driver {}'.format(driver_version)
        assert driver_name in nvidia_components, 'Graphic driver {} should not be installed without nvidia gpu'.format(
            driver_name)

        # check components
        installed_components_1 = ['NVIDIA CUDA Development ', 'NVIDIA CUDA Documentation ',
                                  'NVIDIA CUDA Runtime ', 'NVIDIA CUDA Visual Studio Integration ']
        # Per CTK-4743 and 4193236
        # from CUDA 12.3, sub components "CUDA Development", "CUDA Documentation", "CUDA Runtime", "CUDA Visual Studio Integration" are changed into one component named "Nvidia CUDA Toolkit"
        if float(tk_branch) >= 12.3:
            installed_components_1 = ['NVIDIA CUDA Toolkit ']
        # Per https://jirasw.nvidia.com/browse/CTK-3929， CUDA Nsight NVTX was removed from CUDA 12.0
        if float(tk_branch) < 12.0:
            installed_components_1.append('NVIDIA CUDA Nsight NVTX ')
        for installed_component in installed_components_1:
            component_test = installed_component + tk_branch
            assert component_test in nvidia_components, '{} is not installed'.format(component_test)

        installed_components_1_pre_cuda = ['NVIDIA CUDA Development ', 'NVIDIA CUDA Documentation ',
                                           'NVIDIA CUDA Runtime ', 'NVIDIA CUDA Visual Studio Integration ']
        if float(release_cuda_branch) >= 12.3:
            installed_components_1_pre_cuda = ['NVIDIA CUDA Toolkit ']
        if float(release_cuda_branch) < 12.0:
            installed_components_1_pre_cuda.append('NVIDIA CUDA Nsight NVTX ')
        for installed_component in installed_components_1_pre_cuda:
            component_pre = installed_component + release_cuda_branch
            assert component_pre in nvidia_components, '{} is not installed'.format(component_pre)

        # check other components
        installed_components_2 = ['NVIDIA PhysX System Software ', 'NVIDIA Nsight Compute ', 'NVIDIA Nsight Systems ',
                                  'NVIDIA Nsight Visual Studio Edition ', 'NVIDIA Tools Extension SDK ']
        if float(tk_branch) >= 12.0:
            installed_components_2.pop()
        for installed_component in installed_components_2:
            assert installed_component in ''.join(nvidia_components), '{} is not installed'.format(installed_component)

        # other components depends on gpu
        product_brand = os.getenv('PRODUCT_BRAND')
        if 'Tesla' in product_brand or 'NVIDIA' in product_brand:
            installed_components_by_gpu = ['NVIDIA RTX Desktop Manager']
        else:
            installed_components_by_gpu = ['NVIDIA HD Audio Driver', 'NVIDIA GeForce Experience', 'NVIDIA USBC Driver']
        for installed_component in installed_components_by_gpu:
            assert installed_component in ''.join(nvidia_components), '{} is not installed'.format(installed_component)

    with Step(3, 'check CUDA environment paths in PATH'):
        envs_test = ['CUDA_PATH', 'CUDA_PATH_V{}'.format(tk_branch.replace('.', '_'))]
        env_pre = ['CUDA_PATH_V{}'.format(release_cuda_branch.replace('.', '_'))]
        if float(tk_branch) < 11.6:
            envs_test.extend(['NVCUDASAMPLES_ROOT', 'NVCUDASAMPLES{}_ROOT'.format(tk_branch.replace('.', '_'))])
            env_pre.extend(['NVCUDASAMPLES{}_ROOT'.format(release_cuda_branch.replace('.', '_'))])
        if math.isclose(float(tk_branch), 11.6, rel_tol=1e-5):
            env_pre.extend(['NVCUDASAMPLES_ROOT', 'NVCUDASAMPLES{}_ROOT'.format(release_cuda_branch.replace('.', '_'))])
        for env in envs_test:
            assert os.getenv(env) is not None, '{} is not in environment variables'.format(env)
            assert tk_branch in os.getenv(env), 'value of {} : {}  is not match with {}'.format(env, os.environ[env], tk_branch)
        for env in env_pre:
            assert os.getenv(env) is not None, '{} is not in environment variables'.format(env)
            assert release_cuda_branch in os.getenv(env), 'value of {} : {}  is not match with {}'.format(env, os.environ[env], release_cuda_branch)

        # check path
        assert os.getenv('path'), 'path is not in environment variables'
        cuda_lib = r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\libnvvp'.format(tk_branch)
        cuda_bin = r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin'.format(tk_branch)
        cuda_lib_pre = r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\libnvvp'.format(release_cuda_branch)
        cuda_bin_pre = r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin'.format(release_cuda_branch)
        assert cuda_lib in os.getenv('path'), 'error: cuda lib path: {} is not in path value'.format(cuda_lib)
        assert cuda_bin in os.getenv('path'), 'error: cuda bin path: {} is not in path value'.format(cuda_bin)
        assert cuda_lib_pre in os.getenv('path'), 'error: cuda lib path: {} is not in path value'.format(cuda_lib)
        assert cuda_bin_pre in os.getenv('path'), 'error: cuda bin path: {} is not in path value'.format(cuda_bin)

    with Step(4, 'Build and run samples'):
        sample_instance = cuda_samples.get_sample('deviceQuery')
        logger.info('Build sample: {}'.format('deviceQuery'))
        sample_instance.build_sample()
        run_sample('deviceQuery')


def run_sample(sample_name):
    sample_instance = cuda_samples.get_sample(sample_name)
    binary_folder = os.path.dirname(sample_instance.get_sample_binary_path())
    logger.info('Run sample in folder {}'.format(binary_folder))
    os.chdir(binary_folder)
    cmd = '{}.exe'.format(sample_name)
    if sample_instance.is_display_only():
        res = run_loc_cmd(cmd, timeout=300, auto_close_window=True, auto_close_interval=15)
    else:
        res = run_loc_cmd(cmd, timeout=1800)

    if res.timeout:
        logger.warning('Run command {} timeout'.format(cmd))

    if res.exit == 2:
        pass
    elif res.exit == 0:
        pass
    else:
        raise AssertionError('Run command {} fail with exit code {}'.format(cmd, res.exit))
