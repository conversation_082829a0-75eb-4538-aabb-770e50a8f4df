import re
import os
import pytest
import winreg
import json
from windows_test_helper.ui_app_wrapper.driver_install import Driver
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.ui_app_wrapper.cuda_installer import CUDAInstaller
from windows_test_helper import logger
from windows_test_helper.run_cmd import run_loc_cmd
from windows_test_helper.gpu_info import get_nvsmi
from windows_test_helper.steps import Step, check_results, init_results
from windows_test_helper.cuda_samples import CUDASamples


tk_branch = os.getenv('TK_BRANCH')
cuda_samples = ['deviceQuery', 'batchCUBLAS', 'bandwidthTest']
opencl_samples = ['oclDeviceQuery', 'oclMatrixMul']


@pytest.mark.disable_gpu_select_envs
class TestDriverInstallonLatestCUDA:
    '''
    Template id: 2003390
    '''
    @pytest.mark.parametrize('flag', ['pre_dch', 'current_dch'])
    def test_driver_install(self, flag, get_dch_driver_path, customized_configs):
        restart = False
        released_cuda_path = customized_configs['cuda_package_path']
        assert released_cuda_path, 'please specify cuda path {}'.format(released_cuda_path)
        assert os.path.exists(released_cuda_path), 'the cuda path {} does not exist'.format(released_cuda_path)
        released_cuda_name = released_cuda_path.split('\\')[-1]
        released_driver_version = re.search(r'_(\d+\.\d+)_', released_cuda_name).group(1)
        pre_driver_path, current_driver_path = get_dch_driver_path
        if flag == 'pre_dch':
            test_driver = pre_driver_path
        else:
            test_driver = current_driver_path

        assert test_driver, 'driver path in customized config is empty, please specify tested driver path'
        assert os.path.exists(test_driver), 'the driver path {} does not exist'.format(test_driver)

        driver_name = test_driver.split('\\')[-1]
        driver_version = driver_name.split('_')[0]
        driver_version_cqa = os.getenv('DRV_VER')
        if flag == 'pre_dch':
            assert driver_version < released_driver_version, 'the pre-dch driver {} is newer than driver of pre-cuda {}, Please specify a pre-dch driver which is old than the one in the latest posted CUDA'.format(driver_version, released_driver_version)
        else:
            assert driver_version_cqa == driver_version, 'The driver version {} in specified dirver path does not match testing driver version {}, please check'.format(driver_version, driver_version_cqa)
        driver = Driver(test_driver)
        restart = driver.driver_express_install()
        if restart:
            set_reboot_flag()

    @pytest.mark.parametrize('flag', ['pre_dch', 'current_dch', 'current_cuda'])
    def test_driver_cuda_check(self, flag, get_dch_driver_path, customized_configs):
        pre_driver_path, current_driver_path = get_dch_driver_path
        if flag == 'current_cuda':
            released_cuda_path = customized_configs['cuda_package_path']
            released_cuda_branch = re.search(r'cuda_(\d+.\d+)', released_cuda_path).group(1)
            assert released_cuda_branch == tk_branch, 'CUDA branch {} got from path does not match branch {} got from CQA'.format(released_cuda_branch, tk_branch)
            cuda_name = released_cuda_path.split('\\')[-1]
            released_driver_version = re.search(r'_(\d+\.\d+)_', cuda_name).group(1)
        else:
            if flag == 'pre_dch':
                test_driver = pre_driver_path
            else:
                test_driver = current_driver_path
            driver_name = test_driver.split('\\')[-1]
            driver_version = driver_name.split('_')[0]

        nvidia_smi = get_nvsmi()
        out = run_loc_cmd(nvidia_smi)
        assert out.succeeded, 'nvidia-smi run failed'
        driver_version_actual = re.search(r'Driver Version: ([\d\.]+)', out.stdout).group(1)
        if flag == 'pre_dch' or flag == 'current_dch':
            assert driver_version_actual == driver_version, 'The driver version {} in nvidia-smi output does not match with installed driver {}'.format(driver_version_actual, driver_version)
            logger.debug('The driver version {} in nvidia-smi output matches with required driver {}'.format(driver_version_actual, driver_version))
        else:
            assert driver_version_actual == released_driver_version, 'The driver version {} in nvidia-smi output does not match with installed CUDA driver {}'.format(driver_version_actual, released_driver_version)
            res = run_loc_cmd(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\bin\nvcc.exe -V'.format(released_cuda_branch))
            assert 'cuda_' + released_cuda_branch in res.stdout, 'installed cuda version is incorrect'
            logger.debug('latest posted Toolkit installed successfully with the right driver version and cuda version')

        assert get_DCHUven('DCHUVen') and get_DCHUven('DCHUVen') == 4318, ' Value of key "DCHUVen" is None or not be 4318'
        logger.debug('check DCH registry key and Value of key "DCHUVen" is 4:dword:000010de, it is DCH driver')

    def test_install_released_cuda(self, customized_configs):
        released_cuda_path = customized_configs['cuda_package_path']
        cuda_release = CUDAInstaller(released_cuda_path)
        cuda_release.cuda_install_express()
        set_reboot_flag()

    def test_cuda_and_opencl_samples(self, download_opencl_sdk):
        results = init_results()
        opencl_sdk_local_binary_path = download_opencl_sdk
        with Step(1, 'Run CUDA samples'):
            for index, sample in enumerate(cuda_samples):
                with Step('2.{}'.format(index), 'Run command: {}'.format(sample), results=results, suppress_excp=True):
                    run_sample(sample)
        with Step(2, 'Run Opencl samples'):
            for index, sample in enumerate(opencl_samples):
                with Step('3.{}'.format(index), 'Run command: {}'.format(sample), results=results, suppress_excp=True):
                    run_opencl_sample(sample, opencl_sdk_local_binary_path)
        err_msg_json = check_results(results)
        assert not err_msg_json, json.dumps(err_msg_json)


def get_DCHUven(name):
    key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, r'SYSTEM\CurrentControlSet\Services\nvlddmkm')
    try:
        v = winreg.QueryValueEx(key, name)[0]
    except FileNotFoundError:
        return None
    else:
        return v


def run_sample(sample_name):
    cudaSamples = CUDASamples(tk_branch)
    sample_instance = cudaSamples.get_sample(sample_name)
    logger.info('Build sample: {}'.format(sample_name))
    sample_instance.build_sample(rebuild=True)

    binary_folder = os.path.dirname(sample_instance.get_sample_binary_path())
    logger.info('Run sample in folder {}'.format(binary_folder))
    os.chdir(binary_folder)
    sample_cmd = '{}.exe'.format(sample_name)
    res = run_loc_cmd(sample_cmd, timeout=300)
    if res.timeout:
        logger.warning('Run command {} timeout'.format(sample_cmd))

    if res.exit == 2:
        pass
    elif res.exit == 0:
        pass
    else:
        raise AssertionError('Run command {} fail with exit code {}'.format(sample_cmd, res.exit))


def run_opencl_sample(sample, opencl_sdk_local_binary_path):
    os.chdir(opencl_sdk_local_binary_path)
    logger.debug('Run sample in directory: {}'.format(opencl_sdk_local_binary_path))

    logger.info('Run command {}'.format(sample))
    res = run_loc_cmd(sample, timeout=1800)

    assert res.succeeded, 'Run {} failed'.format(sample)
    assert 'PASSED' in res.stdout, 'There is no PASSED in stdout'
