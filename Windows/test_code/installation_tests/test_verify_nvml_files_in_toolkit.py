import os
import logging
from windows_test_helper.steps import Step
from windows_test_helper.ui_app_wrapper.cuda_installer import CUDAInstaller

logger = logging.getLogger(__name__)
cuda_version = os.getenv('TK_BRANCH')
nvml_files = [r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\include\nvml.h'.format(cuda_version),
              r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\lib\x64\nvml.lib'.format(cuda_version),
              r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\nvml\example\example.c'.format(cuda_version),
              r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\nvml\example\example.sln'.format(cuda_version),
              r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\nvml\example\example.vcxproj'.format(cuda_version),
              r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\nvml\example\README.txt'.format(cuda_version)]


def test_express_install_with_all_vs_versions(customized_configs):
    """
    template id: 1165502
    """
    cuda_test_path = customized_configs['cuda_package_path']
    assert os.path.isfile(cuda_test_path), 'cuda path in test {} does not exist'.format(cuda_test_path)
    with Step(1, 'install cuda by express'):
        logger.info('begin install cuda')
        cuda_release = CUDAInstaller(cuda_test_path)
        cuda_release.cuda_install_express()

    with Step(2, 'check nvml files'):
        for file in nvml_files:
            assert os.path.exists(file), '{} does not exists'.format(file)
