import os
import pytest
import subprocess
import time
import re
import json
from windows_test_helper import logger
from windows_test_helper.windows import get_product_type
from windows_ui_automation.uia import uia_wrapper, iuia_object, find_first, find_all, WindowsUIAutomationError
from windows_test_helper.steps import UIStep
from windows_test_helper.constants import constants
from windows_test_helper.share_folder import copy_from_share_folder
from windows_test_helper.reboot import set_reboot_flag
from windows_test_helper.gpu_info import get_nvsmi
from windows_test_helper.run_cmd import run_loc_cmd


local_driver_path = r'c:\tmp\driver'
local_cuda_driver = r'c:\tmp\cuda'
tk_branch = os.getenv('TK_BRANCH')


@pytest.fixture(scope='session')
def get_release_bundled_driver_path(customized_configs):
    driver_path = ''
    product_brand = os.getenv('PRODUCT_BRAND')
    # get driver path
    if 'Tesla' in product_brand or 'Quadro' in product_brand or 'NVIDIA' in product_brand:
        os_type = get_product_type()  # 1-- win10,win11, 3-- windows server
        if os_type == 1:
            driver_path = customized_configs['quadro_driver_bundled_in_last_release']
        elif os_type == 3:
            driver_path = customized_configs['quadroserver_driver_bundled_in_last_release']
    elif 'Titan' in product_brand or 'Geforce' in product_brand:
        driver_path = customized_configs['gameready_driver_bundled_in_last_release']
    else:
        logger.warning('Not support gpu brand {}'.format(product_brand))
    return driver_path


def download_package(server_path, flag, overwrite):
    if flag == 'cuda':
        local_path = os.path.join(local_cuda_driver, os.path.basename(server_path))
    else:
        local_path = os.path.join(local_cuda_driver, os.path.basename(server_path))

    logger.info('download cuda pkg from server {} to local'.format(server_path))
    share_folder_username = constants['share_folder_username']
    share_folder_password = constants['share_folder_password']
    copy_from_share_folder(server_path, local_path, share_folder_username,
                           share_folder_password, overwrite=overwrite)
    assert os.path.isfile(local_path), 'package copy failed'
    return local_path


def test_driver_thin_cuda_first(customized_configs, get_release_bundled_driver_path):
    """
    template id: 872533
    """

    cuda_test_path = customized_configs['cuda_network_package_path']
    standalone_driver = get_release_bundled_driver_path
    assert cuda_test_path, 'please specify network package path'
    assert os.path.exists(cuda_test_path), 'the package path {} does not exist'.format(cuda_test_path)
    assert tk_branch in cuda_test_path and 'network' in cuda_test_path, 'Please specify network package with {} branch'.format(tk_branch)
    assert standalone_driver, 'Please specify a standalone driver at xx driver_bundled_in_last_release '
    assert os.path.exists(standalone_driver), 'The specified release bundled driver {} does not exist'.format(standalone_driver)
    local_cuda_path = download_package(cuda_test_path, 'cuda', True)
    local_driver_path = download_package(standalone_driver, 'driver', True)

    with UIStep(1, 'Launch standalone driver', screenshot_label='launch_driver'):
        driver_instance = subprocess.Popen(local_driver_path)
        desktop = iuia_object.GetRootElement()
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_ProcessIdPropertyId, driver_instance.pid)
        c = iuia_object.CreateAndCondition(c1, c2)
        driver_installer_dialog = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=60)

        # invoke OK button
        find_button_by_name_and_invoke('OK', driver_installer_dialog)

    with UIStep(2, 'Find driver installer window', screenshot_label='installer_window'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer')
        c = iuia_object.CreateAndCondition(c1, c2)
        driver_install_window = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)
        driver_ProcessID = driver_install_window.GetCurrentPropertyValue(uia_wrapper.UIA_ProcessIdPropertyId)
        logger.debug('first driver process id: {}'.format(driver_ProcessID))

    with UIStep(3, 'Install - Agree and continue for driver', screenshot_label='install_agree and contiune'):
        # involke agree and continue
        find_button_by_name_and_invoke('AGREE AND CONTINUE', driver_install_window)

    # at the same time, launch CUDA
    with UIStep(4, 'Launch thin CUDA', screenshot_label='launch_thin_cuda'):
        cuda_instance = subprocess.Popen(local_cuda_path)
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_ProcessIdPropertyId, cuda_instance.pid)
        c = iuia_object.CreateAndCondition(c1, c2)
        cuda_installer_dialog = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=60)

        # invoke OK button
        find_button_by_name_and_invoke('OK', cuda_installer_dialog)

    with UIStep(5, 'Find cuda installer window ', screenshot_label='cuda_window'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer')
        c = iuia_object.CreateAndCondition(c1, c2)
        timeout = 360
        start = current = time.time()
        succeed_cuda_window = False
        while current - start < timeout:
            install_windows = find_all(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)
            if len(install_windows) == 2:
                logger.debug('Second installer window appear, continue')
                succeed_cuda_window = True
                break
            current = time.time()
        assert succeed_cuda_window, 'After timeout, second cuda install window still does not appear, abort!'
        for item in install_windows:
            processID = item.GetCurrentPropertyValue(uia_wrapper.UIA_ProcessIdPropertyId)
            logger.debug('process id: {}'.format(processID))
            if processID != driver_ProcessID:
                # cuda installer window
                cuda_install_window = item
                break

    with UIStep(6, 'Check if CUDA thin installer continue ', screenshot_label='check_cuda_error'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer cannot continue')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(cuda_install_window, uia_wrapper.TreeScope_Children, c, timeout=60)

        logger.debug('Found text "NVIDIA Installer cannot continue", close installer window')
        c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'pane')
        pane = find_first(cuda_install_window, uia_wrapper.TreeScope_Children, c, timeout=60)
        assert 'Other installations are running. Finish the other installations then try again.' in pane.CurrentName, 'Error message wrong'
        logger.debug('It shows expected message "Other installations are runnning, Finish other installations then try again"')
        # invoke CLOSE button
        find_button_by_name_and_invoke('CLOSE', cuda_install_window)

    with UIStep(7, 'Install - with Express for driver', screenshot_label='install_express_option'):
        # invoke NEXT button
        find_button_by_name_and_invoke('NEXT', driver_install_window)

    with UIStep(8, 'Check windows security dialog', screenshot_label='windows_security'):
        check_if_windows_security()

    with UIStep(9, 'Install - finish for driver', screenshot_label='install_finish'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer has finished')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(driver_install_window, uia_wrapper.TreeScope_Descendants, c, timeout=3600)
        logger.debug('NVIDIA Installer has finished')

        try:
            c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list')
            c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, '')
            c = iuia_object.CreateAndCondition(c1, c2)
            item_list = find_first(driver_install_window, uia_wrapper.TreeScope_Descendants, c)

            c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'list item')
            all_items = find_all(item_list, uia_wrapper.TreeScope_Children, c)

        except WindowsUIAutomationError:
            logger.warning('No list item with checkbox is detected for some certain driver, continue next step')
        else:
            for item in all_items:
                toggle_pattern = item.GetCurrentPattern(uia_wrapper.UIA_InvokePatternId).QueryInterface(uia_wrapper.IUIAutomationInvokePattern)
                toggle_pattern.Invoke()

        try:
            c1_restart = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
            c2_restart = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'To complete the installation, restart the computer.')
            c_restart = iuia_object.CreateAndCondition(c1_restart, c2_restart)
            find_first(driver_install_window, uia_wrapper.TreeScope_Descendants, c_restart)
        except WindowsUIAutomationError:
            logger.warning('No Restart needed after installation')
            # invoke NEXT button
            find_button_by_name_and_invoke('CLOSE', driver_install_window)
        else:
            # invoke NEXT button
            find_button_by_name_and_invoke('RESTART LATER', driver_install_window)
            logger.debug('Need restart after installation')
            set_reboot_flag()


def test_thin_cuda_driver_second(customized_configs, get_release_bundled_driver_path):
    """
    template id: 872533
    """
    cuda_test_path = customized_configs['cuda_network_package_path']
    standalone_driver = get_release_bundled_driver_path
    local_cuda_path = download_package(cuda_test_path, 'cuda', False)
    local_driver_path = download_package(standalone_driver, 'driver', False)

    with UIStep(1, 'Launch thin CUDA', screenshot_label='1_launch_thin_cuda'):
        cuda_instance = subprocess.Popen(local_cuda_path)
        desktop = iuia_object.GetRootElement()
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_ProcessIdPropertyId, cuda_instance.pid)
        c = iuia_object.CreateAndCondition(c1, c2)
        cuda_installer_dialog = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=60)

        # invoke OK button
        find_button_by_name_and_invoke('OK', cuda_installer_dialog)

    with UIStep(2, 'Find cuda installer window for thin cuda ', screenshot_label='2_find_cuda_window'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer')
        c = iuia_object.CreateAndCondition(c1, c2)
        cuda_install_window = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)
        cuda_ProcessID = cuda_install_window.GetCurrentPropertyValue(uia_wrapper.UIA_ProcessIdPropertyId)

    with UIStep(3, 'Install - Agree and continue for thin cuda', screenshot_label='3_install_agree_and_contiune_for_cuda'):
        # wait until the license agreement panel  load
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA software license agreement')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(cuda_install_window, uia_wrapper.TreeScope_Descendants, c, timeout=1200)
        # invoke agree and continue
        find_button_by_name_and_invoke('AGREE AND CONTINUE', cuda_install_window)

    with UIStep(4, 'Launch standalone driver', screenshot_label='4_launch_driver'):
        driver_instance = subprocess.Popen(local_driver_path)
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_ProcessIdPropertyId, driver_instance.pid)
        c = iuia_object.CreateAndCondition(c1, c2)
        driver_installer_dialog = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=60)
        # invoke OK button
        find_button_by_name_and_invoke('OK', driver_installer_dialog)

    with UIStep(5, 'Find driver installer window ', screenshot_label='5_find_driver_window'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer')
        c = iuia_object.CreateAndCondition(c1, c2)
        timeout = 360
        start = current = time.time()
        succeed_driver_window = False
        while current - start < timeout:
            install_windows = find_all(desktop, uia_wrapper.TreeScope_Children, c, timeout=180)
            if len(install_windows) == 2:
                logger.debug('Second installer window appear, continue')
                succeed_driver_window = True
                break
            current = time.time()
        assert succeed_driver_window, 'After timeout, second driver install window still does not appear, abort!'
        for item in install_windows:
            processID = item.GetCurrentPropertyValue(uia_wrapper.UIA_ProcessIdPropertyId)
            if processID != cuda_ProcessID:
                # cuda installer window
                driver_install_window = item
                break

    with UIStep(6, 'Check if driver installer continue ', screenshot_label='6_check_driver_error'):
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'NVIDIA Installer cannot continue')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(driver_install_window, uia_wrapper.TreeScope_Children, c, timeout=60)

        logger.debug('Found text "NVIDIA Installer cannot continue", close installer window')
        c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'pane')
        pane = find_first(driver_install_window, uia_wrapper.TreeScope_Children, c, timeout=60)
        assert 'Other installations are running. Finish the other installations then try again.' in pane.CurrentName, 'Error message wrong'
        logger.debug('It shows expected message "Other installations are runnning,Finish other installations then try again"')
        # invoke CLOSE button
        find_button_by_name_and_invoke('CLOSE', driver_install_window)

    with UIStep(7, 'Install - with express for thin cuda', screenshot_label='7_install_express_option'):
        #  invoke next button
        find_button_by_name_and_invoke('NEXT', cuda_install_window)

    with UIStep(8, 'Install - check if error shortcut for thin cuda', screenshot_label='8_error_shortcut'):
        check_if_error_shortcut_and_cancel()

    with UIStep(9, 'Install - check if security diag for thin cuda', screenshot_label='9_security_diag'):
        check_if_windows_security()

    with UIStep(10, 'Install - finish for thin cuda', screenshot_label='10_install_finish'):
        # check install finished
        c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Nsight Visual Studio Edition Summary')
        c = iuia_object.CreateAndCondition(c1, c2)
        find_first(cuda_install_window, uia_wrapper.TreeScope_Descendants, c, timeout=72000)

        # invoke next button
        find_button_by_name_and_invoke('NEXT', cuda_install_window)

    with UIStep(11, 'Install - close for thin cuda', screenshot_label='11_close_window'):
        # invoke close button
        try:
            find_button_by_name_and_invoke('CLOSE', cuda_install_window)
            logger.info('find close button and invoke close')
        except WindowsUIAutomationError:
            logger.info('cannot find close button and try to find restart button')
            # check if need to restart
            try:
                find_button_by_name_and_invoke('RESTART LATER', cuda_install_window)
                logger.info('find restart button and need to restart')
                set_reboot_flag()
            except WindowsUIAutomationError:
                logger.info('No Restart needed after installation')


@pytest.mark.parametrize('flag', ['toolkit', 'driver'])
def test_verify_installation(flag, get_release_bundled_driver_path):
    standalone_driver = get_release_bundled_driver_path
    nvidia_smi = get_nvsmi()
    out = run_loc_cmd(nvidia_smi)
    assert out.succeeded, 'nvidia-smi run failed'
    if flag == 'driver':
        driver_name = standalone_driver.split('\\')[-1]
        driver_version = driver_name.split('_')[0]
        driver_version_actual = re.search(r'Driver Version: ([\d\.]+)', out.stdout).group(1)
        assert driver_version_actual == driver_version, 'The driver version {} in nvidia-smi output does not match with installed driver {}'.format(driver_version_actual, driver_version)
        logger.debug('The driver version {} in nvidia-smi output matches with installed driver {}, driver installed successfully'.format(driver_version_actual, driver_version))
    else:
        cuda_tookit_version = os.environ['TK_VER'].strip()
        version_json = r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v{}\version.json'.format(tk_branch)
        with open(version_json) as f:
            json_file = json.load(f)
            cuda_cudart_version = json_file['cuda_cudart']['version']
        assert cuda_tookit_version == cuda_cudart_version, 'Test cuda version {} is not matched with the cuda {} installed on the machine'.format(cuda_cudart_version, cuda_tookit_version)
        logger.debug('Thin CUDA installed successfully')


def check_if_windows_security():
    desktop = iuia_object.GetRootElement()
    c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
    c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Windows Security')
    c = iuia_object.CreateAndCondition(c1, c2)
    try:
        windows_security = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=300)
    except WindowsUIAutomationError:
        logger.info('no find windows security dialog, so continue')
    else:
        logger.info('find windows security dialog, click install')
        find_button_by_name_and_invoke('Install', windows_security)


def find_button_by_name_and_invoke(button_name, target_window):
    c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'button')
    c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, button_name)
    c = iuia_object.CreateAndCondition(c1, c2)
    target_button = find_first(target_window, uia_wrapper.TreeScope_Descendants, c, timeout=300)
    invoke_pattern = target_button.GetCurrentPattern(uia_wrapper.UIA_InvokePatternId).QueryInterface(uia_wrapper.IUIAutomationInvokePattern)
    try:
        invoke_pattern.Invoke()
    except WindowsUIAutomationError:
        logger.warning('perphaps app hang ,wait some time to try again')
        time.sleep(60)


def check_if_error_shortcut_and_cancel(timeout=600):
    desktop = iuia_object.GetRootElement()
    c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'dialog')
    c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Missing Shortcut')
    c = iuia_object.CreateAndCondition(c1, c2)
    c2_1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'Problem with Shortcut')
    c_1 = iuia_object.CreateAndCondition(c1, c2_1)
    start = current = time.time()
    while current - start < timeout:
        try:
            miss_windows = find_first(desktop, uia_wrapper.TreeScope_Children, c, timeout=5)
            logger.info('find missed shortcut window, click cancel')
            find_button_by_name_and_invoke('Cancel', miss_windows)
        except WindowsUIAutomationError:
            logger.info('no missed shortcut window, contiue')
        try:
            miss_windows = find_first(desktop, uia_wrapper.TreeScope_Children, c_1, timeout=5)
            logger.info('find problem with shortcut window, click cancel')
            find_button_by_name_and_invoke('Cancel', miss_windows)
        except WindowsUIAutomationError:
            logger.info('no problem with shortcut window, contiue')
        current = time.time()
