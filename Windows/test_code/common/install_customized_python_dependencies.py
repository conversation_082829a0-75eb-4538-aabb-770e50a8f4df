import os
import sys
import json
import logging
import requests


logger = logging.getLogger(os.path.basename(__file__))
# config logger
logging.basicConfig(level=logging.DEBUG, format='%(message)s')


customized_python_dependencies = [
    ('windows_ui_automation', 'GITLAB_WINDOWS_UI_AUTOMATION_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/windows_ui_automation.git@master'),
    ('cuda_driver_api_wrapper', 'GITLAB_CUDA_DRIVER_API_WRAPPER_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/cuda_driver_api_wrapper.git@master'),
    ('nvapi_python', 'GITLAB_NVAPI_PYTHON_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/nvapi_python.git@main'),
    ('windows_test_helper', 'GITLAB_WINDOWS_TEST_HELPER_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/windows_test_helper.git@master'),
    ('pytest_case_logging', 'GITLAB_PYTEST_CASE_LOGGING_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/pytest_case_logging.git@master'),
    ('pytest_customized_sections', 'GITLAB_PYTEST_CUSTOMIZED_SECTIONS_TOKEN', 'git+https://{}@gitlab-master.nvidia.com/compute-swqa/cuda-automation-infra/windows-automation/pytest_customized_sections.git@master'),
]


def install_dependencies():
    use_ATP = os.environ.get('USE_ATP')

    for dep in customized_python_dependencies:
        if use_ATP == 'True':
            # use rest api to get token
            cqa_api_token = os.environ['CQA_API_Token']
            res = requests.get(r'http://cqasystem.nvidia.com/api/credential/?ctype=gitlab&name={}'.format(dep[0]), headers={'api-token': cqa_api_token, 'accept': 'application/json'})
            if res.status_code != 200:
                logger.error(res.text)
                raise RuntimeError('Fail to get gitlab token for repo: {}'.format(dep[0]))
            token = json.loads(res.text)['data']
        else:
            # use sys environments to get token
            token = os.environ[dep[1]]

        url = dep[2].format(token)
        if use_ATP == 'True':
            if os.path.isfile(r'C:\PythonLinkTest\Scripts\pip.exe'):
                ret = os.system(r'C:\PythonLinkTest\Scripts\pip.exe install {}'.format(url))
            else:
                ret = os.system(r'C:\Python37\Scripts\pip.exe install {}'.format(url))
        else:
            ret = os.system(r'C:\PythonLinkTest\Scripts\pip.exe install {}'.format(url))
        if ret != 0:
            raise RuntimeError('pip install {} failed'.format(dep[0]))


if __name__ == '__main__':
    try:
        install_dependencies()
    except Exception:
        logger.exception('Fail to install python customized dependencies')
        print('exit execution')
        sys.exit(1)
    else:
        sys.exit(0)
