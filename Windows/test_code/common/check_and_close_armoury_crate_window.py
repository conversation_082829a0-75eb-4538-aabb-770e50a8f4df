import logging
from windows_ui_automation.uia import uia_wrapper, iuia_object, find_first, find_all, get_clickable_point, WindowsUIAutomationError
from windows_ui_automation.mouse import click


# setup logging
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(levelname)s - %(message)s')
ch.setFormatter(formatter)
root_logger.addHandler(ch)
logger = logging.getLogger(__name__)


def check_and_close_armoury_crate_window():
    logger.debug('Try search and close Armoury Crate window if exists')
    desktop = iuia_object.GetRootElement()
    c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'window')

    try:
        all_windows = find_all(desktop, uia_wrapper.TreeScope_Children, c, timeout=60)
    except WindowsUIAutomationError:
        logger.debug('Detect no window under desktop, do nothing')
        return

    logger.debug('Checking all available windows under desktop, there are {} windows under desktop'.format(len(all_windows)))
    for i, window in enumerate(all_windows):
        logger.debug('Check window {}, name: {}'.format(i, window.CurrentName))
        is_target_window = False

        c = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'text')
        try:
            all_text = find_all(window, uia_wrapper.TreeScope_Children, c, timeout=10)
        except WindowsUIAutomationError:
            logger.debug('No text element in this window, not target window, continue with next window')
            continue
        else:
            for text in all_text:
                logger.debug('Text contents: {}'.format(text.CurrentName))
                if 'Armoury Crate' in text.CurrentName:
                    logger.debug('Detect "Armoury Crate" in text content, get target window, try to close this window')
                    is_target_window = True

        if is_target_window:
            logger.debug('Try to get and invoke "No" button')
            c1 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_LocalizedControlTypePropertyId, 'button')
            c2 = iuia_object.CreatePropertyCondition(uia_wrapper.UIA_NamePropertyId, 'No')
            c = iuia_object.CreateAndCondition(c1, c2)
            try:
                no_button = find_first(window, uia_wrapper.TreeScope_Children, c)
            except WindowsUIAutomationError:
                logger.warning('Cannot find "No" button in this window, do nothing')
            else:
                logger.debug('Try click "No" button to close this window')
                button_coords = get_clickable_point(no_button)

                invoke_pattern = no_button.GetCurrentPattern(uia_wrapper.UIA_InvokePatternId).QueryInterface(uia_wrapper.IUIAutomationInvokePattern)
                invoke_pattern.Invoke()

                # in case invoke not working, send a mount click
                click(coords=button_coords)

        else:
            logger.debug('Cannot find  "Armoury Crate" in text content, not target window, continue with next window')

    logger.debug('Checking finished')


if __name__ == '__main__':
    check_and_close_armoury_crate_window()
