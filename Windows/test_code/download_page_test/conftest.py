import os
import logging
import pytest
import shutil
import requests
import re
import urllib.parse

from windows_test_helper.util_webdriver import chromedriver, geckodriver, edgedriver
from windows_test_helper.windows import get_caption


logger = logging.getLogger(__name__)
cuda_version = os.getenv('TK_BRANCH')


@pytest.fixture(scope='session', autouse=True)
def edit_hosts_file(request):
    # get proxy info, see https://confluence.nvidia.com/display/CQS/Tips+for+CUDA+proxy+in+Download+page+test
    res = requests.get('https://shipit.nvidia.com/api/proxy', verify=False)
    res.raise_for_status()
    proxys = res.json()['results']
    logger.debug(proxys)
    proxy_ip_for_CUDA_GA = ''
    proxy_ip_for_CUDA_update = ''
    proxy_target_for_CUDA_GA = ''
    proxy_target_for_CUDA_update = ''
    for proxy_ip_name, proxy_target in proxys.items():
        proxy_ip, proxy_name = proxy_ip_name.split(':')
        if proxy_name == 'CUDA_GA':
            proxy_ip_for_CUDA_GA = proxy_ip
            proxy_target_for_CUDA_GA = proxy_target
        elif proxy_name == 'CUDA_update':
            proxy_ip_for_CUDA_update = proxy_ip
            proxy_target_for_CUDA_update = proxy_target

    # edit hosts
    hosts_file_path = r'C:\Windows\System32\Drivers\etc\hosts'  # backup and restoreb this file
    shutil.copyfile(hosts_file_path, hosts_file_path + '_backup')
    with open(hosts_file_path) as file_input:
        lines = file_input.readlines()

    with open(hosts_file_path, 'w') as f:
        for line in lines:
            if 'developer.download.nvidia.com' not in line:
                f.write(line)
        f.write('\n')
        if os.getenv('RELEASE_LABEL').endswith('0'):
            assert proxy_ip_for_CUDA_GA, 'Cannot get proxy ip for CUDA GA'
            assert os.environ['BUILD_ID'] in proxy_target_for_CUDA_GA.split('/')[-1], 'proxy target does not match with build ID'
            logger.debug('Use proxy {}, pointing to {}'.format(proxy_ip_for_CUDA_GA, proxy_target_for_CUDA_GA))
            f.write('{} developer.download.nvidia.com'.format(proxy_ip_for_CUDA_GA))
        else:
            assert proxy_ip_for_CUDA_update, 'Cannot get proxy ip for CUDA update'
            assert os.environ['BUILD_ID'] in proxy_target_for_CUDA_update.split('/')[-1], 'proxy target does not match with build ID'
            logger.debug('Use proxy {}, pointing to {}'.format(proxy_ip_for_CUDA_update, proxy_target_for_CUDA_update))
            f.write('{} developer.download.nvidia.com'.format(proxy_ip_for_CUDA_update))

    def finalizer():
        logger.info('restore hosts file')
        os.remove(hosts_file_path)
        os.rename(hosts_file_path + '_backup', hosts_file_path)

    request.addfinalizer(finalizer)


@pytest.fixture(scope='session', autouse=True)
def get_download_url():
    dl_json_url = r'https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}/{}/{}/download-page/cuda-downloads-page-{}-{}.json'.\
        format(cuda_version.replace('.', '-'), os.getenv('RELEASE_LABEL'), os.getenv('BUILD_ID'), os.getenv('RELEASE_LABEL').replace('.', '-'), os.getenv('BUILD_ID'))
    dl_text = requests.get(dl_json_url).json()
    dl_url = dl_text['url']
    # dl_url = 'https://cont-stage-devzone-d7.pantheonsite.io/cuda-downloads-page-11-5-0-028'

    username = urllib.parse.quote_plus(os.environ['DZ3_QA_USERNAME'])
    password = urllib.parse.quote_plus(os.environ['DZ3_QA_PASSWORD'])
    dl_url_parts = dl_url.split('//')
    url_with_authentication = dl_url_parts[0] + '//{}:{}@'.format(username, password) + dl_url_parts[1]
    # logger.debug(final_dl_url)
    return url_with_authentication, dl_url


@pytest.fixture(scope='session')
def get_windows_platform():
    os_name = get_caption()

    if re.search('Windows 10', os_name, re.I):
        windows_os = '10'
    elif re.search('Windows 11', os_name, re.I):
        windows_os = '11'
    elif re.search('Windows Server 2016', os_name, re.I):
        windows_os = 'Server 2016'
    elif re.search('Windows Server 2019', os_name, re.I):
        windows_os = 'Server 2019'
    elif re.search('Windows Server 2022', os_name, re.I):
        windows_os = 'Server 2022'
    elif re.search('Windows Server 2025', os_name, re.I):
        windows_os = 'Server 2025'
    logger.info('windows platform of this machine is windows {}'.format(windows_os))
    return windows_os


@pytest.fixture(scope='session', autouse=True)
def prepare_webdriver():

    chrome = chromedriver()
    firefox = geckodriver()
    edge = edgedriver()

    chrome_path = chrome.download_chromedriver()
    firefox_path = firefox.download_geckodriver()
    edge_path = edge.download_edgedriver()
    assert os.path.exists(chrome_path), 'Chrome webdriver does not exist'
    assert os.path.exists(firefox_path), 'Firefox webdriver does not exist'
    assert os.path.exists(edge_path), 'Edge webdriver does not exist'
