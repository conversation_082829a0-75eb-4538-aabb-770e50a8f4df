- name: Disable All firewalls
  community.windows.win_firewall:
    state: disabled
    profiles:
      - Domain
      - Private
      - Public
  tags: disable_firewall
  ignore_errors: true
  notify: Reboot windows machine

- name: Set test signing and disable the prompt when installing driver
  ansible.windows.win_shell: |
    bcdedit.exe -set testsigning on
    bcdedit.exe /set nointegritychecks off
  args:
    chdir: C:\Windows\System32

- name: Set timezone 
  ansible.windows.win_shell: tzutil /s "China Standard Time"
  when: machine_type == "qafarm"

- name: Set an environment variable for all users
  win_environment:
    state: present
    name: NV_DEVTOOLS_UXT_ROLE
    value: internal-qa
    level: machine
  notify: Reboot windows machine

- name: Check login domain
  ansible.builtin.set_fact:
    login_domain: nvidia.com
    login_user: "{{ machineuser.split('@')[0] }}"
  when: "'@nvidia.com' in machineuser"

- name: Check login domain
  ansible.builtin.set_fact:
    login_user: "{{ machineuser }}"
  when: "'@nvidia.com' not in machineuser"

- name: Configure Autologin - 1/4 
  ansible.windows.win_regedit: 
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: DefaultUserName
    data: "{{ login_user }}"
    type: string
  notify: Reboot windows machine

- name: Configure Autologin - 2/4
  ansible.windows.win_regedit: 
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: DefaultPassword
    data: "{{ userpasswd }}"
    type: string
  notify: Reboot windows machine

- name: Configure Autologin - 3/4 (add the domain)
  ansible.windows.win_regedit: 
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: DefaultDomainName
    data: nvidia.com
    type: string
  when: login_domain is defined
  notify: Reboot windows machine

- name: Configure Autologin - 3/4 (remove the name)
  ansible.windows.win_regedit: 
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: DefaultDomainName
    state: absent
  when: login_domain is not defined
  notify: Reboot windows machine

- name: Configure Autologin - 4/4 
  ansible.windows.win_regedit: 
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: AutoAdminLogon
    data: 1 
    type: string
  notify: Reboot windows machine

- name: Enable Developer Mode - 1/2
  ansible.windows.win_regedit:
    path: HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppModelUnlock
    name: AllowAllTrustedApps
    data: 1
    type: dword
    state: present
  notify: Reboot windows machine

- name: Enable Developer Mode - 2/2
  ansible.windows.win_regedit:
    path: HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppModelUnlock
    name: AllowDevelopmentWithoutDevLicense
    data: 1
    type: dword
    state: present

- name: Enable long paths used in file system
  ansible.windows.win_regedit:
    path: HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem
    name: LongPathsEnabled
    data: 1
    type: dword
    state: present
  notify: Reboot windows machine

- name: Add DNS search list
  ansible.windows.win_regedit:
    path: HKLM:\SYSTEM\CurrentControlSet\services\Tcpip\Parameters
    name: SearchList
    data: "zpk.gtl.nvidia.com,nvidia.com"
    type: string
    state: present

- name: Exclude driver from Windows Update
  ansible.windows.win_regedit:
    path: HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate
    name: ExcludeWUDriversInQualityUpdate
    data: 1
    type: dword
    state: present

- name: turn UAC off
  ansible.windows.win_regedit:
    path: HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\policies\system
    name: EnableLUA
    data: 0
    type: dword
    state: present
  notify: Reboot windows machine

- name: Power option - Never sleep
  ansible.windows.win_shell: |
    powercfg /x -hibernate-timeout-ac 0
    powercfg /x -disk-timeout-ac 0
    powercfg /x -monitor-timeout-ac 0
    Powercfg /x -standby-timeout-ac 0

- name: Reboot if needed
  ansible.builtin.meta: flush_handlers