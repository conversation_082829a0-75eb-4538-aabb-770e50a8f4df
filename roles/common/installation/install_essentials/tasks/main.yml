- name: Install essentials for Ubuntu / Debian
  ansible.builtin.import_tasks: ubuntu.yml
  become: yes
  when: ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian'

- name: Install essentials for Redhat/Rocky/Fedora
  ansible.builtin.import_tasks: redhat_rocky.yml
  become: yes
  when: ansible_distribution == 'RedHat' or ansible_distribution == 'Rocky' or ansible_distribution == 'Fedora'

- name: Install essentials for Windows
  ansible.builtin.import_tasks: windows.yml
  when: ansible_os_family == 'Windows'

- name: Install gtl_tools
  ansible.builtin.import_tasks: install_gtl_tools.yml

- name: Install gtlfs
  ansible.builtin.import_tasks: install_gtlfs.yml