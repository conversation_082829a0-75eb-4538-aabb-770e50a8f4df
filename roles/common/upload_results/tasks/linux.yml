- name: Upload the result dir to gtl on Linux
  ansible.builtin.shell: |
    gtl_build_upload --gtl-token "{{ NVM_GTLAPI_TOKEN }}" --source-dir {{ result_dir }} --app-name "farm_results" --build-version "{{ result_gtl }}" --exact-version --evolve --app-type WORKSTATION
  environment:
    PATH: "{{ ansible_env.HOME }}/gtl_venv/bin:{{ ansible_env.HOME }}/.venv/bin:{{ ansible_env.HOME }}/.local/bin:{{ ansible_env.PATH }}:{{ gtltoolspath }}"
  register: upload_ret
  retries: 5
  until: upload_ret is not failed
  delay: 30

- name: clear result dir after uploadting to gtl
  ansible.builtin.file:
    path: "{{ '~/farm_results' | expanduser }}"
    state: absent
  become: yes
  ignore_errors: yes
  when: upload_ret.rc == 0

- name: set gtl results meta new status for sh01 rerun tasks
  delegate_to: localhost
  ansible.builtin.shell: 'python3 colossus_ansiblebooks/common/files/update_build_meta.py -a "farm_results" -b "{{ result_gtl }}" -t "{{ NVM_GTLAPI_TOKEN }}"'
  ignore_errors: no