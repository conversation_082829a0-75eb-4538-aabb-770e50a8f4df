- name: Install CTK {{ cuda_ver }}
  block:

  - name: create tmp folder
    ansible.windows.win_file:
      path: C:\tmp
      state: directory

  - name: copy colossus_ansiblebooks/files/PsExec64.exe to C:\tmp
    win_copy:
      src: colossus_ansiblebooks/files/PsExec64.exe
      dest: C:\tmp
    loop:
      - 'PsExec64.exe'

  - name: create install_ctk.bat
    win_shell: |
      echo python C:\sanity_test_windows\test_sanity_win.py -e GENERAL -v {{ cuda_ver }} -kit {{ kit_option }} > C:\sanity_test_windows\install_ctk.bat
    args:
      executable: cmd.exe

  - name: empty log file
    win_shell: |
      echo  > C:\sanity_test_windows\execution_log\sanity_test.log
    args:
      executable: cmd.exe

  - name: run test - run_win_suites.bat
    ansible.builtin.win_psexec:
      chdir:  C:\sanity_test_windows\
      interactive: yes
      elevated: true
      username: '{{ machineuser }}'
      password: '{{ userpasswd }}'
      session: 1
      executable: C:\tmp\PsExec64.exe
      command: 'cmd /c "C:\sanity_test_windows\install_ctk.bat"'
      timeout: 10800
    ignore_errors: yes

  - name: Get log "C:\sanity_test_windows\execution_log\sanity_test.log"
    win_shell: type C:\sanity_test_windows\execution_log\sanity_test.log
    register: file_content

  - name: Check if installation is successful
    ansible.builtin.fail:
      msg: "The installation is not successful"
    when: "'install driver(latest) successfully' not in file_content.stdout"
    ignore_errors: no

  rescue:
  - name: Print when errors
    debug:
      msg: 'Install CTK  {{ cuda_ver }}  and driver failed, please check'

