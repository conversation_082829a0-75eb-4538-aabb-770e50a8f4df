- name: remove {{ test_work_dir_win_tmp }} directory
  win_file:
    path: "{{ test_work_dir_win_tmp  }}"
    state: absent
    ignore: yes
  vars:
    test_work_dir_win_tmp: '\\?\{{ test_work_dir_win}}'       #extend long path as sample test

- name: Create {{ test_work_dir_win }} directory
  win_file:
    path: "{{ test_work_dir_win  }}"
    state: directory
    recurse: yes

- name: remove {{ test_run_dir_win_tmp }} directory
  win_file:
    path: "{{ test_run_dir_win_tmp }}"
    state: absent
    ignore: yes
  vars:
    test_run_dir_win_tmp: '\\?\{{ test_run_dir_win}}'

- name: Create {{ test_run_dir_win }} directory
  win_file:
    path: "{{ test_run_dir_win }}"
    state: directory
    recurse: yes