- name: Remove any installed nsys (apt)
  apt:
    name: 
      - nsight-systems*
    state: absent
  ignore_errors: yes
  become: yes
  when: ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian'

- name: Remove any installed nsys (yum)
  yum:
    name:
      - nsight-systems*
    state: absent
  ignore_errors: yes
  become: yes
  when: ansible_distribution == 'RedHat' or ansible_distribution == 'Fedora' or ansible_distribution == 'Rocky' or ansible_distribution == "Kylin"


- name: Download deb file (x86_64, Ubuntu / Debian)
  get_url:
    # url: "{{ download_link }}/nsight-systems-DVS-1_amd64.deb"
    url: "{{ download_link }}"
    dest: "/tmp/nsight-systems-DVS-1_amd64.deb"
    mode: '0644'
  when: (ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian') and ansible_architecture == 'x86_64'

- name: Install deb package (x86_64, Ubuntu / Debian)
  apt:
    deb: "/tmp/nsight-systems-DVS-1_amd64.deb"
  when: (ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian') and ansible_architecture == 'x86_64'
  become: yes

- name: Download RPM file (x86_64, RedHat / Fedora)
  get_url:
    # url: "{{ download_link }}/nsight-systems-DVS-0.x86_64.rpm"
    url: "{{ download_link }}"
    dest: "/tmp/nsight-systems-DVS-0.x86_64.rpm"
    mode: '0644'
  when: (ansible_distribution == 'RedHat' or ansible_distribution == 'Fedora' or ansible_distribution == 'Rocky' or ansible_distribution == "Kylin") and ansible_architecture == 'x86_64'

- name: Install RPM file (x86_64, RedHat / Fedora)
  yum:
    name: /tmp/nsight-systems-DVS-0.x86_64.rpm
    state: present 
    disable_gpg_check: true
  become: yes 
  when: (ansible_distribution == 'RedHat' or ansible_distribution == 'Fedora' or ansible_distribution == 'Rocky' or ansible_distribution == "Kylin") and ansible_architecture == 'x86_64'


- name: Download RPM file (aarch64, RedHat / Fedora)
  get_url:
    # url: "{{ download_link }}/nsight-systems-DVS-0.aarch64.rpm"
    url: "{{ download_link }}"
    dest: "/tmp/nsight-systems-DVS-0.aarch64.rpm"
    mode: '0644'
  when: (ansible_distribution == 'RedHat' or ansible_distribution == 'Fedora' or ansible_distribution == 'Rocky' or ansible_distribution == "Kylin") and ansible_architecture == 'aarch64'

- name: Install RPM file (aarch64, RedHat / Fedora)
  yum:
    name: /tmp/nsight-systems-DVS-0.aarch64.rpm
    state: present 
    disable_gpg_check: true
  become: yes 
  when: (ansible_distribution == 'RedHat' or ansible_distribution == 'Fedora' or ansible_distribution == 'Rocky' or ansible_distribution == "Kylin") and ansible_architecture == 'aarch64'
