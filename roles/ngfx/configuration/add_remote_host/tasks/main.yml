- name: Display the remote host machine name
  ansible.builtin.debug:
    msg: "Remote host: {{ vars[remote_var_name] }}"

- name: Get remote host machine info from machine API
  ansible.windows.win_uri:
    url: http://qafarm-sh01:5052/devtoolscolossusmachine/{{ vars[remote_var_name] }}
    method: GET
    return_content: yes
    headers:
      Accept: "application/json"
  register: response
  retries: 5
  delay: 10
  until: response.status_code == 200

- name: Set fact remote machine info
  ansible.builtin.set_fact:
    remote_host_info: "{{ response.json }}"

- name: Add remote host to ansible host
  ansible.builtin.add_host:
    name: ngfx_remote_host
    ansible_host: "{{ remote_host_info.machine_ip }}"
    ansible_user: "{{ remote_host_info.login }}"
    ansible_ssh_pass: "{{ remote_host_info.password }}"
    ansible_connection: psrp
    ansible_psrp_cert_validation: ignore
    ansible_host_key_checking: false
    ansible_command_timeout: 180