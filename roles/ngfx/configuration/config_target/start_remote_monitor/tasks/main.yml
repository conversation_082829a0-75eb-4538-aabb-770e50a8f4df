- name: Find Nomad remote monitor
  ansible.windows.win_find:
    paths: "{{ nomad_installation_dir }}"
    recurse: yes
    patterns:
      - 'nv-nsight-remote-monitor.exe'
  register: monitor_files_found

- name: Set fact nomad_remote_monitor
  ansible.builtin.set_fact:
    nomad_remote_monitor: "{{ (monitor_files_found.files | first).path }}"

- name: Start remote monitor on target machine (aka. job assigned machine)
  ansible.builtin.win_psexec:
    interactive: yes
    session: 1
    command: "\"{{ nomad_remote_monitor }}\""
    username: "{{ ansible_user }}"
    password: "{{ ansible_password }}"
    elevated: true
    wait: false