- name: create the folder /localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}/{{ suite }}
  ansible.builtin.file:
    path: "/localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}/{{ suite }}"
    state: directory

- name: copy results from /localhome/local-devtools_teste/nvidia_data/{{ project_name }}/data/TESTINSS.DAT to /localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}
  ansible.builtin.copy:
    src: "{{ item }}"
    dest: "/localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}/{{ suite }}"
    remote_src: true
  with_items:
    - "/localhome/local-devtools_teste/nvidia_data/{{ project_name }}/data/TESTINSS.DAT"

- name: set fact
  ansible.builtin.set_fact:
    src_path: /localhome/local-devtools_teste/NomadAutomation/Apps
    dest_path: "/localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}"
    file_name: TESTINSS.DAT

- name: Find file_name
  ansible.builtin.find:
    paths: "{{ src_path }}"
    file_type: file
    patterns: TESTINSS.DAT
    recurse: yes
  register: res

#- debug:
#    msg: "src_path: {{ item.path }}, keyword: {{ item.path[(src_path | length) : -(file_name | length)] }}, dest_path: {{ dest_path }}/{{ item.path[(src_path | length) : -(file_name | length)] }}"
#  with_items: "{{ res['files'] }}"

- name: create dest folders for file_name
  ansible.builtin.file: 
    path: "{{ dest_path }}/{{ item.path[(src_path | length) : -(file_name | length)] }}"
    state: directory
  with_items: "{{ res['files'] }}"

- name: copy results from src_path to /localhome/local-devtools_teste/code_coverage_export/{{ dir_name }}
  ansible.builtin.copy:
    src: "{{ item.path }}"
    dest: "{{ dest_path }}/{{ item.path[(src_path | length) : ] }}"
    remote_src: true
  with_items: "{{ res['files'] }}"