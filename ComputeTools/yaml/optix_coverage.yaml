global:
  env:
    CU<PERSON>_MAJOR: 13
    CUDA_MINOR: 0
    CUDA_REVISION: 0
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: r580
    DRIVER_VERSION: 580.58
    DEVICES: 0
    MIG: None
    VGPU: none
    DVS_BUILD: D
    PLATFORM: x86
    INSTALLER: runfile
    HOST_HOME: /home/<USER>
    SAMPLE_OPTIX: 1
    SAMPLE: optix
    CQA_USER: Y3FhdXNlcg
    CQA_PASSWORD: Y3FhdGVzdA
    TOOLS_HOME: ${HOST_HOME}/tesla_automation/${SAMPLE}_coverage_device${DEVICES}
    TOOLS_HOME1: ${HOST_HOME}/tesla_automation
    DATE: 202507011317
    DATE1: 20250701
    SAMPLE_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples
    SAMPLE_BIN_PATH: ${SAMPLE_PATH}/bin/x86_64/linux/release
    SAMPLE_1_PATH: ${SAMPLE_PATH}/Samples/1_Utilities
    SAMPLE_1_PATH1: ${SAMPLE_PATH}/1_Utilities
    SAMPLE_1_PATH2: ${SAMPLE_PATH}/build/Samples/1_Utilities
    CUDA_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}
    NVCC_PATH: ${CUDA_PATH}/bin
    SANITIZER_BIN_PATH: ${CUDA_PATH}/bin
    HOST_PASSWORD: cuda
    CUPTI_EXTRACT_PATH: linux-desktop-glibc_2_11_3-x64
    OUTPUT_FLAG: 1
    OPTIX_PATH: ${HOST_HOME}/optix
    OPTIX_BIN_PATH: ${OPTIX_PATH}/SDK/bin
    OPTIX_LIB_PATH: ${OPTIX_PATH}/SDK/lib
    CUPTI_DOWNLOAD_PATH: ${HOST_HOME}/cupti_${PLATFORM}_${DATE1}
    SANITIZER_OPTIX_LOG_PATH: ${TOOLS_HOME}/sanitizer_${SAMPLE}_%s_result_${DATE}
    CUPTI_TRACE_OPTIX_LOG_PATH: ${TOOLS_HOME}/cupti_trace_${SAMPLE}_result_${DATE}
    CUPTI_PROFILE_INJECTION_OPTIX_LOG_PATH: ${TOOLS_HOME}/cupti_profile_${SAMPLE}_result_${DATE}
    CUPTI_PROFILE_INJECTION_OPTIX_OLD_API_LOG_PATH: ${TOOLS_HOME}/cupti_profile_old_api_${SAMPLE}_result_${DATE}
    CUPTI_TRACE_INJECTION_OPTIX_LOG_PATH: ${TOOLS_HOME}/cupti_trace_${SAMPLE}_result_${DATE}
    NCU_OPTIX_LOG_PATH: ${TOOLS_HOME}/ncu_${SAMPLE}_result_${DATE}
    NCU_OPTION_OPTIX_LOG_PATH: ${TOOLS_HOME}/ncu_${SAMPLE}_single_result_${DATE}
    CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_LOG_PATH: ${TOOLS_HOME}/cupti_trace_injection_graphic_sanity_result_${DATE}
    CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES_LOG_PATH: ${TOOLS_HOME}/cupti_trace_injection_graphic_sanity_hes_result_${DATE}
    CUPTI_PROFILE_INJECTION_OPTIX_BASIC_LOG_PATH: ${TOOLS_HOME}/cupti_profile_injection_optix_basic_result_${DATE}
    CUDA_GDB_GRAPHIC_LOG_PATH: ${TOOLS_HOME}/cuda_gdb_graphic_result_${DATE}
    CUDA_GDB_OPTIX_LOG_PATH: ${TOOLS_HOME}/cuda_gdb_optix_result_${DATE}
    CUPTI_PATH1: ${HOST_HOME}/cupti_x86_${DATE1}
    CUPTI_PATH2: ${HOST_HOME}/cupti_arm_${DATE1}
    CUPTI_RUN_PATH1: ${CUPTI_PATH1}/host/linux-desktop-GNU_glibc_2_11_3-x64
    CUPTI_TARGET_PATH1: ${CUPTI_PATH1}/target/linux-desktop-GNU_glibc_2_11_3-x64
    CUPTI_RUN_PATH2: ${CUPTI_PATH2}/host/linux-desktop-t210-a64
    CUPTI_TARGET_PATH2: ${CUPTI_PATH2}/target/linux-desktop-t210-a64
    LD_LIBRARY: "`pwd`:${CUDA_PATH}/lib64:$LD_LIBRARY_PATH"


# get the SM value of GPU and name of GPU
PREPARE1:
    CMD1: "cd %s/deviceQuery; make clean; make"
    CMD2: "cd %s/deviceQuery; make clean; make; ./deviceQuery | grep 'Device 0'"
    CMD3: "cd %s/deviceQuery; make clean; make; ./deviceQuery |
          grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'"
    CMD4: "cd %s/deviceQuery; make clean; make;./deviceQuery | grep 'Device 0'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'"


CUPTI_SMOKE_ADDR:
  CUPTI_10.1: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA10.1_Release_Linux_Cupti/
  CUPTI_10.2: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA10.2_Release_Linux_Cupti/
  CUPTI_11: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Linux_Cupti/
  CUPTI_10.1_P9: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_CUDA10.1_Release_Linux_Cupti_PPC64LE/
  CUPTI_10.2_P9: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_CUDA10.2_Release_Linux_Cupti_PPC64LE/
  CUPTI_11_P9: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Linux_Cupti_PPC64LE/
  CUPTI_10.1_ARM: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_CUDA11.0_Release_Armserver_Cupti/
  CUPTI_10.2_ARM: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA10.2_Release_Balboa_Cupti/
  CUPTI_11_ARM: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Armserver_Cupti/

NCU_OPTION:
  #OPTIONS1: --metrics gpc__cycles_elapsed.sum
  OPTIONS1: --metrics sm__instruction_throughput.avg.pct_of_peak_sustained_elapsed
  OPTIONS2: --replay-mode application

PREPARE:
  CMD1: "cd %s; wget --user %s --password %s %s/optix/optixDenoiser/color.exr"
  CMD2: "cd %s; wget --user %s --password %s %s/optix/%s"
  CMD3: "cd %s; wget --user %s --password %s %s/optix/optixCompileWithTasks/synthetic.ptx"
  CMD4: "cd %s; wget --user %s --password %s %s/optix/optixOpticalFlow/%s"

SANITIZER_OPTIX:
  LOG_NAME: ${SANITIZER_OPTIX_LOG_PATH}/sanitizer_${SAMPLE}_%s.log
  CHECK_POINT: "ERROR SUMMARY: 0 errors"
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  CHECK_POINT2: "LEAK SUMMARY: 0 bytes leaked in 0 allocations"


CUPTI_PROFILE_INJECTION_BASIC_CHECK:
  METRIC_LIST: "l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed_op_global_ld.sum,sm__ctas_launched.sum,sm__mioc_inst_issued.sum"
  RANGE_REPLAY_MODE: "-r auto -e kernel,-r auto -e application,-r user -e application"
  STEP1:
    # Kernel replay profiling with HWPM/SASS/SMPC metrics + range_replay_mode
    CMD1: cd {};LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH ./profiler_injection_test -m {} {} -a ./{}
  CHECK_POINT1: 'ERROR'
  CHECK_POINT2: 'n/a'
  CHECK_POINT3: 'fail'



NCU_OPTIX:
  LOG_NAME: ${NCU_OPTIX_LOG_PATH}/ncu_${SAMPLE}.log
  LOG_NAME1: ${NCU_OPTION_OPTIX_LOG_PATH}/ncu_${SAMPLE}_%s.log

CUPTI_TRACE_OPTIX:
  LOG_NAME: ${CUPTI_TRACE_OPTIX_LOG_PATH}/cupti_trace_${SAMPLE}.log

CUPTI_PROFILE_INJECTION_OPTIX:
  LOG_NAME1: "${CUPTI_PROFILE_INJECTION_OPTIX_LOG_PATH}/cupti_profile_injection_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r %s/* %s/
    CMD1: cp -r %s/target %s/../../
    CMD2: cp -r %s/host %s/../../
  STEP1:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -o counterdata -r auto -e kernel -j step1_%s.json -a './%s' | tee %s_step1.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -r auto -e kernel -j step1_%s.json %s -a './%s' | tee %s_step1.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__sass_inst_executed.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step1.txt"
    CMD1_3: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__sass_inst_executed.sum,sm__mioc_inst_issued.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step1.txt"
  STEP2:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -o counterdata -r auto -e kernel -j step2_%s.json -a './%s' | tee %s_step2.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r auto -e kernel -j step2_%s.json %s -a './%s' | tee %s_step2.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step2.txt"
  STEP3:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -o counterdata -r user -e application -j step3_%s.json -a './%s' | tee %s_step3.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r user -e application -j step3_%s.json %s -a './%s' | tee %s_step3.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r user -e application %s -a './%s' | tee %s_step3.txt"
  CHECK_POINT: 'ERROR'
  CHECK_POINT1: 'n/a'
  CHECK_POINT2: 'N/A'
  CHECK_POINT3: 'nan'

#CUPTI profile injection - Sample Coverage(graphics) -- old profiler API 3897007
CUPTI_PROFILE_INJECTION_OPTIX_OLD_API:
  LOG_NAME1: "${CUPTI_PROFILE_INJECTION_OPTIX_OLD_API_LOG_PATH}/cupti_profile_injection_old_api_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r %s/* %s/
    CMD1: cp -r %s/target %s/../../
    CMD2: cp -r %s/host %s/../../
  STEP1:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -o counterdata -r auto -e kernel -j step1_%s.json -a './%s' | tee %s_step1.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -r auto -e kernel -j step1_%s.json %s -a './%s' | tee %s_step1.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__sass_inst_executed.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step1.txt"
    CMD1_3: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed.sum,sm__sass_inst_executed.sum,sm__mioc_inst_issued.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step1.txt"
  STEP2:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -o counterdata -r auto -e kernel -j step2_%s.json -a './%s' | tee %s_step2.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r auto -e kernel -j step2_%s.json %s -a './%s' | tee %s_step2.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r auto -e kernel %s -a './%s' | tee %s_step2.txt"
  STEP3:
    CMD1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -o counterdata -r user -e application -j step3_%s.json -a './%s' | tee %s_step3.txt"
    CMD1_1: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r user -e application -j step3_%s.json %s -a './%s' | tee %s_step3.txt"
    CMD1_2: "cd %s; LD_LIBRARY_PATH=${LD_LIBRARY} CUPTI_RANGE_PROFILING_DISABLE=1 ./profiler_injection_test -m sm__ctas_launched.sum -n 5 -r user -e application %s -a './%s' | tee %s_step3.txt"
  CHECK_POINT: 'ERROR'
  CHECK_POINT1: 'n/a'
  CHECK_POINT2: 'N/A'
  CHECK_POINT3: 'nan'


CUPTI_PROFILE_INJECTION_OPTIX_BASIC:
  LOG_NAME1: "${CUPTI_PROFILE_INJECTION_OPTIX_BASIC_LOG_PATH}/cupti_profile_injection_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r {}/* {}/
    CMD_1: cp -r {}/* {}/; cp ${SAMPLE_PATH}/Samples/4_CUDA_Libraries/cudaNvSci/* {}/;cp ${SAMPLE_PATH}/Samples/5_Domain_Specific/volumeRender/data/* {}/;cp ${SAMPLE_PATH}/Samples/3_CUDA_Features/bindlessTexture/data/*  ${SAMPLE_PATH}/Samples/5_Domain_Specific/vulkanImageCUDA/* ${SAMPLE_PATH}/Samples/5_Domain_Specific/smokeParticles/data/* ${SAMPLE_PATH}/Samples/5_Domain_Specific/recursiveGaussian/data/* ${SAMPLE_PATH}/Samples/4_CUDA_Libraries/oceanFFT/data/* ${SAMPLE_PATH}/Samples/2_Concepts_and_Techniques/imageDenoising/data/* ${SAMPLE_PATH}/Samples/5_Domain_Specific/bilateralFilter/data/* {}/
    CMD1: cp -r {}/target {}/../../
    CMD2: cp -r {}/host {}/../../
  METRIC_LIST: "l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed_op_global_ld.sum,sm__ctas_launched.sum,sm__mioc_inst_issued.sum"
  RANGE_REPLAY_MODE: "-r auto -e kernel,-r auto -e application,-r user -e application"
  STEP:
      # Kernel replay profiling with HWPM/SASS/SMPC metrics + range_replay_mode
      CMD1: cd {};LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH ./profiler_injection_test -m {} {} -j step_cmd{}_{}.json {} -a ./{}| tee {}_step{}_cmd{}.txt
  STEP1:
    CMD1: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed_op_global_ld.sum,sm__ctas_launched.sum -n 5 -r auto -e kernel -j step1_cmd1_{}.json {} -a './{}' | tee {}_step1_cmd1.txt"
    CMD2: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__mioc_inst_issued.sum -n 5 -r auto -e kernel -j step1_cmd2_{}.json {} -a './{}' | tee {}_step1_cmd2.txt"
  STEP2:
    CMD1: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed_op_global_ld.sum,sm__ctas_launched.sum -n 5 -r auto -e application -j step2_cmd1_{}.json {} -a './{}' | tee {}_step2_cmd1.txt"
    CMD2: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__mioc_inst_issued.sum -n 5 -r auto -e application -j step2_cmd2_{}.json {} -a './{}' | tee {}_step2_cmd2.txt"
  STEP3:
    CMD1: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m l1tex__t_requests_pipe_lsu_mem_global_op_ld.sum,sm__sass_inst_executed_op_global_ld.sum,sm__ctas_launched.sum -n 5 -r user -e application -j step3_cmd1_{}.json {} -a './{}' | tee {}_step3_cmd1.txt"
    # optinx sample can't support CMD2, graphic sample support
    CMD2: "cd {}; LD_LIBRARY_PATH=${LD_LIBRARY} ./profiler_injection_test -m sm__mioc_inst_issued.sum -n 5 -r user -e application -j step3_cmd2_{}.json {} -a './{}' | tee {}_step3_cmd2.txt"
  CHECK_OUTPUT:
    CMD1: cd {}; ls *_step_cmd*.txt
  CHECK_POINT: 'ERR'
  CHECK_POINT1: 'n/a'
  CHECK_POINT2: 'fail'
  CHECK_POINT3: 'nan'


CUPTI_TRACE_INJECTION_OPTIX:
  LOG_NAME: "${CUPTI_TRACE_INJECTION_OPTIX_LOG_PATH}/cupti_trace_injection_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r %s/* %s/
    CMD1: cd %s; rm test_%s.json; touch test_%s.json
  STEP1:
    CMD: cd %s; python3 CuptiTestInjection.py --test-list-file=test_%s.json 2>&1| tee %s_trace_injection.txt
    CMD1: cd %s; python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection.txt
  STEP1_1:
    CMD: cd %s; python3 CuptiTestInjection.py --cpuarch=aarch64sbsa --test-list-file=test_%s.json 2>&1| tee %s_trace_injection.txt
    CMD1: cd %s; python3 CuptiSmoke.py --arch=aarch64sbsa --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection.txt
  STEP1_2:
    CMD: cd %s; python3 CuptiTestInjection.py --cpuarch=ppc64le --test-list-file=test_%s.json 2>&1| tee %s_trace_injection.txt
    CMD1: cd %s; python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection.txt
  STEP2:
    CMD1: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $9}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD1_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& FAILED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $3}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& PASSED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"

CUPTI_TRACE_INJECTION_GRAPHIC_SANITY:
  LOG_NAME: "${CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_PATH}/cupti_trace_injection_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r %s/optixNVLink %s/
    CMD1: cd {}; cp simpleGL oceanFFT postProcessGL {}/; cp ${SAMPLE_BIN_PATH}/../../../../Samples/4_CUDA_Libraries/oceanFFT/data/* {}/
    CMD2: cp -r {}/target {}/../../; cp -r {}/host {}/../../
  STEP1:
    CMD: cd %s; python3 CuptiTestInjection.py --test-list-file=test_%s.json 2>&1| tee %s_trace_injection_sanity.txt
    CMD1: cd %s; python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection_sanity.txt
  STEP1_1:
    CMD: cd %s; python3 CuptiTestInjection.py --cpuarch=aarch64sbsa --test-list-file=test_%s.json 2>&1| tee %s_trace_injection_sanity.txt
    CMD1: cd %s; python3 CuptiSmoke.py --arch=aarch64sbsa --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection_sanity.txt
  STEP1_2:
    CMD: cd %s; python3 CuptiTestInjection.py --cpuarch=ppc64le --test-list-file=test_%s.json 2>&1| tee %s_trace_injection.txt
    CMD1: cd %s; python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection.txt
  STEP2:
    CMD1: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $9}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD1_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& FAILED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $3}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& PASSED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"


CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES:
  LOG_NAME: "${CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES_PATH}/cupti_trace_injection_${SAMPLE}.log"
  PREPARE:
    CMD: cp -r %s/optixCurves %s/
    CMD1: cd {}; cp simpleGL oceanFFT postProcessGL {}/; cp ${SAMPLE_BIN_PATH}/../../../../Samples/4_CUDA_Libraries/oceanFFT/data/* {}/
    CMD2: cp -r {}/target {}/../../; cp -r {}/host {}/../../
  STEP1:
    CMD: cd %s; export CUPTI_ENABLE_HW_TRACE=1;python3 CuptiTestInjection.py --test-list-file=test_%s.json 2>&1| tee %s_trace_injection_sanity.txt; unset CUPTI_ENABLE_HW_TRACE
    CMD1: cd %s; export CUPTI_ENABLE_HW_TRACE=1;python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection_sanity.txt; unset CUPTI_ENABLE_HW_TRACE
  STEP1_1:
    CMD: cd %s; export CUPTI_ENABLE_HW_TRACE=1;python3 CuptiTestInjection.py --cpuarch=aarch64sbsa --test-list-file=test_%s.json 2>&1| tee %s_trace_injection_sanity.txt; unset CUPTI_ENABLE_HW_TRACE
    CMD1: cd %s; export CUPTI_ENABLE_HW_TRACE=1;python3 CuptiSmoke.py --arch=aarch64sbsa --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection_sanity.txt; unset CUPTI_ENABLE_HW_TRACE
  STEP1_2:
    CMD: cd %s; python3 CuptiTestInjection.py --cpuarch=ppc64le --test-list-file=test_%s.json 2>&1| tee %s_trace_injection.txt
    CMD1: cd %s; python3 CuptiSmoke.py --testconfig tracing-injection --testlist test_%s.json 2>&1| tee %s_trace_injection.txt
  STEP2:
    CMD1: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $9}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD1_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& FAILED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2: "cd %s; cp %s_trace_injection.log %s; grep 'Test results:' %s_trace_injection.log | awk -F ' ' '{print $3}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
    CMD2_1: "cd %s; cp %s_trace_injection.log %s; grep '&&&& PASSED' %s_trace_injection.log |wc -l|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
