global:
  env:
    CUDA_MAJOR: 13
    CUDA_MINOR: 0
    CUDA_REVISION: 35
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: r580
    DRIVER_VERSION: 580.56
    DEVICES: 1
    MIG: None
    VGPU: None
    DVS_BUILD: I
    PLATFORM: x86
    INSTALLER: runfile
    HOST_HOME: /home/<USER>
    TOOLS_HOME: ${HOST_HOME}/tesla_automation/cuda_gdb_device${DEVICES}
    DATE: 202504221110
    DATE1: 20250422
    CQA_USER: Y3FhdXNlcg
    CQA_PASSWORD: Y3FhdGVzdA
    HOST_P4_PATH: ${HOST_HOME}/p4
    SAMPLE_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples
    SAMPLE_BIN_PATH: ${SAMPLE_PATH}/bin/x86_64/linux/debug
    SAMPLE_0_PATH: ${SAMPLE_PATH}/Samples/0_Introduction
    SAMPLE_0_PATH1: ${SAMPLE_PATH}/0_Simple
    SAMPLE_1_PATH: ${SAMPLE_PATH}/Samples/1_Utilities
    SAMPLE_1_PATH1: ${SAMPLE_PATH}/1_Utilities
    SAMPLE_0_PATH2: ${SAMPLE_PATH}/build/Samples/0_Introduction
    SAMPLE_1_PATH2: ${SAMPLE_PATH}/build/Samples/1_Utilities
    CUDA_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}
    DEBUGGER_DOWNLOAD_PATH: ${HOST_HOME}/debugger_${PLATFORM}_{}
    DEBUGGER_RUN_PATH: ${DEBUGGER_DOWNLOAD_PATH}/debugger/testsuite
    HOST_PASSWORD: cuda
    NVCC_PATH: ${CUDA_PATH}/bin
    OUTPUT_FLAG: 1
    OPTIX_PATH: ${HOST_HOME}/optix
    OPTIX_BIN_PATH: ${OPTIX_PATH}/SDK/bin
    OPTIX_LIB_PATH: ${OPTIX_PATH}/SDK/lib
    OPTIX_FLAG: 1
    CUDA_GDB_SANITY_PATH: ${TOOLS_HOME}/1820917_cuda_gdb_sanity_result_${DATE}
    CUDA_GDB_REMOTE_PATH: ${TOOLS_HOME}/1820919_cuda_gdb_remote_result_${DATE}
    CUDA_GDB_COREDUMP_1820921_PATH: ${TOOLS_HOME}/1820921_cuda_gdb_coredump_result_${DATE}
    CUDA_GDB_RCCA_3820537_PATH: ${TOOLS_HOME}/3820537_cuda_gdb_rcca_3820537_result_${DATE}
    CUDA_GDB_RCCA_3825550_PATH: ${TOOLS_HOME}/3825550_cuda_gdb_rcca_3825550_result_${DATE}
    CUDA_GDB_RCCA_3679456_PATH: ${TOOLS_HOME}/3679456_cuda_gdb_rcca_3679456_result_${DATE}
    CUDA_GDB_RCCA_3773016_PATH: ${TOOLS_HOME}/3773016_cuda_gdb_rcca_3773016_result_${DATE}
    CUDA_GDB_RCCA_3692213_PATH: ${TOOLS_HOME}/3692213_cuda_gdb_rcca_3692213_result_${DATE}
    CUDA_GDB_RCCA_3671381_PATH: ${TOOLS_HOME}/3671381_cuda_gdb_rcca_3671381_result_${DATE}
    CUDA_GDB_MPS_MULTIUSER_PATH: ${TOOLS_HOME}/3665535_cuda_gdb_mps_multiuser_result_${DATE}
    CUDA_GDB_OPTIX_PATH: ${TOOLS_HOME}/3794754_cuda_gdb_optix_result_${DATE}
    CUDA_GDB_RCCA_4040416_PATH: ${TOOLS_HOME}/4040416_cuda_gdb_rcca_4040416_result_${DATE}
    CUDA_GDB_OPTION_4019656_PATH: ${TOOLS_HOME}/4019656_cuda_gdb_option_4019656_result_${DATE}
    CUDA_GDB_RESTORE_GPU_THREAD_PATH: ${TOOLS_HOME}/5120057_cuda_gdb_restore_gpu_thread_result_${DATE}
    SUDO_EXECUTE: echo ${HOST_PASSWORD}|sudo -S PATH=${CUDA_PATH}/bin:"$PATH"
    SECOND_GPU: ~/tesla_automation/2485834
    HPC_PGI: ************:/opt/nvidia
    PGI_VERSION: 24.11
    PGI_ENV: export MANPATH=/opt/nvidia/hpc_sdk/Linux_x86_64/${PGI_VERSION}/compilers/man:$MANPATH;
      export PATH=/opt/nvidia/hpc_sdk/Linux_x86_64/${PGI_VERSION}/compilers/include:/opt/nvidia/hpc_sdk/Linux_x86_64/${PGI_VERSION}/compilers/bin:$PATH;
      export PGI=/opt/nvidia/hpc_sdk; export pgi_version=${PGI_VERSION}
    UNSET_PGI_ENV: unset MANPATH; export PATH=${CUDA_PATH}/bin:/opt/cmake/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin;
      unset PGI; unset pgi_version



# get the SM value of GPU and name of GPU
PREPARE:
    CMD1: "cd %s/deviceQuery; make clean; make"
    CMD2: "cd %s/deviceQuery;make clean; make; ./deviceQuery | grep 'Device 0'"
    CMD3: "cd %s/deviceQuery; make clean; make; ./deviceQuery |
          grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'"
    CMD4: "cd %s/deviceQuery;make clean; make; ./deviceQuery | grep 'Device 0'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'"
    CMD5: cd %s/deviceQuery;make clean; make; ./deviceQuery

# [CUDA-GDB][RCCA] handling the two kernel ready events correctly when have multiple grids
CUDA_GDB_RCCA_3820537:
  LOG_NAME: ${CUDA_GDB_RCCA_3820537_PATH}/cuda_gdb_rcca_3820537.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_RCCA_3820537_PATH}; wget --user {} --password {} {}/P1072_T3820537/test.cu; ${NVCC_PATH}/nvcc -arch sm_{} -g -G test.cu

# [CUDA-GDB][RCCA] C++ reference types should be emitting DW_AT_address_class on DW_TAG_reference_type
CUDA_GDB_RCCA_3825550:
  LOG_NAME: ${CUDA_GDB_RCCA_3825550_PATH}/cuda_gdb_rcca_3825550.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_RCCA_3825550_PATH}; wget --user {} --password {} {}/P1072_T3825550/reftest2.cu; ${NVCC_PATH}/nvcc -g -G reftest2.cu

# [CUDA-GDB][RCCA] should gracefully handle errors when disassembling with the debugAPI
CUDA_GDB_RCCA_3679456:
  LOG_NAME: ${CUDA_GDB_RCCA_3679456_PATH}/cuda_gdb_rcca_3679456.txt
  PREPARE:
    CMD1: cp -r {}/pred_exit* ${CUDA_GDB_RCCA_3679456_PATH}

# [CUDA-GDB][RCCA] No segmentation fault after using "target cudacore" more than once
CUDA_GDB_RCCA_3773016:
  LOG_NAME: ${CUDA_GDB_RCCA_3773016_PATH}/cuda_gdb_rcca_3773016.txt
  PREPARE:
    CMD1: cp -r {}/lightweight-coredump* ${CUDA_GDB_RCCA_3773016_PATH}
  STEP1:
    CMD1: cd ${CUDA_GDB_RCCA_3773016_PATH}; CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1 CUDA_COREDUMP_FILE=foobar1 ./lightweight-coredump

# [CUDA-GDB][RCCA] C++ reference types should be emitting DW_AT_address_class on DW_TAG_reference_type
CUDA_GDB_RCCA_3692213:
  LOG_NAME: ${CUDA_GDB_RCCA_3692213_PATH}/cuda_gdb_rcca_3692213.txt
  LOG_NAME1: ${CUDA_GDB_RCCA_3692213_PATH}/cuda_gdb_rcca_3692213-1.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_RCCA_3692213_PATH}; wget --user {} --password {} {}/P1072_T3692213/illegal_memory_access.cu; ${NVCC_PATH}/nvcc -L /usr/local/cuda-${CUDA_SHORT_VERSION} illegal_memory_access.cu -o illegal_memory
  STEP1:
    CMD1: cd ${CUDA_GDB_RCCA_3692213_PATH}; CUDA_INJECTION64_PATH=none CUDA_DEVICE_WAITS_ON_EXCEPTION=1 ./illegal_memory
    CMD2: ps -ef|grep illegal|awk -F ' ' '{print $2}'|head -n 2|tail -n 1
    CMD2_1: ps -ef|grep illegal|awk -F ' ' '{print $2}'|head -n 1

# [CUDA-GDB][RCCA] C++ reference types should be emitting DW_AT_address_class on DW_TAG_reference_type
CUDA_GDB_RCCA_3671381:
  LOG_NAME: ${CUDA_GDB_RCCA_3671381_PATH}/cuda_gdb_rcca_3671381.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_RCCA_3671381_PATH};wget https://github.com/llvm/llvm-project/releases/download/llvmorg-16.0.3/llvm-project-16.0.3.src.tar.xz;tar -Jxf llvm-project-16.0.3.src.tar.xz; cd llvm-project-16.0.3.src; mkdir build; cd build; cmake -DLLVM_ENABLE_PROJECTS=clang -DCMAKE_BUILD_TYPE=Release -G "Unix Makefiles" ../llvm; make -j50
    CMD2: cd ${CUDA_GDB_RCCA_3671381_PATH}; cd llvm-project-16.0.3.src; mkdir build2; cd build2;CC=../build/bin/clang CXX=../build/bin/clang++ cmake ../llvm/ -DCMAKE_BUILD_TYPE=Release -DCLANG_OPENMP_NVPTX_DEFAULT_ARCH=sm_{} -DLIBOMPTARGET_NVPTX_COMPUTE_CAPABILITIES=75,80,{} -DLLVM_TARGETS_TO_BUILD="X86;NVPTX" -DLLVM_ENABLE_PROJECTS="clang;clang-tools-extra;openmp;compiler-rt" -DCMAKE_INSTALL_PREFIX=/opt; make -j20; echo '{}'|sudo -S make install
    CMD3: cd ${CUDA_GDB_RCCA_3671381_PATH}; wget --user {} --password {} {}/P1072_T3671381/saxpy-omp.zip; unzip saxpy-omp.zip; ./llvm-project-16.0.3.src/build/bin/clang-16 -Wall -fopenmp -fopenmp-targets=nvptx64 -Xopenmp-target -march=sm_{} -fopenmp-target-debug=7 -fopenmp-offload-mandatory -g3 saxpy-omp.cc  --libomptarget-nvptx-bc-path=/opt/lib -I/opt/lib/clang/16/include/ -I/usr/include -L/opt/lib/ -o saxpy-omp
  STEP1:
    CMD1: cd cd ${CUDA_GDB_RCCA_3671381_PATH}; LD_LIBRARY_PATH=/opt/lib/ cuda-gdb  ./saxpy-omp


#Debug OptiX samples with -G enabled 3794754
CUDA_GDB_OPTIX:
  LOG_NAME: ${CUDA_GDB_OPTIX_PATH}/cuda_gdb_rcca_3794754.txt
  PREPARE:
    CMD1: cp -r {}/assert ${CUDA_GDB_OPTIX_PATH}
  STEP1:
    CMD1: cd ${CUDA_GDB_OPTIX_PATH}; cuda-gdb ./OptixPathTraker


# [CUDA-GDB][RCCA] No crash after assertation fails
CUDA_GDB_RCCA_4040416:
  LOG_NAME: ${CUDA_GDB_RCCA_4040416_PATH}/cuda_gdb_rcca_4040416.txt
  PREPARE:
    CMD1: cp -r {}/assert ${CUDA_GDB_RCCA_4040416_PATH}
  STEP1:
    CMD1: cd ${CUDA_GDB_RCCA_4040416_PATH}; cuda-gdb ./assert

# Debug Option - info cuda barriers
CUDA_GDB_OPTION_4019656:
  LOG_NAME: ${CUDA_GDB_OPTION_4019656_PATH}/cuda_gdb_option_4019656.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_OPTION_4019656_PATH}; wget --user {} --password {} {}/P1072_T4019656/internal_cmd_cbu.zip; unzip internal_cmd_cbu.zip
    CMD2: cp -r {}/assert ${CUDA_GDB_OPTION_4019656_PATH}
  STEP1:
    CMD1: cd ${CUDA_GDB_OPTION_4019656_PATH}; cuda-gdb ./internal_cmd_cbu

CUDA_GDB_SANITY:
  LOG_NAME: ${CUDA_GDB_SANITY_PATH}/cuda_gdb_sanity.txt
  STEP1:
    CMD1: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdb --version
  STEP2:
    CMD1: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdbserver --version
  STEP3:
    CMD1: cd ${CUDA_GDB_SANITY_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdb ./matrixMul
  STEP4:
    PREPARE:
      CMD1: cd ${CUDA_GDB_SANITY_PATH}; wget --user {} --password {} {}/simpleCoreDump/simpleCoreDump.cu && /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G simpleCoreDump.cu -o simpleCoreDump
    CMD1: cd ${CUDA_GDB_SANITY_PATH}; ulimit -c unlimited; CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1 CUDA_COREDUMP_FILE=dump ./simpleCoreDump; ls dump
    CMD2: cd ${CUDA_GDB_SANITY_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdb -ex "target cudacore dump"

CUDA_GDB_REMOTE:
  LOG_NAME: ${CUDA_GDB_REMOTE_PATH}/cuda_gdb_remote.txt
  PREPARE:
    CMD1: cp -r {}/matrixMul/matrixMul ${CUDA_GDB_REMOTE_PATH}


# Support "multiple users to connect to a single MPS server" 3665535
CUDA_GDB_MPS_MULTIUSER:
  LOG_NAME: ${CUDA_GDB_MPS_MULTIUSER_PATH}/cuda_gdb_mps_multiuser.txt
  PREPARE:
    CMD1: cp -r {}/matrixMul/matrixMul ${CUDA_GDB_MPS_MULTIUSER_PATH}

# Test cuda-gdb restore GPU thread 5120057
CUDA_GDB_RESTORE_GPU_THREAD:
  LOG_NAME: ${CUDA_GDB_RESTORE_GPU_THREAD_PATH}/cuda_gdb_restore_gpu_thread.txt
  PREPARE:
    CMD1: cd ${CUDA_GDB_RESTORE_GPU_THREAD_PATH}; wget --user {} --password {} {}/P1072_T5120057/diverge.cu; ${NVCC_PATH}/nvcc -ccbin=g++ -arch=native -g -G -O0 -o diverge diverge.cu
