argument_specs:
  main:
    short_description: create_vGPU
    description: create vGPU
    author:
      - <PERSON>
    options:
      vgpu_tool:
        type: str
        required: true
        description: use vgpu tool to create vGPU
      host_ip:
        type: str
        required: true
        description: vGPU host information

      vGPU_platform:
          type: str
          required: true
          choices: ["kvm", "esxi"]
          default: "kvm"
          description: vGPU platform

      host_user:
          type: str
          required: true
          description: vGPU host user, default can use root

      host_password:
          type: str
          required: true
          description: vGPU host password

      vgpu_list:
          type: str
          required: true
          description: create one vgpu or more vgpu

      vm_name:
        type: str
        required: true
        description: assign one vgpu or more vgpu_list to vm