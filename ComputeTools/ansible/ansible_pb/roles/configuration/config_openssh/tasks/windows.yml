# Install openssh
- name: Setup OpenSSH
  block:
  - name: Get sshd service info
    win_command: sc query sshd
    register: sshd_status
    failed_when: false

  - name: Try stop ssh service if exist
    win_service:
      name: sshd
      start_mode: disabled
      state: stopped
    when: sshd_status.rc == 0

  - name: Install OpenSSH
    block:
    - name: Clean target folder
      ansible.windows.win_file:
        path: C:\OpenSSH-Win64
        state: absent

    - name: create OpenSSH-Win64 folder
      ansible.windows.win_file:
        path: C:\OpenSSH-Win64
        state: directory

    - name: Download OpenSSH package
      ansible.windows.win_get_url:
        url: "{{ server_storage }}/{{ pkg_name }}"
        url_username: "{{ get_username }}"
        url_password: "{{ get_passwd }}"
        dest: "C:\\OpenSSH-Win64\\{{ pkg_name }}"
        force: yes

    - name: Unzip OpenSSH package
      community.windows.win_unzip:
        src: "C:\\OpenSSH-Win64\\{{ pkg_name }}"
        dest: C:\

    - name: Install OpenSSH
      ansible.windows.win_shell: powershell -ExecutionPolicy ByPass -File "C:\OpenSSH-Win64\install-sshd.ps1"
      register: p_output
      failed_when:
      - '"sshd and ssh-agent services successfully installed" not in p_output.stdout'

    - name: Start sshd service and set it auto start
      ansible.windows.win_service:
        name: sshd
        start_mode: auto
        state: started

  - name: Add user "{{ username }}" to ssh AllowUsers
    win_lineinfile:
      path: C:\ProgramData\ssh\sshd_config
      line: AllowUsers "{{ username }}"

  - name: Restart sshd service to make modified sshd_config work
    ansible.windows.win_service:
      name: sshd
      state: restarted

  rescue:
  - name: Print when errors
    debug:
      msg: 'Setup OpenSSH failed, please check'

