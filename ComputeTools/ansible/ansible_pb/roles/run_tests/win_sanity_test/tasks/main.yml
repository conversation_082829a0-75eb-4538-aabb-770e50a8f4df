- name: run windows sanity test
  block:

  - name: create tmp folder
    ansible.windows.win_file:
      path: C:\tmp
      state: directory

  - name: copy roles/run_tests/win_sanity_test/files/PsExec64.exe to C:\tmp
    win_copy:
      src: roles/run_tests/win_sanity_test/files/PsExec64.exe
      dest: C:\tmp

  - name: Create test config JSON file from template
    template:
      src: roles/run_tests/win_sanity_test/files/test_config_template.json.j2
      dest: "C:\\{{ test_dir }}\\test_{{ cuda_ver }}_config.json"

  - name: Get test_{{ cuda_ver }}_config.json file status
    win_stat:
      path: "C:\\{{ test_dir }}\\test_{{ cuda_ver }}_config.json"
    register: test_config_file

  - name: Show test config file exists status
    debug:
      msg: "test_{{ cuda_ver }}_config.json File exists"
    when: test_config_file.stat.exists
    ignore_errors: no

  - name: create run_win_sanity_test.bat
    win_shell: |
      echo python C:\{{ test_dir }}\test_sanity_win.py --config "test_{{ cuda_ver }}_config.json" > C:\{{ test_dir }}\run_win_sanity_test.bat
    args:
      executable: cmd.exe
    ignore_errors: no

  - name: run test - run_win_sanity_test.bat
    ansible.builtin.win_psexec:
      chdir:  C:\{{ test_dir }}
      interactive: yes
      elevated: true
      username: '{{ ansible_user }}'
      password: '{{ ansible_password }}'
      session: 1
      executable: C:\tmp\PsExec64.exe
      command: 'cmd /c "C:\{{ test_dir }}\run_win_sanity_test.bat"'
      timeout: 10800
    ignore_errors: yes
    async: 10800   #the maxiam execution time for the step
    poll: 60


