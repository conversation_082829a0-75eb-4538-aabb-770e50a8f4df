import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ['help', 'spawn='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    CONFORM_Y_OR_N = '\(y or \[n\]\) '
    c.execute_and_compare("b kernel_module::base", prompt=CONFORM_Y_OR_N)
    c.execute_and_compare("y")
    c.execute_and_compare("r", "6\s+i\s+=\s+")
