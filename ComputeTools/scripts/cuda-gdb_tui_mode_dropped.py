import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:v:", ['help', 'spawn=', 'cuda_version='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            elif name in ('-v', '--cuda_version'):
                cuda_version = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("tui enable", "Undefined command: \"tui\"")
