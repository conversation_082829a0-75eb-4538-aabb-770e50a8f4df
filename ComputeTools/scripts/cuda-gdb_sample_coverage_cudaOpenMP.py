import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ['help', 'spawn='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("set cuda break_on_launch app")
    c.execute_and_compare("r", r"Switching focus to CUDA kernel \d+")
    c.execute_and_compare("n", r"\d+\s+g_a\[idx\]")
    c.execute_and_compare("n", r"\d+\s+}")
    c.execute_and_compare("info threads", r"\d+\s+Thread\s+")
    c.execute_and_compare("bt", r"#0\s+")
    c.execute_and_compare("c", "exited normally")
    c.execute_and_compare("q")
