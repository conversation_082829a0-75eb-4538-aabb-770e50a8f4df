import getopt
import os
import re
import signal
import subprocess
import sys
import pexpect
import time
from subprocess import check_output
from shlex import split


def get_pid(name):
    return int(check_output(["pidof", "-s", name]))


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


def execute_and_compare(c, step, expect, prompt="\\(cuda-gdb\\) ", timeout=30):
    c.sendline(step)
    c.expect(prompt, timeout)
    # compare with expection
    if re.search(expect, c.before.decode().strip(step).lstrip().rstrip()):
        print('\n&&&& PASS\n')
    else:
        print('\n&&&& FAIL\n')


def regex_compare(value, regex):
    if re.search(regex, value, re.M):
        print('&&&& PASS, match pattern:%s' % regex)
    else:
        print('&&&& FAIL')


if __name__ == '__main__':
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ['help', 'spawn='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
    except getopt.GetoptError:
        usage()

    cmd = "nvidia-smi"
    res = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=None, shell=True).communicate()[0]
    print('%s' % res)
    regex = re.compile(r'\s+(\d+)MiB\s+/\s+\d+MiB', re.M)
    mem_before = []
    for match in regex.finditer(res.decode()):
        mem_before.append(match.group(1))
    print('memory usage before cuda-gdb: %s' % mem_before)

    VM_PROMPT = '\(cuda-gdb\) '
    # access to gdb
    c = pexpect.spawn(spawn_command, timeout=60, maxread=10000)
    c.logfile = sys.stdout.buffer
    i = c.expect([pexpect.TIMEOUT, VM_PROMPT])
    c.sendline('r')
    time.sleep(5)
    c.sendcontrol('z')
    time.sleep(3)
    pid = get_pid("cuda-gdb")
    print('kill -9 cuda-gdb pid <%s>' % pid)
    os.kill(pid, signal.SIGKILL)
    # os.system("kill -9 %s" % pid)
    time.sleep(2)
    cmd = "nvidia-smi; ps -ef|grep -Ei \"cudbgprocess|matrixMul|{}\"|grep -v grep; ls -d /tmp/cuda-dbg/{}/session*/*; ls /tmp/cuda-dbg/cuda-gdb.lock".format(pid, pid)
    res = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=None, shell=True).communicate()[0]
    print('%s' % res)
    regex = re.compile(r'\s+(\d+)MiB\s+/\s+\d+MiB', re.M)
    mem_after = []
    for match in regex.finditer(res.decode()):
        mem_after.append(match.group(1))
    print('memory usage after cuda-gdb: %s' % mem_after)
    if str(mem_before) == str(mem_after):
        print('\n&&&& PASS for memory leak check\n')
    else:
        print('\n&&&& FAIL for memory leak check\n')
    # c.close()

    c = pexpect.spawn(spawn_command, timeout=60, maxread=10000)
    c.logfile = sys.stdout.buffer
    pid = get_pid("cuda-gdb")
    print('get pid %s' % pid)
    c.expect([pexpect.TIMEOUT, VM_PROMPT])
    execute_and_compare(c, "r", "", timeout=600)
    c.sendline('q')
    time.sleep(3)
    # os.system(
    #     'nvidia-smi; ps -ef|grep -Ei "cudbgprocess|matrixMul|%s"|grep -v grep; ls -d /tmp/cuda-dbg/%s/session*/*; ls /tmp/cuda-dbg/cuda-gdb.lock' % (
    #     pid, pid))
    cmd = "nvidia-smi; ps -ef|grep -Ei \"cudbgprocess|matrixMul|{}\"|grep -v grep; ls -d /tmp/cuda-dbg/{}/session*/*; ls /tmp/cuda-dbg/cuda-gdb.lock".format(pid, pid)
    res = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=None, shell=True).communicate()[0]
    print('%s' % res)
    mem_after2 = []
    for match in regex.finditer(res.decode()):
        mem_after2.append(match.group(1))
    print('memory usage after cuda-gdb: %s' % mem_after2)
    if (str(mem_after2) == str(mem_before)):
        print('\n&&&& PASS for memory leak check\n')
    else:
        print('\n&&&& FAIL for memory leak check\n')
    if os.path.isfile('/tmp/cuda-dbg/{}'.format(pid)):
        print('\n&&&& FAIL, /tmp/cuda-dbg/{} exist\n'.format(pid))
    else:
        print('\n&&&& PASS, no /tmp/cuda-dbg/{} exist\n'.format(pid))
