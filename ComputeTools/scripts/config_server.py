# -*- encoding: utf-8 -*-
import os.path

try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
import argparse
import base64
from common_utils import run_loc_cmd
import logging
import time
import sys
import os
import random
import threading
import paramiko
from scp import SCPClient
from yaml_mgr import YamlManger

logger = logging.getLogger()
logger.setLevel(level=logging.INFO)
# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/server_config.log' % ('/home/<USER>'))
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)


def get_args():
    parser = argparse.ArgumentParser(description='Arguments for operate vgpu server or vm')
    parser.add_argument('-s', '--server', required=True, action='store', help='Specify the vgpu server')
    parser.add_argument('-p', '--platform', required=True, action='store', help='specify the platform to operate')
    parser.add_argument('-hd', '--host_driver', required=False, action='store', help='specify host driver to download')
    parser.add_argument('-gd', '--guest_driver', required=False, action='store', help='specify guest driver to download')
    parser.add_argument('-so', '--server_operate', required=False, action='store', help='upgrade/install/uninstall driver, then reboot')
    parser.add_argument('-vo', '--vm_operate', required=False, action='store', help='power on/off vm which specify by --vm_name')
    parser.add_argument('-vm', '--vm_name', required=False, action='store', help='name of the vm')
    parser.add_argument('-a', '--add_params', required=False, action='store', help='add vgpu params to vgpu, all->debug/profile/uvm, debug_profile, debug, profile')
    parser.add_argument('-mg', '--modify_vgpu', required=False, action='store', help='modify new vgpu to replace old vgpu')
    parser.add_argument('-vg', '--vgpu', required=False, action='store', help='specify vgpu to operate')
    parser.add_argument('-cg', '--create_vgpu', required=False, action='store', help='specify vgpu to create')
    parser.add_argument('-rg', '--release_vgpu', required=False, action='store', help='specify vgpu to release on kvm')
    parser.add_argument('-d', '--delete_params', required=False, action='store', help='delete vgpu params to vgpu, all->debug/profile/uvm, debug_profile, debug, profile')
    args = parser.parse_args()
    return args


server_args = get_args()
print(server_args)
platform = server_args.platform.lower()
if server_args.platform.lower() == 'esxi':
    if server_args.server == '155':
        server_ip = '*************'
        driver_path = '/vmfs/volumes/5ee09f8e-8cc68d1c-ad39-ac1f6b9e8862'
    elif server_args.server == '108':
        server_ip = '*************'
        driver_path = '/vmfs/volumes/630c54e0-ef5dec54-2418-ac1f6ba5a0fe'
    elif server_args.server == '109':
        server_ip = '*************'
        driver_path = ''
    else:
        logger.info('please give a correct server')
        exit()
    server_user = 'root'
    server_password = 'vGPU!@#$'
    if server_args.host_driver:
        if server_ip == '*************':
            host_driver_url = '//builds/linuxbuilds/release/display/VMware/{}'.format(server_args.host_driver)
            if server_args.host_driver.split('.')[0] >= '525':
                host_driver = 'NVD_bootbank_NVD-AIE_ESXi_8.0.0_Driver_{}-1OEM.800.1.0.********.vib'.format(server_args.host_driver)
        else:
            host_driver_url = '//builds/linuxbuilds/release/display/VMware/{}'.format(server_args.host_driver)
            if server_args.host_driver.split('.')[0] >= '525':
                host_driver = 'NVD_bootbank_NVD-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(server_args.host_driver)
            else:
                host_driver = 'NVIDIA_bootbank_NVIDIA-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(server_args.host_driver)
elif server_args.platform.lower() == 'kvm':
    if server_args.server == '157':
        server_ip = '*************'
    elif server_args.server == '108':
        server_ip = '*************'
    elif server_args.server == '109':
        server_ip = '*************'
    else:
        logger.info('please give a correct server')
        exit(6)
    server_user = 'root'
    server_password = 'vGPU!@#$'
    driver_path = '/home/<USER>'
    if server_args.host_driver:
        host_driver_url = '//builds/linuxbuilds/release/display/VGX-x86_64/{}'.format(server_args.host_driver)
        host_driver = 'NVIDIA-vGPU-rhel-8.6-{}.x86_64.rpm'.format(server_args.host_driver)
        if server_args.server == '157':
            host_driver = 'NVIDIA-vGPU-rhel-9.1-{}.x86_64.rpm'.format(server_args.host_driver)
else:
    logger.info('please give correct platform,exit.....')
    exit(6)
if server_args.guest_driver:
    guest_driver_url = '//builds/linuxbuilds/release/display/x86_64/{}'.format(server_args.guest_driver)
    guest_driver = 'NVIDIA-Linux-x86_64-{}-grid.run'.format(server_args.guest_driver)
# driver_path = driver_path
download_path = "/home/<USER>/vgpu"


def fetch_service_account():
    import requests
    if "CQASYSTEM_TOKEN" in os.environ:
        api_token = os.environ["CQASYSTEM_TOKEN"]
    else:
        api_token_list = ['8c913a613f', '88b6f1ad91a', '815f13fd732']
        api_token = ''.join(api_token_list)
    response = requests.get('http://cqasystem.nvidia.com/api/credential/?ctype=service_account',
                            headers={'accept': 'application/json',
                                     'api-token': api_token}).json()
    return response['username'], urllib2.quote(response['password'])


user, password = fetch_service_account()


def prepare_driver(driver):
    if driver == 'host':
        if os.path.exists('{}/{}'.format(download_path, host_driver)):
            cmd = 'ls {}/{}'.format(download_path, host_driver)
        else:
            cmd = "cd {}; lftp -c 'open {}:{}@hqnvhwy02; glob -- pget -n 80 {}/{}'".format(download_path, user, password, host_driver_url, host_driver)
    elif driver == 'guest':
        if os.path.exists('{}/{}'.format(download_path, guest_driver)):
            cmd = 'ls {}/{}'.format(download_path, guest_driver)
        else:
            cmd = "cd {}; lftp -c 'open {}:{}@hqnvhwy02; glob -- pget -n 80 {}/{}'".format(download_path, user, password, guest_drive_url, guest_driver)
    else:
        logger.warning('please give correct params to download driver')
    print('=====================================')
    logger.info('we download driver by ------ {}'.format(cmd))
    out = run_loc_cmd(cmd)
    if out.succeeded:
        logger.info('we prepare {} driver successful'.format(driver))
        if driver == 'host':
            return host_driver
        else:
            return guest_driver
    else:
        logger.info('we prepare {} driver fail'.format(driver))
        # code:2, download the driver failed
        exit(2)


def scp_file(target_ip, target_user, target_password, source_file, target_path):
    trans = paramiko.Transport((target_ip, 22))
    trans.connect(username=target_user, password=target_password)
    ssh = paramiko.SSHClient()
    ssh._transport = trans
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    scp = SCPClient(ssh.get_transport())
    scp.put(source_file, remote_path=target_path)
    print('*******************************************************************')
    trans.close()


def uninstall_driver(vm_ip, vm_user, vm_password, root_password):
    trans = paramiko.Transport((vm_ip, 22))
    trans.connect(username=vm_user, password=vm_password)
    ssh = paramiko.SSHClient()
    ssh._transport = trans
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    cmd1 = 'ls /usr/bin/nvidia-smi'
    ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
    cmd1_out = ssh_stdout1.read()
    if 'nvidia-smi' in cmd1_out:
        cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S nvidia-uninstall --silent'.format(root_password, root_password)
        logger.info('we uninstall the driver by --- {}'.format(cmd))
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
        print(ssh_stdout.read())
    else:
        logger.info('no need uninstall the driver, because there is no driver in vm')
    trans.close()


def install_driver(vm_ip, vm_user, vm_password, root_password, vm_driver):
    trans = paramiko.Transport((vm_ip, 22))
    trans.connect(username=vm_user, password=vm_password)
    ssh = paramiko.SSHClient()
    ssh._transport = trans
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S bash {} --silent; sleep 10'.format(root_password, root_password, vm_driver)
    logger.info('we install driver by --- {}'.format(cmd))
    ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
    print(ssh_stdout.read())
    cmd1 = "nvidia-smi | grep 'Driver Version:'|awk -F ' ' '{print $3}'"
    ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
    cmd1_out = ssh_stdout1.read()
    print(cmd1_out.split('\n'))
    if cmd1_out.split('\n')[0] in vm_driver:
        logger.info('we install the driver successful')
    else:
        logger.info('we install the driver failed')
    trans.close()


def ip_alive(ip_address):
    result = os.system('ping {}'.format(ip_address))
    if result == 0:
        logger.info('the ip address is alive now')
        return True
    else:
        logger.info('the ip address is not alive now')
        return False


def get_all_vm():
    trans = paramiko.Transport((server_ip, 22))
    trans.connect(username=server_user, password=server_password)
    ssh = paramiko.SSHClient()
    ssh._transport = trans
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    if server_args.platform.lower() == 'kvm':
        cmd = "virsh list --all | awk '{print $2}'"
    else:
        cmd = "vim-cmd vmsvc/getallvms | awk '{print $1,$2}'"
    ssh_stdin, cmd_out, ssh_stderr = ssh.exec_command(cmd)
    if server_args.platform.lower() == 'kvm':
        vm_list1 = cmd_out.read().split('\n')[2:-1]
        vm_list = [i for i in vm_list1 if i != '']
        trans.close()
        return dict(zip(vm_list, vm_list))
    else:
        vm_list, vm_id = [], []
        for line in cmd_out.read().split('\n')[0:-1]:
            print(line)
            if 'vCLS' in line or 'Vmid' in line:
                continue
            else:
                vm_list.append(line.split(' ')[1].strip(' '))
                vm_id.append(int(line.split(' ')[0].strip(' ')))
        trans.close()
        return dict(zip(vm_list, vm_id))


vm_dict = get_all_vm()


class Vgpu_server:
    def __init__(self):
        self.user = server_user
        self.password = server_password
        self.ip = server_ip
        self.platform = platform
        try:
            self.trans = paramiko.Transport((self.ip, 22))
            self.trans.connect(username=self.user, password=self.password)
            self.ssh = paramiko.SSHClient()
            self.ssh._transport = self.trans
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        except:
            logger.info('ssh connect server fail')
            logger.info('please check server is power on or not')
            exit(3)

    def connect_server(self):
        trans = paramiko.Transport((self.ip, 22))
        trans.connect(username=self.user, password=self.password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        return trans, ssh

    def disconnect_server(self):
        self.trans.close()

    def excute_cmd_on_server(self, cmd):
        print('we will run cmd---{}'.format(cmd))
        ssh_stdin, ssh_stdout, ssh_stderr = self.ssh.exec_command(cmd)
        return ssh_stdout, ssh_stderr

    def power_off_all_vm(self):
        print(vm_dict)
        print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
        for vm_name, vm_id in vm_dict.items():
            if self.get_vm_status(vm_name) == 'power_off':
                logger.info('vm ---{} is power off, no need shutdown'.format(vm_name))
            else:
                if self.vm_power_off(vm_name):
                    logger.info('vm --- {} was shut down'.format(vm_name))
                else:
                    logger.info('vm ---{} is not shutdown, exit.....'.format(vm_name))
                    exit(3)

    def get_vm_status(self, vm_name):
        if self.platform == 'kvm':
            cmd = 'virsh list --all | grep {}'.format(vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            '''
            for line in cmd_out.read().split('\n'):
                if vm_name in line:
                    if 'running' in line:
                        logger.info('the vm ----{} is power on now'.format(vm_name))
                        return 'power_on'
                    else:
                        logger.warn('the vm ----{} is power off now'.format(vm_name))
                        return 'power_off'
                else:
                    continue
            '''
            if 'running' in cmd_out.read():
                logger.info('the vm ----{} is power on now'.format(vm_name))
                return 'power_on'
            else:
                logger.warn('the vm ----{} is power off now'.format(vm_name))
                return 'power_off'
        if self.platform == 'esxi':
            print('tttttttttttttttttttttttttttttttttttttttttttttt')
            cmd = 'vim-cmd vmsvc/power.getstate {}'.format(vm_dict[vm_name])
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            time.sleep(10)
            if 'Powered on' in cmd_out.read():
                return 'power_on'
            else:
                return 'power_off'

    def vm_power_off(self, vm_name):
        if self.platform == 'kvm':
            cmd = 'virsh shutdown {}'.format(vm_name)
        if self.platform == 'esxi':
            cmd = 'vim-cmd vmsvc/power.off {}'.format(vm_dict[vm_name])
        self.excute_cmd_on_server(cmd)
        time.sleep(60)
        vm_status = self.get_vm_status(vm_name)
        print('========================================')
        print('the vm {} status is {}'.format(vm_name, vm_status))
        print('++++++++++++++++++++++++++++++++++++++++')
        if vm_status == 'power_off':
            logger.info('we power_off the vm ---{} successful'.format(vm_name))
            return True
        else:
            self.force_power_off(vm_name)
            time.sleep(30)
            if self.get_vm_status(vm_name) == 'power_off':
                logger.info('vm ---{} is power off, no need shutdown'.format(vm_name))
                return True
            else:
                logger.info('vm ---{} is not shutdown, exit.....'.format(vm_name))
                return False

    def force_power_off(self, vm_name):
        if platform == 'esxi':
            world_id = []
            cmd = "esxcli vm process list|grep -A 1 {}|grep 'World ID'".format(vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for line in cmd_out.read().split('\n'):
                if 'World ID' in line:
                    world_id.append(line.split(":")[1].strip(' '))
            if len(world_id) != 0:
                for i in world_id:
                    cmd1 = "esxcli vm process kill -t hard -w {}".format(i)
                    self.excute_cmd_on_server(cmd1)
                    time.sleep(2)

    def vm_power_on(self, vm_name):
        if self.get_vm_status(vm_name) == 'power_on':
            self.vm_power_off(vm_name)
        if self.platform == 'kvm':
            cmd = 'virsh start {}'.format(vm_name)
        if self.platform == 'esxi':
            cmd = 'vim-cmd vmsvc/power.on {}'.format(vm_dict[vm_name])
        self.excute_cmd_on_server(cmd)
        time.sleep(60)
        vm_status = self.get_vm_status(vm_name)
        print('========================================')
        print('the vm {} status is {}'.format(vm_name, vm_status))
        print('++++++++++++++++++++++++++++++++++++++++')
        if vm_status == 'power_on':
            logger.info('we power on the vm--{} successful'.format(vm_name))
            return True
        else:
            logger.info('we power on the vm--{} fail'.format(vm_name))
            return False

    def get_vm_ip(self, vm_name):
        ip_list = []
        try_time = 0
        if self.get_vm_status(vm_name) == 'power_on':
            if platform == 'esxi':
                cmd = 'vim-cmd vmsvc/get.guest {} | grep -m 1 "ipAddress = \"'.format(vm_dict[vm_name])
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)

                for line in cmd_out.read().split('\n'):
                    if 'ipAddress' in line:
                        ip_list.append(line.split('"')[1])
                    else:
                        continue
                return ip_list
            elif platform == 'kvm':
                cmd = 'virsh dumpxml {}|grep "mac address"'.format(vm_name)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                mac_list = []
                for line in cmd_out.read().split('\n'):
                    if 'mac address' in line:
                        print(line)
                        mac_list.append(line.split("'")[1])
                    else:
                        continue
                # self.disconnect_server()
                print('the vm ---{} mac address is {}'.format(vm_name, mac_list))
                trans = paramiko.Transport(("*************", 22))
                trans.connect(username="swqa", password="labuser")
                ssh = paramiko.SSHClient()
                ssh._transport = trans
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                for mac_address in mac_list:
                    get_ip_cmd = 'echo {}|sudo -S arp-scan -l | grep {}'.format(root_password, mac_address)
                    ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(get_ip_cmd)
                    ip_list.append(ssh_stdout.read().split(mac_address)[0].strip(' ').strip('\t'))
                trans.close()
                return ip_list
        else:
            self.vm_power_on(vm_name)
            try_time += 1
            self.get_vm_ip(vm_name)
            logger.info('the vm is shutdown, can not get ip address')
            logger.info('so power on the vm, then try to get the ip address')
            # exit(3)
        if try_time == 3:
            exit(3)
            logger.info('we try 3 time, but can not power on the {}, please take a look'.format(vm_name))

    def get_host_driver_version(self):
        print('ddddddddddddddds')
        # cmd = "nvidia-smi| grep 'Driver Version'"
        # cmd = "nvidia-smi| grep 'Driver Version'| awk -F : '{print $2}'|awk -F ' ' '{print $1}'"
        if platform == 'kvm':
            cmd = "rpm -qa |grep NV| awk -F '-' '{print $5}'|awk -F '.x86' '{print $1}'"
        elif platform == 'esxi':
            cmd = "nvidia-smi| grep 'Driver Version'| awk -F : '{print $2}'|awk -F ' ' '{print $1}'"
        cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
        cmd_out = cmd_out1.read()
        if cmd_out:
            logger.info('we can get the driver version----{}'.format(cmd_out.split('\n')[0]))
            return cmd_out.split('\n')[0]
        else:
            logger.warn('there is no driver on host or install driver failed, please check')
            return False

    def get_host_driver(self):
        if platform == 'kvm':
            cmd = 'rpm -qa |grep NV'
        else:
            cmd = "esxcli software vib list | grep -i NVD|awk -F ' ' '{print $1}'"
        cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
        cmd_out = cmd_out1.read()
        print(cmd_out)
        print('ddddddddddddddddddd')
        return cmd_out.split('\n')[0].strip(' ')

    def get_used_vgpu_info(self):
        '''
        {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
        :return: for exsi: {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
                for kvm: {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0', 'vgpu_uuid': '11d111f2-3927-49c4-8661-7c5a1f180f8a'}
        '''
        vgpu_info = {}
        vgpu_device, vm, vgpu_name, vgpu_uuid = [], [], [], []
        if platform == 'kvm':
            cmd = "nvidia-smi vgpu -q |grep -B 10 'Guest Driver Version'"
        else:
            cmd = "nvidia-smi vgpu -q |grep -B 5 'vGPU Name'"
        cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
        cmd_out = cmd_out1.read()
        print(cmd_out)
        print('==========================')
        print(cmd_out.split('\n'))
        for line in cmd_out.split('\n'):
            if platform == 'kvm':
                if 'GPU 00' in line:
                    vgpu_device.append(line.split(' ')[-1].strip(' '))
                elif 'VM Name' in line:
                    vm.append(line.split(':')[-1].strip(' '))
                elif 'vGPU Name' in line:
                    vgpu_name.append(line.split(':')[-1].strip(' ').split(' ')[1])
                elif 'MDEV UUID' in line:
                    vgpu_uuid.append(line.split(":")[-1].strip(' '))
                else:
                    continue
            else:
                if 'GPU 00' in line:
                    vgpu_device.append(line.split(' ')[-1].strip(' '))
                elif 'VM Name' in line:
                    vm.append(line.split(':')[-1].strip(' '))
                elif 'vGPU Name' in line:
                    vgpu_name.append(line.split(':')[-1].strip(' ').split(' ')[1])
                else:
                    continue
        if len(vgpu_device) != 0:
            if platform == 'kvm':
                for i in range(0, len(vgpu_device)):
                    vgpu_info[vgpu_name[i]] = {'vgpu': vgpu_device[i], 'vm_name': vm[i], 'vgpu_uuid': vgpu_uuid[i]}
            else:
                for i in range(0, len(vgpu_device)):
                    vgpu_info[vgpu_name[i]] = {'vgpu': vgpu_device[i], 'vm_name': vm[i]}
            return vgpu_info
        else:
            return False

    def vm_assign_vgpu(self):
        pass

    def reboot_server(self):
        cmd = 'reboot'
        self.excute_cmd_on_server(cmd)

    def upgrade_host_driver(self, driver_path, host_driver):
        self.power_off_all_vm()
        if platform == 'kvm':
            cmd_list = ['rpm -Uv {}/{}'.format(driver_path, host_driver)]
            for cmd in cmd_list:
                logger.info('install driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())
                time.sleep(300)
            self.reboot_server()
        else:
            # cmd_list = ['vim-cmd hostsvc/maintenance_mode_enter', 'localcli software acceptance set --level=CommunitySupported', 'localcli software vib install --no-sig-check -v {}/{}'.format(driver_path, host_driver), 'vim-cmd hostsvc/maintenance_mode_exit', 'reboot']
            cmd_list = ['esxcli software vib update --maintenance-mode --no-sig-check -v {}/{}'.format(driver_path, host_driver)]
            for cmd in cmd_list:
                logger.info('install driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())
                time.sleep(60)
            current_driver = self.get_host_driver_version()
            if current_driver != server_args.host_driver:
                logger.info('we upgrade the host driver failed')
                return False
            else:
                logger.info('we upgrade the host driver Successful')
                return True

    def install_host_driver(self, driver_path, host_driver):
        # self.power_off_all_vm()  # need this steps ?
        if platform == 'kvm':
            cmd_list = ['rpm -iv {}/{}'.format(driver_path, host_driver)]
            for cmd in cmd_list:
                logger.info('install driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())
                time.sleep(120)
            self.reboot_server()
            # how to check install the driver successful or fail on KVM platform? in the main_function--re-connect host, then check it
        else:
            # cmd_list = ['vim-cmd hostsvc/maintenance_mode_enter', 'localcli software acceptance set --level=CommunitySupported', 'localcli software vib install --no-sig-check -v {}/{}'.format(driver_path, host_driver), 'vim-cmd hostsvc/maintenance_mode_exit', 'reboot']
            cmd_list = ['cd {}; esxcli software vib install --maintenance-mode --no-sig-check -v {}'.format(driver_path, host_driver)]
            for cmd in cmd_list:
                logger.info('install driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())
                time.sleep(120)
            current_driver = self.get_host_driver_version()
            if current_driver != server_args.host_driver:
                logger.info('we install the host driver failed')
                return False
            else:
                logger.info('we install the host driver Successful')
                return True

    def uninstall_host_driver(self):
        self.power_off_all_vm()
        current_driver = self.get_host_driver()
        if platform == 'kvm':
            cmd_list = ['rpm -ev {}'.format(current_driver)]
            for cmd in cmd_list:
                logger.info('uninstall driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())
                time.sleep(180)
            # self.reboot_server() # need this steps ?
        else:
            # cmd_list = ['vim-cmd hostsvc/maintenance_mode_enter', 'localcli software acceptance set --level=CommunitySupported', 'localcli software vib install --no-sig-check -v {}/{}'.format(driver_path, host_driver), 'vim-cmd hostsvc/maintenance_mode_exit', 'reboot']
            cmd_list = ['esxcli software vib remove --maintenance-mode -n  {}'.format(current_driver)]
            for cmd in cmd_list:
                logger.info('uninstall driver by ---{}'.format(cmd))
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out.read())

    def get_vm_path(self, vm_name):
        if platform != 'kvm':
            cmd = 'find / -name {}'.format(vm_name)
            cmd1_out, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd1_out.read()
            if cmd_out:
                logger.info('the {} path is {}'.format(vm_name, cmd_out.split('\n')[0].strip(' ')))
                return cmd_out.split('\n')[0].strip(' ')
            else:
                logger.info('please check {} exist or not'.format(vm_name))
                return False
        else:
            logger.info('we need not use this function on kvm platform')
            return False

    def get_gpu_bus_id(self):
        '''
        :return: a list, such as: ['6+A100-PCI...+00000000:24:00.0+On', '7+A100+00000000:3D:00.0+0']
        '''
        '''
        try:
            gpu_bus_id, bus_id, gpu_name = [], [], []
            cmd = "nvidia-smi |grep 0000000|awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'"
            cmd1_out, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd1_out.read()
            for i in cmd_out.split('\n')[0:-1]:
                bus_id.append(i.split(' ')[0].split('-')[0])
            cmd2 = "nvidia-smi |grep 0000000|awk -F ' ' '{print $4}'"
            cmd2_out, cmd_err = self.excute_cmd_on_server(cmd2)
            cmd_out1 = cmd2_out.read()
            for i in cmd_out1.split('\n')[0:-1]:
                gpu_name.append(i.split(' ')[0])
            for index, i in enumerate(bus_id):
                gpu_bus_id.append(gpu_name[index] + '-' + i)
            logger.info('we get the gpu bus id successful')
            return gpu_bus_id
        '''
        try:
            gpu_bus_id = []
            cmd = "nvidia-smi | grep 000000| awk -F '|' '{print $2, $3, $4}'"
            cmd1_out, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd1_out.read()
            for line in cmd_out.split('\n')[0:-1]:
                gpu_info = [i for i in line.strip(' ').split(' ') if i != '']
                gpu_bus_id.append(gpu_info[0] + '+' + gpu_info[2] + '+' + gpu_info[-3] + '+' + gpu_info[-1])
            logger.info('we get the gpu bus id successful')
            return gpu_bus_id
        except:
            logger.info('we get the gpu name:bus id failed ')
            return False

    def get_host_gpu_info(self):
        '''
        :return: [{'cuda_visible_devices:': '0', 'bus_id': '00000000:1A:00.0', 'uuid': 'GPU-64d6486a-8fa5-0268-f713-8c339b8a83d3', 'gpu': 'V100-PCIE-16GB'},
        {'cuda_visible_devices:': '1', 'bus_id': '00000000:1B:00.0', 'uuid': 'GPU-b39ca232-f56c-5fbb-dc17-388d2a1ce579', 'gpu': 'A100-PCIE-40GB'}]
        '''
        try:
            gpu_bus_id = self.get_gpu_bus_id()
            cmd = 'nvidia-smi -L'
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd_out1.read()
            host_gpu_info = []
            for index, line1 in enumerate(cmd_out.split('\n')[0:-1]):
                vgpu_dict = {}
                for line in line1.split(':'):
                    if 'GPU ' in line:
                        print(line.split(' ')[-1])
                        vgpu_dict["cuda_visible_devices:"] = line.split(' ')[-1]
                    elif 'UUID' in line:
                        print(line.split(" ")[2])
                        vgpu_dict["gpu"] = line.split(" ")[2]
                    elif 'GPU-' in line:
                        print(line.split('GPU-')[1].strip(')'))
                        vgpu_dict["uuid"] = line.strip(')').strip(' ')
                    else:
                        continue
                if gpu_bus_id is not False:
                    vgpu_dict['bus_id'] = gpu_bus_id[index].split('+')[-2]
                host_gpu_info.append(vgpu_dict)
            return host_gpu_info
        except:
            logger.info('we get the host gpu info failed')
            return False

    def get_gpu_uuid(self, vgpu):
        '''
        use cmd ------ nvidia-smi -L
        :param vgpu:
        :return: GPU-a528d4cf-ede9-ec1d-264f-a02821803c4d
        '''
        try:
            cmd = 'nvidia-smi -L'
            gpu = vgpu.split('-')[0].upper()
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd_out1.read()
            for line in cmd_out.split('\n')[0:-1]:
                if gpu in line:
                    logger.info('the vgpu map with {}'.format(line.split("UUID: ")[1].strip(')')))
                    return line.split("UUID: ")[1].strip(')')
                else:
                    continue
        except:
            logger.info('get the vgpu--{}  uuid failed'.format(vgpu))
            exit(2)

    def verify_vgpu_params(self, param, config_file):
        cmd = 'grep {} {}'.format(param, config_file)
        cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
        cmd_out = cmd_out1.read()
        print(cmd_out)
        print(cmd_out.split('\n'))
        if param in cmd_out.split('\n')[0]:
            logger.info('{} had been enabled'.format(param))
            return True
        else:
            logger.info('{} was not be enabled'.format(param))
            return False

    def enable_mig_on_vmware(self, vgpu):
        used_vgpu = self.get_used_vgpu_info()
        gpu_bus_info = self.get_gpu_bus_id()
        gpu = vgpu.split('-')[0].upper()
        status_list = []
        for line in gpu_bus_info:
            status_list = line.split('+')[-1]

    # it had mig enabled, so get available CI
    def get_available_ci(self, vgpu):
        used_vgpu = self.get_used_vgpu_info()
        pass

    def get_mig_available_gpu_list(self, vgpu):
        if vgpu.split('-')[0].upper() in ['A100', 'A30', 'A100D']:
            # get used vgpu info {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
            used_vgpu = self.get_used_vgpu_info()
            # get host gpu info
            '''
            [{'cuda_visible_devices:': '0', 'bus_id': '00000000:1A:00.0', 'uuid': 'GPU-64d6486a-8fa5-0268-f713-8c339b8a83d3', 'gpu': 'V100-PCIE-16GB'}]
            '''
            gpu_info = self.get_host_gpu_info()
            A100_list, A30_list, A100D_list = [], [], []
            A100_used, A30_used, A100D_list = [], [], []
            for gpu in gpu_info:
                if 'A30' in gpu['gpu']:
                    A30_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                elif 'A100' in gpu['gpu']:
                    A100D_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                elif 'A100-PCIE' in gpu['gpu']:
                    A100_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                else:
                    continue
            for key, value in used_vgpu.items():
                if 'A30' in key:
                    A30_used.append(value(vgpu))
                elif '100' in key:
                    A100_used.append(value(vgpu))
                else:
                    continue
            A100_available = [i for i in A100_list if i.split('-')[-1] not in A100_used]
            A100D_available = [i for i in A100D_list if i.split('-')[-1] not in A100_used]
            A30_available = [i for i in A30_list if i.split('-')[-1] not in A30_used]
            return A30_available, A100_available, A100D_available
        else:
            logger.info('{} not support mig'.format(vgpu))
            return False

    # enable the avilable gpu, if one is used, one is mig mode, still exit() now
    def enable_mig_kvm(self, vgpu):
        """
        enable the mig on gpu
        :param vgpu:
        :return:
        """
        if self.get_mig_avilable_gpu_list() is not False:
            A30_avilable, A100_avilable, A100D_avilable = self.get_mig_avilable_gpu_list(vgpu)
            if vgpu.split('-')[0].upper() == 'A30':
                failed = 0
                if len(A30_avilable) != 0:
                    for i in A30_avilable:
                        enable_mig_cmd = 'nvidia-smi -mig 1 -i {}'.format(i.split('-')[0])
                        cmd_out1, cmd_err = self.excute_cmd_on_server(enable_mig_cmd)
                        if cmd_err.read('\n')[0] == '':
                            logger.info('we enable mig success full on KVM')
                        else:
                            failed += 1
                            continue
                else:
                    logger.info('there is no resource to enable mig on A30')
                if failed == len(A30_avilable):
                    logger.info('we enable mig on gpu A30 in KVM platform fail')
                    exit(2)
            if vgpu.split('-')[0].upper() == 'A100':
                failed = 0
                if len(A100_avilable) != 0:
                    for i in A100_avilable:
                        enable_mig_cmd = 'nvidia-smi -mig 1 -i {}'.format(i.split('-')[0])
                        cmd_out1, cmd_err = self.excute_cmd_on_server(enable_mig_cmd)
                        if cmd_err.read('\n')[0] == '':
                            logger.info('we enable mig success full on KVM')
                        else:
                            failed += 1
                            continue
                else:
                    logger.info('there is no resource to enable mig on A30')
                if failed == len(A100_avilable):
                    logger.info('we enable mig on gpu A30 in KVM platform fail')
                    exit(2)
            if vgpu.split('-')[0].upper() == 'A100D':
                failed = 0
                if len(A100D_avilable) != 0:
                    for i in A100D_avilable:
                        enable_mig_cmd = 'nvidia-smi -mig 1 -i {}'.format(i.split('-')[0])
                        cmd_out1, cmd_err = self.excute_cmd_on_server(enable_mig_cmd)
                        if cmd_err.read('\n')[0] == '':
                            logger.info('we enable mig success full on KVM')
                        else:
                            failed += 1
                            continue
                else:
                    logger.info('there is no resource to enable mig on A30')
                if failed == len(A100D_avilable):
                    logger.info('we enable mig on gpu A30 in KVM platform fail')
                    exit(2)
        else:
            logger.info('{} not support mig, exit......'.format(vgpu))
            exit(2)

    def add_vgpu_params_vmware(self, vgpu, *args):
        if args:
            if vgpu.split('-')[0].lower() in ['a40', 'a10', 'a16', 'a30']:
                print('the vgpu mode is nvidia!!!!!!!!!!!!!!11')
                config_vgpu = 'nvidia_' + vgpu.lower()
            else:
                print('the vgpu mode is grid!!!!!!!!!!!!')
                config_vgpu = 'grid_' + vgpu.lower()
            print(vgpu.lower)
            print(config_vgpu)
            config_file = '/usr/share/nvidia/vgx/{}.conf'.format(config_vgpu)
            for arg in args:
                if arg == 'debug':
                    logger.info('we will try to enable debugging on vgpu ---{}'.format(vgpu))
                    param = 'enable_debugging=0x1'
                    if self.verify_vgpu_params(param, config_file) is True:
                        logger.info('no need enable debug, it has been enabled')
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.cuda_enabled/a\plugin0.enable_debugging=0x1' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is True:
                            logger.info('enable debugger successful')
                        else:
                            logger.info('enable debugger fail')
                            exit(2)
                elif arg == 'profile':
                    logger.info('we will try to enable profile on vgpu ---{}'.format(vgpu))
                    param = 'enable_profiling=0x1'
                    if self.verify_vgpu_params(param, config_file) is True:
                        logger.info('no need enable profile, it has been enabled')
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.cuda_enabled/a\plugin0.enable_profiling=0x1' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is True:
                            logger.info('enable profiling successful')
                        else:
                            logger.info('enable profiling fail')
                            exit(2)
                elif arg == 'uvm':
                    logger.info('we will try to enable UVM on vgpu ---{}'.format(vgpu))
                    param = 'enable_uvm=0x1'
                    if self.verify_vgpu_params(param, config_file) is True:
                        logger.info('no need enable uvm, it has been enabled')
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.cuda_enabled/a\plugin0.enable_uvm=0x1' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is True:
                            logger.info('enable uvm successful')
                        else:
                            logger.info('enable uvm fail')
                            exit(2)
                else:
                    continue
        else:
            logger.info('no need add params to vgpu---{}'.format(vgpu))

    def delete_vgpu_params_vmware(self, vgpu, *args):
        if args:
            if vgpu.split('-')[0].lower() in ['a40', 'a10', 'a16', 'a30']:
                config_vgpu = 'nvidia_' + vgpu.lower()
            else:
                config_vgpu = 'grid_' + vgpu.lower()
            config_file = '/usr/share/nvidia/vgx/{}.conf'.format(config_vgpu)
            for arg in args:
                if arg == 'debug':
                    logger.info('we will try to delete debugging on vgpu ---{}'.format(vgpu))
                    param = 'enable_debugging=0x1'
                    if self.verify_vgpu_params(param, config_file) is False:
                        logger.info('no need delete {}, there is no {} enabled'.format(param, param))
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.enable_debugging=0x1/d' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is False:
                            logger.info('delete debugger successful')
                        else:
                            logger.info('delete debugger fail')
                            exit(2)
                elif arg == 'profile':
                    logger.info('we will try to delete profile on vgpu ---{}'.format(vgpu))
                    param = 'enable_profiling=0x1'
                    if self.verify_vgpu_params(param, config_file) is False:
                        logger.info('no need delete {}, there is no {} enabled'.format(param, param))
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.enable_profiling=0x1/d' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is False:
                            logger.info('delete profiling successful')
                        else:
                            logger.info('delete profiling fail')
                            exit(2)
                elif arg == 'uvm':
                    logger.info('we will try to enable UVM on vgpu ---{}'.format(vgpu))
                    param = 'enable_uvm=0x1'
                    if self.verify_vgpu_params(param, config_file) is False:
                        logger.info('no need delete {}, there is no {} enabled'.format(param, param))
                    else:
                        cmd_debug = "vsish -e set /config/VisorFS/intOpts/VisorFSPristineTardisk 0; chmod +wt {}; sed -i '/plugin0.enable_uvm=0x1/d' {} ".format(config_file, config_file)
                        self.excute_cmd_on_server(cmd_debug)
                        time.sleep(3)
                        if self.verify_vgpu_params(param, config_file) is False:
                            logger.info('delete uvm successful')
                        else:
                            logger.info('delete uvm fail')
                            exit(2)
                else:
                    continue
        else:
            logger.info('no need delete params to vgpu---{}'.format(vgpu))

    def modify_vgpu_vmware_vm(self, vgpu, vm_name):
        """
        change vgpu of vm, if power on, do power off first, then change vgpu
        :param vgpu:
        :param vm_name:
        :return: True or False
        """
        # host_vgpu_info = self.get_host_gpu_info()
        self.vm_power_off(vm_name)
        if len(vgpu.split('-')) == 2:
            gpu = vgpu.split('-')[0].lower()
        else:
            gpu = vgpu.split('-')[0].lower() + '-'
        if platform != 'kvm':
            vm_path = self.get_vm_path(vm_name)
            # if gpu in ['t4', 'v100'] or gpu in ['a100-', 'a30-']:
            if gpu in ['t4', 'v100']:
                pgpu_uuid = self.get_gpu_uuid(vgpu)
            elif gpu in ['a100', 'a100-', 'a100d-']:
                pgpu_uuid = "20F1145F0707080808080800000003"  # A100-7-40c  20F1145F0707080808080800000003
            elif gpu in ['a16']:
                pgpu_uuid = "25B614A90500000003"  # A16
            elif gpu in ['a40']:
                pgpu_uuid = "2235145A0606060606060600000003"  # A40-48C
            elif gpu in ['a30', 'a30-']:
                pgpu_uuid = "20B715320707070700000003"  # A30
            elif gpu in ['a10']:
                pgpu_uuid = "2236148206060606060600000003"  # A10-24c
            else:
                logger.info('please give correct vgpu to deploy')
                # pgpu_uuid = "1DB41214XX00000001"  #V100-4c
                # pgpu_uuid = "1EB812A2040404040400000001" T4-8c
                # pgpu_uuid = self.get_gpu_uuid(vgpu)
            cmd_uuid = "cd {}; grep pgpu {}.vmx".format(vm_path, vm_name)
            cmd1_out, cmd_err = self.excute_cmd_on_server(cmd_uuid)
            cmd_out = cmd1_out.read()
            vm_pgpu_uuid = cmd_out.split('\n')[0].split('"')[1]
            logger.info('the vm pgpu uuid is {}'.format(vm_pgpu_uuid))
            cmd_vgpu = "cd {}; grep vgpu {}.vmx".format(vm_path, vm_name)
            cmd2_out, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
            vm_vgpu = cmd2_out.read().split('\n')[0].split('"')[1]
            logger.info('the vm vgpu is {}'.format(vm_vgpu))
            if vgpu.split('-')[0].lower() in ['a40', 'a10', 'a16', 'a30']:
                target_vgpu = 'nvidia_' + vgpu
            else:
                target_vgpu = 'grid_' + vgpu
            logger.info('For vgpu: we want to use {} to replace {} in vm--{}'.format(target_vgpu, vm_vgpu, vm_name))
            logger.info('For pgpu uuid: we want to use {} to replace {} in vm--{}'.format(pgpu_uuid, vm_pgpu_uuid, vm_name))
            replace_vgpu_cmd = "cd {}; sed -i 's/{}/{}/' {}.vmx".format(vm_path, vm_vgpu, target_vgpu, vm_name)
            logger.info('we will vgpu replace cmd---%s' % replace_vgpu_cmd)
            self.excute_cmd_on_server(replace_vgpu_cmd)
            if pgpu_uuid != vm_pgpu_uuid:
                replace_uuid_cmd = "cd {}; sed -i 's/{}/{}/' {}.vmx".format(vm_path, vm_pgpu_uuid, pgpu_uuid, vm_name)
                logger.info('we will uuid replace cmd---%s' % replace_uuid_cmd)
                self.excute_cmd_on_server(replace_uuid_cmd)
            else:
                logger.info('no need do uuid replace')
            time.sleep(30)
            if self.vm_power_on(vm_name) == 'power_on':
                logger.info('we modify {} on {} successful'.format(vgpu, vm_name))
                return True
            else:
                logger.info('we modify {} on {} fail'.format(vgpu, vm_name))
                return False

    def get_exist_vgpu_kvm(self):
        """
         get exist  vgpu list
        :return: dict such as :  T4-4c: 'asdfs-dsaf-sdfsd-fsdfsdf-sdfsdf'
        """
        if platform == 'kvm':
            gpu_list, vgpu_name_list = [], []
            cmd = 'ls /sys/bus/mdev/devices/'
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd_out1.read()
            for i in cmd_out.split('\n')[0:-1]:
                gpu_list.append(i.strip(' '))
            for j in gpu_list:
                cmd1 = 'cat /sys/bus/mdev/devices/{}/mdev_type/name'.format(j)
                cmd1_out1, cmd_err = self.excute_cmd_on_server(cmd1)
                cmd1_out = cmd1_out1.read()
                vgpu_name_list.append(cmd1_out.split(' ')[1].strip('\n'))
            if len(vgpu_name_list) != 0:
                return dict(zip(vgpu_name_list, gpu_list))
            else:
                return False

    def get_available_vgpu_uuid_kvm(self, vgpu):
        """
        :param vgpu:
        :return: vgpu_uuid or False
        """
        used_gpu_info = self.get_used_vgpu_info()
        exist_gpu = self.get_exist_vgpu_kvm()
        available = 0
        if exist_gpu is not False:
            for vgpu1, uuid in exist_gpu.items():
                if vgpu.upper() == vgpu1:
                    if used_gpu_info is not False:
                        for vgpu2, value in used_gpu_info.items():
                            if vgpu1 == vgpu2:
                                if uuid != value['vgpu_uuid']:
                                    available += 1
                                    return uuid
                                    break
                                else:
                                    continue
                    else:
                        return uuid
                    break
                else:
                    continue
            if available == '0':
                logger.info('ther is no available uuid for {}'.format(vgpu))
                return False
        else:
            return False

    def generate_vgpu_kvm(self, vgpu):
        """
        # generate a no mig uuid, then assign it to vgpu, only use it on kvm platform
        :param vgpu:
        :param mig:
        :return: vgpu_uuid or False
        """
        # vgpu is exist in the created vgpu list
        # vgpu_exist = self.get_exist_vgpu_kvm()  # T4-4C: 'asdfs-dsaf-sdfsd-fsdfsdf-sdfsdf'
        # used_vgpu = self.get_used_vgpu_info() # {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
        '''
        print(vgpu_exist)
        print(used_vgpu)
        print('0000000000000000000000000000')
        available_vgpu = {}
        if vgpu_exist and used_vgpu:
            for key, value in used_vgpu.items():
                for key1, value1 in vgpu_exist.items():
                    if key != key1:
                        available_vgpu(key1, value1)
                    else:
                        continue
        if vgpu_exist and used_vgpu is False:
            available_vgpu = vgpu_exist
        for vgpu1, uuid1 in available_vgpu.items():
            if vgpu1.upper() == vgpu.upper():
                vgpu_uuid = uuid1
                break
            else:
                continue
        print(available_vgpu)
        print('----------------')
        '''
        vgpu_uuid = None
        if vgpu_uuid:  # if vgpu is in exist vgpu list and not be used, return it
            return vgpu_uuid
        else:  # if vgpu is not exist, create a new vgpu
            # get vgpu id, such as: v100-16c: 301, get 301
            cmd = "cat /usr/share/nvidia/vgpu/vgpuConfig.xml | grep {}".format(vgpu.upper())
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd)
            cmd_out = cmd_out1.read()
            vgpu_id = cmd_out.split('\n')[0].split('"')[1]
            host_gpu_bus = self.get_gpu_bus_id()
            gpu = vgpu.split('-')[0].upper()
            bus_id_list = []
            print(gpu)
            print(host_gpu_bus)
            print('9999999999999999999999')
            for i in host_gpu_bus:
                if gpu in i:
                    bus_id_list.append(i.split('+')[-2])
                else:
                    continue
            print(bus_id_list)
            print('dddddddddddsssss')
            # generate uuid, then assign it to vgpu
            vgpu_uuid_cmd = 'uuidgen'
            uuid_out1, uuid_err1 = self.excute_cmd_on_server(vgpu_uuid_cmd)
            uuid_out = uuid_out1.read()
            vgpu_uuid = uuid_out.split('\n')[0].strip(' ')
            print(vgpu_uuid)
            config_failed = 0
            for i in bus_id_list:
                if vgpu.split('-')[0].upper() in ['T4', 'V100']:
                    cmd_vgpu = 'echo {} >/sys/bus/pci/devices/0000\:{}\:00.0/mdev_supported_types/nvidia-{}/create'.format(vgpu_uuid, i.split(':')[1].lower(), vgpu_id)
                else:
                    cmd_vgpu = 'echo {} >/sys/bus/pci/devices/0000\:{}\:01.{}/mdev_supported_types/nvidia-{}/create'.format(vgpu_uuid, i.split(':')[1].lower(), random.randint(1, 7), vgpu_id)
                logger.info('we will run create vgpu command--{}'.format(cmd_vgpu))
                cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd_vgpu)
                cmd_err = cmd_err1.read()
                print(cmd_err.split('\n'))
                if cmd_err.split('\n')[0] != '':
                    config_failed += 1
                    continue
                else:
                    return vgpu_uuid
                    break
            print(config_failed)
            print(len(bus_id_list))
            print('rrrrrrrrrrrrrrrrrrrrrrrrr')
            if config_failed == len(bus_id_list):
                logger.info('generate vgpu failed')
                return False

    def add_param_kvm_vgpu(self, vgpu, param):
        """
        add param to new vgpu or not used vgpu(existed)
        :param vgpu:
        :param param:
        :return: True or False
        """
        if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
            uuid = self.get_available_vgpu_uuid_kvm(vgpu)
        else:
            uuid = self.generate_vgpu_kvm(vgpu)
        if param:
            if param.lower() == 'debug':
                cmd = 'echo "enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'profile':
                cmd = 'echo "enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'uvm':
                cmd = 'echo "enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'debug_profile':
                cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'debug_uvm':
                cmd = 'echo "enable_debugging=0x1, enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'profile_uvm':
                cmd = 'echo "enable_uvm=0x1, enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            elif param.lower() == 'all':
                cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            else:
                logger.info('please give correct param ---- {} to config'.format(param))
        cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
        cmd_err = cmd_err1.read()
        if cmd_err.split('\n')[0]:
            logger.info('add the tools option  by {} ----fail'.format(cmd))
            return False
        else:
            logger.info('add the tools option  by {} ----successful'.format(cmd))
            return True

    def delete_param_kvm_vm(self, vgpu, param):
        """
         delete params of not used vgpu (exist)
        :param vgpu:
        :param param:
        :return: True or False
        """
        if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
            uuid = self.get_available_vgpu_uuid_kvm(vgpu)
            gpu = vgpu.split('-')[0].lower()
            config_file = '/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
            if param:
                if param == 'all':
                    cmd = 'echo "1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                    pattern = '1'
                elif param == 'debug_profile':
                    if gpu in ['a40', 'a16', 'a10']:
                        cmd = 'echo "enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        pattern = "enable_uvm=0x1"
                    else:
                        cmd = 'echo "1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        pattern = '1'
                elif param == 'debug_uvm':
                    cmd = 'echo "enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                    pattern = 'enable_profiling=0x1'
                elif param == 'profile_uvm':
                    cmd = 'echo "enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                    pattern = 'enable_debugging=0x1'
                elif param == 'debug':
                    if gpu in ['a40', 'a16', 'a10']:
                        cmd = 'echo "enable_uvm=0x1,enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                            uuid)
                        pattern = 'enable_uvm=0x1,enable_profiling=0x1'
                    else:
                        cmd = 'echo "enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        pattern = 'enable_profiling=0x1'
                elif param == 'profile':
                    if gpu in ['a40', 'a16', 'a10']:
                        cmd = 'echo "enable_uvm=0x1,enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        pattern = 'enable_uvm=0x1,enable_debugging=0x1'
                    else:
                        cmd = 'echo "enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        pattern = 'enable_uvm=0x1,enable_debugging=0x1'
                elif param == 'uvm':
                    cmd = 'echo "enable_debugging=0x1,enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                    pattern = 'enable_debugging=0x1,enable_profiling=0x1'
                else:
                    logger.info('please give correct param to delete')
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
            if self.verify_vgpu_params(pattern, config_file):
                logger.info('we delete the param---{} successful'.format(param))
                return True
            else:
                logger.info('we delete the param---{} fail'.format(param))
                return False
        else:
            logger.info('there is no {} exit in the kvm platform, can not delete the vgpu params')
            exit()

    def add_vgpu_kvm_vm(self, vgpu, vm_name, param):
        """
        add param to new vgpu or existed vgpu, then assign vgpu to vm
        :param vgpu:
        :param vm_name:
        :param param:
        :return: True or False
        """
        if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
            uuid = self.get_available_vgpu_uuid_kvm(vgpu)
        else:
            if self.generate_vgpu_kvm(vgpu) is not False:
                uuid = self.generate_vgpu_kvm(vgpu)
            else:
                logger.info('there is no vgpu to use, exist.......')
                exit(2)
        self.add_param_kvm_vgpu(vgpu, param)
        cmd_uuid = "grep 'uuid=' /etc/libvirt/qemu/{}.xml".format(vm_name)
        cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd_uuid)
        cmd_out = cmd_out1.read()
        vm_uuid_list = []
        for i in cmd_out.split('\n')[0:-1]:
            vm_uuid_list.append(i.split("'")[1])
        if len(vm_uuid_list) != 1 or len(vm_uuid_list) == 0:
            logger.info('please check the vm, it has 2 vgpu in xml file or it has no exist vgpu in xml')
            exit()
        else:
            replace_cmd = 'cp /etc/libvirt/qemu/{}.xml /etc/libvirt/qemu/{}.xml_bak;  sed -i "s#{}#{}#g" /etc/libvirt/qemu/{}.xml; virsh define /etc/libvirt/qemu/{}.xml; virsh start {}; sleep 20'.format(vm_name, vm_name, vm_uuid_list[0], uuid, vm_name, vm_name, vm_name)
            self.excute_cmd_on_server(replace_cmd)
            if self.get_vm_status(vm_name) == 'power_on':
                logger.info('we add vgpu to kvm vm--{} successful'.format(vm_name))
                return True
            else:
                logger.info('we add vgpu to kvm vm--{} fail'.format(vm_name))
                return False

    def modify_vgpu_kvm_vm(self, vgpu, vm_name):
        """
        for KVM: modify vgpu of vm_name,  change vgpu of vm_name is "param: vgpu"
        :param vgpu: new vgpu
        :param vm_name:
        :return: vpug_uuid  # will use for add tools params to vgpu or not add
        """
        if platform == 'kvm':
            if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
                vgpu_uuid = self.get_available_vgpu_uuid_kvm(vgpu)
            else:
                if self.generate_vgpu_kvm(vgpu) is not False:
                    vgpu_uuid = self.generate_vgpu_kvm(vgpu)
                else:
                    logger.info('there is no vgpu to use, exist.......')
                    exit(2)
            cmd = "grep 'uuid=' /etc/libvirt/qemu/{}_auto.xml |awk -F = '{print $2}'"
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
            cmd_out = cmd_out1.read()
            old_uuid = cmd_out.split('\n')[0].split("'")[1]
            replace_cmd = 'sed -i "s#{}#{}#g" /etc/libvirt/qemu/{}.xml; virsh define /etc/libvirt/qemu/{}.xml'.format(old_uuid, vgpu_uuid, vm_name, vm_name)
            cmd_out2, cmd_err2 = self.excute_cmd_on_server(replace_cmd)
            cmd1_err = cmd_err2.read()
            if cmd1_err.split('\n')[0]:
                logger.info('modify the vgpu --{} from {} to {} fail'.format(vgpu, old_uuid, vgpu_uuid))
                return False
            else:
                logger.info('modify the vgpu --{} from {} to {} successful'.format(vgpu, old_uuid, vgpu_uuid))
                return vgpu_uuid  # will use for add params to vgpu or not add
        else:
            logger.info('please give correct platform, now platform is {}'.format(platform))
            return False

    def release_vgpu_on_kvm(self, vgpu, **kwargs):
        """
        delete vgpu
        :param vgpu: such as T4-8C
        :param kwargs: can not power off the vm, if the vm is using the vgpu, such as : vm='ubuntu20.04-desktop-108' is using T4-8c
        :return:
        """
        used_vgpu = self.get_used_vgpu_info()
        exist_vgpu = self.get_exist_vgpu_kvm()
        if exist_vgpu is False:
            logger.info('there is no vgpu to delete')
            return False
        else:
            print('there are vgpu to delete')
            uuid_list = []
            for key, value in exist_vgpu.items():
                if key == vgpu.upper():
                    uuid_list.append(value)
                else:
                    continue
            print(uuid_list)
            print('00000000000dddddddddddddddddddd')
            if len(uuid_list) == 0:
                logger.info('there is no {} to delete'.format(vgpu))
                return False
            elif len(uuid_list) == 1:
                if used_vgpu is not False:
                    if vgpu.upper() in used_vgpu.keys():
                        for vgpu1, value1 in used_vgpu.items():
                            if vgpu.upper() == vgpu1:
                                uuid = used_vgpu[vgpu1]['vgpu_uuid']
                                vm_name = value1['vm_name']
                                self.vm_power_off(vm_name)
                                cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                                self.excute_cmd_on_server(cmd)
                                cmd1 = 'ls /sys/bus/mdev/devices/'
                                cmd1_out1, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                                cmd1_out = cmd1_out1.read()
                                if uuid not in cmd1_out.split('\n')[0]:
                                    logger.info('delete vgpu successful')
                                    return True
                                else:
                                    logger.info('delete vgpu fail')
                                    return False
                            else:
                                continue
                    else:
                        logger.info('we will delete the vgpu ----- {}'.format(uuid_list[0]))
                        uuid = uuid_list[0]
                        cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                        self.excute_cmd_on_server(cmd)
                        cmd1 = 'ls /sys/bus/mdev/devices/'
                        cmd1_out1, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                        cmd1_out = cmd1_out1.read()
                        if uuid not in cmd1_out.split('\n')[0]:
                            logger.info('delete vgpu successful')
                            return True
                        else:
                            logger.info('delete vgpu fail')
                            return False
                else:
                    logger.info('we will delete the vgpu ----- {}'.format(uuid_list[0]))
                    uuid = uuid_list[0]
                    cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                    self.excute_cmd_on_server(cmd)
                    cmd1 = 'ls /sys/bus/mdev/devices/'
                    cmd1_out1, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                    cmd1_out = cmd1_out1.read()
                    if uuid not in cmd1_out.split('\n')[0]:
                        logger.info('delete vgpu successful')
                        return True
                    else:
                        logger.info('delete vgpu fail')
                        return False
            else:
                if used_vgpu is not False:
                    uuid_list1, vm_list1 = [], []
                    for vgpu2, value2 in used_vgpu.items():
                        if vgpu.upper() == vgpu2:
                            uuid_list1.append(value2['vgpu_uuid'])
                            vm_list1.append(value2['vm_name'])
                        else:
                            continue
                    vgpu_info = dict(zip(vm_list1, uuid_list1))
                    if kwargs:  # do not power off this vm
                        closed_vm = [i for i in uuid_list1 not in kwargs.values()]
                    else:
                        closed_vm = vm_list1
                    remove_uuid_list = []
                    for vm in closed_vm:
                        self.vm_power_off(vm)
                        remove_uuid_list.append(vgpu_info[vm])
                else:
                    remove_uuid_list = uuid_list
                passed = 0
                for remove_uuid in remove_uuid_list:
                    remove_cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(remove_uuid)
                    self.excute_cmd_on_server(remove_cmd)
                    cmd1 = 'ls /sys/bus/mdev/devices/'
                    cmd1_out1, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                    cmd1_out = cmd1_out1.read()
                    if remove_uuid not in cmd1_out.split('\n')[0]:
                        logger.info('delete vgpu {}: {} successful'.format(vgpu, remove_uuid))
                        passed += 1
                    else:
                        logger.info('delete vgpu {}: {} fail'.format(vgpu, remove_uuid))
                if passed == len(remove_uuid_list):
                    logger.info('remove all vgpu successful')
                    return True
                else:
                    logger.info('can not remove all vgpu')
                    return False


def server_alive(ip):
    for i in range(1, 6):
        if ip_alive(ip) is False:
            logger.info('try connect server {} time'.format(i))
            ip_alive(ip)
            time.sleep(60)
        else:
            break
    if ip_alive(ip) is True:
        return True
    else:
        return False


if __name__ == '__main__':
    vgpu_server = Vgpu_server()
    if server_args.guest_driver:
        guest_drive_url = '//builds/linuxbuilds/release/display/VGX-x86_64/{}'.format(server_args.guest_driver)
        guest_driver = 'NVIDIA-Linux-x86_64-{}-grid.run'.format(server_args.guest_driver)
    if server_args.server_operate:
        if server_args.server_operate.lower() == 'install':
            host_driver = prepare_driver('host')
            scp_file(server_ip, server_user, server_password, '{}/{}'.format(download_path, host_driver), driver_path)
            vgpu_server.install_host_driver(driver_path, host_driver)
        elif server_args.server_operate.lower() == 'upgrade':
            host_driver = prepare_driver('host')
            scp_file(server_ip, server_user, server_password, '{}/{}'.format(download_path, host_driver), driver_path)
            vgpu_server.upgrade_host_driver(driver_path, host_driver)
        elif server_args.server_operate.lower() == 'uninstall':
            vgpu_server.uninstall_host_driver()
        else:
            logger.info('please give correct operation to operate driver of server, such as install/uninstall/upgrade')
    if server_args.add_params:
        if server_args.platform.lower() == 'esxi':
            if server_args.add_params.lower() == 'all':
                vgpu_server.add_vgpu_params_vmware(server_args.vgpu, 'debug', 'profile', 'uvm')
            elif server_args.add_params.lower() == 'debug_profile':
                vgpu_server.add_vgpu_params_vmware(server_args.vgpu, 'debug', 'profile')
            elif server_args.add_params.lower() == 'debug':
                vgpu_server.add_vgpu_params_vmware(server_args.vgpu, 'debug')
            elif server_args.add_params.lower() == 'profile':
                vgpu_server.add_vgpu_params_vmware(server_args.vgpu, 'profile')
            else:
                vgpu_server.add_vgpu_params_vmware(server_args.vgpu, 'uvm')
        elif server_args.platform.lower() == 'kvm':
            vgpu_server.add_param_kvm_vgpu(server_args.vgpu, server_args.add_params.lower())
        else:
            logger.info('please give correct params to operate')
    if server_args.modify_vgpu:
        if server_args.platform.lower() == 'esxi':
            vgpu_server.modify_vgpu_vmware_vm(server_args.modify_vgpu, server_args.vm_name)
            vgpu_server.vm_power_on(server_args.vm_name)
        elif server_args.platform.lower() == 'kvm':
            vgpu_server.modify_vgpu_kvm_vm(server_args.modify_vgpu, server_args.vm_name)
            if server_args.add_params:
                vgpu_server.add_param_kvm_vgpu(server_args.vgpu, server_args.add_params.lower())
                if vgpu_server.add_param_kvm_vgpu(server_args.vgpu, server_args.add_params.lower()) is not False:
                    vgpu_server.vm_power_on(server_args.vm_name)
            else:
                vgpu_server.vm_power_on(server_args.vm_name)
        else:
            logger.info('please give correct platform to operate')
            exit(2)
    if server_args.delete_params:
        if server_args.platform.lower() == 'esxi':
            if server_args.delete_params.lower() == 'all':
                vgpu_server.delete_vgpu_params_vmware(server_args.vgpu, 'debug', 'profile', 'uvm')
            elif server_args.delete_params.lower() == 'debug_profile':
                vgpu_server.delete_vgpu_params_vmware(server_args.vgpu, 'debug', 'profile')
            elif server_args.delete_params.lower() == 'debug':
                vgpu_server.delete_vgpu_params_vmware(server_args.vgpu, 'debug')
            elif server_args.delete_params.lower() == 'profile':
                vgpu_server.delete_vgpu_params_vmware(server_args.vgpu, 'profile')
            else:
                vgpu_server.delete_vgpu_params_vmware(server_args.vgpu, 'uvm')
        elif server_args.platform.lower() == 'kvm':
            if server_args.delete_params.lower() == 'all':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'debug', 'profile', 'uvm')
            elif server_args.delete_params.lower() == 'debug_profile':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'debug', 'profile')
            elif server_args.delete_params.lower() == 'debug':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'debug')
            elif server_args.delete_params.lower() == 'profile':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'profile')
            else:
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'uvm')
        else:
            logger.info('please give correct params to operate')
    if server_args.create_vgpu:
        if platform == 'kvm':
            vgpu_server.generate_vgpu_kvm(server_args.create_vgpu)
    if server_args.vm_operate:
        if server_args.vm_operate.lower() == 'power_on':
            vgpu_server.vm_power_on(server_args.vm_name)
        elif server_args.vm_operate.lower() == 'power_off':
            vgpu_server.vm_power_off(server_args.vm_name)
        else:
            logger.info('please give correct option to operate vm')
    if server_args.guest_driver:
        prepare_driver('guest')
        if vgpu_server.vm_power_on(server_args.vm_name):
            ip_list = vgpu_server.get_vm_ip(server_args.vm_name)
            print(ip_list)
            logger.info('the vm has ip address---{}'.format(ip_list))
            scp_file(ip_list[0], 'lab', 'labuser', '{}/{}'.format(download_path, guest_driver), '/home/<USER>')
            uninstall_driver(ip_list[0], 'lab', 'labuser', 'labuser')
            install_driver(ip_list[0], 'lab', 'labuser', 'labuser', guest_driver)
    if server_args.platform == 'kvm':
        if server_args.release_vgpu:
            vgpu_server.release_vgpu_on_kvm(server_args.release_vgpu)

    vgpu_server.disconnect_server()


example_text = """
Example:
python config_server.py -s 157 -p kvm -hd 520.18 -so upgrade/install/uninstall     #  upgrade/install/uninstall host driver
python config_server.py -s 155 -p esxi -a debug_profile -vg A40-48c # add tools option--debug and profile to A40-48c
python config_server.py -s 155 -p esxi -mg v100-16c -vm ubuntu20.04-desktop-auto # modify vgpu of ubuntu20.04-desktop-auto to v100-16c
python config_server.py -s 157 -p kvm -cg v100-16c #  create a vgpu---"v100-16c" on kvm-157
python config_server.py -s 155 -p esxi -d all -vg A40-48c  # delete all the tools option on vgpu-----A40-48c
python config_server.py -s 155 -p esxi -vo power_on -vm ubuntu20.04-desktop-auto   # power on vm on server 155
"""
version = '1.0'
# host_driver = 'NVIDIA_bootbank_NVIDIA-AIE_ESXi_7.0.2_Driver_515.44-1OEM.702.0.0.********.vib'
# host_driver, guest_driver = prepare_driver()
# host_driver = 'NVIDIA_bootbank_NVIDIA-AIE_ESXi_7.0.2_Driver_510.73.06-1OEM.702.0.0.********'
# scp_file(server_ip, server_user, server_password, '{}/{}'.format(download_path, host_driver), target_path)
# host_driver = 'NVIDIA-vGPU-rhel-8.5-510.71.x86_64.rpm'
'''
vgpu_server = Vgpu_server()
# print vgpu_server.get_host_driver()
#vgpu_server.install_host_driver(target_path, host_driver)
# print(vgpu_server.get_host_driver_version())

vm_name = 'ubuntu20.04-desktop-auto'
try:
    #vgpu_server.upgrade_host_driver(target_path, host_driver)
    vgpu_server.add_vgpu_params_vmware('a100-40c', 'debug', 'profile')
    vgpu_server.add_vgpu_params_vmware('t4-8c', 'debug', 'profile')
    vgpu_server.vm_power_on(vm_name)
    vgpu_server.vm_power_on('ubuntu20.04-desktop12')
    #vgpu_server.modify_vgpu_vmware_vm('t4-8c', vm_name)

    # vgpu_server.generate_vgpu_kvm('v100-16c')
    # vgpu_server.add_vgpu_kvm_vm('v100-16c', vm_name, uvm='None')
    #vgpu_server.vm_power_on('ubuntu20.04-desktop-108')
    #vgpu_server.add_vgpu_params_vmware('a16-8c', 'debug', 'profile')
    #vgpu_server.modify_vgpu_vmware_vm('a16-8c', vm_name)
    #vgpu_server.vm_power_on('ubuntu20.04-desktop-108')
    #vgpu_server.vm_power_on(vm_name)
    #vgpu_server.generate_vgpu_kvm('a100-20c', mig=None)
    # vgpu_server.vm_power_on(vm_name)
    # vgpu_server.get_vm_status(vm_name)
    current_driver_version = vgpu_server.get_host_driver_version()
    vgpu_server.uninstall_host_driver()
    time.sleep(120)
    vgpu_server1 = Vgpu_server()
    # if vgpu_server1.get_host_driver_version() is not False:
    logger.info('we uninstall the driver---{} successful'.format(current_driver_version))
    logger.info('we will install the driver---{}'.format(host_driver_version))
    vgpu_server1.install_host_driver(target_path, host_driver)
    current_driver_version = vgpu_server.get_host_driver_version()
    if host_driver_version > current_driver_version:
        logger.info('we will upgrade the host driver from {} to {}'.format(current_driver_version, host_driver_version))
        vgpu_server.upgrade_host_driver(target_path, host_driver)
    else:
        vgpu_server.uninstall_host_driver()
        time.sleep(180)
        vgpu_server1 = Vgpu_server()
        # if vgpu_server1.get_host_driver_version() is not False:
        logger.info('we uninstall the driver---{} successful'.format(current_driver_version))
        logger.info('we will install the driver---{}'.format(host_driver_version))
        vgpu_server1.install_host_driver(target_path, host_driver)
    else:
        logger.info('now there is no driver on host, so install the driver---{}'.format(host_driver))
        vgpu_server.install_host_driver(target_path, host_driver)
        if vgpu_server.get_host_driver_version() == host_driver_version:
                logger.info('we install the host driver--{} successful'.format(host_driver))
        else:
                logger.info('we install the host driver--{} fail'.format(host_driver))
# vgpu_info = vgpu_server.get_used_vgpu_info()
# print(vgpu_info)
finally:
    vgpu_server.disconnect_server()
'''
