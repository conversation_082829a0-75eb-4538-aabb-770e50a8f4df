import re
import sys
import pexpect


def regex_compare(value, regex):
    if re.search(regex, value, re.M | re.S):
        print("\n&&&& PASS, match pattern:{}\n".format(regex))
    else:
        print("\n&&&& FAIL\n")
        print(("\n".join(["-" * 20, "result:%s", "expect include:%s", "-" * 20]) % (value, regex)))
    sys.stdout.flush()


class Gdb:
    VM_PROMPT = r"\(cuda-gdb\) "
    CONTINUE_WITHOUT_PAGING = "c to continue without paging--"
    CONFORM_Y_OR_N = r"\(y or n\) "

    def __init__(self, spawn_command, timeout=60, env=None):
        # access to gdb and stop at first prompt
        self._c = pexpect.spawn(spawn_command, timeout=timeout, env=env, maxread=10000)
        self._c.logfile = sys.stdout.buffer
        self.result = ""
        index = self._c.expect([Gdb.VM_PROMPT, Gdb.CONTINUE_WITHOUT_PAGING], timeout)
        if index == 1:
            self.result = self._c.before.decode().strip(spawn_command).lstrip().rstrip()
            self.result += Gdb.CONTINUE_WITHOUT_PAGING
            self._c.sendline("c")
            self._c.expect(Gdb.VM_PROMPT, timeout)
        self.result += self._c.before.decode().strip(spawn_command).lstrip().rstrip()
        self.go_on = True

    def execute_and_compare(self, step, expect="", prompt=VM_PROMPT, timeout=120, include=True):
        # skip following steps if fail already happen
        if not self.go_on:
            return
        if step:
            self._c.sendline(step)
            # skip if step is q, and have expectation in the meanwhile
            if step == "q" and len(expect) == 0:
                index = self._c.expect([Gdb.CONFORM_Y_OR_N, pexpect.EOF], timeout)
                if index == 0:
                    self._c.sendline("y")
                    index1 = self._c.expect([pexpect.EOF, pexpect.TIMEOUT], timeout)
                    if index1 == 1:
                        print("\nTimeout when quit cuda-gdb in %s seconds, quit now\n" % timeout)
                        self._c.close()
                        sys.exit(0)
                if index == 1:
                    pass
                self._c.close()
                return
        try:
            # in case ouput is null
            self.result = ""
            index = self._c.expect([prompt, Gdb.CONTINUE_WITHOUT_PAGING, pexpect.EOF], timeout)
            if index == 0:
                self.result = self._c.before.decode().strip(step).lstrip().rstrip()
            if index == 1:
                self.result = self._c.before.decode().strip(step).lstrip().rstrip()
                self.result += Gdb.CONTINUE_WITHOUT_PAGING
                self._c.sendline("c")
                self._c.expect(prompt, timeout)
                self.result += self._c.before.decode().strip(step).lstrip().rstrip()
            if index == 2:
                print("\nEOF, as expected\n")
        except Exception as e:
            print("\n&&&& FAIL, Exception happen, will skip all following steps\n")
            print(e)
            self.go_on = False
        # ignore ANSI colors in pexpect response
        # https://stackoverflow.com/questions/14693701/how-can-i-remove-the-ansi-escape-sequences-from-a-string-in-python
        ansi_escape = re.compile(r"\x1b(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
        self.result = ansi_escape.sub("", self.result)
        # compare with expectation
        if re.search(expect, self.result, re.M | re.S):
            # want to include expectation
            if include:
                print("\n&&&& PASS\n")
            # want to exclude expectation
            else:
                print("\n&&&& FAIL, will skip all following steps\n")
                print(("\n".join(["-" * 20, "cmd:%s", "result:%s", "expect NOT include:%s", "-" * 20]) % (step, self.result, expect)))
                self.go_on = False
        else:
            if include:
                print("\n&&&& FAIL, will skip all following steps\n")
                print(("\n".join(["-" * 20, "cmd:%s", "result:%s", "expect include:%s", "-" * 20]) % (step, self.result, expect)))
                self.go_on = False
            else:
                print("\n&&&& PASS\n")
        sys.stdout.flush()
