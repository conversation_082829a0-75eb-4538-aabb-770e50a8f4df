# -*- encoding: utf-8 -*-
import os.path

try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
import argparse
import re
try:
    import colorlog
except ImportError:
    os.popen('pip3 install colorlog')
import os
import random
import paramiko
import threading
import queue
import pexpect
import collections
import subprocess
import sys
from scp import SCPClient
from common_utils import *
import time
import logging
import argparse

path = os.getcwd()
from yaml_mgr import YamlManger

cuda_gdb_yaml = '{}/../yaml/cuda_gdb_case1.yaml'.format(os.getcwd())
yaml_mgr = YamlManger(cuda_gdb_yaml)
case_config = yaml_mgr.load()

cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
host_home = case_config['global']['env']['HOST_HOME']
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH1']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH1']
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH']
else:
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH2']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH2']
cuda_path = case_config['global']['env']['CUDA_PATH']
cuda_bin = '{}/bin'.format(cuda_path)
tools_home = case_config['global']['env']['TOOLS_HOME']
date1 = time.strftime("%Y%m%d", time.localtime())
debugger_download_to_path = case_config['global']['env']['DEBUGGER_DOWNLOAD_PATH'].format(date1)
debugger_run_path = case_config['global']['env']['DEBUGGER_RUN_PATH']
cuda_app_download_path = case_config['global']['env']['CUDA_APP_DOWNLOAD_PATH'].format(date1)
platform = case_config['global']['env']['PLATFORM'].lower()
if platform == 'x86':
    debugger_bin_path = debugger_download_to_path + '/bin/x86_64_Linux_release/debugger-testsuite'
else:
    debugger_bin_path = debugger_download_to_path + '/bin/aarch64_Linux_release/debugger-testsuite'
mkdir(tools_home)
output_flag = case_config['global']['env']['OUTPUT_FLAG']
vgpu = case_config['global']['env']['VGPU'].lower()
host_password = case_config['global']['env']['HOST_PASSWORD']
password1 = case_config['global']['env']['CQA_PASSWORD']
user1 = case_config['global']['env']['CQA_USER']
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
cuda_major = case_config['global']['env']['CUDA_MAJOR']
driver_branch = case_config['global']['env']['DRV_BRANCH']
cuda_min = case_config['global']['env']['CUDA_MINOR']
# date1 = case_config['global']['env']['DATE1']
dvs_build = case_config['global']['env']['DVS_BUILD']
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
logger = logging.getLogger()
logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/cuda_gdb_case.txt' % tools_home)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger = logging.getLogger(__name__)


def get_sm():
    sm_cmd = case_config['PREPARE']['CMD3'] % sample_1_path
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        sm = sm_out['output']
        print(sm_out['output'])
        print('=++++=======================')
        print('---*** sm is {} ***---'.format(sm.split('\n')[-1].strip(' ')))
        print('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        if len(sm.split('\n')[-1].strip(' ')) == 3:
            return float(sm.split('\n')[-1].strip(' '))
        else:
            logger.info('we get the sm failed， please check deviceQuery sample')
            exit()
    else:
        logger.info('we get the sm failed， please check deviceQuery sample')
        exit()


class CUDBGExpect():

    def __init__(self, cmd=None, logfile=None, mode=0, closure=None):
        self.step_count = 0
        self.step_status = "go_on"
        self.step_result = True
        self.judgemode = mode
        self.spawn_cmd = cmd or None
        self.timeout = 60
        self.step_pass, self.step_fail = 0, 0
        self.record = collections.OrderedDict()
        self.logfile_path = os.path.realpath(logfile) and logfile or None
        self.logfile = logfile and open(logfile, "ab") or None
        self.closure = closure and "rm -rf /tmp/cuda-dbg"
        self.PATTERN = [r"\(cuda-gdb\) ", "c to continue without paging--", r"y/n", r"\(y or n\)", r"\(y or \[n\]\)", pexpect.EOF, pexpect.TIMEOUT]
        self.RAWPATTERN = [
            r"(cuda-gdb) ",
            "c to continue without paging--",
            r"y/n",
            r"(y or n)",
            r"(y or [n])",
            "pexpect.EOF",
            "pexpect.TIMEOUT",
        ]
        if self.spawn_cmd:
            self.spawn(self.spawn_cmd)

    __repr__ = lambda self: f'<CUDBGExpect {self.spawn_cmd}|{self.step_status}|{self.step_result}>'
    __getstate__ = lambda self: self.step_status
    __str__ = lambda self: f'{self.spawn_cmd}|{self.step_cmd}|{self.status}|{self.step_count}'

    def stepper(self, step_checkpoint, mode=None):
        # sourcery skip: extract-duplicate-method
        idx = self.step_count
        # clear step output first
        judgemode = mode or self.judgemode
        self.step_output = ""
        self.step_count += 1
        self.step_match = self.RAWPATTERN[self.step_rc]
        logger.debug(f"## match **{self.step_match}**")
        self.step_checkpoint = step_checkpoint
        try:
            self.step_output = (self.process.before + self.process.after + self.process.buffer).decode()
        except:
            self.step_output = ''
        # self.step_output = (self.process.before + self.process.after + self.process.buffer).decode()
        if self.step_rc == 0:
            self.step_status = "go_on"
            logger.info(f"=> {self.step_status}")
        elif self.step_rc == 1:
            self.step_status = "c_to_go"
            logger.info(f"=> {self.step_status}")
            self.gdbconfirm("continue")
        elif self.step_rc in [2, 3, 4]:
            self.step_status = "y_to_go"
            logger.info(f"=> {self.step_status}")
            self.gdbconfirm("y")
        elif self.step_rc == 5:
            self.step_status = "end"
            logger.info(f"=> {self.step_status}")
        else:
            self.step_status = "exception"
            logger.info(f"=> {self.step_status}")
            self.gdbexit()

        print(self.step_output)

        self.step_rawout = re.sub(r"\x1b\[([0-9]{2};){0,2}([0-9]{2})?m", "", self.step_output)
        if self.step_status == "go_on":
            self.step_result = self.CUDBGJudge(checkpoint=self.step_checkpoint, output=self.step_rawout, mode=judgemode)
        elif self.step_status == "end":
            self.step_result = True
        elif self.step_status == "exception":
            self.step_result = False
        pof = 'Pass' if self.step_result else 'Fail'
        msg = f"{pof} {self.step_cmd}"
        logger.info("\n" + self.surround_result(msg))
        self._write(f"&& {pof} {self.step_cmd} &&")

        self.step_pass += 1 if self.step_result else 0
        self.step_fail += 0 if self.step_result else 1

        self.record[idx] = {
            "idx": idx,
            "cmd": self.step_cmd,
            "match": self.step_match,
            "status": self.step_status,
            "checkpoint": self.step_checkpoint,
            "result": self.step_result,
            "rawout": self.step_rawout,
            "output": self.step_output
        }
        return self.step_result

    def CUDBGJudge(self, checkpoint, output, mode=0, case=False):
        """
        None and 0 are offset
        0 - 00: search + or  partially serach, or
        1 - 01: search + and partially serach, and
        2 - 10: match  + or  complete match, or
        3 - 11: match  + and complete match, and
        """
        if not checkpoint:
            checklist = []
            return True
        elif isinstance(checkpoint, str):
            checklist = [checkpoint]
        elif isinstance(checkpoint, list):
            checklist = checkpoint
        else:
            raise TypeError("Checkpoint Type not Allowed")
        fail_count = 0
        pass_count = 0
        if mode not in [0, 1, 2, 3]:
            return False
        for chkp in checklist:
            if mode in [0, 1]:
                res = case and re.search(chkp, output) or re.search(chkp, output, re.I)
            elif mode in [2, 3]:
                res = case and re.match(chkp, output) or re.match(chkp, output, re.I)
            fail_count += res and 0 or 1
            pass_count += res and 1 or 0
        return pass_count > 0 if mode in [0, 2] else fail_count == 0

    def gdbconfirm(self, cmd):
        self.process.sendline(cmd)
        try:
            self.step_rc = self.process.expect(self.PATTERN, timeout=self.timeout)
            self.step_match = self.RAWPATTERN[self.step_rc]
            self.step_output = self.step_output + (self.process.before + self.process.after + self.process.buffer).decode()
            if self.step_rc == 0:
                # this means that confirm to go, need to merge output
                self.step_status = "go_on"
                logger.info(f"=> {self.step_status}")
            elif self.step_rc == 1:
                self.step_status = "exception"
                logger.error("=> {self.step_status} Continue Failed")
                self.gdbexit()
            elif self.step_rc in [2, 3, 4]:
                self.step_status = "exception"
                logger.error("=> {self.step_status} y Failed")
                self.gdbexit()
            elif self.step_rc == 5:
                self.step_status = "end"
                logger.info(f"=> {self.step_status}")
                self.gdbexit()
            else:
                self.step_status = "exception"
                logger.error("Confirm - Something wrong")
                self.gdbexit()
        except Exception:
            self.gdbexception(Exception, f"gdbconfirm {cmd}")
            self.gdbexit()

    def spawn(self, cmd, checkpoint=None, timeout=None, userenv=None, mode=None):
        self.step_count = 0
        self.spawn_cmd = cmd
        self.userenv = userenv
        self.step_cmd = cmd
        self.step_output = ""
        checkpoint = checkpoint or r"NVIDIA (R) CUDA Debugger"
        self.step_tmout = timeout or self.timeout
        self.process = pexpect.spawn(self.spawn_cmd, timeout=self.step_tmout, logfile=self.logfile, env=self.userenv)
        try:
            self.step_rc = self.process.expect(self.PATTERN, timeout=self.step_tmout)
            return self.stepper(checkpoint, mode=mode)
        except Exception:
            self.gdbexception(Exception, f"spawn {cmd}")
            return self.stepper(checkpoint, mode=mode)

    def expect(self, cmd, checkpoint=None, timeout=None, mode=None):
        self.step_output = ""
        step_tmout = timeout or self.timeout
        self.step_cmd = cmd
        self.process.sendline(self.step_cmd)
        try:
            self.step_rc = self.process.expect(self.PATTERN, timeout=step_tmout)
            return self.stepper(checkpoint, mode=mode)
        except Exception:
            self.gdbexception(Exception, f"expect {cmd}")
            return self.stepper(checkpoint, mode=mode)

    def gdbexception(self, exception, name):
        if self.step_cmd in ["q", "quit"]:
            message = "Quit debug session"
            logger.info(message)
            self._write(message)
            self.step_status = "end"
            self.step_match = ""
            self.step_output = self.step_output
        else:
            message = f"Exception - {name} throw {exception}"
            logger.error(message)
            self._write(message)
            self.step_status = "exception"
            self.step_match = f"Exception {Exception.__name__}"
            self.step_output = self.step_output + Exception.__name__

    def gdbexit(self):
        logger.info("Exit and Clear GDB Session")
        self.process.close()
        if self.closure:
            run_test_cmd(self.closure)
        if self.logfile:
            self.logfile.close()

    def _write(self, message):
        if self.logfile:
            if self.logfile.closed:
                with open(self.logfile_path, "ab") as f:
                    f.write(f"{message}\n".encode())
            else:
                self.logfile.write(f"{message}\n".encode())

    def break_on_launch(self, mode=None):
        if not mode or mode not in ["all", "app", "none"]:
            mode = "all"
            logger.info("default set to break_on_launch all")
        self.expect(f"set cuda break_on_launch {mode}")

    def run(self):
        return self.expect("run")

    def quit(self):
        return self.expect("quit")

    def setbreak(self, where=None):
        return self.expect(f"break {where}")

    def backtrace(self):
        return self.expect("backtrace")

    def dumps_record(self, flag=True):
        import json
        if flag:
            print(json.dumps(self.record, indent=2))
        return json.dumps(self.record, indent=2)

    def dumps_items(self, items=None):
        """print dict in json dums and return a dict, with items both filtered

        Args:
            items (_type_, optional): str or list of str. Defaults to None to all.

        Returns:
            dict: filtered new dict
        """
        import json
        if not items:
            return self.dumps_items()
        dump_dict = collections.OrderedDict()
        item_list = items if isinstance(items, list) else [items]
        for idx, value in self.record.items():
            dump_dict[idx] = {}
            for item in item_list:
                if item in value.keys():
                    dump_dict[idx][item] = value[item]
        print(json.dumps(dump_dict, indent=2))
        return dump_dict

    def dumps_idxs(self, idxs=None):
        """print dict in json dums and return a dict, with idxs filtered

        Args:
            idxs (_type_, optional): int or list of int. Defaults to None to all.

        Returns:
            dict: filtered new dict
        """
        import json
        if not idxs:
            return self.dumps_items()
        dump_dict = collections.OrderedDict()
        idx_list = idxs if isinstance(idxs, list) else [idxs]
        for idx in idx_list:
            if idx in self.record.keys():
                dump_dict[idx] = self.record[idx]
        print(json.dumps(dump_dict, indent=2))
        return dump_dict

    def dumps_idxs_items(self, idxs=None, items=None):
        """print dict in json dums and return a dict, with idxs and items both filtered

        Args:
            idxs (_type_, optional): int or list of int. Defaults to None to all.
            items (_type_, optional): str or list of str. Defaults to None to all.

        Returns:
            dict: filtered new dict
        """
        import json
        if not idxs and not items:
            return self.dumps_items()
        if not idxs:
            return self.dumps_items(items)
        if not items:
            return self.dumps_idxs(idxs)
        dump_dict = collections.OrderedDict()
        idx_list = idxs if isinstance(idxs, list) else [idxs]
        item_list = items if isinstance(items, list) else [items]
        for idx in idx_list:
            if idx in self.record.keys():
                dump_dict[idx] = {}
                for item in item_list:
                    if item in self.record[idx].keys():
                        dump_dict[idx][item] = self.record[idx][item]
        print(json.dumps(dump_dict, indent=2))
        return dump_dict

    def surround_result(self, message, symbol="*"):
        line_list = message.split("\n")
        line_num = len(line_list)
        max_len = 0
        for line in line_list:
            max_len = max(len(line), max_len)
        for i in range(line_num):
            line_list[i] = line_list[i].center(max_len + 4, " ")
            line_list[i] = line_list[i].center(max_len + 6, symbol)
        line_list.insert(0, symbol.center(max_len + 6, symbol))
        line_list.append(symbol.center(max_len + 6, symbol))
        return "\n".join(line_list)

    def conclude_result(self):
        print(f'self.step_fail is {self.step_fail}')
        print(f'self.step_pass is {self.step_pass}')
        print(f'self.step_count is {self.step_count}')
        if self.step_fail > 0:
            logger.error('some steps run fail')
            return False
        elif self.step_pass == self.step_count:
            return True
        else:
            logger.error("Somthing Wrong with step_count")
            return False


def change_yaml():
    change_list = ['CUDA_MAJOR:', 'CUDA_MINOR:', 'DRV_BRANCH:', 'DVS_BUILD:', 'PLATFORM:', 'DATE:']
    update_content = [
        'CUDA_MAJOR: %s' % cuda_major,
        'CUDA_MINOR: %s' % cuda_min,
        'DRV_BRANCH: %s' % driver_branch,
        'DVS_BUILD: %s' % dvs_build,
        'PLATFORM: %s' % platform,
        'DATE: %s' % date1
    ]
    change_content = []
    for i in change_list:
        cmd = 'grep %s %s/../yaml/cuda_gdb_case.yaml' % (i, path)
        out = run_loc_cmd(cmd)
        change_content.append(out['output'].strip('\n').strip('\r'))
    print(change_content)
    dict_change = dict(zip(change_content, update_content))
    for key, value in dict_change.items():
        change_cmd = "sed -i 's#%s#%s#g' %s/../yaml/cuda_gdb_case.yaml" % (key, value, os.getcwd())
        run_loc_cmd(change_cmd)
    updated_content = []
    for i in change_list:
        cmd = 'grep %s %s/../yaml/smoke.yaml' % (i, os.getcwd())
        out = run_loc_cmd(cmd)
        updated_content.append(out['output'].strip('\n').strip('\r'))
    print(updated_content)


def cuda_gdb_rcca_3820537():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3820537')
    if cuda_short_version >= '12.7':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3820537_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3820537']['LOG_NAME']
        mkdir(log_path)
        sm = get_sm()
        sm1 = str(sm).split('.')[0] + str(sm).split('.')[1]
        prepare_cmd = case_config['CUDA_GDB_RCCA_3820537']['PREPARE']['CMD1'].format(user, password, base_url, sm1)
        run_test_cmd(prepare_cmd)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        os.chdir("{}".format(log_path))
        cmd = '{}/cuda-gdb --quiet --ex "b foo" --ex "r" --ex "c" ./a.out'.format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"Breakpoint 1", mode=0)
        # dgl_gdb.spawn(cmd, checkpoint=r"sdfasdfsdf", mode=0)
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb testing rcca 3820537"] = "passed"
            logger.info('we run cuda-gdb about rcca 3820537 passed')
        else:
            result["cuda-gdb testing rcca 3820537"] = "failed"
            logger.info('we run cuda-gdb about rcca 3820537 failed')
        i = 0
        with open(gdb_log_name, 'r') as f:
            for line in f.readlines():
                if 'printf("Hello World' in line:
                    i = i + 1
            if i == 2:
                result['check print hello world 2 times'] = 'passed'
            else:
                result['check print hello world 2 times'] = 'failed'
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3820537.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('support this case since 12.7')
        return WAIVED


def cuda_gdb_rcca_3825550():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3825550')
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_GDB_RCCA_3825550_PATH']
    gdb_log_name = case_config['CUDA_GDB_RCCA_3825550']['LOG_NAME']
    mkdir(log_path)
    prepare_cmd = case_config['CUDA_GDB_RCCA_3825550']['PREPARE']['CMD1'].format(user, password, base_url)
    run_test_cmd(prepare_cmd)
    cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
    run_test_cmd(cmd1)
    os.chdir("{}".format(log_path))
    cmd = "{}/cuda-gdb ./a.out".format(cuda_bin)
    dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
    dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
    dgl_gdb.expect('b get', checkpoint=r"Breakpoint 1", mode=0)
    dgl_gdb.expect('r', checkpoint=r"10.*ref.*4", mode=0)
    dgl_gdb.expect("c", checkpoint=r"10.*ref.*4", mode=0)
    dgl_gdb.expect("print obj_formal", checkpoint=r"\$1.*membref.*")
    dgl_gdb.expect("print obj_formal.membref", checkpoint=r"\$2.*generic.*", mode=0)
    dgl_gdb.expect("quit")
    dgl_result = dgl_gdb.conclude_result()
    if dgl_result is True:
        result["cuda-gdb testing rcca 3825550"] = "passed"
        logger.info('we run cuda-gdb about rcca 3825550 passed')
    else:
        result["cuda-gdb testing rcca 3825550"] = "failed"
        logger.info('we run cuda-gdb about rcca 3825550 failed')
    # summary result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_rcca_3825550.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cuda_gdb_rcca_3671381():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3671381')
    if cuda_short_version > '12.3':
        sm = get_sm()
        sm1 = str(sm).split('.')[0] + str(sm).split('.')[1]
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3671381_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3671381']['LOG_NAME']
        mkdir(log_path)
        prepare_cmd1 = case_config['CUDA_GDB_RCCA_3671381']['PREPARE']['CMD1']
        prepare_cmd2 = case_config['CUDA_GDB_RCCA_3671381']['PREPARE']['CMD2'].format(sm1, sm1, host_password)
        prepare_cmd3 = case_config['CUDA_GDB_RCCA_3671381']['PREPARE']['CMD3'].format(user, password, base_url, sm1)
        print(prepare_cmd1)
        print(prepare_cmd2)
        print(prepare_cmd3)
        # run_test_cmd(prepare_cmd1)
        check_result(prepare_cmd1, 'prepare_cmd1', gdb_log_name, result)
        check_result(prepare_cmd2, 'prepare_cmd2', gdb_log_name, result)
        check_result(prepare_cmd3, 'prepare_cmd3', gdb_log_name, result)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        os.chdir("{}".format(log_path))
        cmd = "LD_LIBRARY_PATH=/opt/lib/ {}/cuda-gdb ./saxpy-omp".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('b saxpy-omp.cc:21', checkpoint=r"Breakpoint 1", mode=0)
        dgl_gdb.expect('r')
        dgl_gdb.expect("x/i $pc")
        dgl_gdb.expect("set cuda disassemble_per file")
        dgl_gdb.expect("x/i $pc")
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True and search_keyword_in_file('{}'.format(gdb_log_name), 'Cannot disassemble instruction') is False:
            result["cuda-gdb testing rcca 3671381"] = "passed"
            logger.info('we run cuda-gdb about rcca 3671381 passed')
        else:
            result["cuda-gdb testing rcca 3671381"] = "failed"
            logger.info('we run cuda-gdb about rcca 3671381 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3671381.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        return WAIVED
    

def cuda_gdb_rcca_3679456():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3679456')
    if cuda_short_version > '12.4':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3679456_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3679456']['LOG_NAME']
        mkdir(log_path)
        prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            debug_sample_path = debugger_download_to_path + '/bin/x86_64_Linux_release/debugger-testsuite'
        elif platform == 'arm':
            debug_sample_path = debugger_download_to_path + '/bin/aarch64_Linux_release/debugger-testsuite'
        prepare_cmd = case_config['CUDA_GDB_RCCA_3679456']['PREPARE']['CMD1'].format(debug_sample_path)
        run_test_cmd(prepare_cmd)
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        cmd = "{}/cuda-gdb ./pred_exit".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('set pagination off', mode=0)
        dgl_gdb.expect('break pred_exit_kernel', checkpoint=r"Breakpoint 1", mode=0)
        dgl_gdb.expect('set cuda disassemble_from device_memory', mode=0)
        dgl_gdb.expect('r', checkpoint=r"hit Breakpoint 1", mode=0)
        dgl_gdb.expect("disassemble pred_exit_kernel", checkpoint=r"End.*assembler.*dump", mode=0)
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb testing rcca 3679456"] = "passed"
            logger.info('we run cuda-gdb about rcca 3679456 passed')
        else:
            result["cuda-gdb testing rcca 3679456"] = "failed"
            logger.info('we run cuda-gdb about rcca 3679456 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3679456.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support this case since cuda 12.5')
        return WAIVED


def cuda_gdb_rcca_3692213():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3692213')
    sm = get_sm()
    if cuda_short_version > '12.4' and sm > 6.0:
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3692213_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3692213']['LOG_NAME']
        log_name = case_config['CUDA_GDB_RCCA_3692213']['LOG_NAME1']
        mkdir(log_path)
        prepare_cmd = case_config['CUDA_GDB_RCCA_3692213']['PREPARE']['CMD1'].format(user, password, base_url)
        run_test_cmd(prepare_cmd)
        step1_cmd1 = case_config['CUDA_GDB_RCCA_3692213']['STEP1']['CMD1']
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        get_os_cmd = 'cat /etc/issue'
        out = run_loc_cmd(get_os_cmd)
        if 'ubuntu' in out['output'].split('\n')[0].lower():
            machine_os = 'ubuntu'
        else:
            machine_os = 'redhat'
        print('os is {}'.format(machine_os))

        def check_error_information():
            time.sleep(5)
            if machine_os == 'ubuntu':
                step1_cmd2 = case_config['CUDA_GDB_RCCA_3692213']['STEP1']['CMD2']
            else:
                step1_cmd2 = case_config['CUDA_GDB_RCCA_3692213']['STEP1']['CMD2_1']
            out = run_loc_cmd(step1_cmd2)
            cmd_pid = out['output'].split('/n')[0]
            os.chdir("{}".format(log_path))
            cmd = "{}/cuda-gdb -p {}".format(cuda_bin, cmd_pid)
            dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
            dgl_gdb.spawn(cmd, checkpoint=r"CUDA_EXCEPTION_14, Warp Illegal Address")
            dgl_gdb.expect("quit")

        def run_cmd():
            run_loc_cmd(step1_cmd1)

        # run step2
        import threading
        threads1 = []
        threads1.append(threading.Thread(target=run_cmd))
        threads1.append(threading.Thread(target=check_error_information))
        for t1 in threads1:
            t1.start()
        time.sleep(10)
        result11 = search_keyword_in_file('{}'.format(gdb_log_name), 'CUDA_EXCEPTION_14, Warp Illegal Address')
        if result11 is True:
            result["cuda-gdb testing rcca 3692213"] = "passed"
            logger.info('we run cuda-gdb about rcca 3692213 passed')
        else:
            result["cuda-gdb testing rcca 3692213"] = "failed"
            logger.info('we run cuda-gdb about rcca 3692213 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3692213.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support this case since cuda 12.5 and pascal++')
        return WAIVED


def cuda_gdb_rcca_3773016():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3773016')
    if cuda_short_version > '12.6':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3773016_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3773016']['LOG_NAME']
        mkdir(log_path)
        prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            debug_sample_path = debugger_download_to_path + '/bin/x86_64_Linux_release/debugger-testsuite'
        elif platform == 'arm':
            debug_sample_path = debugger_download_to_path + '/bin/aarch64_Linux_release/debugger-testsuite'
        prepare_cmd = case_config['CUDA_GDB_RCCA_3773016']['PREPARE']['CMD1'].format(debug_sample_path)
        run_test_cmd(prepare_cmd)
        step1_cmd1 = case_config['CUDA_GDB_RCCA_3773016']['STEP1']['CMD1']
        run_test_cmd(step1_cmd1)
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        cmd = "{}/cuda-gdb".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('target cudacore foobar1', checkpoint=r"Warp Illegal Address", mode=0)
        dgl_gdb.expect('target cudacore foobar1', checkpoint=r"Warp Illegal Address", mode=0)
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb testing rcca 3773016"] = "passed"
            logger.info('we run cuda-gdb about rcca 3773016 passed')
        else:
            result["cuda-gdb testing rcca 3773016"] = "failed"
            logger.info('we run cuda-gdb about rcca 3773016 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3773016.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support the case since cuda 12.7')
        return WAIVED


def cuda_gdb_rcca_3773016():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3773016')
    if cuda_short_version > '12.6':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_3773016_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_3773016']['LOG_NAME']
        mkdir(log_path)
        prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            debug_sample_path = debugger_download_to_path + '/bin/x86_64_Linux_release/debugger-testsuite'
        elif platform == 'arm':
            debug_sample_path = debugger_download_to_path + '/bin/aarch64_Linux_release/debugger-testsuite'
        prepare_cmd = case_config['CUDA_GDB_RCCA_3773016']['PREPARE']['CMD1'].format(debug_sample_path)
        run_test_cmd(prepare_cmd)
        step1_cmd1 = case_config['CUDA_GDB_RCCA_3773016']['STEP1']['CMD1']
        run_test_cmd(step1_cmd1)
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        cmd = "{}/cuda-gdb".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('target cudacore foobar1', checkpoint=r"Warp Illegal Address", mode=0)
        dgl_gdb.expect('target cudacore foobar1', checkpoint=r"Warp Illegal Address", mode=0)
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb testing rcca 3773016"] = "passed"
            logger.info('we run cuda-gdb about rcca 3773016 passed')
        else:
            result["cuda-gdb testing rcca 3773016"] = "failed"
            logger.info('we run cuda-gdb about rcca 3773016 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_3773016.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support the case since cuda 12.7')
        return WAIVED


def cuda_gdb_optix():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3794754')
    if cuda_short_version > '12.6':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_OPTIX_PATH']
        gdb_log_name = case_config['CUDA_GDB_OPTIX']['LOG_NAME']
        mkdir(log_path)
        prepare_optix_sample(log_path, user, password, base_url, cuda_short_version, platform, build=1)
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        gdb_bin_path = '/usr/local/cuda-{}/bin'.format(cuda_short_version)
        prepare_sample = 'cp {}/NVIDIA-OptiX-SDK*/build_dbg/bin/optixPathTracer {}'.format(log_path, log_path)
        run_test_cmd(prepare_sample)
        os.chdir(log_path)
        cmd = "{}/cuda-gdb ./optixPathTracer".format(gdb_bin_path)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('b optixPathTracer.cu:260', checkpoint=r"Breakpoint 1", mode=0)
        dgl_gdb.expect('r', checkpoint=r"260.*optixGetLaunchIndex", mode=0)
        dgl_gdb.expect('bt', checkpoint=r"#0.*", mode=0)
        dgl_gdb.expect('info locals', mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect('b optixPathTracer.cu:315', checkpoint=r"Breakpoint.*", mode=0)
        dgl_gdb.expect('c', checkpoint=r"315.*subframe_index", mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect('b optixPathTracer.cu:322', checkpoint=r"Breakpoint.*", mode=0)
        dgl_gdb.expect('c', checkpoint=r"322.*params.frame_buffer", mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect('b optixPathTracer.cu:333', checkpoint=r"Breakpoint.*", mode=0)
        dgl_gdb.expect('c', checkpoint=r"333.*prd.radiance", mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect('b optixPathTracer.cu:345', checkpoint=r"Breakpoint.*", mode=0)
        dgl_gdb.expect('c', checkpoint=r"345.*optixGetSbtDataPointer", mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect('b optixPathTracer.cu:357', checkpoint=r"Breakpoint.*", mode=0)
        dgl_gdb.expect('c', checkpoint=r"357.*optixGetWorldRayOrigin", mode=0)
        dgl_gdb.expect('d', mode=0)
        dgl_gdb.expect("quit")
        find_string = search_keyword_in_file(gdb_log_name, 'Cannot access memory at address')
        logger.info('should not have "Cannot access memory at address" info. now find the "Cannot access memory at address" is {}'.format(find_string))
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True and find_string is False:
            result["cuda-gdb testing rcca 3794754"] = "passed"
            logger.info('we run cuda-gdb about rcca 3794754 passed')
        else:
            result["cuda-gdb testing rcca 3794754"] = "failed"
            logger.info('we run cuda-gdb about rcca 3794754 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_optix.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support the case since cuda 12.7')
        return WAIVED


def cuda_gdb_rcca_4040416():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T4040416')
    if cuda_short_version > '12.7':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_RCCA_4040416_PATH']
        gdb_log_name = case_config['CUDA_GDB_RCCA_4040416']['LOG_NAME']
        mkdir(log_path)
        prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            debug_sample_path = debugger_download_to_path + '/bin/x86_64_Linux_release/debugger-testsuite'
            gdb_bin_path = debugger_download_to_path + '/bin/x86_64_Linux_release'
        elif platform == 'arm':
            debug_sample_path = debugger_download_to_path + '/bin/aarch64_Linux_release/debugger-testsuite'
            gdb_bin_path = debugger_download_to_path + '/bin/aarch64_Linux_release'
        prepare_cmd = case_config['CUDA_GDB_RCCA_4040416']['PREPARE']['CMD1'].format(debug_sample_path)
        run_test_cmd(prepare_cmd)
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        cmd = "{}/cuda-gdb ./assert".format(gdb_bin_path)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('b __assert_fail', checkpoint=r"Breakpoint 1.*assert_fail", mode=0)
        dgl_gdb.expect('r', checkpoint=r"hit Breakpoint", mode=0)
        dgl_gdb.expect('step', checkpoint=r"CUDA_EXCEPTION_12, Warp Assert", mode=0)
        try:
            dgl_gdb.expect('step', checkpoint=r"assert_one.*", mode=0)
        except:
            logger.info('run step fail, maybe crash, please check')
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb testing rcca 4040416"] = "passed"
            logger.info('we run cuda-gdb about rcca 4040416 passed')
        else:
            result["cuda-gdb testing rcca 4040416"] = "failed"
            logger.info('we run cuda-gdb about rcca 4040416 failed')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_rcca_4040416.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support the case since cuda 12.8')
        return WAIVED


def cuda_gdb_option_4019656():
    logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T4019656')
    if cuda_short_version > '12.8':
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUDA_GDB_OPTION_4019656_PATH']
        gdb_log_name = case_config['CUDA_GDB_OPTION_4019656']['LOG_NAME']
        mkdir(log_path)
        prepare_cmd = case_config['CUDA_GDB_OPTION_4019656']['PREPARE']['CMD1'].format(user, password, base_url)
        run_test_cmd(prepare_cmd)
        step1_cmd1 = case_config['CUDA_GDB_OPTION_4019656']['STEP1']['CMD1']
        os.chdir(log_path)
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        cmd = "{}/cuda-gdb ./internal_cmd_cbu".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}".format(gdb_log_name))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        dgl_gdb.expect('b cbuBarrierParticipationTest', checkpoint=r"Breakpoint 1", mode=0)
        dgl_gdb.expect('r', checkpoint=r"hit Breakpoint", mode=0)
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect('stepi')
        dgl_gdb.expect('info cuda barriers')
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["cuda-gdb run case 4019656"] = "passed"
            logger.info('we run cuda-gdb case 4019656 passed')
        else:
            result["cuda-gdb run case 4019656"] = "failed"
            logger.info('we run cuda-gdb case 4019656 failed')
        find1, find2 = 0, 0
        file_list = []
        with open(gdb_log_name, 'r') as f:
            for line in f.readlines():
                file_list.append(line.strip('\n'))
        for index, line1 in enumerate(file_list):
            if '3       Ready' in line1 and '(4-10)' in file_list[index + 1] and '(11-31)' in file_list[index + 2]:
                find1 += 1
            if '3       Ready' in line1 and '(4-6)' in file_list[index + 1] and '7' in file_list[index + 2] and '(8-10)' in file_list[index + 3] and '(11-31)' in file_list[index + 4]:
                find2 += 1
        if find1 != 0 and find2 != 0:
            result['check gdb output'] = 'passed'
            logger.info('check the expect output successful')
        else:
            result['check gdb output'] = 'failed'
            logger.info('check the expect output fail')
        # summary result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_gdb_option_4019656.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.warning('support this case since cuda 12.9')
        return WAIVED


def cuda_gdb_sanity():
    """
    Run CUDA-GDB sanity check test case
    """
    logger.info('Running CUDA-GDB sanity check')
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_GDB_SANITY_PATH']
    gdb_log_name = case_config['CUDA_GDB_SANITY']['LOG_NAME']

    def get_expected_versions(cuda_version):
        current_year = str(datetime.datetime.now().year)  # This would normally be dynamically generated
        if cuda_version == '12.5':
            cuda_gdb_gnu_version = 'GNU gdb: 13.2'
            cuda_gdbserver_gnu_version = 'cuda-gdbserver GNU gdb 13.2'
            cuda_gdb_version = 'cuda-gdb 12.5'
            cuda_gdbserver_version = 'cuda-gdb12.5'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        elif cuda_version == '12.6':
            cuda_gdb_gnu_version = 'GNU gdb: 13.2'
            cuda_gdbserver_gnu_version = 'cuda-gdbserver GNU gdb 13.2'
            cuda_gdb_version = 'cuda-gdb 12.6'
            cuda_gdbserver_version = 'cuda-gdb 12.6'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        elif cuda_version == '12.7':
            cuda_gdb_gnu_version = 'GNU gdb 13.2'
            cuda_gdbserver_gnu_version = 'GNU gdbserver (CUDA-GDB) 13.2'
            cuda_gdb_version = 'cuda-gdb 12.7'
            cuda_gdbserver_version = '12.7 release'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        elif cuda_version == '12.8':
            cuda_gdb_gnu_version = 'GNU gdb 13.2'
            cuda_gdbserver_gnu_version = 'GNU gdbserver (CUDA-GDB) 13.2'
            cuda_gdb_version = 'cuda-gdb 12.8'
            cuda_gdbserver_version = '12.8 release'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        elif cuda_version == '12.9':
            cuda_gdb_gnu_version = 'GNU gdb 14.2'
            cuda_gdbserver_gnu_version = 'GNU gdbserver (CUDA-GDB) 14.2'
            cuda_gdb_version = 'cuda-gdb 12.9'
            cuda_gdbserver_version = '12.9 release'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        elif cuda_version == '13.0':
            cuda_gdb_gnu_version = 'GNU gdb 14.2'
            cuda_gdbserver_gnu_version = 'GNU gdbserver (CUDA-GDB) 14.2'
            cuda_gdb_version = 'cuda-gdb 13.0'
            cuda_gdbserver_version = '13.0 release'
            cuda_gdb_copyright = f"2007-{current_year}"
            cuda_gdbserver_copyright = f"2013-{current_year}"
        else:
            cuda_gdb_gnu_version = None
            cuda_gdbserver_gnu_version = None
            cuda_gdb_version = None
            cuda_gdbserver_version = None
            cuda_gdb_copyright = None
            cuda_gdbserver_copyright = None
        return cuda_gdb_gnu_version, cuda_gdbserver_gnu_version, cuda_gdb_version, cuda_gdbserver_version, cuda_gdb_copyright, cuda_gdbserver_copyright

    cuda_gdb_gnu_version, cuda_gdbserver_gnu_version, cuda_gdb_version, cuda_gdbserver_version, cuda_gdb_copyright, cuda_gdbserver_copyright = get_expected_versions(cuda_short_version)
    try:
        # Create log directory
        mkdir(log_path)
        # Prepare log file
        cmd1 = 'rm {}; touch {}'.format(gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        # Check cuda-gdb version
        cmd = "{}/cuda-gdb --version".format(cuda_bin)
        check_result(cmd, 'step1- check cuda-gdb version via cuda-gdb --version', gdb_log_name, result, cuda_gdb_version, cuda_gdb_copyright, cuda_gdb_gnu_version)
        # Check cuda-gdbserver version
        cmd = "{}/cuda-gdbserver --version".format(cuda_bin)
        check_result(cmd, 'step2- heck cuda-gdbserver version via cuda-gdbserver --version', gdb_log_name, result, cuda_gdbserver_version, cuda_gdbserver_copyright, cuda_gdbserver_gnu_version)
        # Run matrixMul debug session
        prepare_cmd = 'cp {}/matrixMul/matrixMul {}'.format(sample_0_path, log_path)
        run_test_cmd(prepare_cmd)
        os.chdir(log_path)
        cmd = "{}/cuda-gdb ./matrixMul".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        # Basic breakpoint and run
        dgl_gdb.expect('b 151', checkpoint=r"Breakpoint 1.*line 151", mode=0)
        dgl_gdb.expect('r', checkpoint=r"151\s+checkCudaErrors", mode=0)
        dgl_gdb.expect('bt', checkpoint=r"#0\s+.*", mode=0)
        # CUDA specific commands
        dgl_gdb.expect('set cuda break_on_launch app', mode=0)
        dgl_gdb.expect('c', checkpoint=r"\d+\s+.*int.* bx = .*blockIdx.*.x;", mode=0)
        dgl_gdb.expect('info cuda devices', checkpoint=r"\*\s+\d+", mode=0)
        # Step through code
        for _ in range(3):
            dgl_gdb.expect('n', mode=0)
        # Thread information
        dgl_gdb.expect('c', mode=0)
        dgl_gdb.expect('info threads', checkpoint=r".*cuda-EvtHandlr.*", mode=0)
        dgl_gdb.expect('bt', checkpoint=r"#0\s+.*", mode=0)
        # Delete breakpoints
        dgl_gdb.expect('delete breakpoints', mode=0)
        # dgl_gdb.expect('y', mode=0)
        # Test conditional breakpoint
        dgl_gdb.expect('break matrixMul.cu:45 if blockIdx.x==0 && threadIdx.x==0', checkpoint=r"Breakpoint.*at.*matrixMul.cu:45", mode=0)
        dgl_gdb.expect('c', mode=0)
        # CUDA context commands
        dgl_gdb.expect('set cuda break_on_launch none', mode=0)
        dgl_gdb.expect('cuda device', checkpoint=r"Device.*", mode=0)
        dgl_gdb.expect('cuda block', checkpoint=r"Block.*", mode=0)
        dgl_gdb.expect('cuda thread', checkpoint=r"Thread.*", mode=0)
        # Final cleanup and exit
        dgl_gdb.expect('delete breakpoints', mode=0)
        # dgl_gdb.expect('y', mode=0)
        dgl_gdb.expect('c', mode=0)
        dgl_gdb.expect("quit")
        # Check results
        dgl_result = dgl_gdb.conclude_result()
        if dgl_result is True:
            result["step3-cuda-gdb sanity check"] = "passed"
            logger.info('cuda-gdb sanity check passed')
            passed += 1
        else:
            result["step3-cuda-gdb sanity check"] = "failed"
            logger.info('cuda-gdb sanity check failed')
            failed += 1
    except Exception as e:
        result["cuda-gdb sanity check"] = "failed"
        result["error"] = str(e)
        logger.error(f'cuda-gdb sanity check failed with error: {str(e)}')
        failed += 1
    # check coredump
    prepare_cmd = case_config['CUDA_GDB_SANITY']['STEP4']['PREPARE']['CMD1'].format(user, password, base_url)
    run_test_cmd(prepare_cmd)
    step4_cmd1 = case_config['CUDA_GDB_SANITY']['STEP4']['CMD1']
    os.chdir(log_path)
    step4_cmd2 = '{}/cuda-gdb -ex "target cudacore dump"'.format(cuda_bin)
    # step4_cmd2 = case_config['CUDA_GDB_SANITY']['STEP4']['CMD2']
    check_result(step4_cmd1, 'run cuda-gdb sanity by ---{}'.format(step4_cmd1), gdb_log_name, result)
    dgl_gdb1 = CUDBGExpect(cmd=None, logfile=gdb_log_name)
    dgl_gdb1.spawn(step4_cmd2, checkpoint=r"CUDA Exception: Warp Out-of-range Address")
    # dgl_gdb1.expect("CUDA Exception: Warp Out-of-range Address")
    dgl_gdb1.expect("quit")
    dgl_result1 = dgl_gdb1.conclude_result()
    if dgl_result1 is True:
        result["cuda-gdb testing coredump"] = "passed"
        logger.info('we run cuda-gdb about coredump passed')
    else:
        result["cuda-gdb testing coredump"] = "failed"
        logger.info('we run cuda-gdb about coredump failed')
    # Add summary
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_sanity.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cuda_gdb_remote_1820919():
    def kill_gdbserver():
        """Safely kill cuda-gdbserver process"""
        try:
            subprocess.run(["pkill", "-f", "cuda-gdbserver"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(1)
        except Exception as e:
            logger.warning(f"Failed to kill cuda-gdbserver: {e}")

    def run_cuda_gdbserver(cuda_bin, event_queue):
        """Run cuda-gdbserver in a separate thread"""
        try:
            # Kill any existing cuda-gdbserver
            kill_gdbserver()
            os.chdir(log_path)

            # Start cuda-gdbserver with output streaming
            gdbserver_cmd = f"{cuda_bin}/cuda-gdbserver :2345 ./matrixMul"
            print(f"[cuda-gdbserver] Executing command: {gdbserver_cmd}")
            process = subprocess.Popen(
                gdbserver_cmd.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Merge stderr into stdout
                universal_newlines=True,  # Return strings instead of bytes
                bufsize=1  # Line buffered
            )

            # Create a thread to handle output streaming
            def stream_output():
                for line in process.stdout:
                    print(f"[cuda-gdbserver] {line.strip()}")
                process.stdout.close()

            output_thread = threading.Thread(target=stream_output)
            output_thread.daemon = True  # Thread will exit when main thread exits
            output_thread.start()
            # Wait for server to start
            time.sleep(3)

            # Verify server is running
            try:
                ps_check = subprocess.run(["pgrep", "-f", "cuda-gdbserver"], stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE)
                if ps_check.returncode == 0:
                    event_queue.put(("server_started", True))
                else:
                    event_queue.put(("server_started", False))
                    event_queue.put(("error", "cuda-gdbserver failed to start"))
                    return
            except Exception as e:
                event_queue.put(("error", f"Failed to check server status: {e}"))
                return

            # Wait for server to complete
            process.wait()

        except Exception as e:
            event_queue.put(("error", str(e)))
        finally:
            try:
                process.terminate()
            except:
                pass
            kill_gdbserver()

    def run_cuda_gdb(cuda_bin, gdb_log_name, event_queue):
        """Run cuda-gdb commands in a separate thread"""
        try:
            cmd = f"{cuda_bin}/cuda-gdb ./matrixMul"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
            dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
            # Debug commands
            dgl_gdb.expect('target remote :2345', checkpoint=r"Remote debugging using :2345", mode=0)
            dgl_gdb.expect('set cuda break_on_launch app', mode=0)
            dgl_gdb.expect('c', checkpoint=r"Switching focus to CUDA kernel", mode=0)
            dgl_gdb.expect('cuda device', checkpoint=r"device\s+\d+", mode=0)
            dgl_gdb.expect('cuda block', checkpoint=r"block\s+\(\d+", mode=0)
            dgl_gdb.expect('cuda thread', checkpoint=r"thread\s+\(\d+", mode=0)
            dgl_gdb.expect('set cuda break_on_launch none', mode=0)
            dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
            dgl_gdb.expect('quit', mode=0)
            dgl_result = dgl_gdb.conclude_result()
            event_queue.put(("gdb_result", dgl_result))
        except Exception as e:
            event_queue.put(("error", str(e)))

    logger.info('Running cuda-gdb remote debugging test')
    result = {}
    passed, failed = 0, 0
    # Get paths from config
    log_path = case_config['global']['env']['CUDA_GDB_REMOTE_PATH']
    gdb_log_name = case_config['CUDA_GDB_REMOTE']['LOG_NAME']
    mkdir(log_path)
    try:
        # Prepare test environment
        prepare_cmd = case_config['CUDA_GDB_REMOTE']['PREPARE']['CMD1'].format(sample_0_path)
        if isinstance(prepare_cmd, str):
            # Prepare test environment
            prepare_cmd = case_config['CUDA_GDB_REMOTE']['PREPARE']['CMD1'].format(sample_0_path)
            if isinstance(prepare_cmd, str):
                run_test_cmd(prepare_cmd)
            else:
                logger.error('Invalid prepare command for CUDA GDB remote test')
                return WAIVED
        else:
            logger.error('Invalid prepare command for CUDA GDB remote test')
            return WAIVED
        # Start cuda-gdbserver in a separate thread
        event_queue = queue.Queue()
        server_thread = threading.Thread(target=run_cuda_gdbserver, args=(cuda_bin, event_queue))
        server_thread.start()
        # Wait for cuda-gdbserver to start
        server_started, error = event_queue.get()
        if not server_started:
            logger.error(f"Failed to start cuda-gdbserver: {error}")
            return WAIVED
        # Start cuda-gdb in a separate thread
        gdb_thread = threading.Thread(target=run_cuda_gdb, args=(cuda_bin, gdb_log_name, event_queue))
        gdb_thread.start()
        # Wait for cuda-gdb to complete
        gdb_result, error = event_queue.get()
        if error:
            logger.error(f"Error running cuda-gdb: {error}")
            return WAIVED
        # Check the result of cuda-gdb
        if gdb_result:
            result["cuda-gdb remote debugging test"] = "passed"
            logger.info('cuda-gdb remote debugging test passed')
        else:
            result["cuda-gdb remote debugging test"] = "failed"
            logger.info('cuda-gdb remote debugging test failed')
    except Exception as e:
        logger.error(f"Error in cuda-gdb remote test: {str(e)}")
        result["cuda-gdb remote debugging test"] = "failed"
        result["error"] = str(e)
    finally:
        # Clean up
        kill_gdbserver()
        if os.path.exists(gdb_log_name):
            os.remove(gdb_log_name)
        if os.path.exists(log_path):
            os.rmdir(log_path)
    # Add summary
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_remote.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def test_mps_multiuser_debug():
    """
    Test CUDA-GDB behavior with MPS in multi-user scenario according to 3665535-MPS-user.
    """
    result = {}
    result_runtime = {}
    nbody_path = sample_bin_path + '/nbody'
    vectoradd_path = sample_bin_path + '/vectorAdd'
    log_path = case_config['global']['env']['CUDA_GDB_MPS_MULTIUSER_PATH']
    mkdir(log_path)
    try:
        # Step 0: Setup test users
        users = ["test1", "test2"]
        user_password = "cuda"
        print("[MPS Test] Step0: Creating test users...")
        for user1 in users:
            if subprocess.run(f"id {user1}", shell=True, stderr=subprocess.DEVNULL).returncode != 0:
                print(f"[MPS Test] Creating user {user1}...")
                # Create user with home directory and bash shell
                subprocess.run(f"echo {host_password} | sudo -S useradd -m -s /bin/bash {user1}", shell=True)
                # Add user to sudo group
                subprocess.run(f"echo {host_password} | sudo -S usermod -aG sudo {user1}", shell=True)
                # Set password
                subprocess.run(f"echo {host_password}| sudo -S echo '1'; echo '{user1}:{user_password}' | sudo chpasswd", shell=True)
            else:
                print(f"[MPS Test] User {user1} already exists")
                # Update password anyway
                subprocess.run(f"echo {host_password}| sudo -S echo '1'; echo '{user1}:{user_password}' | sudo chpasswd", shell=True)

        # Step 1: Setup MPS service
        print("[MPS Test] Step1: Starting MPS service in multi-user mode...")
        mps_cmd = f"echo {host_password} | sudo -S nvidia-cuda-mps-control -d --multiuser-server"
        subprocess.run(mps_cmd, shell=True)
        time.sleep(2)
        run_loc_cmd(vectoradd_path)
        # Verify MPS service is running
        mps_check = subprocess.run("ps -ef | grep nvidia-cuda-mps-server", shell=True, capture_output=True)
        if "nvidia-cuda-mps-server -multiuser-server" not in mps_check.stdout.decode():
            result["step1_mps_service"] = "failed"
            raise Exception("Step1 failed: MPS service not running in multi-user mode")
        result["step1_mps_service"] = "passed"

        # Step 2: Verify cuda-gdb suspension with MPS client
        print("\n[MPS Test] Step2: Testing cuda-gdb suspension...")
        nbody_complete = threading.Event()
        debug_started = threading.Event()

        def run_nbody_step2():
            print("[MPS Test] test1: Starting nbody...")
            cmd = f"echo {host_password} | sudo -S echo '1'; sudo -u test1 {nbody_path} -benchmark -numbodies=1280000"
            start_time = time.time()
            
            out = run_loc_cmd(cmd)
            print(out['output'])
            runtime = time.time() - start_time
            result_runtime['nbody_runtime'] = runtime
            print(f"[MPS Test] Step2 - nbody runtime: {runtime:.2f} seconds")
            nbody_complete.set()

        def run_debug_step2():
            print("[MPS Test] test2: Starting cuda-gdb...")
            time.sleep(3)
            cmd = f"echo {host_password} | sudo -S echo '1'; sudo -u test2 /usr/local/cuda-{cuda_short_version}/bin/cuda-gdb -ex 'r' -ex 'q' {vectoradd_path}"
            start_time = time.time()
            out = run_loc_cmd(cmd)
            print(out['output'])
            runtime = time.time() - start_time
            result_runtime['debug_runtime'] = runtime
            print(f"[MPS Test] Step2 - cuda-gdb runtime: {runtime:.2f} seconds")

        # record nbody start time
        nbody_start_time = time.time()
        nbody_thread = threading.Thread(target=run_nbody_step2)
        nbody_thread.start()
        time.sleep(2)
        
        # start debug
        debug_thread = threading.Thread(target=run_debug_step2)
        debug_thread.start()
        
        # wait nbody finish
        nbody_thread.join()
        nbody_runtime = time.time() - nbody_start_time
        
        # wit debug
        debug_thread.join()
        
        # verify suspension time
        nbody_runtime = result_runtime.get('nbody_runtime', 0)
        debug_runtime = result_runtime.get('debug_runtime', 0)
        suspension_time = debug_runtime - nbody_runtime

        print(f"[MPS Test] Step2 - Suspension time: {suspension_time:.2f} seconds")
        
        if suspension_time > 5:
            result["step2_suspension_test"] = "passed"
            print("[MPS Test] Step2 - Suspension test passed")
        else:
            result["step2_suspension_test"] = "failed"
            print(f"[MPS Test] Step2 - Suspension test failed: suspension time ({suspension_time:.2f}s) <= 5s")

        # Step 3: Verify normal operation
        print("\n[MPS Test] Step3: Testing normal operation...")
        cuda_gdb_started = threading.Event()
        sample_complete = threading.Event()

        def run_debug_step3():
            print("[MPS Test] test1: Starting cuda-gdb (waiting before 'r')...")
            cmd = f"echo {host_password} | sudo -S echo '1'; sudo -u test1 /usr/local/cuda-{cuda_short_version}/bin/cuda-gdb --args {nbody_path} -benchmark -numbodies=512000"
            cuda_gdb_started.set()
            proc = subprocess.Popen(cmd, shell=True, stdin=subprocess.PIPE)
            time.sleep(3)  # Give time for test2's sample to start
            proc.communicate(input=b"r\nq\n")

        def run_sample_step3():
            cuda_gdb_started.wait()  # Wait for cuda-gdb to start
            print("[MPS Test] test2: Running CUDA sample...")
            cmd = f"echo {host_password} | sudo -S echo '1'; sudo -u test2 {vectoradd_path}"
            start_time = time.time()
            proc = subprocess.Popen(cmd, shell=True)
            proc.wait()
            runtime = time.time() - start_time
            print(f"[MPS Test] Step3 - sample runtime: {runtime:.2f} seconds")
            sample_complete.set()

        debug_thread = threading.Thread(target=run_debug_step3)
        sample_thread = threading.Thread(target=run_sample_step3)
        
        debug_thread.start()
        sample_thread.start()
        
        debug_thread.join(timeout=30)
        sample_thread.join(timeout=30)

        if debug_thread.is_alive() or sample_thread.is_alive():
            raise Exception("Step3 failed: Test timeout")

        if not sample_complete.is_set():
            result["step3_normal_operation"] = "failed"
        else:
            result["step3_normal_operation"] = "passed"

    except Exception as e:
        print(f"[MPS Test] Error: {str(e)}")
        result["test_error"] = str(e)
    finally:
        # Cleanup
        print("[MPS Test] Cleaning up...")
        subprocess.run("sudo sh -c 'echo quit | nvidia-cuda-mps-control'", shell=True)
        subprocess.run("sudo pkill -f nbody", shell=True)
        subprocess.run("sudo pkill -f vectorAdd", shell=True)
        subprocess.run("sudo pkill -f cuda-gdb", shell=True)

    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_mps_multiuser.json' % log_path)
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_coredump_1820921():
    """Test cuda-gdb coredump functionality based on test case 1820021"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_COREDUMP_1820921_PATH']
    mkdir(log_path)
    prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
    gdb_log = "cuda_gdb_coredump.log"
    # link_cmd = f'ln -s {debugger_bin_path} {log_path}'
    link_cmd = f'cp {debugger_bin_path}/memexceptions {log_path}'
    run_test_cmd(link_cmd)
    ulimit_cmd = 'ulimit -c unlimited'
    run_test_cmd(ulimit_cmd)
    os.chdir(log_path)
    memexceptions_dict = {
        1: "CUDA Exception: Warp Illegal Address",
        2: "CUDA Exception: Warp Out-of-range Address",
        3: "CUDA Exception: Warp Illegal Address",
        4: "CUDA Exception: Warp Misaligned Address",
        5: "CUDA Exception: Warp Misaligned Address",
        6: "CUDA Exception: Warp Misaligned Address"
    }
    for i in [1, 2, 3, 4, 5, 6]:
        try:
            generate_core_cmd = f"CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1 CUDA_COREDUMP_FILE=foobar{i} ./memexceptions {i}"
            run_test_cmd(generate_core_cmd)
            run_test_cmd(f"echo {host_password} | sudo -S coredumpctl list | tail -1 | awk '{{print $5}}'")
            run_test_cmd(f"echo {host_password} | sudo -S coredumpctl dump $r -o core{i}")
            # Initialize CUDBGExpect
            cmd = f'{cuda_bin}/cuda-gdb -ex "target cudacore foobar{i}"'
            dgl_gdb = CUDBGExpect(cmd=None, logfile="{}/memexceptions{}.txt".format(log_path, i))
            dgl_gdb.spawn(cmd, checkpoint=r"{}".format(memexceptions_dict[i]))
            dgl_gdb.expect('quit', mode=0)
            dgl_result = dgl_gdb.conclude_result()
            result[f"cuda-gdb testing memexceptions{i} cudacore"] = "passed" if dgl_result is True else 'failed'
            if result[f"cuda-gdb testing memexceptions{i} cudacore"] == "passed":
                logger.info(f'we run cuda-gdb about memexceptions{i} cudacore passed')
            else:
                logger.info(f'we run cuda-gdb about memexceptions{i} cudacore failed')
            # Test cuda-gdb with CPU core
            if os.path.exists(f"core{i}"):
                print("[Coredump Test] Testing cuda-gdb with CPU core...")
                cmd = f"{cuda_bin}/cuda-gdb -ex 'target core core{i} foobar{i}'"
                dgl_gdb1 = CUDBGExpect(cmd=None, logfile="{}/memexceptions{}.txt".format(log_path, i))
                dgl_gdb1.spawn(cmd, checkpoint=r"{}".format(memexceptions_dict[i]))
                dgl_gdb1.expect('bt', checkpoint=r"exception_kernel")
                dgl_gdb1.expect('thread 1', checkpoint=r"Switching to thread 1")
                dgl_gdb1.expect('bt')
                dgl_gdb1.expect('cuda device 0', checkpoint=r"Switching focus to CUDA kernel 0")
                dgl_gdb1.expect('bt', checkpoint=r".*exception_kernel.*")
                dgl_gdb1.expect('quit', mode=0)
                dgl_result1 = dgl_gdb1.conclude_result()
                result[f"cuda-gdb testing memexceptions{i} cpu core"] = "passed" if dgl_result1 is True else 'failed'
                if result[f"cuda-gdb testing memexceptions{i} cpu core"] == "passed":
                    logger.info(f'we run cuda-gdb about memexceptions{i} cpu core passed')
                else:
                    logger.info(f'we run cuda-gdb about memexceptions{i} cpu core failed')
        except Exception as e:
            print(f"[Coredump Test] Error: {str(e)}")
            result["test_error"] = str(e)
        finally:
            # Cleanup
            print("[Coredump Test] Cleaning up...")
            if os.path.exists("core"):
                os.remove("core")
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    dict_to_json(result1, f'{log_path}/cuda_gdb_coredump.json')
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_remote_debug_process_1820922():
    """Test cuda-gdb remote debug process functionality based on test case 1820922"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_REMOTE_DEBUG_PROCESS_PATH']
    mkdir(log_path)
    process_id = None
    
    try:
        # Step 4 & 5: Set up environment
        ptrace_cmd = f'echo {host_password} | sudo -S sh -c "echo 0 >/proc/sys/kernel/yama/ptrace_scope"'
        run_test_cmd(ptrace_cmd)

        # Kill any existing processes
        cleanup_cmd = "ps -ef|grep -E 'UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9"
        run_test_cmd(cleanup_cmd)

        # Change to samples directory
        os.chdir(os.path.expandvars(sample_bin_path))

        # Check if sample exists
        if not os.path.isfile("UnifiedMemoryPerf"):
            logger.error("UnifiedMemoryPerf sample not found")
            result['the sample status'] = 'FAIL'
            return result

        # Use Event for synchronization between threads
        sample_ready = threading.Event()
        gdbserver_ready = threading.Event()

        def run_sample():
            """Thread function to run the UnifiedMemoryPerf sample"""
            nonlocal process_id
            try:
                # Step 6: Launch sample using subprocess instead of run_test_cmd
                sample_process = subprocess.Popen(
                    [f"{sample_bin_path}/UnifiedMemoryPerf"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                time.sleep(2)

                # Get PID directly from the subprocess
                process_id = str(sample_process.pid)
                if process_id:
                    logger.info(f"Sample process ID: {process_id}")
                    sample_ready.set()  # Signal that sample is ready
                else:
                    logger.error("Failed to get UnifiedMemoryPerf process ID")
            except Exception as e:
                logger.error(f"Error in sample thread: {str(e)}")

        def run_cuda_gdb():
            """Thread function to run cuda-gdb commands"""
            try:
                # Wait for sample to be ready
                if not sample_ready.wait(timeout=10):
                    logger.error("Timeout waiting for sample to start")
                    return
                if process_id:
                    gdb_log_name = f"{log_path}/gdb_commands.txt"
                    gdb_cmd = f"{cuda_bin}/cuda-gdb --pid {process_id}"
                    dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
                    dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
                    dgl_gdb.expect('set cuda break_on_launch all', mode=0)
                    dgl_gdb.expect('r', checkpoint=r"135\s+int by = blockIdx.y", mode=0)
                    dgl_gdb.expect('n', checkpoint=r"145\s+int aBegin = matrixDim.*", mode=0)
                    dgl_gdb.expect('n', checkpoint=r"138\s+int tx = threadIdx.x;", mode=0)
                    dgl_gdb.expect('si', checkpoint=r"\s+138\s+int tx = threadIdx.x;", mode=0)
                    dgl_gdb.expect('si', checkpoint=r"\s+139\s+int ty = threadIdx.y;", mode=0)
                    dgl_gdb.expect('set cuda break_on_launch none', mode=0)
                    dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
                    dgl_gdb.expect('quit', mode=0)
                    dgl_result = dgl_gdb.conclude_result()
                    result["cuda-gdb basic check with process id"] = "passed" if dgl_result is True else 'failed'
                    logger.info(f'we run cuda-gdb basic check with process id {result["cuda-gdb basic check with process id"]}')
            except Exception as e:
                logger.error(f"Error in cuda-gdb basic check with process id: {str(e)}")
                result["cuda-gdb basic check with process id"] = "failed"

        # Create and start threads
        sample_thread = threading.Thread(target=run_sample)
        gdb_thread = threading.Thread(target=run_cuda_gdb)

        # Start threads with proper error handling
        try:
            sample_thread.start()
            # Wait for sample to be ready before starting gdb
            if not sample_ready.wait(timeout=10):
                logger.error("Timeout waiting for sample to start")
                return FAILED
            
            gdb_thread.start()
            
            # Wait for threads to complete with timeout
            sample_thread.join(timeout=30)
            gdb_thread.join(timeout=30)
            
            # Check if threads completed successfully
            if sample_thread.is_alive() or gdb_thread.is_alive():
                logger.error("Threads did not complete within timeout")
                return FAILED
                
        except Exception as e:
            logger.error(f"Error during thread execution: {str(e)}")
            return FAILED
        time.sleep(6)
        if process_id:
            os.system(f"kill -9 {process_id}")
        kill_cmd = "ps -ef|grep -E 'UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9"
        run_test_cmd(kill_cmd)
        time.sleep(2)
        # Reset synchronization events
        sample_ready.clear()
        gdbserver_ready.clear()

        def run_gdbserver():
            """Thread function to run cuda-gdbserver"""
            try:
                # Wait for sample to be ready
                if not sample_ready.wait(timeout=10):
                    logger.error("Timeout waiting for sample to start")
                    return

                # Step 7: Launch cuda-gdbserver using subprocess
                if process_id:
                    gdbserver_process = subprocess.Popen(
                        ["cuda-gdbserver", "--attach", ":2345", process_id],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    time.sleep(2)
                    print('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa')
                    gdbserver_ready.set()  # Signal that gdbserver is ready
            except Exception as e:
                logger.error(f"Error in gdbserver thread: {str(e)}")

        # Create and start threads
        sample_thread1 = threading.Thread(target=run_sample)
        gdbserver_thread = threading.Thread(target=run_gdbserver)

        sample_thread1.start()
        gdbserver_thread.start()

        # Wait for both threads to complete their initial tasks
        if not gdbserver_ready.wait(timeout=20):
            logger.error("Timeout waiting for gdbserver to start")
            result['status'] = 'FAIL'
            return result

        # Step 8 & 9: Debug with cuda-gdb
        try:
            gdb_log_name = f"{log_path}/gdb_commands.txt"
            if cuda_short_version < '12.1':
                gdb_cmd = f"cuda-gdb --cuda-use-lockfile=0 ./UnifiedMemoryPerf"
            else:
                gdb_cmd = f"cuda-gdb ./UnifiedMemoryPerf"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
            dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
            
            # Debug commands
            dgl_gdb.expect('target remote :2345', checkpoint=r"Remote debugging using :2345", mode=0)
            dgl_gdb.expect('set cuda break_on_launch all', mode=0)
            dgl_gdb.expect('c', checkpoint=r"135\s+int by = blockIdx.y", mode=0)
            dgl_gdb.expect('n', checkpoint=r"145\s+int aBegin = matrixDim.*", mode=0)
            dgl_gdb.expect('n', checkpoint=r"138\s+int tx = threadIdx.x;", mode=0)
            dgl_gdb.expect('si', checkpoint=r"\s+138\s+int tx = threadIdx.x;", mode=0)
            dgl_gdb.expect('si', checkpoint=r"\s+139\s+int ty = threadIdx.y;", mode=0)
            dgl_gdb.expect('set cuda break_on_launch none', mode=0)
            dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
            dgl_gdb.expect('quit', mode=0)
            
            dgl_result = dgl_gdb.conclude_result()
            result["cuda-gdb testing remote debug process"] = "passed" if dgl_result else "failed"
            
            if result["cuda-gdb testing remote debug process"] == "passed":
                logger.info('cuda-gdb remote debug process test passed')
            else:
                logger.info('cuda-gdb remote debug process test failed')

        except Exception as e:
            logger.error(f"Error during cuda-gdb debugging: {str(e)}")
            result["cuda-gdb testing remote debug process"] = "failed"

        finally:
            # Wait for threads to complete
            sample_thread1.join(timeout=5)
            gdbserver_thread.join(timeout=5)
            
            # Cleanup
            cleanup_cmd = "ps -ef|grep -E 'UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9"
            run_test_cmd(cleanup_cmd)

    except Exception as e:
        logger.error(f"Error in cuda_gdb_remote_debug_process_1820922: {str(e)}")
        result['cuda_gdb_remote_debug_process status'] = 'FAIL'

    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    dict_to_json(result1, f'{log_path}/cuda_gdb_coredump.json')
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdbserver_remote_debug_process_1820923():
    """Test cuda-gdb remote debug process functionality based on test case 1820922"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_REMOTE_DEBUG_PROCESS_PATH']
    mkdir(log_path)
    process_id = None

    try:
        # Set up environment
        ptrace_cmd = f'echo {host_password} | sudo -S sh -c "echo 0 >/proc/sys/kernel/yama/ptrace_scope"'
        run_test_cmd(ptrace_cmd)

        # Kill any existing processes
        cleanup_cmd = "ps -ef|grep -E 'UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9"
        run_test_cmd(cleanup_cmd)

        # Change to samples directory
        os.chdir(os.path.expandvars(sample_bin_path))

        # Check if sample exists
        if not os.path.isfile("UnifiedMemoryPerf"):
            logger.error("UnifiedMemoryPerf sample not found")
            result['the sample status'] = 'FAIL'
            return result

        # Use Event for synchronization between threads
        sample_ready = threading.Event()
        gdbserver_ready = threading.Event()

        def run_sample():
            """Thread function to run the UnifiedMemoryPerf sample"""
            nonlocal process_id
            try:
                # Step 6: Launch sample using subprocess instead of run_test_cmd
                sample_process = subprocess.Popen(
                    [f"{sample_bin_path}/UnifiedMemoryPerf"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                time.sleep(2)

                # Get PID directly from the subprocess
                process_id = str(sample_process.pid)
                if process_id:
                    logger.info(f"Sample process ID: {process_id}")
                    sample_ready.set()  # Signal that sample is ready
                else:
                    logger.error("Failed to get UnifiedMemoryPerf process ID")
            except Exception as e:
                logger.error(f"Error in sample thread: {str(e)}")

        def run_gdbserver():
            """Thread function to run cuda-gdbserver"""
            try:
                # Wait for sample to be ready
                if not sample_ready.wait(timeout=10):
                    logger.error("Timeout waiting for sample to start")
                    return

                # Step 7: Launch cuda-gdbserver using subprocess
                if process_id:
                    gdbserver_process = subprocess.Popen(
                        ["cuda-gdbserver", "--attach", ":2345", process_id],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    time.sleep(2)
                    gdbserver_ready.set()  # Signal that gdbserver is ready
            except Exception as e:
                logger.error(f"Error in gdbserver thread: {str(e)}")

        # Create and start threads
        sample_thread = threading.Thread(target=run_sample)
        gdbserver_thread = threading.Thread(target=run_gdbserver)

        sample_thread.start()
        gdbserver_thread.start()

        # Wait for both threads to complete their initial tasks
        if not gdbserver_ready.wait(timeout=20):
            logger.error("Timeout waiting for gdbserver to start")
            result['status'] = 'FAIL'
            return result

        # run cuda-gdbserver check
        try:
            gdb_log_name = f"{log_path}/gdb_commands.txt"
            if cuda_short_version < '12.1':
                gdb_cmd = f"cuda-gdb --cuda-use-lockfile=0 ./UnifiedMemoryPerf"
            else:
                gdb_cmd = f"cuda-gdb ./UnifiedMemoryPerf"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
            dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")

            # Debug commands
            # dgl_gdb.expect('set sysroot remote')
            dgl_gdb.expect('target remote 127.0.0.1:2345', checkpoint=r"Remote debugging using 127.0.0.1:2345", mode=0)
            dgl_gdb.expect('set cuda break_on_launch all', mode=0)
            dgl_gdb.expect('c', checkpoint=r"135\s+int by = blockIdx.y", mode=0)
            dgl_gdb.expect('n', checkpoint=r"145\s+int aBegin = matrixDim.*", mode=0)
            dgl_gdb.expect('n', checkpoint=r"138\s+int tx = threadIdx.x;", mode=0)
            dgl_gdb.expect('si', checkpoint=r"\s+138\s+int tx = threadIdx.x;", mode=0)
            dgl_gdb.expect('si', checkpoint=r"\s+139\s+int ty = threadIdx.y;", mode=0)
            dgl_gdb.expect('set cuda break_on_launch none', mode=0)
            dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
            dgl_gdb.expect('quit', mode=0)
            dgl_result = dgl_gdb.conclude_result()
            result["cuda-gdb testing remote debug process"] = "passed" if dgl_result else "failed"

            if result["cuda-gdb testing remote debug process"] == "passed":
                logger.info('cuda-gdb remote debug process test passed')
            else:
                logger.info('cuda-gdb remote debug process test failed')

        except Exception as e:
            logger.error(f"Error during cuda-gdb debugging: {str(e)}")
            result["cuda-gdb testing remote debug process"] = "failed"

        finally:
            # Wait for threads to complete
            sample_thread.join(timeout=5)
            gdbserver_thread.join(timeout=5)

            # Cleanup
            cleanup_cmd = "ps -ef|grep -E 'UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9"
            run_test_cmd(cleanup_cmd)

    except Exception as e:
        logger.error(f"Error in cuda_gdb_remote_debug_process_1820922: {str(e)}")
        result['cuda_gdb_remote_debug_process status'] = 'FAIL'

    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    dict_to_json(result1, f'{log_path}/cuda_gdb_coredump.json')
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_debug_with_graphic_sample_running_1820924():
    """Test cuda-gdb debugging while a graphic sample is running"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_DEBUG_WITH_GRAPHIC_SAMPLE_RUNNING_PATH']
    mkdir(log_path)
    
    try:
        # Change to samples directory
        os.chdir(os.path.expandvars(sample_bin_path))

        # Check if samples exist
        if not os.path.isfile("binomialOptions"):
            logger.error("binomialOptions sample not found")
            result['sample_status'] = 'failed'
            return FAILED

        if not os.path.isfile("UnifiedMemoryPerf"):
            logger.error("UnifiedMemoryPerf sample not found")
            result['sample_status'] = 'failed'
            return FAILED

        # Use Event for synchronization
        graphic_sample_ready = threading.Event()

        def run_sample():
            """Thread function to run the binomialOptions graphic sample"""
            try:
                logger.info("Starting binomialOptions sample...")
                sample_process = subprocess.Popen(
                    [f"{sample_bin_path}/binomialOptions"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                time.sleep(2)  # Give time for the sample to initialize
                graphic_sample_ready.set()
                
                # Wait for process to complete
                sample_process.wait()
                
            except Exception as e:
                logger.error(f"Error in graphic sample thread: {str(e)}")

        def run_cuda_gdb():
            """Thread function to run cuda-gdb directly on UnifiedMemoryPerf"""
            try:
                # Wait for graphic sample to be ready
                if not graphic_sample_ready.wait(timeout=10):
                    logger.error("Timeout waiting for sample to start")
                    return

                logger.info("Starting cuda-gdb with UnifiedMemoryPerf...")
                gdb_log_name = f"{log_path}/gdb_commands.txt"
                gdb_cmd = f"{cuda_bin}/cuda-gdb ./UnifiedMemoryPerf"
                
                dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
                
                # Debug commands
                dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
                dgl_gdb.expect('set cuda break_on_launch all', mode=0)
                dgl_gdb.expect('r', checkpoint=r"135\s+int by = blockIdx.y", mode=0)
                dgl_gdb.expect('n', checkpoint=r"145\s+int aBegin = matrixDim.*", mode=0)
                dgl_gdb.expect('n', checkpoint=r"138\s+int tx = threadIdx.x;", mode=0)
                dgl_gdb.expect('si', checkpoint=r"\s+138\s+int tx = threadIdx.x;", mode=0)
                dgl_gdb.expect('si', checkpoint=r"\s+139\s+int ty = threadIdx.y;", mode=0)
                dgl_gdb.expect('set cuda break_on_launch none', mode=0)
                dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
                dgl_gdb.expect('quit', mode=0)
                
                dgl_result = dgl_gdb.conclude_result()
                result["cuda-gdb debug with graphic sample running"] = "passed" if dgl_result else "failed"
                logger.info(f'cuda-gdb debug with graphic sample running: {result["cuda-gdb debug with graphic sample running"]}')
                
            except Exception as e:
                logger.error(f"Error in cuda-gdb thread: {str(e)}")
                result["cuda-gdb debug with graphic sample running"] = "failed"

        # Create and start threads
        sample_thread = threading.Thread(target=run_sample)
        gdb_thread = threading.Thread(target=run_cuda_gdb)

        # Start threads in sequence
        sample_thread.start()
        # Wait a moment for the graphic sample to start
        time.sleep(2)
        
        # Start debugging
        gdb_thread.start()

        # Wait for threads to complete with timeout
        sample_thread.join(timeout=60)
        gdb_thread.join(timeout=60)

    except Exception as e:
        logger.error(f"Error in cuda-gdb debug with graphic sample running: {str(e)}")
        result['cuda_gdb_debug_with_graphic_sample_running_status'] = 'failed'
        return FAILED

    finally:
        # Cleanup
        cleanup_cmd = "ps -ef|grep -E 'binomialOptions|UnifiedMemoryPerf|cuda-gdb'|grep -v grep|awk '{print $2}' |xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
        # Generate result summary
        result1 = calculate_result(result)
        dict_output(result, flag=output_flag)
        dict_to_json(result1, f'{log_path}/cuda_gdb_graphic.json')
        return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_threadMigration_1893046():
    """Test cuda-gdb threadMigration functionality"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_THREADMIGRATION_1893046_PATH']
    mkdir(log_path)
    
    try:
        # Copy threadMigration sample to a temporary directory if needed
        working_dir = os.path.expandvars(sample_bin_path)
        
        # Check if the sample exists
        if not os.path.isfile(os.path.join(working_dir, "threadMigration")):
            logger.error("threadMigration sample not found")
            result['sample_status'] = 'failed'
            return FAILED
        
        # Change to the working directory
        os.chdir(sample_bin_path)
        
        # Log file for cuda-gdb session
        gdb_log_name = os.path.join(log_path, "gdb_threadMigration_commands.txt")
        
        # Initialize the CUDBGExpect object
        gdb_cmd = f"{cuda_bin}/cuda-gdb ./threadMigration"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        
        # Start the cuda-gdb session
        logger.info("Starting cuda-gdb with threadMigration...")
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Execute the commands according to the template
        dgl_gdb.expect('set cuda break_on_launch app', mode=0)
        dgl_gdb.expect('r', checkpoint=r"29.*\[threadIdx\.x\].*", mode=0)
        dgl_gdb.expect('n', checkpoint=r"\d+\s+\}", mode=0)
        dgl_gdb.expect('thread 1', checkpoint=r"Switching to thread 1", mode=0)
        dgl_gdb.expect('c', checkpoint=r"   ", mode=0)
        dgl_gdb.expect('set cuda break_on_launch none', mode=0)
        dgl_gdb.expect('c', checkpoint=r"exited normally", mode=0)
        dgl_gdb.expect('quit', mode=0)
        
        # Analyze the results
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb threadMigration test"] = "passed" if dgl_result else "failed"
        logger.info(f'cuda-gdb threadMigration test: {result["cuda-gdb threadMigration test"]}')
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb threadMigration test: {str(e)}")
        result['cuda_gdb_threadMigration_status'] = 'failed'
    
    finally:
        result1 = calculate_result(result)
        dict_output(result, flag=output_flag)
        test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
        return PASSED if test_passed else FAILED


def cuda_gdb_performance_2002235():
    """Test cuda-gdb performance with matrixMul sample"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_PERFORMANCE_2002235_PATH']
    mkdir(log_path)
    
    try:
        # Check if matrixMul sample exists
        
        if not os.path.isfile(os.path.join(sample_bin_path, "matrixMul")):
            logger.error("matrixMul sample not found")
            result['sample_status'] = 'failed'
        else:
            # Change to the working directory
            os.chdir(sample_bin_path)
            
            # Log file for cuda-gdb session
            gdb_log_name = os.path.join(log_path, "gdb_performance_commands.txt")
            
            # Initialize the CUDBGExpect object
            gdb_cmd = f"{cuda_bin}/cuda-gdb ./matrixMul"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
            
            try:
                # Start the cuda-gdb session
                logger.info("Starting cuda-gdb with matrixMul...")
                dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
                
                if cuda_short_version < '13.0':
                    breakpoint_line = '62 if threadIdx.x==0'
                    check_point = r"62.*int.* t.* = .*threadIdx.*."
                else:
                    breakpoint_line = '61 int bx = blockIdx.x'
                    check_point = r"61.*int.* b.* = .*blockIdx.*."
                # Step 2: Set a conditional breakpoint
                logger.info("Setting conditional breakpoint at line 62 or 61(since 13.0)...")
                start_time = time.time()
                dgl_gdb.expect(f"b {breakpoint_line}", checkpoint=check_point, mode=0)
                dgl_gdb.expect('set cuda break_on_launch app', mode=0)
                # Step 3: Run the program
                logger.info("Running the program...")
                run_start_time = time.time()
                # dgl_gdb.expect('r', checkpoint=r"62.*int.* b.* = .*blockIdx.*.", mode=0)
                dgl_gdb.expect('r', checkpoint=check_point, mode=0)
                run_end_time = time.time()
                run_time = run_end_time - run_start_time
                
                # Step 4: Continue execution
                logger.info("Continuing execution...")
                continue_start_time = time.time()
                # dgl_gdb.expect('c', checkpoint=r"62.*int.* b.* = .*blockIdx.*.", mode=0)
                dgl_gdb.expect('c', checkpoint=check_point, mode=0)
                continue_end_time = time.time()
                continue_time = continue_end_time - continue_start_time
                dgl_gdb.expect('set cuda break_on_launch none', mode=0)
                dgl_gdb.expect('c', mode=0)
                # Exit cuda-gdb
                dgl_gdb.expect('quit', mode=0)
                
                # Analyze the results
                dgl_result = dgl_gdb.conclude_result()
                
                # Record performance metrics
                result["cuda-gdb performance test"] = "passed" if dgl_result else "failed"
                logger.info(f'cuda-gdb performance test: {result["cuda-gdb performance test"]}')
                # Check if performance is acceptable (adjust thresholds as needed)
                run_time_threshold = 10.0  # Maximum acceptable time to hit breakpoint in seconds
                continue_time_threshold = 15.0  # Maximum acceptable time to continue to next breakpoint
                
                if run_time <= run_time_threshold:
                    result["run_performance"] = "passed"
                    logger.info(f"Run performance test passed: {run_time:.2f} seconds (threshold: {run_time_threshold})")
                else:
                    result["run_performance"] = "failed"
                    logger.error(f"Run performance test failed: {run_time:.2f} seconds (threshold: {run_time_threshold})")
                
                if continue_time <= continue_time_threshold:
                    result["continue_performance"] = "passed"
                    logger.info(f"Continue performance test passed: {continue_time:.2f} seconds (threshold: {continue_time_threshold})")
                else:
                    result["continue_performance"] = "failed"
                    logger.error(f"Continue performance test failed: {continue_time:.2f} seconds (threshold: {continue_time_threshold})")
                
                logger.info(f'cuda-gdb performance test: {result["cuda-gdb performance test"]}')
                
            except Exception as e:
                logger.error(f"Error during cuda-gdb execution: {str(e)}")
                result["cuda-gdb performance test"] = "failed"
                result["execution_error"] = str(e)
                
    except Exception as e:
        logger.error(f"Error in cuda-gdb performance test: {str(e)}")
        result['cuda_gdb_performance_status'] = 'failed'
        result["setup_error"] = str(e)
    
    finally:
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'matrixMul|cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    dict_to_json(result1, f'{log_path}/cuda_gdb_coredump.json')
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_attach_application_linked_against_libcuda_2022634():
    """Test cuda-gdb attaching to applications linked against libcuda"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_ATTACH_APPLICATION_LINKED_AGAINST_LIBCUDA_2022634_PATH']
    log_name = case_config['CUDA_GDB_ATTACH_APPLICATION_LINKED_AGAINST_LIBCUDA']['LOG_NAME']
    mkdir(log_path)
    
    prepare_cmd = case_config['CUDA_GDB_ATTACH_APPLICATION_LINKED_AGAINST_LIBCUDA']['PREPARE']['CMD1'].format(user, password, base_url)
    run_loc_cmd(prepare_cmd)
    ldd_cmd = f"cd {log_path}; ldd ./a.out"
    check_result(ldd_cmd, 'check libcuda.so', log_name, result, 'libcuda.so')

    # Use Event for thread synchronization
    app_ready = threading.Event()
    app_pid = None
    app_process = None
    
    def run_app():
        """Thread function to run the application"""
        nonlocal app_pid, app_process
        try:
            logger.info("Starting application in background...")
            # Change to temp directory
            os.chdir(log_path)
            
            # Ensure binary file exists
            binary_path = f'{log_path}/a.out'
            if not os.path.isfile(binary_path):
                logger.error("Binary file not found at: " + binary_path)
                result["app_start"] = "failed"
                return
            # Start the application
            app_process = subprocess.Popen(
                [binary_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            app_pid = app_process.pid
            logger.info(f"Application started with PID: {app_pid}")
            time.sleep(2)
            app_ready.set()
            app_process.wait()
            
        except Exception as e:
            logger.error(f"Error in run_app thread: {str(e)}")
            result["app_start"] = "failed"
    
    def cuda_gdb_attach():
        """Thread function to attach cuda-gdb debugger"""
        try:
            # Wait for the application to be ready
            if not app_ready.wait(timeout=10):
                logger.error("Timeout waiting for application to start")
                result["cuda-gdb attach test"] = "failed"
                return
            if not app_pid:
                logger.error("No valid PID for application")
                result["cuda-gdb attach test"] = "failed"
                return
            logger.info(f"Attaching to process with PID {app_pid} using cuda-gdb...")
            gdb_log_name = os.path.join(log_path, "gdb_2022634_commands.txt")
            gdb_cmd = f"{cuda_bin}/cuda-gdb --pid={app_pid}"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
            dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
            dgl_gdb.expect('n', checkpoint=r".*dummy", mode=0)
            dgl_gdb.expect('n', checkpoint=r".*dummy", mode=0)
            dgl_gdb.expect('n', checkpoint=r".*dummy", mode=0)
            dgl_gdb.expect('n', checkpoint=r".*dummy", mode=0)
            dgl_gdb.expect('quit', mode=0)
            dgl_result = dgl_gdb.conclude_result()
            result["cuda-gdb attach test"] = "passed" if dgl_result else "failed"
            logger.info(f'cuda-gdb attach test: {result["cuda-gdb attach test"]}')
            
            if os.path.exists(gdb_log_name):
                with open(gdb_log_name, 'r') as f:
                    log_content = f.read() 
                error_patterns = [
                    "Segmentation fault",
                    "Cannot attach",
                    "Failed to initialize",
                    "Connection refused"
                ]
                errors_found = False
                for pattern in error_patterns:
                    if pattern in log_content:
                        errors_found = True
                        logger.error(f"Error detected in log: {pattern}")
                if not errors_found:
                    result["log_validation"] = "passed"
                else:
                    result["log_validation"] = "failed"
        except Exception as e:
            logger.error(f"Error in cuda_gdb_attach thread: {str(e)}")
            result["cuda-gdb attach test"] = "failed"
    
    try:
        # 步骤3和4：使用线程同时运行应用程序和附加调试器
        logger.info("Steps 3 & 4: Starting application and attaching debugger using threads...")
        app_thread = threading.Thread(target=run_app)
        gdb_thread = threading.Thread(target=cuda_gdb_attach)
        app_thread.start()
        time.sleep(1)
        gdb_thread.start()
        app_thread.join(timeout=60)
        gdb_thread.join(timeout=60)
        if app_pid:
            result["app_start"] = "passed"
        else:
            result["app_start"] = "failed"
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb attach test: {str(e)}")
        result['cuda_gdb_attach_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        try:
            if app_process and app_process.poll() is None:
                app_process.terminate()
                time.sleep(1)
                if app_process.poll() is None:
                    app_process.kill()
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {str(cleanup_error)}")
        cleanup_cmd = "ps -ef | grep -E 'a.out|cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    json_file = os.path.join(log_path, "cuda_gdb_attach_linked.json")
    dict_to_json(result1, json_file)
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_coredump_generate_for_certain_kernel_2082584():
    """Test cuda-gdb's ability to generate and analyze coredumps for specific CUDA kernels"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_COREDUMP_CERTAIN_KERNEL_PATH']
    log_name = case_config['CUDA_GDB_COREDUMP_CERTAIN_KERNEL']['LOG_NAME']
    mkdir(log_path)
    try:
        # step1: prepare the test program
        prepare_cmd = case_config['CUDA_GDB_COREDUMP_CERTAIN_KERNEL']['PREPARE']['CMD1'].format(user, password, base_url)
        print(prepare_cmd)
        run_loc_cmd(prepare_cmd)
        # Step 2: Run the program with cuda-gdb to generate a coredump
        cmd1 = case_config['CUDA_GDB_COREDUMP_CERTAIN_KERNEL']['STEP2']['CMD1']
        print(cmd1)
        run_loc_cmd(cmd1)
        logger.info("Step 2: Running the program with cuda-gdb to generate a coredump...")
        gdb_log_name = os.path.join(log_path, "gdb_step2_commands.txt")
        os.chdir(log_path)
        gdb_cmd = f'{cuda_bin}/cuda-gdb --ex "target cudacore foobar1"'
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"CUDA Exception: Warp Illegal Address")
        dgl_gdb.expect('info cuda devices', checkpoint=r"\*\s+0", mode=0)
        dgl_gdb.expect('info cuda contexts', checkpoint=r"\*\s+0", mode=0)
        dgl_gdb.expect('info cuda kernels', checkpoint=r"\*.*Active", mode=0)
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result['step2_status'] = 'passed' if dgl_result else 'failed'

        # Step 4: Run the program with cuda-gdb to generate a coredump
        prepare_cmd = case_config['CUDA_GDB_COREDUMP_CERTAIN_KERNEL']['PREPARE']['CMD2']
        run_loc_cmd(prepare_cmd)
        cmd2 = case_config['CUDA_GDB_COREDUMP_CERTAIN_KERNEL']['STEP2']['CMD2']
        run_loc_cmd(cmd2)
        logger.info("Step 2: Running the program with cuda-gdb to generate a coredump...")
        gdb_log_name1 = os.path.join(log_path, "gdb_step4_commands.txt")
        os.chdir(log_path)
        gdb_cmd = f'{cuda_bin}/cuda-gdb --ex "target cudacore foobar2"'
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name1)
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"CUDA Exception: Warp Illegal Address")
        dgl_gdb.expect('info cuda devices', checkpoint=r"\*\s+0", mode=0)
        dgl_gdb.expect('info cuda contexts', checkpoint=r"\*\s+0", mode=0)
        dgl_gdb.expect('info cuda kernels', checkpoint=r"\*.*Active", mode=0)
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result['step4_lineinfo_status'] = 'passed' if dgl_result else 'failed'
    except Exception as e:
        logger.error(f"Error in cuda-gdb coredump test: {str(e)}")
        result['cuda_gdb_coredump_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'coredump_test|cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Always write the JSON file
    json_file = os.path.join(log_path, "cuda_gdb_coredump_kernel.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Determine the final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_support_watchpoints_on_memory_locations_2211802():
    """Test cuda-gdb's support for watchpoints on device memory locations"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_SUPPORT_WATCHPOINTS_ON_MEMORY_LOCATIONS_2211802_PATH']
    mkdir(log_path)
    try:
        # step1: prepare the test program
        prepare_cmd = case_config['CUDA_GDB_SUPPORT_WATCHPOINTS_ON_MEMORY_LOCATIONS']['PREPARE']['CMD1'].format(user, password, base_url)
        run_loc_cmd(prepare_cmd)
        # Step 2: Run the program with cuda-gdb and set watchpoints
        logger.info("Step 2: Running the program with cuda-gdb to test watchpoints...")
        os.chdir(log_path)
        gdb_log_name = os.path.join(log_path, "gdb_watchpoints.txt")
        gdb_cmd = f"{cuda_bin}/cuda-gdb -q ./a.out"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"Reading symbols.*")
        dgl_gdb.expect('start', checkpoint=r"4\s+.*volatile.* .*int.* foo = .*7.*;")
        dgl_gdb.expect('watch foo', checkpoint=r"[Ww]atchpoint 2: foo", mode=0)
        dgl_gdb.expect('delete 2')
        dgl_gdb.expect('watch -l foo', checkpoint=r"[Ww]atchpoint 3: -location foo", mode=0)
        dgl_gdb.expect('watch $pc', checkpoint=r"[Ww]atchpoint 4: \$pc", mode=0)
        dgl_gdb.expect('c', checkpoint=r"Old value = ", mode=0)
        dgl_gdb.expect('help watch', checkpoint=r"A watchpoint stops execution of", mode=0)
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result["watchpoint_test"] = "passed" if dgl_result else "failed"
        logger.info(f'Watchpoint test: {result["watchpoint_test"]}')
    except Exception as e:
        logger.error(f"Error in cuda-gdb watchpoint test: {str(e)}")
        result['cuda_gdb_watchpoint_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'watchpoint_test|cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Always write the JSON file
    json_file = os.path.join(log_path, "cuda_gdb_watchpoints.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Determine the final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_ptype_option_2508682():
    """Test cuda-gdb's support Check cuda-gdb ptype option"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_PTYPE_OPTION_2508682_PATH']
    mkdir(log_path)
    try:
        # step1: prepare the test program
        prepare_cmd = case_config['CUDA_GDB_PTYPE_OPTION']['PREPARE']['CMD1'].format(user, password, base_url)
        run_loc_cmd(prepare_cmd)
        # Step 2: Run the program with cuda-gdb and set watchpoints
        logger.info("Step 2: Running the program with cuda-gdb to check ptype option...")
        os.chdir(log_path)
        gdb_log_name = os.path.join(log_path, "gdb_ptype_option.txt")
        gdb_cmd = f"{cuda_bin}/cuda-gdb ./a.out"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"Reading symbols.*")
        dgl_gdb.expect('b 10', checkpoint=r"Breakpoint 1 at.*line (10|19)", mode=0)
        dgl_gdb.expect('r', checkpoint=r"(10|19)\s+", mode=0)
        dgl_gdb.expect('ptype box', checkpoint=r"type = struct Types::box", mode=0)
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result["ptype_option_test"] = "passed" if dgl_result else "failed"
        logger.info(f'check ptype option: {result["ptype_option_test"]}')
    except Exception as e:
        logger.error(f"Error in cuda-gdb ptype option test: {str(e)}")
        result['cuda_gdb_ptype_option_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Always write the JSON file
    json_file = os.path.join(log_path, "cuda_gdb_watchpoints.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Determine the final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_variable_should_reflect_proper_block_count_2623454():
    """Test cuda-gdb variable should reflect proper block count"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_VARIABLE_SHOULD_REFLECT_PROPER_BLOCK_COUNT_2623454_PATH']
    mkdir(log_path)
    try:
        # step1: prepare the test program
        prepare_cmd = case_config['CUDA_GDB_VARIABLE_SHOULD_REFLECT_PROPER_BLOCK_COUNT']['PREPARE']['CMD1'].format(user, password, base_url)
        run_loc_cmd(prepare_cmd)
        # Step 2: Run the program with cuda-gdb and set watchpoints
        logger.info("Step 2: Running cuda-gdb variable should reflect proper block count...")
        os.chdir(log_path)
        gdb_log_name = os.path.join(log_path, "gdb_variable_should_reflect_proper_block_count.txt")
        gdb_cmd = f"{cuda_bin}/cuda-gdb ./t68"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(gdb_cmd, checkpoint=r"Reading symbols.*")
        dgl_gdb.expect('break t68.cu:5', checkpoint=r"Breakpoint 1 at\s+", mode=0)
        dgl_gdb.expect('r', checkpoint=r"5\s+.*int.* idx=", mode=0)
        dgl_gdb.expect('p gridDim', checkpoint=r"\$1", mode=0)
        dgl_gdb.expect('p blockDim', checkpoint=r"\$2", mode=0)
        dgl_gdb.expect('info cuda kernels', checkpoint=r"GridDim.*BlockDim", mode=0)
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result["cuda_gdb_variable_should_reflect_proper_block_count"] = "passed" if dgl_result else "failed"
        logger.info(f'check variable should reflect proper block count: {result["cuda_gdb_variable_should_reflect_proper_block_count"]}')
        """
        $1 = {x = 70000, y = 1, z = 1}，$2 = {x = 1024, y = 1, z = 1}  Active 0x3fffffffff (70000,1,1) (1024,1,1)
        """
        with open(gdb_log_name, 'r') as f:
            for line in f:
                if '$1' in line:
                    gridDim = tuple(int(x.split('=')[1].strip()) for x in line.replace('$1 = {', '').replace('}', '').split(','))
                    logger.info(f'gridDim value: {gridDim}')
                if '$2' in line:
                    blockDim = tuple(int(x.split('=')[1].strip()) for x in line.replace('$2 = {', '').replace('}', '').split(','))
                    logger.info(f'blockDim value: {blockDim}')
                if 'Active' in line:
                    pattern = r'\((\d+),(\d+),(\d+)\)'
                    matches = re.findall(pattern, line)
                    cuda_thread_info = [tuple(map(int, match)) for match in matches]
                    logger.info(f'cuda kernels information: {cuda_thread_info}')
        if cuda_thread_info == [gridDim, blockDim]:
            result["compare_gridDim_and_blockDim_values"] = "passed"
        else:
            result["compare_gridDim_and_blockDim_values"] = "failed"
        logger.info(f'compare gridDim and blockDim values: {result["compare_gridDim_and_blockDim_values"]}')
    except Exception as e:
        logger.error(f"Error in cuda-gdb variable should reflect proper block count test: {str(e)}")
        result['cuda_gdb_variable_should_reflect_proper_block_count_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Always write the JSON file
    json_file = os.path.join(log_path, "cuda_gdb_watchpoints.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Determine the final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_user_induces_coredump_2638978():
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_USER_INDUCES_COREDUMP_2638978_PATH']
    mkdir(log_path)
    os.chdir(sample_bin_path)
    hostname = socket.gethostname()
    corepipe_pattern1 = re.compile(f"^prw.*corepipe_{hostname}_[0-9]+.*$")
    nvcudmp_pattern1 = re.compile(f"core_[0-9]+_{hostname}_[0-9]+\.nvcudmp")
    corepipe_pattern2 = re.compile(f"^prw.*test_[0-9]+_{hostname}.*$")
    nvcudmp_pattern2 = re.compile(f"core\.[0-9]+\.[0-9]+\.{hostname}")
    # step1: clean the nvcudmp and corepipe files
    def run_sample():
        import glob
        for file in glob.glob("*.nvcudmp"):
            os.remove(file)
        for file in glob.glob("*corepipe*"):
            os.remove(file)
        for file in glob.glob("*test*"):
            os.remove(file)
        os.environ["CUDA_ENABLE_USER_TRIGGERED_COREDUMP"] = "1"
        process = subprocess.Popen([f"{sample_bin_path}/matrixMul", "-wA=4096", "-hA=4096", "-wB=4090", "-hB=4096"])
        time.sleep(5)
    
    def run_sample1():
        import glob
        for file in glob.glob("*.nvcudmp"):
            os.remove(file)
        for file in glob.glob("*corepipe*"):
            os.remove(file)
        for file in glob.glob("*test*"):
            os.remove(file)
        os.environ["CUDA_ENABLE_USER_TRIGGERED_COREDUMP"] = "1"
        os.environ["CUDA_COREDUMP_PIPE"] = "test_%t_%h"
        os.environ["CUDA_COREDUMP_FILE"] = "core.%p.%t.%h"
        process = subprocess.Popen([f"{sample_bin_path}/matrixMul", "-wA=4096", "-hA=4096", "-wB=4090", "-hB=4096"])
        time.sleep(5)
    
    def write_corepipe_file(corepipe_pattern):
        corepipe_files = 'ls -lrt|tail -1|awk -F " " "{print $9}"'
        out = run_loc_cmd(corepipe_files)
        out_file = out['output'].split('\n')[0].split(' ')[-1]
        if re.search(corepipe_pattern, out['output']):
            result[f"generate_corepipe_file--{out_file}"] = "passed"
            logger.info(f"step1: generate corepipe file {out_file} PASS")
        else:
            result[f"generate_corepipe_file--{out_file}"] = "failed"
            logger.info(f"step1: generate corepipe file {out_file} FAIL")
        time.sleep(30)
        write_cmd = f"echo '1' > {out_file}"
        time.sleep(10)
        run_loc_cmd(write_cmd)
    
    # step 1--step6
    try:
        run_sample_thread = threading.Thread(target=run_sample)
        run_sample_thread.start()
        time.sleep(5)
        run_sample_thread.join()
        write_corepipe_thread = threading.Thread(target=write_corepipe_file, args=[corepipe_pattern1])
        write_corepipe_thread.start()
        write_corepipe_thread.join()
        time.sleep(10)
        
    except Exception as e:
        logger.error(f"Error in run_user_triggered_coredump_test_2638978: {str(e)}")
        result['run_user_triggered_coredump_test_2638978_status'] = 'failed'
        result["error"] = str(e)
    finally:
        if 'run_sample_thread' in locals() and run_sample_thread.is_alive():
            logger.info("Waiting for sample thread to finish...")
            run_sample_thread.join(timeout=10)
        cleanup_cmd = "pkill -f matrixMul || true"
        try:
            subprocess.run(cleanup_cmd, shell=True)
        except Exception as e:
            logger.error(f"Error cleaning up processes: {str(e)}")
    nvcudmp_files = f'ls {sample_bin_path}/*.nvcudmp'
    out1 = run_loc_cmd(nvcudmp_files)
    out_file1 = out1['output'].split('\n')[0]
    if re.search(nvcudmp_pattern1, out1['output']):
        result[f"generate_nvcudmp_file--{out_file1}"] = "passed"
        logger.info(f"generate nvcudmp file-- {out_file1} PASS")
    else:
        result[f"generate_nvcudmp_file--{out_file1}"] = "failed"
        logger.info(f"generate nvcudmp file-- {out_file1} FAIL")

    # step 6
    try:
        run_sample_thread1 = threading.Thread(target=run_sample1)
        run_sample_thread1.start()
        time.sleep(5)
        run_sample_thread1.join()
        write_corepipe_thread1 = threading.Thread(target=write_corepipe_file, args=[corepipe_pattern2])
        write_corepipe_thread1.start()
        write_corepipe_thread1.join()
        time.sleep(10)
    except Exception as e:  
        logger.error(f"Error in run_user_triggered_coredump_test_2638978: {str(e)}")
        result['run_user_triggered_coredump_test_2638978_status'] = 'failed'
        result["error"] = str(e)
    finally:
        if 'run_sample_thread1' in locals() and run_sample_thread1.is_alive():
            logger.info("Waiting for sample thread to finish...")
            run_sample_thread1.join(timeout=10)
        cleanup_cmd = "pkill -f matrixMul || true"
    nvcudmp_files = f'ls {sample_bin_path}/core*'
    out1 = run_loc_cmd(nvcudmp_files)
    out_file1 = out1['output'].split('\n')[0]
    if re.search(nvcudmp_pattern2, out1['output']):
        result[f"generate_customer_core_file--{out_file1}"] = "passed"
        logger.info(f"generate customer core file-- {out_file1} PASS")
    else:
        result[f"generate_customer_core_file--{out_file1}"] = "failed"
        logger.info(f"generate customer core file-- {out_file1} FAIL")
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    json_file = os.path.join(log_path, "cuda_gdb_attach_linked.json")
    dict_to_json(result1, json_file)
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_sanity_with_cuda_app_sample_running_2641840():
    """Test cuda-gdb sanity check with another CUDA sample running in the background"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_SANITY_WITH_CUDA_APP_SAMPLE_RUNNING_2641840_PATH']
    mkdir(log_path)
    prepare_tools_package('cuda_app', cuda_app_download_path, platform, cuda_short_version)
    # use event to sync threads
    background_sample_ready = threading.Event()
    stop_background_sample = threading.Event()
    
    def run_background_sample():
        """Thread function to run a CUDA sample in the background"""
        try:

            logger.info("Step 1: Starting background CUDA application sample...")
            prepare_cmd = case_config['CUDA_GDB_SANITY_WITH_CUDA_APP_SAMPLE_RUNNING']['PREPARE']['CMD1']
            run_loc_cmd(prepare_cmd)
            
            # switch to sample directory
            bg_sample_dir = os.path.expandvars(sample_bin_path)
            os.chdir(bg_sample_dir)
            
            # select a long running sample, here use nbody
            if not os.path.isfile("./nbody"):
                logger.error("nbody sample not found")
                result["background_sample_status"] = "failed"
                return
            
            # start sample program
            cmd = ["./nbody", "-benchmark", "-numbodies=2560000"]
            logger.info(f"Running background sample: {' '.join(cmd)}")
            
            # use -benchmark option to set infinite loop running
            bg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # wait for sample initialization
            time.sleep(5)
            
            # set event to notify other threads that background sample is ready
            background_sample_ready.set()
            
            # wait for stop signal
            while not stop_background_sample.is_set():
                # check if process is still running
                if bg_process.poll() is not None:
                    logger.error("Background sample unexpectedly terminated")
                    break
                time.sleep(1)
            
            # terminate background sample
            if bg_process.poll() is None:
                logger.info("Terminating background sample...")
                bg_process.terminate()
                try:
                    bg_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.info("Background sample did not terminate, killing it...")
                    bg_process.kill()
            
            result["background_sample_status"] = "passed"
            logger.info("Background sample thread completed")
            
        except Exception as e:
            logger.error(f"Error in background sample thread: {str(e)}")
            result["background_sample_status"] = "failed"
    
    def run_cuda_gdb_sanity():
        """Thread function to run cuda-gdb sanity check"""
        try:
            # wait for background sample to be ready
            if not background_sample_ready.wait(timeout=30):
                logger.error("Timeout waiting for background sample to start")
                result["cuda_gdb_sanity_status"] = "failed"
                return
            
            logger.info("Step 2: Running cuda-gdb sanity check while background sample is running...")
            
            # switch to sample directory
            os.chdir(sample_bin_path)
            
            # select a simple sample for debugging
            if not os.path.isfile("./matrixMul"):
                logger.error("matrixMul sample not found")
                result["cuda_gdb_sanity_check"] = "failed"
                return
            
            # set cuda-gdb log file
            gdb_log_file = os.path.join(log_path, "cuda_gdb_sanity.log")
            
            # start cuda-gdb session
            gdb_cmd = f"{cuda_bin}/cuda-gdb ./matrixMul"
            dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_file)
            
            # start cuda-gdb
            dgl_gdb.spawn(gdb_cmd, checkpoint=r"NVIDIA \(R\).*")
            
            # execute cuda-gdb basic command test
            dgl_gdb.expect('set cuda break_on_launch application', mode=0)
            dgl_gdb.expect('run', checkpoint=r"Switching focus to CUDA kernel", mode=0)
            dgl_gdb.expect('cuda device', checkpoint=r"Device", mode=0)
            dgl_gdb.expect('cuda sm', checkpoint=r"SM", mode=0)
            dgl_gdb.expect('cuda block', checkpoint=r"Block", mode=0)
            dgl_gdb.expect('cuda thread', checkpoint=r"Thread", mode=0)
            dgl_gdb.expect('cuda kernel', checkpoint=r"Kernel", mode=0)
            dgl_gdb.expect('print gridDim', checkpoint=r"gridDim", mode=0)
            dgl_gdb.expect('print blockDim', checkpoint=r"blockDim", mode=0)
            dgl_gdb.expect('c', mode=0)
            dgl_gdb.expect('quit', mode=0)
            
            # analyze cuda-gdb execution result
            dgl_result = dgl_gdb.conclude_result()
            result["cuda_gdb_sanity_check"] = "passed" if dgl_result else "failed"
            logger.info(f"CUDA-GDB sanity check: {result['cuda_gdb_sanity_check']}")
            
            # check log to ensure no errors
            if os.path.exists(gdb_log_file):
                with open(gdb_log_file, 'r') as f:
                    log_content = f.read()
                
                error_patterns = [
                    "Segmentation fault",
                    "Assertion",
                    "Error",
                    "Failed to initialize"
                ]
                
                errors_found = False
                for pattern in error_patterns:
                    if pattern in log_content:
                        errors_found = True
                        logger.error(f"Error pattern found in log: {pattern}")
                
                if not errors_found:
                    result["log_check"] = "passed"
                else:
                    result["log_check"] = "failed"
            
        except Exception as e:
            logger.error(f"Error in cuda-gdb sanity check thread: {str(e)}")
            result["cuda_gdb_sanity_status"] = "failed"
    
    try:
        # create and start background sample thread
        logger.info("Starting background sample thread...")
        bg_thread = threading.Thread(target=run_background_sample)
        bg_thread.start()
        
        # create and start cuda-gdb sanity check thread
        logger.info("Starting cuda-gdb sanity check thread...")
        gdb_thread = threading.Thread(target=run_cuda_gdb_sanity)
        gdb_thread.start()
        
        # wait for two threads to complete
        gdb_thread.join(timeout=120)  # give sanity check 2 minutes time
        
        # tell background sample thread to stop
        stop_background_sample.set()
        bg_thread.join(timeout=30)  # give background sample 30 seconds time to stop
        
        # check result
        if "cuda_gdb_sanity_check" in result and result["cuda_gdb_sanity_check"] == "passed":
            logger.info("CUDA-GDB sanity check with background sample: PASSED")
        else:
            logger.error("CUDA-GDB sanity check with background sample: FAILED")
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb sanity with background sample test: {str(e)}")
        result['test_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # stop background sample
        stop_background_sample.set()
        
        # wait for threads to complete
        if 'bg_thread' in locals() and bg_thread.is_alive():
            bg_thread.join(timeout=10)
        
        if 'gdb_thread' in locals() and gdb_thread.is_alive():
            gdb_thread.join(timeout=10)
        
        # clean up possible still running processes
        cleanup_cmd = "pkill -f 'nbody|matrixMul|cuda-gdb' || true"
        try:
            subprocess.run(cleanup_cmd, shell=True)
        except Exception as e:
            logger.error(f"Error running cleanup command: {str(e)}")
    
    # generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # write to JSON file
    json_file = os.path.join(log_path, "cuda_gdb_sanity_with_background.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # determine final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_python_usage_2679726():
    """Test CUDA-GDB Python usage capabilities with existing files"""
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_PYTHON_USAGE_PATH']
    mkdir(log_path)
    
    # Define log file paths
    gdb_log_file = case_config['CUDA_GDB_PYTHON_USAGE']['LOG_NAME']
    python_version = 'python3 --version'
    out = run_loc_cmd(python_version)
    python_version_info = out['output'].split('\n')[0].split(' ')[1]
    
    try:
        # Save the original directory
        original_dir = os.getcwd()
        
        # Step 1: Check Python version in CUDA-GDB
        logger.info("Step 1: Checking Python version in CUDA-GDB...")
        
        # Create a temporary script to check Python version
        version_script = os.path.join(log_path, "check_version.gdb")
        with open(version_script, "w") as f:
            f.write("""
python
import sys
print("Python Version in CUDA-GDB:", sys.version)
end
quit
""")
        
        # Run CUDA-GDB with the version check script
        version_cmd = f"{cuda_bin}/cuda-gdb -x {version_script} --batch"
        version_process = subprocess.Popen(
            version_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for the command to complete
        version_stdout, version_stderr = version_process.communicate()
        
        # Check if the Python version information was obtained
        if python_version_info in version_stdout:
            result["step1_python_version_check"] = "passed"
            logger.info("Successfully obtained Python version information in CUDA-GDB")
            
            # Extract and log the version information
            version_line = next((line for line in version_stdout.splitlines() if "Python Version in CUDA-GDB:" in line), "")
            logger.info(version_line)
        else:
            result["step1_python_version_check"] = "failed"
            logger.error("Failed to obtain Python version information in CUDA-GDB")
        
        # Save the output to the log file
        with open(gdb_log_file, "w") as f:
            f.write(f"STEP 1 - Python Version Check\n")
            f.write(f"STDOUT:\n{version_stdout}\n\n")
            f.write(f"STDERR:\n{version_stderr}\n\n")
        
        # Step 2: Load and execute existing mergeSort app and Python API script
        logger.info("Step 2: Testing with existing mergeSort app and Python API script...")
        
        # Locate the mergeSort binary and Python API script
        # Assuming they are in a known location relative to the script directory
        sample_dir = os.path.expandvars(sample_bin_path)
        mergesort_path = os.path.join(sample_dir, "mergeSort")
        python_api_path = os.path.join(sample_dir, "python_api.py")
        prepare_cmd = case_config['CUDA_GDB_PYTHON_USAGE']['PREPARE']['CMD1'].format(user, password, base_url)
        run_loc_cmd(prepare_cmd)
        # Check if the required files exist
        if not os.path.isfile(mergesort_path):
            logger.warning(f"mergeSort binary not found at {mergesort_path}, will use relative path in CUDA-GDB")
        
        if not os.path.isfile(python_api_path):
            logger.warning(f"python_api.py not found at {python_api_path}, will use relative path in CUDA-GDB")
        
        # Change to the sample directory
        os.chdir(sample_dir)
        # Create a GDB script to load mergeSort and Python API
        load_script = os.path.join(log_path, "load_files.gdb")
        with open(load_script, "w") as f:
            f.write("""
file ./mergeSort
source python_api.py
quit
""")
        
        # Run CUDA-GDB with the load script
        load_cmd = f"{cuda_bin}/cuda-gdb -x {load_script} --batch"
        load_process = subprocess.Popen(
            load_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for the command to complete
        load_stdout, load_stderr = load_process.communicate()
        
        # Check if the files were loaded successfully
        if "show listsize" in load_stdout and 'Breakpoint 1' in load_stdout and 'main.cpp:100' in load_stdout:
            result["step2_file_loading"] = "passed"
            logger.info("Successfully loaded mergeSort binary and Python API script")
        else:
            result["step2_file_loading"] = "failed"
            logger.error("Failed to load mergeSort binary and Python API script")
        
        # Append the output to the log file
        with open(gdb_log_file, "a") as f:
            f.write(f"\nSTEP 2 - Loading and Executing Files\n")
            f.write(f"STDOUT:\n{load_stdout}\n\n")
            f.write(f"STDERR:\n{load_stderr}\n\n")
        
    except Exception as e:
        logger.error(f"Error in CUDA-GDB Python usage test: {str(e)}")
        result['cuda_gdb_python_usage_status'] = 'failed'
        result["error"] = str(e)
    
    finally:
        # Restore the original directory
        try:
            os.chdir(original_dir)
        except Exception as e:
            logger.error(f"Failed to restore original directory: {str(e)}")
        
        # Cleanup
        cleanup_cmd = "ps -ef | grep -E 'mergeSort|cuda-gdb' | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
        run_test_cmd(cleanup_cmd)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Always write the JSON file
    json_file = os.path.join(log_path, "cuda_gdb_python_usage.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Determine the final result
    test_passed = (result1["failed"] == 0 and result1["passed"] != 0)
    return PASSED if test_passed else FAILED


def cuda_gdb_scheduler_locking_2714136():
    """
    Test scheduler-locking command in CUDA-GDB (2714136)
    This test verifies that scheduler-locking commands are properly handled in CUDA-GDB
    """
    logger.info('Running test case for cuda-gdb scheduler-locking command: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2714136')
    
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_GDB_SCHEDULER_LOCKING_PATH']
    gdb_log_name = case_config['CUDA_GDB_SCHEDULER_LOCKING']['LOG_NAME']
    
    try:
        # Create log directory
        mkdir(log_path)
        # Prepare log file
        cmd1 = 'cd {}; rm -f {}; touch {}'.format(log_path, gdb_log_name, gdb_log_name)
        run_test_cmd(cmd1)
        os.chdir(log_path)
        
        # Initialize CUDA-GDB session
        cmd = "{}/cuda-gdb".format(cuda_bin)
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Test 1: Verify default scheduler-locking is "off"
        dgl_gdb.expect('show scheduler-locking', checkpoint=r"Mode for locking scheduler during execution is \"off\"", mode=0)
        
        # Test 2: Try to set scheduler-locking to "replay" - should fail
        dgl_gdb.expect('set scheduler-locking replay', checkpoint=r"Target .* cannot support this command", mode=0)
        
        # Verify setting didn't change
        dgl_gdb.expect('show scheduler-locking', checkpoint=r"Mode for locking scheduler during execution is \"off\"", mode=0)
        
        # Test 3: Try to set scheduler-locking to "step" - should fail
        dgl_gdb.expect('set scheduler-locking step', checkpoint=r"Target .* cannot support this command", mode=0)
        
        # Verify setting didn't change
        dgl_gdb.expect('show scheduler-locking', checkpoint=r"Mode for locking scheduler during execution is \"off\"", mode=0)
        
        # Test 4: Try to set scheduler-locking to "on" - should fail
        dgl_gdb.expect('set scheduler-locking on', checkpoint=r"Target .* cannot support this command", mode=0)
        
        # Verify setting didn't change
        dgl_gdb.expect('show scheduler-locking', checkpoint=r"Mode for locking scheduler during execution is \"off\"", mode=0)
        
        # Test 5: Try to set scheduler-locking to "off" - should fail, but mode remains "off"
        dgl_gdb.expect('set scheduler-locking off', checkpoint=r"Target .* cannot support this command", mode=0)
        
        # Verify setting is still "off"
        dgl_gdb.expect('show scheduler-locking', checkpoint=r"Mode for locking scheduler during execution is \"off\"", mode=0)
        
        # Run a simple program and check if scheduler locking impacts execution
        dgl_gdb.expect('q', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb scheduler-locking test"] = "passed" if dgl_result else "failed"
        logger.info(f"CUDA-GDB scheduler-locking test: {result['cuda-gdb scheduler-locking test']}")
            
    except Exception as e:
        logger.error(f"Error in cuda-gdb scheduler-locking test: {str(e)}")
        result["error"] = str(e)
        result["cuda-gdb scheduler-locking test"] = "failed"
        failed += 1
        
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Write results to JSON
    json_file = os.path.join(log_path, "cuda_gdb_scheduler_locking.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Return final status
    return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


def cuda_gdb_good_backtrace_with_extended_debug_line_info_2755196():
    """
    Test cuda-gdb's backtrace functionality with extended debug line information - test case 2755196.
    This test verifies that cuda-gdb correctly displays backtrace information with proper line numbers 
    and function names after encountering a CUDA exception.
    """
    logger.info('Running test case for extended debug line info backtrace: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2755196')
    
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_GOOD_BACKTRACE_WITH_EXTENDED_DEBUG_LINE_INFO_PATH']
    log_name = case_config['CUDA_GDB_GOOD_BACKTRACE_WITH_EXTENDED_DEBUG_LINE_INFO']['LOG_NAME']
    
    try:
        # Create log directory
        mkdir(log_path)
        sm = get_sm()
        sm1 = str(sm).split('.')[0] + str(sm).split('.')[1]
        # Prepare test environment
        prepare_cmd = case_config['CUDA_GDB_GOOD_BACKTRACE_WITH_EXTENDED_DEBUG_LINE_INFO']['PREPARE']['CMD1'].format(user, password, base_url, sm1, sm1)
        run_test_cmd(prepare_cmd)
        

        
        if not os.path.isfile(f"{log_path}/lineinfo"):
            logger.error("Failed to compile lineinfo test program")
            result["compilation sample program"] = "failed"
            return FAILED
        else:
            result["compilation sample program"] = "passed"
            logger.info("Successfully compiled lineinfo test program")
        
        # Prepare log file
        cmd1 = f'cd {log_path}; rm -f {log_name}; touch {log_name}'
        run_test_cmd(cmd1)
        
        # Determine GPU family for proper backtrace validation
        # Blackwell and newer GPUs have slightly different backtrace format
        is_blackwell = False
        if sm >= 9.0:
            is_blackwell = True
        # Run CUDA-GDB and validate backtrace
        os.chdir(log_path)
        cmd = f"{cuda_bin}/cuda-gdb ./lineinfo"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Set pagination off to avoid paging issues
        dgl_gdb.expect('set pagination off', mode=0)
        
        # Run the program and wait for the exception
        dgl_gdb.expect('r', checkpoint=r"CUDA Exception: Warp Misaligned Address", mode=0)
        # Verify we received the Misaligned Address exception (CUDA_EXCEPTION_6)
        dgl_gdb.expect('print $pc', checkpoint=r"0x[0-9a-fA-F]+", mode=0)
        
        # Execute backtrace command
        bt_result = dgl_gdb.expect('bt', checkpoint=r"#0\s+.*function_fault_noinline", mode=0)
        
        # Validate backtrace output based on GPU architecture
        if is_blackwell:
            # Blackwell format validation
            result["check_bt_function_fault_noinline"] = 'passed' if search_keyword_in_file(log_name, r"#0\s+.*function_fault_noinline") else 'failed'
            result["check_bt_function_recursive_noinline_1"] = 'passed' if search_keyword_in_file(log_name, r"#1\s+.*function_recursive_noinline") else 'failed'
            result["check_bt_function_recursive_noinline_2"] = 'passed' if search_keyword_in_file(log_name, r"#2\s+.*function_recursive_noinline") else 'failed'
            result["check_bt_function_recursive_noinline_3"] = 'passed' if search_keyword_in_file(log_name, r"#3\s+.*function_recursive_noinline") else 'failed'
            result["check_bt_function_recursive_maybe_inline"] = 'passed' if search_keyword_in_file(log_name, r"#[6-9]\s+.*function_recursive_maybe_inline") else 'failed'
            result["check_bt_device_kernel0"] = 'passed' if search_keyword_in_file(log_name, r"#1[0-9]\s+.*device_kernel0") else 'failed'
        else:
            # Non-Blackwell format validation
            result["check_bt_function_fault_noinline"] = 'passed' if search_keyword_in_file(log_name, r"#0\s+.*function_fault_noinline") else 'failed'
            result["check_bt_function_recursive_noinline"] = 'passed' if search_keyword_in_file(log_name, r"#1\s+.*function_recursive_noinline") else 'failed'    
            result["check_bt_function_maybe_inline"] = 'passed' if search_keyword_in_file(log_name, r"#2\s+.*function_maybe_inline") else 'failed'
            result["check_bt_function_recursive_noinline_second"] = 'passed' if search_keyword_in_file(log_name, r"#[3-4]\s+.*function_recursive_noinline") else 'failed'
            result["check_bt_function_recursive_maybe_inline"] = 'passed' if search_keyword_in_file(log_name, r"#[8-9]\s+.*function_recursive_maybe_inline") else 'failed'
            result["check_bt_device_kernel0"] = 'passed' if search_keyword_in_file(log_name, r"#1[0-9]\s+.*device_kernel0") else 'failed'
        
        # Extract line numbers and verify they are present
        line_numbers_present = 'passed' if search_keyword_in_file(log_name, r"lineinfo\.cu:[0-9]+") else 'failed'
        result["check_line_numbers_present"] = line_numbers_present
        
        # Exit CUDA-GDB
        dgl_gdb.expect('quit', mode=0)
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb extended debug line info test"] = "passed" if dgl_result else "failed"
        logger.info(f"CUDA-GDB extended debug line info backtrace test: {result['cuda-gdb extended debug line info test']}")
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb extended debug line info test: {str(e)}")
        result["cuda-gdb extended debug line info test"] = "failed"
        result["error"] = str(e)
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    
    # Write to JSON file
    json_file = os.path.join(log_path, "cuda_gdb_extended_debug_line_info.json")
    dict_to_json(result1, json_file)
    
    return (PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED)


def cuda_gdb_check_register_info_2772109():
    """
    Test register information display in CUDA-GDB (2772109).
    This test verifies that register information is correctly displayed,
    especially checking the rename from (U)P7 to (U)PT in CUDA 13.0+.
    """
    logger.info('Running test case for cuda-gdb register info check: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2772109')
    
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_CHECK_REGISTER_INFO_PATH']
    gdb_log_name = case_config['CUDA_GDB_CHECK_REGISTER_INFO']['LOG_NAME']
    
    try:
        # Create log directory
        mkdir(log_path)
        
        # Prepare log file
        cmd1 = f'rm -f {gdb_log_name}; touch {gdb_log_name}'
        run_test_cmd(cmd1)
        
        # Copy a sample CUDA program with debug info
        os.chdir(sample_bin_path)
        if not os.path.isfile("./matrixMul"):
            logger.error("matrixMul sample not found")
            result["sample_availability"] = "failed"
            return FAILED
        else:
            result["sample_availability"] = "passed"
        
        # Initialize CUDA-GDB session
        cmd = f"{cuda_bin}/cuda-gdb ./matrixMul"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Set breakpoint on kernel launch
        dgl_gdb.expect('set cuda break_on_launch app', mode=0)
        
        # Run program to hit breakpoint
        dgl_gdb.expect('r', checkpoint=r"\d+\s+.*int.*\s+bx\s+", mode=0)
        
        # Check for specific registers based on CUDA version
        is_cuda_13_plus = cuda_short_version >= '13.0'
        
        if is_cuda_13_plus:
            # Check renamed registers in CUDA 13.0+
            dgl_gdb.expect('info register $UPT', checkpoint=r"UPT\s+0x\w+\s+\w+", mode=0)
            dgl_gdb.expect('info register system', checkpoint=r"PT\s+0x\w+\s+\w+", mode=0)
            
            # Verify the old register names are not available in CUDA 13.0+
            dgl_gdb.expect('info register $UP7', checkpoint=r"Invalid register", mode=0)
            
            # Check the system register output for PT instead of P7
            result["check_UPT_register"] = 'passed' if search_keyword_in_file(gdb_log_name, r"UPT\s+0x\w+\s+\w+") else 'failed'
            result["check_PT_in_system"] = 'passed' if search_keyword_in_file(gdb_log_name, r"PT\s+0x\w+\s+\w+") else 'failed'
            result["check_UP7_not_available"] = 'passed' if search_keyword_in_file(gdb_log_name, r"Invalid register") else 'failed'
        else:
            # Check legacy register names in CUDA < 13.0
            dgl_gdb.expect('info register $UP7', checkpoint=r"UP7\s+0x\w+\s+\w+", mode=0)
            dgl_gdb.expect('info register system', checkpoint=r"P7\s+0x\w+\s+\w+", mode=0)
            
            # Verify the new register names are not available in older CUDA
            dgl_gdb.expect('info register $UPT', checkpoint=r"Invalid register", mode=0)
            
            # Check the system register output for P7 instead of PT
            result["check_UP7_register"] = 'passed' if search_keyword_in_file(gdb_log_name, r"UP7\s+0x\w+\s+\w+") else 'failed'
            result["check_P7_in_system"] = 'passed' if search_keyword_in_file(gdb_log_name, r"P7\s+0x\w+\s+\w+") else 'failed'
            result["check_UPT_not_available"] = 'passed' if search_keyword_in_file(gdb_log_name, r"Invalid register") else 'failed'
        
        # Exit cuda-gdb
        dgl_gdb.expect('quit', mode=0)
        
        # Process results
        dgl_result = dgl_gdb.conclude_result()
        test_passed = dgl_result
        
        result["cuda-gdb register info test"] = "passed" if test_passed else "failed"
        logger.info(f"CUDA-GDB register info test: {result['cuda-gdb register info test']}")
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb register info test: {str(e)}")
        result["error"] = str(e)
        result["cuda-gdb register info test"] = "failed"
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Write results to JSON
    json_file = os.path.join(log_path, "cuda_gdb_register_info.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Return final status
    return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


def cuda_gdb_perf_check_2788750():
    """
    Test CUDA-GDB performance with UnifiedMemoryStreams sample (2788750).
    This test verifies that CUDA-GDB's execution time meets performance standards
    by running UnifiedMemoryStreams sample in batch mode and comparing execution time
    against platform-specific baselines.
    """
    logger.info('Running test case for cuda-gdb performance check: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2788750')
    
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_PERF_CHECK_2788750_PATH']
    gdb_log_name = case_config['CUDA_GDB_PERF_CHECK_2788750']['LOG_NAME']
    perf_log = os.path.join(log_path, "time.output")
    
    try:
        # Create log directory
        mkdir(log_path)
        
        # Prepare log file
        cmd1 = f'rm -f {gdb_log_name}; touch {gdb_log_name}'
        run_test_cmd(cmd1)
        
        # Change to samples directory
        os.chdir(sample_bin_path)
        
        # Check if UnifiedMemoryStreams sample exists
        if not os.path.isfile("./UnifiedMemoryStreams"):
            logger.error("UnifiedMemoryStreams sample not found")
            result["sample_availability"] = "failed"
            return FAILED
        else:
            result["sample_availability"] = "passed"
            logger.info("UnifiedMemoryStreams sample found, proceeding with performance test")
        
        # Define baseline thresholds based on platform
        platform_type = platform.lower()
        if platform_type == "x86":
            baseline = 27.8 * 2  # seconds
            logger.info(f"Using x86 platform baseline: {baseline:.1f} seconds")
        elif platform_type == "arm":
            baseline = 18.0 * 2  # seconds
            logger.info(f"Using ARM platform baseline: {baseline:.1f} seconds")
        elif platform_type == "power":
            baseline = 29.4 * 2  # seconds
            logger.info(f"Using Power platform baseline: {baseline:.1f} seconds")
        else:
            baseline = 30.0 * 2  # Default fallback
            logger.warning(f"Unknown platform '{platform_type}', using default baseline: {baseline:.1f} seconds")
        
        # Execute CUDA-GDB with timing measurement
        logger.info("Running CUDA-GDB performance test...")
        perf_cmd = f"time -o {perf_log} -f \"%e\" {cuda_bin}/cuda-gdb ./UnifiedMemoryStreams -ex run -batch"
        run_test_cmd(perf_cmd)
        
        # Read the execution time
        if os.path.exists(perf_log):
            with open(perf_log, 'r') as f:
                execution_time = float(f.read().strip())
            
            # Check if performance meets baseline requirement
            if execution_time < baseline:
                result["performance_check"] = "passed"
                logger.info(f"Performance test passed: {execution_time:.2f} seconds (below baseline: {baseline:.1f} seconds)")
            else:
                result["performance_check"] = "failed"
                logger.error(f"Performance test failed: {execution_time:.2f} seconds (exceeds baseline: {baseline:.1f} seconds)")
        else:
            logger.error(f"Time output file not found: {perf_log}")
            result["performance_check"] = "failed"
            result["error"] = f"Time output file not found: {perf_log}"
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb performance check: {str(e)}")
        result["error"] = str(e)
        result["performance_check"] = "failed"
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Write results to JSON
    json_file = os.path.join(log_path, "cuda_gdb_perf_check.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Return final status
    return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


def cuda_gdb_hit_template_function_breakpoint_2793993():
    """
    Test CUDA-GDB's ability to hit template function breakpoints (2793993).
    This test verifies that CUDA-GDB can correctly set breakpoints on template
    functions and that GPU debug information is correctly loaded, checking for 
    "CP: GPU debug info is loaded and BP is" messages.
    """
    logger.info('Running test case for cuda-gdb template function breakpoint: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2793993')
    
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_TEMPLATE_FUNCTION_BREAKPOINT_PATH']
    gdb_log_name = case_config['CUDA_GDB_TEMPLATE_FUNCTION_BREAKPOINT']['LOG_NAME']
    
    try:
        # Create log directory
        mkdir(log_path)
        
        # Prepare log file
        cmd1 = f'rm -f {gdb_log_name}; touch {gdb_log_name}'
        run_test_cmd(cmd1)
        
        # Copy a sample CUDA program with debug info
        os.chdir(sample_bin_path)
        if not os.path.isfile("./matrixMul"):
            logger.error("matrixMul sample not found")
            result["sample_availability"] = "failed"
            return FAILED
        else:
            result["sample_availability"] = "passed"
            logger.info("matrixMul sample found, proceeding with template function test")
        
        # Initialize CUDA-GDB session
        cmd = f"{cuda_bin}/cuda-gdb ./matrixMul"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Set breakpoint on template function
        if cuda_short_version >= '11.6':
            dgl_gdb.expect('break MatrixMulCUDA<32>', checkpoint=r"Breakpoint.*line.*", mode=0)
            dgl_gdb.expect('info breakpoints', checkpoint=r"MatrixMulCUDA<32>.*", mode=0)
        else:
            dgl_gdb.expect('break MatrixMulCUDA<32>', checkpoint=r"Breakpoint.*line.*42", mode=0)
            dgl_gdb.expect('info breakpoints', checkpoint=r"1\s+breakpoint\s+.*matrixMul.cu.*42", mode=0)
        
        # Run program and check for the breakpoint to be hit
        dgl_gdb.expect('r', checkpoint=r"\d+\s+.*int.*\s+bx\s+", mode=0)
        
        # Verify backtrace shows template function
        dgl_gdb.expect('bt', checkpoint=r"MatrixMulCUDA<32>.*", mode=0)
        
        # Check for debug information messages
        # result["cp_debug_info_loaded"] = "passed" if search_keyword_in_file(gdb_log_name, r"CP: GPU debug info is loaded") else "failed"
        # result["breakpoint_loaded"] = "passed" if search_keyword_in_file(gdb_log_name, r"and BP is") else "failed"
        
        # Check that focus is on GPU side
        dgl_gdb.expect('info thread', checkpoint=r".*Thread.*", mode=0)
        
        # Step through the code to verify debugging works
        if cuda_short_version >= '11.6':
            dgl_gdb.expect('s', checkpoint=r"\d+\s+.*int.* by = .*blockIdx.*.y;", mode=0)
            dgl_gdb.expect('n', checkpoint=r"\d+\s+.*int.* tx = .*threadIdx.*.x;", mode=0)
        else:
            dgl_gdb.expect('s', checkpoint=r"45\s+.*int.* by = .*blockIdx.*.y;", mode=0)
            dgl_gdb.expect('n', checkpoint=r"48\s+.*int.* tx = .*threadIdx.*.x;", mode=0)
        
        # Verify breakpoint status
        dgl_gdb.expect('info breakpoints', checkpoint=r"breakpoint already hit 1 time", mode=0)
        
        # Continue execution and verify it hits the breakpoint again
        dgl_gdb.expect('c', checkpoint=r"\d+\s+.*int.* bx = .*blockIdx.*.x;", mode=0)
        
        # Check focus is still on GPU side
        dgl_gdb.expect('cuda device', checkpoint=r"device\s+\d+", mode=0)
        dgl_gdb.expect('cuda block', checkpoint=r"block\s+\(\d+", mode=0)
        dgl_gdb.expect('cuda thread', checkpoint=r"thread\s+\(\d+", mode=0)
        
        # Exit cuda-gdb
        dgl_gdb.expect('quit')
        
        # Process results
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb template function test"] = "passed" if dgl_result else "failed"
        logger.info(f"CUDA-GDB template function test: {result['cuda-gdb template function test']}")
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb template function test: {str(e)}")
        result["error"] = str(e)
        result["cuda-gdb template function test"] = "failed"
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Write results to JSON
    json_file = os.path.join(log_path, "cuda_gdb_template_function.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Return final status
    return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


def cuda_gdb_hit_assert_simpleAssert_2960952():
    """
    Test CUDA-GDB's ability to handle CUDA assertions (2960952).
    This test verifies that CUDA-GDB properly detects and displays assertion failures
    in CUDA kernels, allowing users to debug the failure point.
    """
    logger.info('Running test case for cuda-gdb assertion handling: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T2960952')
    
    result = {}
    log_path = case_config['global']['env']['CUDA_GDB_ASSERT_SIMPLEASSERT_PATH']
    gdb_log_name = case_config['CUDA_GDB_ASSERT_SIMPLEASSERT']['LOG_NAME']
    
    try:
        # Create log directory
        mkdir(log_path)
        
        # Prepare log file
        cmd1 = f'rm -f {gdb_log_name}; touch {gdb_log_name}'
        run_test_cmd(cmd1)
        
        # Change to samples directory and check for simpleAssert sample
        os.chdir(sample_bin_path)
        if not os.path.isfile("./simpleAssert"):
            logger.error("simpleAssert sample not found")
            result["sample_availability"] = "failed"
            return FAILED
        else:
            result["sample_availability"] = "passed"
            logger.info("simpleAssert sample found, proceeding with assertion test")
        
        # Initialize CUDA-GDB session with simpleAssert sample
        cmd = f"{cuda_bin}/cuda-gdb ./simpleAssert"
        dgl_gdb = CUDBGExpect(cmd=None, logfile=gdb_log_name)
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
        
        # Set break on kernel launch to stop at the beginning of the kernel
        dgl_gdb.expect('set cuda break_on_launch app', mode=0)
        
        # Run program to hit kernel launch breakpoint
        dgl_gdb.expect('r', checkpoint=r"\d+\s+.*int.*gtid", mode=0)
        
        # Step through the code until assertion line
        dgl_gdb.expect('n', checkpoint=r"\d+\s+.*assert\(gtid < N\)", mode=0)
        
        # Step past assertion (for first thread, which should not assert)
        dgl_gdb.expect('n', checkpoint=r"\d+\s+\}", mode=0)
        
        # Step to next thread's kernel execution
        dgl_gdb.expect('n', checkpoint=r"\d+\s+.*int.*gtid", mode=0)
        
        # Continue program execution to hit assertion failures in threads where gtid >= N
        dgl_gdb.expect('c', checkpoint=r"CUDA_EXCEPTION_12, Warp Assert", mode=0)
        
        # Verify we can examine the assertion call stack
        dgl_gdb.expect('up', checkpoint=r"#1\s+.*testKernel.*", mode=0)
        
        # Check backtrace shows assertion location
        dgl_gdb.expect('bt', checkpoint=r"[\#0\s+.*__assert_fail.*， \#1\s+.*testKernel]", mode=0)
        
        # Continue execution to exit program
        dgl_gdb.expect('c', checkpoint=r"exited with code 01", mode=0)
        
        # Exit CUDA-GDB
        dgl_gdb.expect('quit', mode=0)
        
        # Validate test results
        result["break_on_launch"] = 'passed' if search_keyword_in_file(gdb_log_name, r"set cuda break_on_launch app") else 'failed'
        result["assertion_hit"] = 'passed' if search_keyword_in_file(gdb_log_name, r"Assertion failed") else 'failed'
        result["exception_signal"] = 'passed' if search_keyword_in_file(gdb_log_name, r"CUDA_EXCEPTION_12, Warp Assert") else 'failed'
        result["program_exits"] = 'passed' if search_keyword_in_file(gdb_log_name, r"exited with code 01") else 'failed'
        
        # Process results
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb assertion test"] = "passed" if dgl_result else "failed"
        logger.info(f"CUDA-GDB assertion test: {result['cuda-gdb assertion test']}")
        
    except Exception as e:
        logger.error(f"Error in cuda-gdb assertion test: {str(e)}")
        result["error"] = str(e)
        result["cuda-gdb assertion test"] = "failed"
    
    # Generate result summary
    result1 = calculate_result(result)
    dict_output(result, flag=output_flag)
    
    # Write results to JSON
    json_file = os.path.join(log_path, "cuda_gdb_assert_simpleAssert.json")
    try:
        dict_to_json(result1, json_file)
        logger.info(f"Results written to {json_file}")
    except Exception as e:
        logger.error(f"Failed to write results to JSON: {str(e)}")
    
    # Return final status
    return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


def cuda_gdb_UD_backend_cgaexception_for_Hopper_3051671():
    """
    Test CUDA-GDB's ability to handle CGA exceptions on Hopper architecture (3051671).
    This test verifies that CUDA-GDB correctly detects and displays Cluster exceptions
    in CUDA kernels on Hopper/Blackwell GPUs, including Cluster target block not present (CUDA_EXCEPTION_17)
    and Cluster out of range address (CUDA_EXCEPTION_18).
    """
    logger.info('Running test case for cuda-gdb CGA exceptions on Hopper: http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3051671')
    
    # Get SM version to check if running on Hopper or Blackwell
    sm = get_sm()
    if sm < 9.0:  # Hopper is SM90
        logger.warning(f"This test requires Hopper (SM90) or Blackwell GPU, current SM is {sm}")
        return WAIVED
    else:
        result = {}
        log_path = case_config['global']['env']['CUDA_GDB_UD_BACKEND_CGAEXCEPTION_PATH']
        gdb_log_name = case_config['CUDA_GDB_UD_BACKEND_CGAEXCEPTION']['LOG_NAME']
        
        try:
            # Create log directory
            mkdir(log_path)
            
            # Prepare log file
            cmd1 = f'rm -f {gdb_log_name}; touch {gdb_log_name}'
            run_test_cmd(cmd1)
            
            # Download and extract the debugger test suite
            prepare_tools_package('gdb', debugger_download_to_path, platform, cuda_short_version)
            
            # Determine path to test binaries based on platform
            if platform == 'x86':
                debug_sample_path = os.path.join(debugger_download_to_path, 'bin/x86_64_Linux_release/debugger-testsuite')
            elif platform == 'arm':
                debug_sample_path = os.path.join(debugger_download_to_path, 'bin/aarch64_Linux_release/debugger-testsuite')
            else:
                logger.error(f"Unsupported platform {platform}")
                result["platform_support"] = "failed"
                return FAILED
            
            # Check if cgaexceptions binary exists
            cga_binary = os.path.join(debug_sample_path, 'cgaexceptions')
            if not os.path.isfile(cga_binary):
                logger.error("cgaexceptions binary not found")
                result["binary_availability"] = "failed"
                return FAILED
            else:
                result["binary_availability"] = "passed"
                logger.info("cgaexceptions binary found, proceeding with CGA exception test")
            
            # Define the exception types to test
            cuda_exceptions_dict = {
                1: "CUDA Exception: Warp Out-of-range Address.*CUDA_EXCEPTION_5.*WARP_ESR_OOR_SHARED",
                2: "CUDA Exception: Warp Misaligned Address.*CUDA_EXCEPTION_6.*WARP_ESR_MISALIGNED_SHARED",
                3: "CUDA Exception: Warp Out-of-range Address.*CUDA_EXCEPTION_5.*WARP_ESR_CTA_NOT_PRESENT",
                4: "CUDA Exception: Cluster target block not present.*CUDA_EXCEPTION_17.*CGA_ESR_CTA_NOT_PRESENT",
                5: "CUDA Exception: Cluster out of range address.*CUDA_EXCEPTION_18.*CGA_ESR_OOR_ADDR"
            }
            
            # Run tests for each exception type
            for x in range(1, 6):  # Test all 5 exception types
                # Create a fresh log file for each exception type
                exception_log = os.path.join(log_path, f"cga_exception_{x}.log")
                cmd1 = f'rm -f {exception_log}; touch {exception_log}'
                run_test_cmd(cmd1)
                
                # Initialize CUDA-GDB with the cgaexceptions binary and exception number
                cmd = f"{cuda_bin}/cuda-gdb --args {cga_binary} {x}"
                logger.info(f"Testing exception type {x}: {cmd}")
                
                # Use CUDBGExpect to run the test
                dgl_gdb = CUDBGExpect(cmd=None, logfile=exception_log)
                dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\).*")
                
                # Set pagination off to avoid paging issues
                dgl_gdb.expect('set pagination off', mode=0)
                
                # Run the program, expecting to hit the specified exception
                dgl_gdb.expect('r', checkpoint=cuda_exceptions_dict[x], mode=0)
                
                # Verify backtrace shows the exception source
                dgl_gdb.expect('bt', checkpoint=r"#0\s+.*exception_kernel", mode=0)
                
                # Exit CUDA-GDB
                dgl_gdb.expect('quit', mode=0)
                
                # Process results for this exception type
                dgl_result = dgl_gdb.conclude_result()
                
                # Check for key indicators in the log
                result[f"exception_{x}_triggered"] = 'passed' if search_keyword_in_file(exception_log, cuda_exceptions_dict[x]) else 'failed'
                result[f"exception_{x}_backtrace"] = 'passed' if search_keyword_in_file(exception_log, "exception_kernel") else 'failed'
                
                # Append results to main log
                cmd = f"cat {exception_log} >> {gdb_log_name}"
                run_test_cmd(cmd)
                
                # For cluster-specific exceptions (4 and 5), perform additional checks
                if x >= 4:
                    # Check for cluster block info in the log for cluster exceptions
                    cluster_info_pattern = r"The exception was triggered by a thread in blockIdx\(.*\) for access to shared memory for target blockIdx\(.*\)"
                    result[f"cluster_info_{x}"] = 'passed' if search_keyword_in_file(exception_log, cluster_info_pattern) else 'failed'
            
            # Summarize all test results
            result["cuda-gdb CGA exception test"] = "passed" if all('failed' not in v for k, v in result.items() if k.startswith("exception_")) else "failed"
            logger.info(f"CUDA-GDB CGA exception test: {result['cuda-gdb CGA exception test']}")
            
        except Exception as e:
            logger.error(f"Error in cuda-gdb CGA exception test: {str(e)}")
            result["error"] = str(e)
            result["cuda-gdb CGA exception test"] = "failed"
        
        # Generate result summary
        result1 = calculate_result(result)
        dict_output(result, flag=output_flag)
        
        # Write results to JSON
        json_file = os.path.join(log_path, "cuda_gdb_cga_exception.json")
        try:
            dict_to_json(result1, json_file)
            logger.info(f"Results written to {json_file}")
        except Exception as e:
            logger.error(f"Failed to write results to JSON: {str(e)}")
        
        # Return final status
        return PASSED if (result1["failed"] == 0 and result1["passed"] != 0) else FAILED


example_text = """
Example:
python run_cuda_gdb_case.py # run all the function
python run_cuda_gdb_case.py -e vgpu_sanity # run single case
python run_cuda_gdb_case.py -el vgpu_sanity,vgpu_no_root,vgpu_smoke # run multi cases
function_list: 'vgpu_non_graphics_sample', 'vgpu_barrier_memory', 'vgpu_sanity', 'racecheck_analysis', 'vgpu_no_root', 'vgpu_smoke'
"""

reversion = '1.0'
check_list = [
    'cuda_gdb_rcca_3820537',
    'cuda_gdb_rcca_3825550',
    'cuda_gdb_rcca_3773016',
    'cuda_gdb_rcca_3692213',
    'cuda_gdb_rcca_3671381',
    'cuda_gdb_optix',
    'cuda_gdb_rcca_4040416',
    'cuda_gdb_option_4019656',
    'cuda_gdb_sanity', 'cuda_gdb_remote_1820919',
    'test_mps_multiuser_debug',
    'cuda_gdb_coredump_1820921',
    'cuda_gdb_remote_debug_process_1820922',
    'cuda_gdbserver_remote_debug_process_1820923',
    'cuda_gdb_debug_with_graphic_sample_running_1820924',
    'cuda_gdb_threadMigration_1893046',
    'cuda_gdb_performance_2002235',
    'cuda_gdb_attach_application_linked_against_libcuda_2022634',
    'cuda_gdb_coredump_generate_for_certain_kernel_2082584',
    'cuda_gdb_support_watchpoints_on_memory_locations_2211802',
    'cuda_gdb_ptype_option_2508682',
    'cuda_gdb_ptype_option_2508682','cuda_gdb_variable_should_reflect_proper_block_count_2623454',
    'run_user_triggered_coredump_test_2638978',
    'cuda_gdb_sanity_with_cuda_app_sample_running_2641840',
    'cuda_gdb_python_usage_2679726',
    'cuda_gdb_scheduler_locking_2714136',
    'cuda_gdb_good_backtrace_with_extended_debug_line_info_2755196',
    'cuda_gdb_check_register_info_2772109',
    'cuda_gdb_perf_check_2788750',
    'cuda_gdb_hit_template_function_breakpoint_2793993',
    'cuda_gdb_hit_assert_simpleAssert_2960952',
    'cuda_gdb_UD_backend_cgaexception_for_Hopper_3051671'
]

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description=None, epilog=example_text, formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument("-c", "--config-file", dest="config_file", required=False, help='Specify test index file. e.g. cases')
    parser.add_argument("-e", "--excute", action='append', required=False, choices=check_list, help="Specify function to be run.")
    parser.add_argument("-el", "--excute_list", action='store', required=False, help="Specify multi function to be run.")
    args = parser.parse_args()
    case_str = args.excute_list
    case_list_single = args.excute
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            print(getattr(mod, case))
            getattr(mod, case)()
    elif case_list_single:
        mod = sys.modules["__main__"]
        ret = getattr(mod, case_list_single[0])()
        print(f"---ret is {ret}")
        sys.exit(ret)
    else:
        logger.info('please give correct case to run')


