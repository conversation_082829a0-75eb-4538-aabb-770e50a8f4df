import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print("python3 %s -h|-s <spawn command>" % script_name)
    print(
        """
    -h help
    -s spawn command
    """
    )
    exit()


if __name__ == "__main__":
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ["help", "spawn="])
        for name, value in options:
            if name in ("-h", "--help"):
                usage()
            elif name in ("-s", "--spawn"):
                spawn_command = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("break test_tailcall", "Breakpoint 1 at ")
    c.execute_and_compare("r", "Breakpoint 1")
    c.execute_and_compare("n", r"65\s+output")
    c.execute_and_compare("info cuda devices", r"\*\s+\d+")
    c.execute_and_compare("d", "Delete all breakpoints", r"\(y or n\)")
    c.execute_and_compare("y")
    c.execute_and_compare("b 31", "Breakpoint 2 at")
    c.execute_and_compare("c", r"31\s+input")
    c.execute_and_compare("bt", r"#\d+\s+")
    c.execute_and_compare("d", "Delete all breakpoints", r"\(y or n\)")
    c.execute_and_compare("y")
    c.execute_and_compare("b 75", "Breakpoint 3 at ")
    c.execute_and_compare("c", r"75\s+input")
    c.execute_and_compare("bt", r"#\d+\s+")
    c.execute_and_compare("d", "Delete all breakpoints", r"\(y or n\)")
    c.execute_and_compare("y")
    c.execute_and_compare("b 134", "Breakpoint 4 at")
    c.execute_and_compare("bt", r"#\d+\s+")
    c.execute_and_compare("d", "Delete all breakpoints", r"\(y or n\)")
    c.execute_and_compare("y")
    c.execute_and_compare("c", "exited normally")
    c.execute_and_compare("q")
