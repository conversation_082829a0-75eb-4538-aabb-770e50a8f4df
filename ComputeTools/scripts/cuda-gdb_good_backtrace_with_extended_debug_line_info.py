import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print("python3 %s -h|-s <spawn command>" % script_name)
    print(
        """
    -h help
    -s spawn command
    """
    )
    exit()


if __name__ == "__main__":
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:g:", ["help", "spawn=", "gpu="])
        for name, value in options:
            if name in ("-h", "--help"):
                usage()
            elif name in ("-s", "--spawn"):
                spawn_command = value
            elif name in ("-g", "--gpu"):
                gpu_family = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    if gpu_family == "blackwell":
        c.execute_and_compare("set pagination off")
        c.execute_and_compare("r", "CUDA Exception: Warp Misaligned Address")
        c.execute_and_compare("bt")
        regex_compare(c.result, "#0.*function_fault_noinline")
        regex_compare(c.result, "#1.*function_recursive_noinline")
        regex_compare(c.result, "#2.*function_recursive_noinline")
        regex_compare(c.result, "#3.*function_recursive_noinline")
        regex_compare(c.result, "#4.*function_recursive_noinline")
        regex_compare(c.result, "#5.*function_recursive_noinline")
        regex_compare(c.result, "#6.*function_recursive_maybe_inline")
        regex_compare(c.result, "#7.*function_recursive_maybe_inline")
        regex_compare(c.result, "#8.*function_recursive_maybe_inline")
        regex_compare(c.result, "#9.*function_recursive_maybe_inline")
        regex_compare(c.result, "#10.*function_recursive_maybe_inline")
        regex_compare(c.result, "#11.*device_kernel0")
    else:
        c.execute_and_compare("set pagination off")
        c.execute_and_compare("r", "CUDA Exception: Warp Misaligned Address")
        c.execute_and_compare("bt")
        regex_compare(c.result, "#0.*function_fault_noinline")
        regex_compare(c.result, "#1.*function_recursive_noinline")
        regex_compare(c.result, "#2.*function_maybe_inline")
        regex_compare(c.result, "#3.*function_recursive_noinline")
        regex_compare(c.result, "#4.*function_recursive_noinline")
        regex_compare(c.result, "#5.*function_maybe_inline")
        regex_compare(c.result, "#6.*function_recursive_noinline")
        regex_compare(c.result, "#7.*function_recursive_noinline")
        regex_compare(c.result, "#8.*function_recursive_maybe_inline")
        regex_compare(c.result, "#9.*function_recursive_maybe_inline")
        regex_compare(c.result, "#10.*function_recursive_maybe_inline")
        regex_compare(c.result, "#11.*function_recursive_maybe_inline")
        regex_compare(c.result, "#12.*function_recursive_maybe_inline")
        regex_compare(c.result, "#13.*device_kernel0")
