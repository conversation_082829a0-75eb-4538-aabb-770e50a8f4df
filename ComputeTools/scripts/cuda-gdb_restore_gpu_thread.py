#!/usr/bin/env python
# -*- coding: utf-8 -*-

import getopt
import os
import re
import sys


def usage():
    print("""
Usage: python cuda-gdb_restore_gpu_thread.py [options]

Options:
  -h, --help            Show this help message and exit
  -s SPAWN, --spawn=SPAWN
                        Spawn command for CUDA-GDB
  -v VERSION, --cuda_version=VERSION
                        CUDA version (e.g., 13.0)
""")
    sys.exit(0)


class Gdb(object):
    def __init__(self, spawn_command):
        if not spawn_command:
            self.spawn_command = "cuda-gdb ./matrixMul"
        else:
            self.spawn_command = spawn_command
        self.result = ""

    def execute_and_compare(self, cmd, *exprs):
        """Execute command and compare result with expected regex patterns."""
        import pexpect

        if getattr(self, 'child', None) is None:
            self.child = pexpect.spawn(self.spawn_command)
            self.child.expect([r"NVIDIA \(R\) CUDA Debugger", pexpect.TIMEOUT], timeout=10)

        self.child.sendline(cmd)
        
        # Different patterns to expect
        if len(exprs) == 0:
            self.child.expect([r"\(cuda-gdb\)", pexpect.TIMEOUT], timeout=10)
        else:
            for expr in exprs:
                try:
                    self.child.expect([expr, pexpect.TIMEOUT], timeout=30)
                except:
                    print(f"Did not match expected pattern: {expr}")
                    print(f"Output: {self.child.before.decode()}")
                    return False
        
        # Store output for verification
        self.result = self.child.before.decode() + self.child.after.decode()
        
        # Check for thread restoration message
        if cmd == "cuda kernel 0 block 0,0,0 thread 9,0,0":
            thread_pattern = r"Switching focus to CUDA kernel 0.* block \(0,0,0\), thread \(9,0,0\)"
            if re.search(thread_pattern, self.result, re.IGNORECASE):
                print("PASS: Successfully restored to specified GPU thread")
                print(f"Found: {re.search(thread_pattern, self.result).group(0)}")
                return True
            else:
                print("FAIL: Could not restore to specified GPU thread")
                print(f"Output: {self.result}")
                return False
                
        return True


if __name__ == '__main__':
    # Get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:v:", ['help', 'spawn=', 'cuda_version='])
        spawn_command = None
        cuda_version = None
        
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            elif name in ('-v', '--cuda_version'):
                cuda_version = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    # Initialize GDB object
    c = Gdb(spawn_command)
    
    # Testing steps
    print("Starting CUDA-GDB test for restoring to specific GPU thread...")
    
    # Set breakpoint and run to kernel launch
    c.execute_and_compare("set cuda break_on_launch application")
    c.execute_and_compare("run", r"Switching focus to CUDA kernel")
    
    # Test specific thread restoration
    thread_restoration_result = c.execute_and_compare("cuda kernel 0 block 0,0,0 thread 9,0,0", 
                                                    r"Switching focus to CUDA kernel 0.* block \(0,0,0\), thread \(9,0,0\)")
    
    # Verify thread info
    c.execute_and_compare("info cuda threads", r"Kernel.*Block.*Thread")
    
    # Check if thread info output contains our specific thread
    thread_pattern = r"0.*\(0,0,0\).*\(9,0,0\)"
    if re.search(thread_pattern, c.result, re.IGNORECASE):
        print("PASS: Thread information verification successful")
        print(f"Found: {re.search(thread_pattern, c.result).group(0)}")
    else:
        print("FAIL: Thread information verification failed")
        print(f"Output: {c.result}")
        thread_restoration_result = False
    
    # Exit cuda-gdb
    c.execute_and_compare("quit")
    
    # Print final results
    if thread_restoration_result:
        print("\n&&&& PASS\n")
    else:
        print("\n&&&& FAIL\n")
    
    sys.exit(0 if thread_restoration_result else 1) 