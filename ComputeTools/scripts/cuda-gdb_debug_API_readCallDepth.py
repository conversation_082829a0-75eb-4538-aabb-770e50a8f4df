import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ['help', 'spawn='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("break NopKernel22499")
    c.execute_and_compare("r", "22500\s+__device__.*int.*NopKernel22499", timeout=45 * 60)
    c.execute_and_compare("bt", "#0\s+NopKernel22499([\w\W])*#1")
    c.execute_and_compare("break NopKernel", "2 locations")
    c.execute_and_compare("info break", "NopKernel22499.*NopKernel")
