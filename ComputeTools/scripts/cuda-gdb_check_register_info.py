import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:v:g:", ['help', 'spawn=', 'cuda_version=', 'gpu_family='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            elif name in ('-v', '--cuda_version'):
                cuda_version = value
            elif name in ('-g', '--gpu_family'):
                gpu_family = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("set cuda break_on_launch app")
    if cuda_version >= '11.6':
        c.execute_and_compare("r", r"\d+\s+.*int.*\s+bx\s+")
    else:
        c.execute_and_compare("r", r"44\s+.*int.*\s+bx\s+")
    if gpu_family not in ['maxwell', 'pascal', 'volta']:
        if cuda_version >= '13.0':
            register_name = "UPT"
        else:
            register_name = "UP7"
        c.execute_and_compare("info register $%s" % register_name, r"%s\s+0x1\s+1" % register_name)
    c.execute_and_compare("info register system", r"PT\s+0x1\s+1" if cuda_version >= '13.0' else r"P7\s+0x1\s+1")
