# -*- encoding: utf-8 -*-
import yaml
from multiprocessing import Pool, Manager, Process

try:
    # Python3
    import copyreg
except ImportError:
    # Python2
    import copy_reg
# from multiprocessing.pool import ThreadPool as Pool
from datetime import datetime
from common_utils import *
import time
import logging
import argparse
from yaml_mgr import YamlManger
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser

optix_coverage_yaml = '{}/../yaml/optix_coverage.yaml'.format(os.getcwd())
yaml_mgr = YamlManger(optix_coverage_yaml)
case_config = yaml_mgr.load()

optix_sample = case_config['global']['env']['SAMPLE_OPTIX']
tools_home = case_config['global']['env']['TOOLS_HOME']
sample11 = case_config['global']['env']['SAMPLE']
platform = case_config['global']['env']['PLATFORM'].lower()
# base_download_url = case_config['global']['env']['CUFFT_BASE_URL']
output_flag = case_config['global']['env']['OUTPUT_FLAG']
cupti_download_to_path = case_config['global']['env']['CUPTI_DOWNLOAD_PATH']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
date1 = case_config['global']['env']['DATE1']
cuda_path = case_config['global']['env']['CUDA_PATH']
cuda_bin = '{}/bin'.format(cuda_path)
cuda_major = case_config['global']['env']['CUDA_MAJOR']
sample_path = case_config['global']['env']['SAMPLE_PATH']
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
if cuda_short_version < '11.6':
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH1']
elif cuda_short_version < '13.0':
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH']
else:
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH2']
if platform == 'x86':
    cupti_extract_path = case_config['global']['env']['CUPTI_EXTRACT_PATH']
else:
    cupti_extract_path = 'linux-desktop-t210-a64'
sample_cupti_path = sample_bin_path + '/../../target/{}'.format(cupti_extract_path)
# LD_LIBRARY_PATH = '%s:$LD_LIBRARY_PATH' % optix_lib_path
LD_LIBRARY_PATH = '$LD_LIBRARY_PATH'
mkdir(tools_home)
host_password = case_config['global']['env']['HOST_PASSWORD']
password1 = case_config['global']['env']['CQA_PASSWORD']
output_flag = case_config['global']['env']['OUTPUT_FLAG']
user1 = case_config['global']['env']['CQA_USER']
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
platform = case_config['global']['env']['PLATFORM']
if optix_sample == '1':
    sample_list = [
        'optixOpticalFlow',
        'optixCompileWithTasks',
        'optixDenoiser',
        'optixRaycasting',
        'optixHair',
        'optixVolumeViewer',
        'optixSphere',
        'optixCurves',
        'optixHello',
        'optixMotionGeometry',
        'optixPathTracer',
        'optixTriangle',
        'optixBoundValues',
        'optixCutouts',
        'optixDynamicGeometry',
        'optixMeshViewer',
        'optixCallablePrograms',
        'optixDynamicMaterials',
        'optixNVLink',
        'optixSimpleMotionBlur',
        'optixWhitted',
        'optixModuleCreateAbort',
        'optixMultiGPU',
        'optixCustomPrimitive',
        'optixOpacityMicromap',
        'optixConsole',
        'optixRibbons'
    ]
    """
    optixMultiGPU
    optixCustomPrimitive
    optixOpacityMicromap
    optixConsole
    """
    run_sample_list = [
        'optixOpticalFlow -o flow.exr soane-Beauty-001.exr soane-Beauty-002.exr',
        'optixCompileWithTasks synthetic.ptx',
        'optixDenoiser color.exr',
        'optixRaycasting',
        'optixHair',
        'optixVolumeViewer',
        'optixSphere',
        'optixCurves',
        'optixHello',
        'optixMotionGeometry',
        'optixPathTracer',
        'optixTriangle',
        'optixBoundValues',
        'optixCutouts',
        'optixDynamicGeometry',
        'optixMeshViewer',
        'optixCallablePrograms',
        'optixDynamicMaterials',
        'optixNVLink',
        'optixSimpleMotionBlur',
        'optixWhitted',
        'optixModuleCreateAbort',
        'optixMultiGPU',
        'optixCustomPrimitive',
        'optixOpacityMicromap',
        'optixConsole',
        'optixRibbons'
    ]
    sample_dict = dict(zip(run_sample_list, sample_list))
    print(sample_dict)
    not_run_list = ['optixModuleCreateProcess']
    no_graphic_sample = ['optixRaycasting', 'optixDemandLoadSimple']
    # sample_list = ['optixHair', 'optixVolumeViewer', 'optixSphere', 'optixCurves', 'optixHello']
    optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
    optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
else:
    sample_list = [
        'bicubicTexture',
        'bilateralFilter',
        'simpleVulkan',
        'bindlessTexture',
        'boxFilter',
        'fluidsGL',
        'FunctionPointers',
        'imageDenoising',
        'simpleVulkanMMAP',
        'Mandelbrot',
        'marchingCubes',
        'nbody',
        'oceanFFT',
        'particles',
        'postProcessGL',
        'randomFog',
        'recursiveGaussian',
        'simpleCUDA2GL',
        'simpleGL',
        'simpleTexture3D',
        'smokeParticles',
        'SobelFilter',
        'volumeFiltering',
        'volumeRender',
        'vulkanImageCUDA'
    ]
    optix_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
    sample_dict = {
        'bicubicTexture': 'CUDA BicubicTexture',
        'bilateralFilter': 'CUDA Bilateral Filter',
        'simpleVulkan': 'vulkanCudaSineWave',
        'bindlessTexture': 'bindlessTexture',
        'boxFilter': 'CUDA Rolling Box Filter',
        'fluidsGL': 'Cuda/GL Stable Fluids',
        'FunctionPointers': 'FunctionPointers',
        'imageDenoising': 'Passthrough',
        'simpleVulkanMMAP': 'simpleVulkanMMAP',
        'Mandelbrot': 'Mandelbrot',
        'marchingCubes': 'CUDA Marching Cubes',
        'nbody': 'CUDA N-Body',
        'oceanFFT': 'CUDA FFT Ocean Simulation',
        'particles': 'CUDA Particles',
        'postProcessGL': 'CUDA GL Post Processing',
        'randomFog': 'Random Fog',
        'recursiveGaussian': 'CUDA Recursive Gaussian',
        'simpleCUDA2GL': 'CUDA GL Post Processing',
        'simpleGL': 'Cuda GL Interop',
        'simpleTexture3D': 'simpleTexture3D',
        'smokeParticles': 'CUDA Smoke Particles',
        'SobelFilter': 'CUDA Edge Detection',
        'volumeFiltering': 'Volume Filtering',
        'volumeRender': 'Volume Render',
        'vulkanImageCUDA': 'Vulkan Image CUDA Box Filter'
    }
    '''
    sample_dict = {sample_path + '/Samples' + '/5_Domain_Specific/bicubicTexture/bicubicTexture': 'CUDA BicubicTexture',
                    sample_path + '/Samples' + '/5_Domain_Specific/bilateralFilter/bilateralFilter': 'CUDA Bilateral Filter',
                    sample_path + '/Samples' + '/5_Domain_Specific/simpleVulkan/simpleVulkan': 'vulkanCudaSineWave',
                    sample_path + '/Samples' + '/3_CUDA_Features/bindlessTexture/bindlessTexture': 'bindlessTexture',
                    sample_path + '/Samples' + '/2_Concepts_and_Techniques/boxFilter/boxFilter': 'CUDA Rolling Box Filter',
                    sample_path + '/Samples' + '/5_Domain_Specific/fluidsGL/fluidsGL': 'Cuda/GL Stable Fluids',
                    sample_path + '/Samples' + '/2_Concepts_and_Techniques/FunctionPointers/FunctionPointers': 'FunctionPointers',
                    sample_path + '/Samples' + '/2_Concepts_and_Techniques/imageDenoising/imageDenoising': 'Passthrough',
                    sample_path + '/Samples' + '/5_Domain_Specific/simpleVulkanMMAP/simpleVulkanMMAP': 'simpleVulkanMMAP',
                    sample_path + '/Samples' + '/5_Domain_Specific/Mandelbrot/Mandelbrot': 'Mandelbrot',
                    sample_path + '/Samples' + '/5_Domain_Specific/marchingCubes/marchingCubes': 'CUDA Marching Cubes',
                    sample_path + '/Samples' + '/5_Domain_Specific/nbody/nbody': 'CUDA N-Body',
                    sample_path + '/Samples' + '/4_CUDA_Libraries/oceanFFT/oceanFFT': 'CUDA FFT Ocean Simulation',
                    sample_path + '/Samples' + '/2_Concepts_and_Techniques/particles/particles': 'CUDA Particles',
                    sample_path + '/Samples' + '/5_Domain_Specific/postProcessGL/postProcessGL': 'CUDA GL Post Processing',
                    sample_path + '/Samples' + '/4_CUDA_Libraries/randomFog/randomFog': 'Random Fog',
                    sample_path + '/Samples' + '/5_Domain_Specific/recursiveGaussian/recursiveGaussian': 'CUDA Recursive Gaussian',
                    sample_path + '/Samples' + '/0_Introduction/simpleCUDA2GL/simpleCUDA2GL': 'CUDA GL Post Processing',
                    sample_path + '/Samples' + '/5_Domain_Specific/simpleGL/simpleGL': 'Cuda GL Interop',
                    sample_path + '/Samples' + '/0_Introduction/simpleTexture3D/simpleTexture3D': 'simpleTexture3D',
                    sample_path + '/Samples' + '/5_Domain_Specific/smokeParticles/smokeParticles': 'CUDA Smoke Particles',
                    sample_path + '/Samples' + '/5_Domain_Specific/SobelFilter/SobelFilter': 'CUDA Edge Detection',
                    sample_path + '/Samples' + '/5_Domain_Specific/volumeFiltering/volumeFiltering': 'Volume Filtering',
                    sample_path + '/Samples' + '/5_Domain_Specific/volumeRender/volumeRender': 'Volume Render',
                    sample_path + '/Samples' + '/5_Domain_Specific/vulkanImageCUDA/vulkanImageCUDA': 'Vulkan Image CUDA Box Filter'
                    }
    '''
logger = logging.getLogger()
logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(filename)s:%(lineno)d - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('graphic_case.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)

if platform.lower() == 'x86':
    cupti_path = case_config['global']['env']['CUPTI_PATH1']
elif platform.lower() == 'arm':
    cupti_path = case_config['global']['env']['CUPTI_PATH2']
else:
    logger.info('please give the supported platform to run optix case')
    exit()
download_to_path = cupti_path
install_cmd = 'echo %s | sudo -S apt install wmctrl -y' % host_password
run_loc_cmd(install_cmd)


def get_sm():
    sm_cmd = case_config['PREPARE1']['CMD3'] % sample_1_path
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        sm = sm_out['output']
        print(sm_out['output'])
        print('=++++=======================')
        print('---*** sm is {} ***---'.format(sm.split('\n')[-1].strip(' ')))
        print('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        if len(sm.split('\n')[-1].strip(' ')) >= 3:
            return float(sm.split('\n')[-1].strip(' '))
        else:
            logger.info('we get the sm failed， please check deviceQuery sample')
            exit()
    else:
        logger.info('we get the sm failed， please check deviceQuery sample')
        exit()


def prepare():
    logger.info('create optix folder, then download the needed file')
    optix_path = case_config['global']['env']['OPTIX_PATH']
    mkdir(optix_path)
    mkdir(optix_bin_path)
    prepare_cmd1 = case_config['PREPARE']['CMD1'] % (optix_bin_path, user, password, base_url)
    if platform == 'x86':
        file_name = 'NVIDIA-OptiX-SDK-7.4.0-linux64-x86_64.sh'
    elif platform == 'arm':
        file_name = 'NVIDIA-OptiX-SDK-7.4.0-linux64-x86_64.sh'
    else:
        logger.info('please give correct platform')
        exit(1)
    prepare_cmd2 = case_config['PREPARE']['CMD2'] % (optix_path, user, password, base_url, file_name)
    if os.path.isfile('%s/color.exr' % optix_bin_path):
        logger.info('no need download the color.exr file')
    else:
        out = run_loc_cmd(prepare_cmd1)
        if out.succeeded:
            logger.info('run the cmd %s successful' % prepare_cmd1)
        else:
            logger.info('run the cmd %s failed' % prepare_cmd1)
    if os.path.isfile('%s/%s' % (optix_path, file_name)):
        logger.info('no need download the sh file ')
    else:
        out1 = run_loc_cmd(prepare_cmd2)
        if out1.succeeded:
            logger.info('run the cmd %s successful' % prepare_cmd2)
        else:
            logger.info('run the cmd %s failed' % prepare_cmd2)
    prepare_cmd3 = case_config['PREPARE']['CMD3'] % (optix_bin_path, user, password, base_url)
    if os.path.isfile('%s/synthetic.ptx' % optix_path):
        logger.info('no need download the "synthetic.ptx" file ')
    else:
        out3 = run_loc_cmd(prepare_cmd3)
        if out3.succeeded:
            logger.info('run the cmd %s successful' % prepare_cmd3)
        else:
            logger.info('run the cmd %s failed' % prepare_cmd3)
    file_list = ['soane-Beauty-001.exr', 'soane-Beauty-002.exr', 'flow.exr']
    for file_name1 in file_list:
        prepare_cmd4 = case_config['PREPARE']['CMD4'] % (optix_bin_path, user, password, base_url, file_name1)
        if os.path.isfile('%s/%s' % (optix_path, file_name1)):
            logger.info('no need download the file-- %s ' % file_name1)
        else:
            out2 = run_loc_cmd(prepare_cmd4)
            if out2.succeeded:
                logger.info('run the cmd %s successful' % prepare_cmd4)
            else:
                logger.info('run the cmd %s failed' % prepare_cmd4)


def change_yaml(optix_sample1=None, optix_path1=None):
    driver_branch_cmd = "nvidia-smi|grep 'Driver Version'|awk '{print $6}'|awk -F '.' '{print $1}'"
    driver_out = run_loc_cmd(driver_branch_cmd)
    driver_branch = driver_out['output']
    cuda_major_cmd = "%s/cuda-gdb --version|grep 'release'|awk '{print $1}'|awk -F '.' '{print $1}'" % cuda_bin
    major_out = run_loc_cmd(cuda_major_cmd)
    cuda_major = major_out['output']
    cuda_min_cmd = "%s/cuda-gdb --version|grep 'release'|awk '{print $1}'|awk -F '.' '{print $2}'" % cuda_bin
    min_out = run_loc_cmd(cuda_min_cmd)
    cuda_min = min_out['output']
    platform_cmd = 'uname -a'
    platform_out = run_loc_cmd(platform_cmd)
    if 'x86_64' in platform_out['output']:
        platform1 = 'x86'
    elif 'aarch64' in platform_out['output']:
        platform1 = 'arm'
    date1 = time.strftime("%Y%m%d", time.localtime())
    date2 = time.strftime("%Y%m%d%H%M", time.localtime())
    cuda_short_version1 = cuda_major + '.' + cuda_min
    dict_dvs_build = {'11.5': 'F', '11.6': 'G', '11.7': 'H', '11.4': 'E'}
    dvs_build = dict_dvs_build[cuda_short_version1]
    if optix_sample1 is not None and optix_path1 is not None:
        change_list = [
            'CUDA_MAJOR:',
            'CUDA_MINOR:',
            'DRV_BRANCH:',
            'DVS_BUILD:',
            'PLATFORM:',
            'OPTIX_SAMPLE:',
            'DATE:',
            'DATE1:',
            'OPTIX_PATH:'
        ]
        update_content = [
            'CUDA_MAJOR: %s' % cuda_major,
            'CUDA_MINOR: %s' % cuda_min,
            'DRV_BRANCH: r%s' % driver_branch,
            'DVS_BUILD: %s' % dvs_build,
            'PLATFORM: %s' % platform1,
            'OPTIX_SAMPLE: %s' % optix_sample1,
            'DATE: %s' % date2,
            'DATE1: %s' % date1,
            'OPTIX_PATH: %s' % optix_path1
        ]
    elif optix_path1 is not None:
        change_list = ['CUDA_MAJOR:', 'CUDA_MINOR:', 'DRV_BRANCH:', 'DVS_BUILD:', 'PLATFORM:', 'DATE:', 'DATE1:', 'OPTIX_PATH:']
        update_content = [
            'CUDA_MAJOR: %s' % cuda_major,
            'CUDA_MINOR: %s' % cuda_min,
            'DRV_BRANCH: r%s' % driver_branch,
            'DVS_BUILD: %s' % dvs_build,
            'PLATFORM: %s' % platform1,
            'DATE: %s' % date2,
            'DATE1: %s' % date1,
            'OPTIX_PATH: %s' % optix_path1
        ]
    elif optix_sample1 is not None:
        change_list = ['CUDA_MAJOR:', 'CUDA_MINOR:', 'DRV_BRANCH:', 'DVS_BUILD:', 'PLATFORM:', 'OPTIX_SAMPLE:', 'DATE:', 'DATE1:']
        update_content = [
            'CUDA_MAJOR: %s' % cuda_major,
            'CUDA_MINOR: %s' % cuda_min,
            'DRV_BRANCH: r%s' % driver_branch,
            'DVS_BUILD: %s' % dvs_build,
            'PLATFORM: %s' % platform1,
            'OPTIX_SAMPLE: %s' % optix_sample1,
            'DATE: %s' % date2,
            'DATE1: %s' % date1
        ]
    else:
        change_list = ['CUDA_MAJOR:', 'CUDA_MINOR:', 'DRV_BRANCH:', 'DVS_BUILD:', 'PLATFORM:', 'DATE:', 'DATE1:']
        update_content = [
            'CUDA_MAJOR: %s' % cuda_major,
            'CUDA_MINOR: %s' % cuda_min,
            'DRV_BRANCH: r%s' % driver_branch,
            'DVS_BUILD: %s' % dvs_build,
            'PLATFORM: %s' % platform1,
            'DATE: %s' % date2,
            'DATE1: %s' % date1
        ]
    change_content = []
    for i in change_list:
        cmd = 'grep %s %s/../yaml/optix_coverage.yaml' % (i, os.getcwd())
        out = run_loc_cmd(cmd)
        change_content.append(out['output'].strip('\n').strip('\r'))
    print(change_content)
    dict_change = dict(zip(change_content, update_content))
    for key, value in dict_change.items():
        change_cmd = "sed -i 's#%s#%s#g' %s/../yaml/optix_coverage.yaml" % (key, value, os.getcwd())
        run_loc_cmd(change_cmd)
    updated_content = []
    for i in change_list:
        cmd = 'grep %s %s/../yaml/optix_coverage.yaml' % (i, os.getcwd())
        out = run_loc_cmd(cmd)
        updated_content.append(out['output'].strip('\n').strip('\r'))
    print(updated_content)


def get_sample_path(sample_list):
    sample_list_path = []
    for sample in sample_list:
        cmd = 'cd {}; find . -name {}'.format(sample_path, sample)
        out = run_test_cmd(cmd)
        sample_list_path.append(sample_path + out.output.split('\n')[0].strip('.'))
    print(sample_list_path)
    return sample_list_path


def run_optix(cmd, log_name, result, tools):
    if tools == 'trace':
        check_result(cmd, 'run_cmd by %s' % cmd, log_name, result)
    elif tools == 'profile':
        if 'cbl' in cmd and 'application' in cmd:
            check_result(cmd,
                         'run_cmd by %s' % cmd,
                         log_name,
                         result,
                         'CBL Profiling is not supported with Application Replay mode',
                         flag=7)
        else:
            check_result(cmd,
                         'run_cmd by %s' % cmd,
                         log_name,
                         result,
                         'FAILED',
                         'nan',
                         'no kernels',
                         flag=6)
    else:
        check_result(cmd,
                     'run_cmd by %s' % cmd,
                     log_name,
                     result,
                     'FAILED',
                     'Error',
                     'Failed',
                     'failed',
                     'nan',
                     'no kernels',
                     flag=2)


def close_sample(sample, result):
    cmd = "sleep 30; ps -ef|egrep %s|awk '{print $2}'" % sample
    out = run_loc_cmd(cmd)
    print(out)
    if out.succeeded and len(out['output'].split('\n')) != 1:
        cmd1 = "pkill -9 %s" % sample
    else:
        cmd1 = 'ls'
    print(result)
    run_loc_cmd(cmd1)
    logger.info('we had run the command "%s"' % cmd1)


def multi_process(cmd_list, log_name, result, tools, sample):
    """
    Based on multiprocessing Pool,  result recorded as {cmd:passed/failed}
    """
    manager = Manager()
    mp_result = manager.dict()
    pool = Pool(3)

    try:
        logging.debug('start run %d' % os.getpid())
        pool.apply_async(run_optix, args=(cmd_list[0], log_name, mp_result, tools))
        pool.apply_async(run_optix, args=(cmd_list[1], log_name, mp_result, tools))
        pool.apply_async(close_sample, args=(sample, mp_result))
        pool.close()
        pool.join()
        for dt in mp_result:
            if 'wmctrl' not in dt:
                result.update({dt: mp_result[dt]})
    except:
        logging.debug('Process terminating')
        pool.terminate()
        pool.join()
        time.sleep(1)
        raise


def check_output(file_name, graphic_sample, result, tools_option, *args, **kwargs):
    passed, failed = 0, 0
    if tools_option != 'trace':
        output = ''
        with open(file_name, 'r+') as f1:
            for line1 in f1.readlines():
                output += line1
        print('**************************************************')
        print(output)
        print('$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$')
        if kwargs:
            for key, value in kwargs.items():
                if value == 1:
                    for arg in args:
                        # if re.search(arg, output, re.IGNORECASE) is not None:
                        if arg in output:
                            passed += 1
                            logger.info('yes, "%s" is in output' % arg)
                        else:
                            failed += 1
                            logger.error('no, "%s" should in output' % arg)
                elif value == 2:
                    for arg in args:
                        # if re.search(arg, output, re.IGNORECASE) is None:
                        if arg not in output:
                            passed += 1
                            logger.info('yes, "%s" is not in output' % arg)
                        else:
                            failed += 1
                            logger.error('no, "%s" is in output' % arg)
                else:
                    continue
        else:
            if args:
                for arg in args:
                    if arg in output:
                        passed += 1
                        logger.info('yes, "%s" is in output' % arg)
                    else:
                        failed += 1
                        logger.error('no, "%s" should not in output' % arg)
            else:
                logger.info('please give checkpoint to check in the output')
        if failed != 0:
            result['check_%s_%s_output' % (graphic_sample, tools_option)] = 'failed'
            logger.error('we check the checkpoints failed in cmd_output')
        else:
            result['check_%s_%s_output' % (graphic_sample, tools_option)] = 'passed'
            logger.info('we check the checkpoints successful in cmd_output')
    else:
        cmd = "grep FAILED %s |awk '{print $1}'" % file_name
        out = run_loc_cmd(cmd)
        print('tracing result start check')
        print(out)
        print('tracing result stop check')
        if out['output'].strip('\n').strip('\r') == '0':
            result['check_%s_trace_output' % graphic_sample] = 'passed'
            logger.info('we check the trace result successful in cmd_output')
        else:
            result['check_%s_trace_output' % graphic_sample] = 'failed'
            logger.error('we check the trace result failed in cmd_output')


def sanitizer_optix_case(tools_option):
    result = {}
    passed, failed = 0, 0
    log_name = case_config['SANITIZER_OPTIX']['LOG_NAME'] % (tools_option, tools_home)
    log_path = case_config['global']['env']['SANITIZER_OPTIX_LOG_PATH'] % tools_option
    mkdir(log_path)
    if tools_option == 'racecheck':
        check_point = case_config['SANITIZER_OPTIX']['CHECK_POINT1']
    else:
        check_point = case_config['SANITIZER_OPTIX']['CHECK_POINT']
    check_point2 = case_config['SANITIZER_OPTIX']['CHECK_POINT2']
    os.chdir(optix_bin_path)
    for sample, value in sample_dict.items():
        if tools_option == 'memcheck':
            if sample == 'simpleVulkan':
                continue
            else:
                if optix_sample == '1':
                    if cuda_short_version < '12.3':
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix-leaks yes --leak-check full ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                    else:
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix-leaks --leak-check full ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --leak-check full ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
        elif tools_option == 'initcheck':
            if optix_sample == '1':
                if '12.3' > cuda_short_version > '12.1':
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix yes ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                elif cuda_short_version >= '12.3':
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            else:
                sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s ./%s | tee %s_%s.txt' % (
                    LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
        elif tools_option == 'racecheck':
            if sample == 'nbody':
                sanitizer_cmd = 'export LD_LIBRARY_PATH=%s; %s/bin/compute-sanitizer --tool %s -c 50 ./%s  -numbodies=20480 | tee %s_%s.txt' % (
                    LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            else:
                sanitizer_cmd = 'export LD_LIBRARY_PATH=%s; %s/bin/compute-sanitizer --tool %s -c 50 ./%s | tee %s_%s.txt' % (
                    LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
        else:
            if sample in ['fluidsGL', 'simpleVulkanMMAP', 'volumeFiltering', 'oceanFFT']:
                if cuda_short_version < '12.3':
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --force-blocking-launches yes --tool %s ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --force-blocking-launches --tool %s ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            else:
                sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --tool %s ./%s | tee %s_%s.txt' % (
                    LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
        if optix_sample == '1':
            if sample in [
                    'optixRaycasting', 'optixDemandLoadSimple', 'optixDenoiser', 'optixOpticalFlow', 'optixCompileWithTasks'
            ]:
                window_cmd = "sleep 30s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
            # windows_cmd1 = "sleep 20s; pkill -9 %s" % sample
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
        run_cmd_list = [sanitizer_cmd, window_cmd]
        multi_process(run_cmd_list, log_name, result, 'sanitizer', sample.split(' ')[0])
        if tools_option == 'memcheck':
            check_output('%s/%s_%s.txt' % (optix_bin_path, tools_option, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'sanitizer',
                         check_point,
                         check_point2,
                         flag=1)
        else:
            check_output('%s/%s_%s.txt' % (optix_bin_path, tools_option, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'sanitizer',
                         check_point,
                         flag=1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/sanitizer_%s_%s.json' % (log_path, tools_option, sample11))
    return result, passed, failed


def cupti_profile_injection_optix():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['LOG_NAME1']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_OPTIX_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
    if platform == 'x86':
        cupti_run_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
    else:
        logger.info('now we do not support the platform---%s' % platform)
    # prepare optix bin
    if optix_sample == '1':
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD'].format(optix_bin_path, cupti_run_path)
    else:
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD_1'].format(
            optix_bin_path, cupti_run_path, cupti_run_path, cupti_run_path, cupti_run_path)
    print(prepare_cmd)
    out = run_loc_cmd(prepare_cmd)
    if out.succeeded:
        logger.info('we prepare bin file successful')
    else:
        logger.info('we prepare bin file fail, exit.......')
        exit(2)
    if optix_sample != '1':
        sample_path_list = get_sample_path(sample_list)
        for path in sample_path_list:
            prepare_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD'] % (path, cupti_run_path)
            run_test_cmd(prepare_cmd1)
        prepare_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD1'] % (cupti_path, sample_bin_path)
        prepare_cmd3 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD2'] % (cupti_path, sample_bin_path)
        run_test_cmd(prepare_cmd2)
        run_test_cmd(prepare_cmd3)
    # run cupti inject optix case
    check_point = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT']
    check_point1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT1']
    check_point2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT2']
    if optix_sample != '1':
        if platform == 'x86':
            cupti_run_path = '%s/../../target/%s' % (sample_bin_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/../../target/linux-desktop-glibc_2_19_0-arm" % sample_bin_path
            else:
                cupti_run_path = "%s/../../target/linux-desktop-t210-a64" % sample_bin_path
        else:
            logger.info('now we do not support the platform---%s' % platform)
    for sample, value in sample_dict.items():
        if cuda_short_version < '12.1':
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
        else:
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_3'] % (
                    cupti_run_path, ' ', sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (
                    sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_1'] % (
                    sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
        if cuda_short_version == '12.1':
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_2'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_2'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
        if optix_sample == '1':
            if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                window_cmd = "sleep 30s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
        cupti_injection_list = [step1_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step1',
                         check_point,
                         check_point1,
                         check_point2,
                         'Error',
                         'nan',
                         'No kernel',
                         flag=2)
        else:
            check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step1',
                         check_point,
                         check_point1,
                         check_point2,
                         'Error',
                         'nan',
                         'No kernel',
                         flag=2)
        cupti_injection_list = [step2_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        else:
            check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        cupti_injection_list = [step3_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step3.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step3', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        else:
            check_output('%s/%s_step3.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step3', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_%s.json' % (log_path, sample11))
    return result, passed, failed


def cupti_profile_injection_optix_old_api():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['LOG_NAME1']
        log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API_LOG_PATH']
        mkdir(log_path)
        prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            cupti_run_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
            else:
                cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
        else:
            logger.info('now we do not support the platform---%s' % platform)
        # prepare optix bin
        if optix_sample == '1':
            prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD'].format(optix_bin_path, cupti_run_path)
        else:
            prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD_1'].format(
                optix_bin_path, cupti_run_path, cupti_run_path, cupti_run_path, cupti_run_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        if optix_sample != '1':
            sample_path_list = get_sample_path(sample_list)
            for path in sample_path_list:
                prepare_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['PREPARE']['CMD'] % (path, cupti_run_path)
                run_test_cmd(prepare_cmd1)
            prepare_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['PREPARE']['CMD1'] % (cupti_path, sample_bin_path)
            prepare_cmd3 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['PREPARE']['CMD2'] % (cupti_path, sample_bin_path)
            run_test_cmd(prepare_cmd2)
            run_test_cmd(prepare_cmd3)
        # run cupti inject optix case
        check_point = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['CHECK_POINT']
        check_point1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['CHECK_POINT1']
        check_point2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['CHECK_POINT2']
        if optix_sample != '1':
            if platform == 'x86':
                cupti_run_path = '%s/../../target/%s' % (sample_bin_path, cupti_extract_path)
            elif platform == 'arm':
                if cuda_short_version < '11.5':
                    cupti_run_path = "%s/../../target/linux-desktop-glibc_2_19_0-arm" % sample_bin_path
                else:
                    cupti_run_path = "%s/../../target/linux-desktop-t210-a64" % sample_bin_path
            else:
                logger.info('now we do not support the platform---%s' % platform)
        for sample, value in sample_dict.items():
            if cuda_short_version < '12.1':
                if optix_sample == '1':
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1'] % (
                        cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                    step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP2']['CMD1'] % (
                        cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                    step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP3']['CMD1'] % (
                        cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                else:
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1'] % (
                        sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                    step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP2']['CMD1'] % (
                        sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                    step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP3']['CMD1'] % (
                        sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                if optix_sample == '1':
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1_1'] % (
                        cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                    step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP2']['CMD1_1'] % (
                        cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                    step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP3']['CMD1_1'] % (
                        cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                else:
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1_3'] % (
                        cupti_run_path, ' ', sample, sample.split(' ')[0])
                    step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP2']['CMD1_1'] % (
                        sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
                    step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP3']['CMD1_1'] % (
                        sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
            if cuda_short_version == '12.1':
                if optix_sample == '1':
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1_2'] % (
                        cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                else:
                    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_OLD_API']['STEP1']['CMD1_2'] % (
                        sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            if optix_sample == '1':
                if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                    window_cmd = "sleep 30s; wmctrl -l"
                else:
                    window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                        value, value, value)
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
            cupti_injection_list = [step1_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            if optix_sample == '1':
                check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]),
                             sample.split(' ')[0],
                             result,
                             'profile_step1',
                             check_point,
                             check_point1,
                             check_point2,
                             'Error',
                             'nan',
                             'No kernel',
                             flag=2)
            else:
                check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]),
                             sample.split(' ')[0],
                             result,
                             'profile_step1',
                             check_point,
                             check_point1,
                             check_point2,
                             'Error',
                             'nan',
                             'No kernel',
                             flag=2)
            cupti_injection_list = [step2_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            if optix_sample == '1':
                check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
            else:
                check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
            cupti_injection_list = [step3_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            if optix_sample == '1':
                check_output('%s/%s_step3.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step3', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
            else:
                check_output('%s/%s_step3.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step3', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_profile_%s_old_api.json' % (log_path, sample11))
        return result, passed, failed
    else:
        logger.info('no need run this case on tesla GPU, Ampere/Hopper/Blackwell')


'''
def cupti_profile_injection_optix1():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['LOG_NAME1']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_OPTIX_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
    if platform == 'x86':
        cupti_run_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
    else:
        logger.info('now we do not support the platform---%s' % platform)
    # prepare optix bin
    prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD'] % (optix_bin_path, cupti_run_path)
    print(prepare_cmd)
    out = run_loc_cmd(prepare_cmd)
    if out.succeeded:
        logger.info('we prepare bin file successful')
    else:
        logger.info('we prepare bin file fail, exit.......')
        exit(2)
    if optix_sample != '1':
        prepare_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD1'] % (cupti_path, optix_bin_path)
        print(prepare_cmd1)
        out1 = run_loc_cmd(prepare_cmd1)
        if out1.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
    # run cupti inject optix case
    check_point = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT']
    check_point1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT1']
    check_point2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT2']
    for sample, value in sample_dict.items():
        if cuda_short_version < '12.1':
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
        else:
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_1'] % (
                    cupti_run_path, sample.split(' ')[0], '--cblProfiling 1', sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_1'] % (
                    sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (
                    sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_1'] % (
                    sample_cupti_path, sample.split(' ')[0], ' ', sample, sample.split(' ')[0])
        if cuda_short_version == '12.1':
            if optix_sample == '1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_2'] % (
                    cupti_run_path, sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_2'] % (
                    sample_cupti_path, sample.split(' ')[0], sample, sample.split(' ')[0])
        if optix_sample == '1':
            if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                window_cmd = "sleep 30s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
        cupti_injection_list = [step1_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step1',
                         check_point,
                         check_point1,
                         check_point2,
                         'Error',
                         'nan',
                         'No kernel',
                         flag=2)
        else:
            check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step1',
                         check_point,
                         check_point1,
                         check_point2,
                         'Error',
                         'nan',
                         'No kernel',
                         flag=2)
        cupti_injection_list = [step2_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step1.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        else:
            check_output('%s/%s_step1.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step2', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        cupti_injection_list = [step3_cmd1, window_cmd]
        multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
        if optix_sample == '1':
            check_output('%s/%s_step3.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step1', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
        else:
            check_output('%s/%s_step3.txt' % (sample_cupti_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'profile_step1', check_point, check_point1, check_point2, 'Error', 'nan', flag=2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_%s.json' % (log_path, sample11))
    return result, passed, failed
'''


def cupti_profile_injection_optix_basic():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['LOG_NAME1']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_OPTIX_BASIC_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
    if platform == 'x86':
        cupti_run_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
    else:
        logger.info('now we do not support the platform---%s' % platform)
    # prepare optix bin
    if optix_sample == '1':
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD'].format(optix_bin_path, cupti_run_path)
    else:
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD_1'].format(
            optix_bin_path, cupti_run_path, cupti_run_path, cupti_run_path, cupti_run_path)
    print(prepare_cmd)
    out = run_loc_cmd(prepare_cmd)
    if out.succeeded:
        logger.info('we prepare bin file successful')
    else:
        logger.info('we prepare bin file fail, exit.......')
        exit(2)
    check_point1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['CHECK_POINT']
    check_point2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['CHECK_POINT1']
    check_point3 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['CHECK_POINT2']
    check_point4 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['CHECK_POINT3']
    if optix_sample != '1':
        prepare_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['PREPARE']['CMD1'].format(cupti_path, optix_bin_path)
        print(prepare_cmd1)
        ''''
        out1 = run_loc_cmd(prepare_cmd1)
        if out1.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        '''
    # run cupti inject optix case
    if optix_sample == '1':
        if cuda_short_version > '12.2':
            optix_flag = '--cblProfiling 1'
        else:
            optix_flag = ''
    else:
        optix_flag = ''
        cupti_run_path = sample_cupti_path
    for sample, value in sample_dict.items():
        step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP1']['CMD1'].format(
            cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
        step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP2']['CMD1'].format(
            cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
        if optix_sample != '1':
            step1_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP1']['CMD2'].format(
                cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
            step2_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP2']['CMD2'].format(
                cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
            step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP3']['CMD1'].format(
                cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
            step3_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX_BASIC']['STEP3']['CMD2'].format(
                cupti_run_path, sample.split(' ')[0], optix_flag, sample, sample.split(' ')[0])
        if optix_sample == '1':
            if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                window_cmd = "sleep 20s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`;sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`;sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
            # cmd_list = [step1_cmd1, step2_cmd1, step3_cmd1]
            cmd_list = [step1_cmd1]
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
            cmd_list = [step1_cmd1, step1_cmd2, step2_cmd1, step2_cmd2, step3_cmd1, step3_cmd2]
        for index, cmd in enumerate(cmd_list):
            cupti_injection_list = [cmd, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            check_output('{}/{}_step{}_cmd{}.txt'.format(cupti_run_path, sample.split(' ')[0], index + 1, index + 1), sample.split(' ')[0], result, 'profile_step1', check_point1, check_point2, check_point3, check_point4, flag=2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_%s.json' % (log_path, sample11))
    return result, passed, failed


def ncu_optix(option=None):
    result = {}
    passed, failed = 0, 0
    if option is not None:
        log_name = case_config['NCU_OPTIX']['LOG_NAME1'] % option
        if option == 'single':
            ncu_option = case_config['NCU_OPTION']['OPTIONS1']
        elif option == 'replay':
            ncu_option = case_config['NCU_OPTION']['OPTIONS2']
        log_path = case_config['global']['env']['NCU_OPTION_OPTIX_LOG_PATH']
    else:
        log_name = case_config['NCU_OPTIX']['LOG_NAME']
        log_path = case_config['global']['env']['NCU_OPTIX_LOG_PATH']
    mkdir(log_path)
    for sample, value in sample_dict.items():
        if option:
            ncu_cmd = 'cd %s; %s/bin/ncu -c 20 %s ./%s | tee ncu_%s_%s.txt' % (
                optix_bin_path, cuda_path, ncu_option, sample, sample.split(' ')[0], option)
        else:
            ncu_cmd = 'cd %s; %s/bin/ncu -c 20 ./%s | tee ncu_%s.txt' % (optix_bin_path, cuda_path, sample, sample.split(' ')[0])
        if optix_sample == '1':
            if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                window_cmd = "sleep 30s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
        # window_cmd1 = "sleep 16s; wmctrl -i -c `wmctrl -l |grep -i '%s/./%s'|awk '{print $1}'`" % (optix_bin_path, value)
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
        cmd_list = [ncu_cmd, window_cmd]
        multi_process(cmd_list, log_name, result, 'ncu', sample.split(' ')[0])
        if option:
            check_output('%s/ncu_%s_%s.txt' % (optix_bin_path, sample.split(' ')[0], option),
                         sample.split(' ')[0],
                         result,
                         'ncu',
                         'error',
                         'n/a',
                         'Error',
                         'ERROR',
                         'CUPTI_ERROR_HARDWARE_BUSY',
                         'No kernel',
                         flag=2)
        else:
            check_output('%s/ncu_%s.txt' % (optix_bin_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'ncu',
                         'error',
                         'n/a',
                         'Error',
                         'ERROR',
                         'CUPTI_ERROR_HARDWARE_BUSY',
                         'No kernel',
                         flag=2)
    """
    for sample in sample_list:
        if option:
            ncu_cmd = 'cd %s; %s/bin/ncu -c 100 --kill yes %s ./%s' % (optix_bin_path, cuda_path, ncu_option, sample)
            check_result(ncu_cmd, 'run  ncu {} with option {}'.format(sample, option), log_name, result, 'error', 'n/a', 'Error', 'ERROR', 'CUPTI_ERROR_HARDWARE_BUSY', flag=2)
        else:
            ncu_cmd = 'cd %s; %s/bin/ncu -c 100 --kill yes ./%s' % (optix_bin_path, cuda_path, sample)
            check_result(ncu_cmd, 'run  ncu {}'.format(sample), log_name, result, 'error', 'n/a', 'Error', 'ERROR', 'CUPTI_ERROR_HARDWARE_BUSY', flag=2)
    """
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    if option:
        dict_to_json(result1, '%s/ncu_%s_%s.json' % (log_path, option, sample11))
    else:
        dict_to_json(result1, '%s/ncu_%s.json' % (log_path, sample11))
    return result, passed, failed


def cupti_trace_injection_optix():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION_OPTIX']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_OPTIX_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
    if platform == 'x86':
        if optix_sample != '1':
            cupti_run_path = '%s/../../host/%s' % (sample_bin_path, cupti_extract_path)
        else:
            cupti_run_path = '%s/host/%s' % (cupti_path, cupti_extract_path)
        cupti_target_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            if optix_sample != '1':
                cupti_run_path = "%s/../../host/linux-desktop-glibc_2_19_0-arm" % sample_bin_path
            else:
                cupti_run_path = "%s/host/linux-desktop-glibc_2_19_0-arm" % cupti_path
            cupti_target_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        else:
            if optix_sample != '1':
                cupti_run_path = "%s/../../host/linux-desktop-t210-a64" % sample_bin_path
            else:
                cupti_run_path = "%s/host/linux-desktop-t210-a64" % cupti_path
            cupti_target_path = "%s/target/linux-desktop-t210-a64" % cupti_path
    else:
        logger.info('now we do not support the platform---%s' % platform)
    # prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
    # prepare graphic sample for cupti testing
    prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD'] % (optix_bin_path, cupti_target_path)
    print(prepare_cmd)
    out = run_loc_cmd(prepare_cmd)
    if out.succeeded:
        logger.info('we prepare bin file successful')
    else:
        logger.info('we prepare bin file fail, exit.......')
        exit(2)
    if optix_sample != '1':
        prepare_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD1'] % (cupti_path, optix_bin_path)
        prepare_cmd2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['PREPARE']['CMD2'] % (cupti_path, optix_bin_path)
        print(prepare_cmd1)
        for cmd in [prepare_cmd2, prepare_cmd1]:
            out1 = run_loc_cmd(cmd)
            if out1.succeeded:
                logger.info('we prepare bin file successful')
            else:
                logger.info('we prepare bin file fail, exit.......')
                exit(2)
    # run cupti trace injection testing
    for sample, value in sample_dict.items():
        if cuda_short_version <= '11.4':
            dict1 = {"tests": []}
            dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
        else:
            dict1 = {"tests": []}
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        create_json_file = 'cd %s; rm test_%s.json; touch test_%s.json' % (
            cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
        run_loc_cmd(create_json_file)
        if len(sample.split(' ')) != 1:
            if cuda_short_version <= '11.4':
                dict1["tests"].append({
                    "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe_args": sample.split(' ')[1::]
                })
            else:
                dict1["tests"].append({
                    "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe-args": sample.split(' ')[1::]
                })
        else:
            dict1["tests"].append({"name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0]})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s.json' % (cupti_run_path + '/test_' + sample.split(' ')[0]))
        if cuda_short_version <= '11.4':
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1']['CMD'] % (
                    cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_1']['CMD'] % (
                    cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_2']['CMD'] % (cupti_run_path, 'cublas', 'cublas')
        else:
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_1']['CMD1'] % (
                    cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_2']['CMD1'] % (cupti_run_path, 'cublas', 'cublas')
        if optix_sample == '1':
            if sample == 'optixRaycasting' or sample == 'optixDemandLoadSimple' or 'optixDenoiser' in sample or 'optixOpticalFlow' in sample or 'optixCompileWithTasks' in sample:
                window_cmd = "sleep 30s; wmctrl -l"
            else:
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
        else:
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
        cmd_list = [step1_cmd, window_cmd]
        multi_process(cmd_list, log_name, result, 'trace', sample.split(' ')[0])
        check_output('%s/%s_trace_injection.txt' % (cupti_run_path, sample.split(' ')[0]), sample.split(' ')[0], result, 'trace')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_trace_%s.json' % (log_path, sample11))
    return result, passed, failed


def sanitizer_optix_case_sanity(tools_option):
    sm = get_sm()
    print(sm)
    print('ssssssssssssssssssssssssssssssssssssssssss')
    if sm not in [8.0, 9.0, 10.0]:
        sample_list = ['optixPathTracer']
        run_sample_list = ['optixPathTracer']
        sample_dict1 = dict(zip(run_sample_list, sample_list))
        print(sample_dict1)
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
        sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
        sample_dict = {
            'postProcessGL': 'CUDA GL Post Processing', 'oceanFFT': 'CUDA FFT Ocean Simulation', 'simpleGL': 'Cuda GL Interop'
        }
        sample_dict.update(sample_dict1)
        # prepare optix sample, copy optix sample to sdk sample bin path
        copy_cmd = 'cp -r {}/* {}'.format(optix_bin_path, sample_bin_path)
        run_loc_cmd(copy_cmd)
        result = {}
        passed, failed = 0, 0
        log_name = case_config['SANITIZER_OPTIX']['LOG_NAME'] % (tools_option, tools_home)
        log_path = case_config['global']['env']['SANITIZER_OPTIX_LOG_PATH'] % tools_option
        mkdir(log_path)
        if tools_option == 'racecheck':
            check_point = case_config['SANITIZER_OPTIX']['CHECK_POINT1']
        else:
            check_point = case_config['SANITIZER_OPTIX']['CHECK_POINT']
        check_point2 = case_config['SANITIZER_OPTIX']['CHECK_POINT2']
        os.chdir(sample_bin_path)
        for sample, value in sample_dict.items():
            if tools_option == 'memcheck':
                if optix_sample == '1':
                    if cuda_short_version < '12.3':
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix-leaks yes --leak-check full ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                    else:
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix-leaks --leak-check full ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --leak-check full ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            elif tools_option == 'initcheck':
                if optix_sample == '1':
                    if cuda_short_version == '12.2':
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix yes ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                    else:
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s --check-optix ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s;%s/bin/compute-sanitizer --tool %s ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            elif tools_option == 'racecheck':
                sanitizer_cmd = 'export LD_LIBRARY_PATH=%s; %s/bin/compute-sanitizer --tool %s -c 50 ./%s | tee %s_%s.txt' % (
                    LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            else:
                if sample in ['oceanFFT']:
                    if cuda_short_version < '12.3':
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --force-blocking-launches yes --tool %s ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                    else:
                        sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --force-blocking-launches --tool %s ./%s | tee %s_%s.txt' % (
                            LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
                else:
                    sanitizer_cmd = 'export LD_LIBRARY_PATH=%s:$LD_LIBRARY_PATH;  %s/bin/compute-sanitizer --tool %s ./%s | tee %s_%s.txt' % (
                        LD_LIBRARY_PATH, cuda_path, tools_option, sample, tools_option, sample.split(' ')[0])
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
            run_cmd_list = [sanitizer_cmd, window_cmd]
            multi_process(run_cmd_list, log_name, result, 'sanitizer', sample.split(' ')[0])
            if tools_option == 'memcheck':
                check_output('%s/%s_%s.txt' % (sample_bin_path, tools_option, sample.split(' ')[0]),
                             sample.split(' ')[0],
                             result,
                             'sanitizer',
                             check_point,
                             check_point2,
                             flag=1)
            else:
                check_output('%s/%s_%s.txt' % (sample_bin_path, tools_option, sample.split(' ')[0]),
                             sample.split(' ')[0],
                             result,
                             'sanitizer',
                             check_point,
                             flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/sanitizer_%s_%s.json' % (log_path, tools_option, sample11))
        return result, passed, failed
    else:
        logger.info('not support this case on tesla GPU, Ampere/Hopper/Blackwell')


def cupti_profile_injection_graphic_sanity():
    sm = get_sm()
    logger.info('start to run profile sanity testing')
    if sm not in [8.0, 9.0, 10.0]:
        sample_list = ['optixNVLink']
        run_sample_list = ['optixNVLink']
        sample_dict1 = dict(zip(run_sample_list, sample_list))
        print(sample_dict1)
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
        sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
        sample_dict = {
            'postProcessGL': 'CUDA GL Post Processing', 'oceanFFT': 'CUDA FFT Ocean Simulation', 'simpleGL': 'Cuda GL Interop'
        }
        sample_dict.update(sample_dict1)
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['LOG_NAME1']
        log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_OPTIX_LOG_PATH']
        mkdir(log_path)
        optix_sample = '1'
        prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            cupti_run_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
            else:
                cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
        else:
            logger.info('now we do not support the platform---%s' % platform)
        # prepare graphic sample for cupti testing
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD'] % (optix_bin_path, cupti_run_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD1'].format(
            sample_bin_path, cupti_run_path, cupti_run_path)
        prepare_cmd2 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD2'].format(
            cupti_path, sample_bin_path, cupti_path, sample_bin_path)
        print(prepare_cmd1)
        print(prepare_cmd2)
        for cmd in [prepare_cmd1, prepare_cmd2]:
            out1 = run_loc_cmd(cmd)
            if out1.succeeded:
                logger.info('we prepare bin file successful')
            else:
                logger.info('we prepare bin file fail, exit.......')
                exit(2)
        # run cupti inject optix case
        check_point = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT']
        check_point1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT1']
        check_point2 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT2']
        check_point3 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['CHECK_POINT3']
        for sample, value in sample_dict.items():
            option = ' '
            if 'optix' in sample:
                if cuda_short_version >= '12.2':
                    option = '--cblProfiling 1'
                path = cupti_run_path
            else:
                path = sample_cupti_path
            if cuda_short_version < '12.1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1'] % (
                    path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1'] % (
                    path, sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1'] % (
                    path, sample.split(' ')[0], sample, sample.split(' ')[0])
            elif cuda_short_version == '12.1':
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_1'] % (
                    path, sample.split(' ')[0], sample.split(' ')[0], sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (
                    path, sample.split(' ')[0], sample.split(' ')[0], sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_1'] % (
                    path, sample.split(' ')[0], sample.split(' ')[0], sample, sample.split(' ')[0])
            else:
                step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP1']['CMD1_2'] % (
                    path, option, sample, sample.split(' ')[0])
                step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP2']['CMD1_2'] % (
                    path, option, sample, sample.split(' ')[0])
                step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_OPTIX']['STEP3']['CMD1_2'] % (
                    path, option, sample, sample.split(' ')[0])
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
            cupti_injection_list = [step1_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            check_output('%s/%s_step1.txt' % (path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step1_%s' % sample.split(' ')[0],
                         check_point,
                         check_point1,
                         check_point2,
                         check_point3,
                         'Error',
                         flag=2)
            cupti_injection_list = [step2_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            check_output('%s/%s_step2.txt' % (path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step2_%s' % sample.split(' ')[0],
                         check_point,
                         check_point1,
                         check_point2,
                         check_point3,
                         'Error',
                         flag=2)
            cupti_injection_list = [step3_cmd1, window_cmd]
            multi_process(cupti_injection_list, log_name, result, 'profile', sample.split(' ')[0])
            check_output('%s/%s_step3.txt' % (path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'profile_step3_%s' % sample.split(' ')[0],
                         check_point,
                         check_point1,
                         check_point2,
                         check_point3,
                         'Error',
                         flag=2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_profile_coverage_graphic.json' % log_path)
        return result, passed, failed
    else:
        logger.info('no need run this case on tesla GPU, Ampere/Hopper/Blackwell')


def cupti_trace_injection_sanity():
    sm = get_sm()
    if sm not in [8.0, 9.0, 10.0]:
        sample_list = ['optixPathTracer']
        run_sample_list = ['optixPathTracer']
        sample_dict1 = dict(zip(run_sample_list, sample_list))
        print(sample_dict1)
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
        sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
        sample_dict = {
            'postProcessGL': 'CUDA GL Post Processing', 'oceanFFT': 'CUDA FFT Ocean Simulation', 'simpleGL': 'Cuda GL Interop'
        }
        sample_dict.update(sample_dict1)
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_LOG_PATH']
        mkdir(log_path)
        prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            cupti_run_path = '%s/../../host/%s' % (sample_bin_path, cupti_extract_path)
            cupti_target_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/../../host/linux-desktop-glibc_2_19_0-arm" % sample_bin_path
                cupti_target_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
            else:
                cupti_run_path = "%s/../../host/linux-desktop-t210-a64" % sample_bin_path
                cupti_target_path = "%s/target/linux-desktop-t210-a64" % cupti_path
        else:
            logger.info('now we do not support the platform---%s' % platform)
        # prepare graphic sample for cupti testing
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD'] % (optix_bin_path, cupti_target_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD1'].format(
            sample_bin_path, cupti_target_path, cupti_target_path)
        prepare_cmd2 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['PREPARE']['CMD2'].format(
            cupti_path, sample_bin_path, cupti_path, sample_bin_path)
        print(prepare_cmd1)
        print(prepare_cmd2)
        for cmd in [prepare_cmd1, prepare_cmd2]:
            out1 = run_loc_cmd(cmd)
            if out1.succeeded:
                logger.info('we prepare bin file successful')
            else:
                logger.info('we prepare bin file fail, exit.......')
                exit(2)
        # run cupti trace injection testing
        for sample, value in sample_dict.items():
            if cuda_short_version <= '11.4':
                dict1 = {"tests": []}
                dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
            else:
                dict1 = {"tests": []}
                dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
            create_json_file = 'cd %s; rm test_%s.json; touch test_%s.json' % (
                cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
            run_loc_cmd(create_json_file)
            if len(sample.split(' ')) != 1:
                if cuda_short_version <= '11.4':
                    dict1["tests"].append({
                        "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe_args": sample.split(' ')[1::]
                    })
                else:
                    dict1["tests"].append({
                        "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe-args": sample.split(' ')[1::]
                    })
            else:
                dict1["tests"].append({"name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0]})
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            dict_to_json(dict2, '%s.json' % (cupti_run_path + '/test_' + sample.split(' ')[0]))
            if cuda_short_version <= '11.4':
                if platform == 'x86':
                    step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['STEP1']['CMD'] % (
                        cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                elif platform == 'arm':
                    step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['STEP1_1']['CMD'] % (
                        cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                else:
                    pass
            else:
                if platform == 'x86':
                    step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['STEP1']['CMD1'] % (
                        cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                elif platform == 'arm':
                    step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY']['STEP1_1']['CMD1'] % (
                        cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                else:
                    pass
            window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                value, value, value)
            cmd_list = [step1_cmd, window_cmd]
            multi_process(cmd_list, log_name, result, 'trace', sample.split(' ')[0])
            check_output('%s/%s_trace_injection_sanity.txt' % (cupti_run_path, sample.split(' ')[0]),
                         sample.split(' ')[0],
                         result,
                         'trace')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_trace_sanity.json' % log_path)
        return result, passed, failed
    else:
        logger.info('no need run this case on tesla GPU, Ampere/Hopper/Blackwell')


def cupti_trace_injection_sanity_hes():
    sm = get_sm()
    vgpu = case_config['global']['env']['VGPU']
    if cuda_short_version >= '13.0' and sm > 9.0 and vgpu.lower() == 'none':
        if sm not in [8.0, 9.0, 10.0]:
            sample_list = ['optixPathTracer']
            run_sample_list = ['optixPathTracer']
            sample_dict1 = dict(zip(run_sample_list, sample_list))
            print(sample_dict1)
            optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
            optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
            sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
            sample_dict = {
                'postProcessGL': 'CUDA GL Post Processing', 'oceanFFT': 'CUDA FFT Ocean Simulation', 'simpleGL': 'Cuda GL Interop'
            }
            sample_dict.update(sample_dict1)
            result = {}
            passed, failed = 0, 0
            log_name = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['LOG_NAME']
            log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES_LOG_PATH']
            mkdir(log_path)
            prepare_tools_package('cupti', cupti_download_to_path, platform, cuda_short_version)
            if platform == 'x86':
                cupti_run_path = '%s/../../host/%s' % (sample_bin_path, cupti_extract_path)
                cupti_target_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
            elif platform == 'arm':
                if cuda_short_version < '11.5':
                    cupti_run_path = "%s/../../host/linux-desktop-glibc_2_19_0-arm" % sample_bin_path
                    cupti_target_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
                else:
                    cupti_run_path = "%s/../../host/linux-desktop-t210-a64" % sample_bin_path
                    cupti_target_path = "%s/target/linux-desktop-t210-a64" % cupti_path
            else:
                logger.info('now we do not support the platform---%s' % platform)
            # prepare graphic sample for cupti testing
            prepare_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['PREPARE']['CMD'] % (optix_bin_path, cupti_target_path)
            print(prepare_cmd)
            out = run_loc_cmd(prepare_cmd)
            if out.succeeded:
                logger.info('we prepare bin file successful')
            else:
                logger.info('we prepare bin file fail, exit.......')
                exit(2)
            prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['PREPARE']['CMD1'].format(
                sample_bin_path, cupti_target_path, cupti_target_path)
            prepare_cmd2 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['PREPARE']['CMD2'].format(
                cupti_path, sample_bin_path, cupti_path, sample_bin_path)
            print(prepare_cmd1)
            print(prepare_cmd2)
            for cmd in [prepare_cmd1, prepare_cmd2]:
                out1 = run_loc_cmd(cmd)
                if out1.succeeded:
                    logger.info('we prepare bin file successful')
                else:
                    logger.info('we prepare bin file fail, exit.......')
                    exit(2)
            # run cupti trace injection testing
            for sample, value in sample_dict.items():
                if cuda_short_version <= '11.4':
                    dict1 = {"tests": []}
                    dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
                else:
                    dict1 = {"tests": []}
                    dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
                create_json_file = 'cd %s; rm test_%s.json; touch test_%s.json' % (
                    cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                run_loc_cmd(create_json_file)
                if len(sample.split(' ')) != 1:
                    if cuda_short_version <= '11.4':
                        dict1["tests"].append({
                            "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe_args": sample.split(' ')[1::]
                        })
                    else:
                        dict1["tests"].append({
                            "name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "exe-args": sample.split(' ')[1::]
                        })
                else:
                    dict1["tests"].append({"name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0]})
                if cuda_short_version <= '11.4':
                    dict2.update(dict1)
                else:
                    dict2["tracing-injection"].update(dict1)
                dict_to_json(dict2, '%s.json' % (cupti_run_path + '/test_' + sample.split(' ')[0]))
                if cuda_short_version <= '11.4':
                    if platform == 'x86':
                        step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['STEP1']['CMD'] % (
                            cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                    elif platform == 'arm':
                        step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['STEP1_1']['CMD'] % (
                            cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                    else:
                        pass
                else:
                    if platform == 'x86':
                        step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['STEP1']['CMD1'] % (
                            cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                    elif platform == 'arm':
                        step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC_SANITY_HES']['STEP1_1']['CMD1'] % (
                            cupti_run_path, sample.split(' ')[0], sample.split(' ')[0])
                    else:
                        pass
                window_cmd = "sleep 20s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`; sleep 3s; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % (
                    value, value, value)
                cmd_list = [step1_cmd, window_cmd]
                multi_process(cmd_list, log_name, result, 'trace', sample.split(' ')[0])
                check_output('%s/%s_trace_injection_sanity.txt' % (cupti_run_path, sample.split(' ')[0]),
                             sample.split(' ')[0],
                             result,
                             'trace')
                if search_keyword_in_file('%s/%s_trace_injection_sanity.txt' % (cupti_run_path, sample.split(' ')[0]), 'Requested HES to be enabled') and search_keyword_in_file('%s/%s_trace_injection_sanity.txt' % (cupti_run_path, sample.split(' ')[0]), 'Enabled HES tracing in CUPTI'):
                    result['check_hes_information-%s' % sample.split(' ')[0]] = 'passed'
                else:
                    result['check_hes_information-%s' % sample.split(' ')[0]] = 'failed'
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '%s/cupti_trace_sanity_hes.json' % log_path)
            return result, passed, failed
        else:
            logger.info('not support this case on tesla GPU, Ampere/Hopper/Blackwell')
    else:
        logger.info('not support this case if cuda version less than 13.0')


def cuda_gdb_optix():
    from run_common_case import CUDBGExpect
    # break_dict = {"optixPathTrachker": "b optixPathTracer.cu:400", }
    break_dict = {
        "optixBoundValues": "b optixBoundValues.cu:99",
        "optixCallablePrograms": "b optixCallablePrograms.cu:109",
        "optixCompileWithTasks": "b optixCompileWithTasks.cpp:136",
        "optixConsole": "b optixConsole.cpp:163",
        "optixCurves": "b optixCurves.cu:105",
        "optixCustomPrimitive": "b optixCustomPrimitive.cu:96",
        "optixCutouts": "b optixCutouts.cu:226",
        "optixDenoiser": "b optixDenoiser.cpp:237",
        "optixDynamicGeometry": "b optixDynamicGeometry.cu:98",
        "optixDynamicMaterials": "b optixDynamicMaterials.cu:87",
        "optixHair": "b optixHair.cu:72,b optixHair.cu:91",
        "optixHello": "b draw_solid_color.cu:41",
        "optixMeshViewer": "b optixMeshViewer.cpp:190",
        "optixModuleCreateAbort": "b optixModuleCreateAbort.cu:92",
        "optixMotionGeometry": "b optixMotionGeometry.cu:169",
        "optixMultiGPU": "b optixMultiGPU.cu:228",
        "optixNVLink": "b optixNVLink.cu:225",
        "optixOpacityMicromap": "b optixOpacityMicromap.cu:77",
        "optixOpticalFlow": "b optixOpticalFlow.cu:91",
        "optixPathTracer": "b optixPathTracer.cu:251",
        "optixRaycasting": "b optixRaycasting.cu:52",
        "optixSimpleMotionBlur": "b optixSimpleMotionBlur.cu:96",
        "optixSphere": "b optixSphere.cu:106",
        "optixTriangle": "b optixTriangle.cu:68",
        "optixVolumeViewer": "b volume.cu:50",
        "optixWhitted": "b optixWhitted.cpp:251"
    }
    break_dict = {
        "optixBoundValues": "b optixBoundValues.cu:99",
        "optixCallablePrograms": "b optixCallablePrograms.cu:109",
        "optixConsole": "b optixConsole.cpp:163",
        "optixCurves": "b optixCurves.cu:68",
        "optixCustomPrimitive": "b optixCustomPrimitive.cu:96",
        "optixCutouts": "b optixCutouts.cu:226",
        "optixDynamicGeometry": "b optixDynamicGeometry.cu:98",
        "optixDynamicMaterials": "b optixDynamicMaterials.cu:87",
        "optixHello": "b draw_solid_color.cu:41",
        "optixMeshViewer": "b optixMeshViewer.cpp:190",
        "optixModuleCreateAbort": "b optixModuleCreateAbort.cu:92",
        "optixMotionGeometry": "b optixMotionGeometry.cu:169",
        "optixMultiGPU": "b optixMultiGPU.cu:228",
        "optixNVLink": "b optixNVLink.cu:225",
        "optixOpacityMicromap": "b optixOpacityMicromap.cu:77",
        "optixOpticalFlow": "b optixOpticalFlow.cu:91",
        "optixPathTracer": "b optixPathTracer.cu:251",
        "optixRaycasting": "b optixRaycasting.cu:52",
        "optixSimpleMotionBlur": "b optixSimpleMotionBlur.cu:96",
        "optixSphere": "b optixSphere.cu:106",
        "optixTriangle": "b optixTriangle.cu:68",
        "optixVolumeViewer": "b volume.cu:50",
        "optixWhitted": "b optixWhitted.cpp:251"
    }
    # do not run optixConsole/optixDenoiser/optixCompileWithTasks on cuda-gdb
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_GDB_OPTIX_LOG_PATH']
    mkdir(log_path)
    optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
    for sample, break_point in break_dict.items():
        cmd1 = 'cd {}; rm cuda-gdb-{}.txt; touch cuda-gdb-{}.txt'.format(log_path, sample, sample)
        run_test_cmd(cmd1)
        os.chdir("{}".format(optix_bin_path))
        if sample == 'optixOpticalFlow':
            cmd = "{}/cuda-gdb ./{} -o flow.exr soane-Beauty-001.exr soane-Beauty-002.exr".format(cuda_bin, sample)
        else:
            cmd = "{}/cuda-gdb ./{}".format(cuda_bin, sample)
        # os.environ['OPTIX_FORCE_DEPRECATED_LAUNCHER'] = '1'
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}/cuda-gdb-{}.txt".format(log_path, sample))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\) CUDA Debugger")
        # dgl_gdb.expect('set pagination off')
        # dgl_gdb.expect('set cuda break_on_launch app')
        # dgl_gdb.expect('r', checkpoint=r"\n\d+\s+.*\w+", mode=0)
        if sample == 'optixHair':
            dgl_gdb.expect("{}".format(break_point.split(',')[0]), checkpoint=r"Breakpoint 1", mode=0)
            dgl_gdb.expect("{}".format(break_point.split(',')[1]), checkpoint=r"Breakpoint 2", mode=0)
        else:
            dgl_gdb.expect("{}".format(break_point), checkpoint=r"Breakpoint 1", mode=0)
        dgl_gdb.expect('r', checkpoint=r"\d+\s+.*\w+", mode=0)
        time.sleep(2)
        # dgl_gdb.expect("c", checkpoint=r"hit Breakpoint", mode=0)
        dgl_gdb.expect("info threads", checkpoint=r"1\s+Thread 0x", mode=0)
        dgl_gdb.expect("bt", checkpoint=r"#0", mode=0)
        if 'cpp' not in break_point:
            dgl_gdb.expect("cuda device", checkpoint=r"device\s+\d+", mode=0)
            dgl_gdb.expect("cuda block", checkpoint=r"block\s+\(\d+", mode=0)
            dgl_gdb.expect("cuda thread", checkpoint=r"thread\s+\(\d+", mode=0)
        # dgl_gdb.expect("c", checkpoint=r"hit Breakpoint", mode=0)
        # dgl_gdb.expect("c", checkpoint=[r"\d+\s+.*\w+", r"hit Breakpoint"], mode=0)
        time.sleep(2)
        dgl_gdb.expect("d breakpoints")
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb testing grachic sample----{}".format(sample)] = "passed" if dgl_result is True else 'failed'
        if result["cuda-gdb testing grachic sample----{}".format(sample)] == "passed":
            logger.info('we run cuda-gdb about {} passed'.format(sample))
        else:
            logger.info('we run cuda-gdb about {} failed'.format(sample))
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_optix_sample.json' % log_path)
    return result, passed, failed


def cuda_gdb_graphic():
    from run_common_case import CUDBGExpect
    result = {}
    passed, failed = 0, 0
    log_path = case_config['global']['env']['CUDA_GDB_GRAPHIC_LOG_PATH']
    mkdir(log_path)
    sample_list = [
        'bicubicTexture',
        'bilateralFilter',
        'simpleVulkan',
        'bindlessTexture',
        'boxFilter',
        'fluidsGL',
        'FunctionPointers',
        'imageDenoising',
        'simpleVulkanMMAP',
        'Mandelbrot',
        'marchingCubes',
        'nbody',
        'oceanFFT',
        'particles',
        'postProcessGL',
        'randomFog',
        'recursiveGaussian',
        'simpleCUDA2GL',
        'simpleGL',
        'simpleTexture3D',
        'smokeParticles',
        'SobelFilter',
        'volumeFiltering',
        'volumeRender',
        'vulkanImageCUDA'
    ]
    sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
    for sample in sample_list:
        cmd1 = 'cd {}; rm cuda-gdb-{}.txt; touch cuda-gdb-{}.txt'.format(log_path, sample, sample)
        run_test_cmd(cmd1)
        os.chdir("{}".format(sample_bin_path))
        cmd = "{}/cuda-gdb ./{}".format(cuda_bin, sample)
        dgl_gdb = CUDBGExpect(cmd=None, logfile="{}/cuda-gdb-{}.txt".format(log_path, sample))
        dgl_gdb.spawn(cmd, checkpoint=r"NVIDIA \(R\) CUDA Debugger")
        dgl_gdb.expect('set pagination off')
        dgl_gdb.expect('set cuda break_on_launch app')
        # dgl_gdb.expect('r', checkpoint=r"\n\d+\s+.*\w+", mode=0)
        dgl_gdb.expect('r', checkpoint=r"\d+\s+.*\w+", mode=0)
        time.sleep(2)
        dgl_gdb.expect("info threads", checkpoint=r"1\s+Thread 0x", mode=0)
        dgl_gdb.expect("bt", checkpoint=r"#0", mode=0)
        dgl_gdb.expect("b", checkpoint=r"Breakpoint 1 at ", mode=0)
        dgl_gdb.expect("cuda device", checkpoint=r"device\s+\d+", mode=0)
        dgl_gdb.expect("cuda block", checkpoint=r"block\s+\(\d+", mode=0)
        dgl_gdb.expect("cuda thread", checkpoint=r"thread\s+\(\d+", mode=0)
        dgl_gdb.expect("c", checkpoint=[r"\d+\s+.*\w+", r"hit Breakpoint"], mode=0)
        time.sleep(2)
        dgl_gdb.expect("d breakpoints")
        dgl_gdb.expect("quit")
        dgl_result = dgl_gdb.conclude_result()
        result["cuda-gdb testing grachic sample----{}".format(sample)] = "passed" if dgl_result is True else 'failed'
        if result["cuda-gdb testing grachic sample----{}".format(sample)] == "passed":
            logger.info('we run cuda-gdb about {} passed'.format(sample))
        else:
            logger.info('we run cuda-gdb about {} failed'.format(sample))
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_gdb_graphic_sample.json' % log_path)
    return result, passed, failed


example_text = """
We use compute-sanitizer/nsight compute/cupti to run optix case
Example:
python optix_coverage.py -s 0  # default is run optix sample, now change it not run optix sample, then run SDK graphic sample
python optix_coverage.py -p /home/<USER>/optix_20220118  # create the optix sample path and download sh file and color.exr file
python optix_coverage.py -t all # run all the tools about optix
python optix_coverage.py -t sanitizer -o racecheck
python optix_coverage.py -t ncu -o single
python optix_coverage.py -t cupti -o profile
sanitizer has four options: racecheck, initcheck, memcheck, synccheck
cupti: will run trace injection and profile injection
ncu: will run option--single/replay or default run ncu
"""
reversion = '1.0'

if __name__ == '__main__':
    if optix_sample == '1':
        prepare_optix_sample(tools_home, user, password, base_url, cuda_short_version, platform, build=1)
    parser = argparse.ArgumentParser(description=None, epilog=example_text, formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument("-t", "--tools", required=False, help='Specify tools to run optix case')
    parser.add_argument("-o", "--option", required=False, help='Specify the option of the tools will used')
    parser.add_argument("-s",
                        "--optix_sample",
                        required=False,
                        help='Specify whether excute optix sample or not, 1: run optix sample; 0: run sdk graphic sample')
    parser.add_argument("-p", "--optix_path", required=False, help='Specify optix sample path')
    args = parser.parse_args()
    if args.optix_sample and args.optix_path:
        change_yaml(optix_sample1=args.optix_sample, optix_path1=args.optix_path)
    elif args.optix_sample:
        change_yaml(optix_sample1=args.optix_sample)
    elif args.optix_path:
        change_yaml(optix_path1=args.optix_path)
    else:
        logger.info('we need not change the optix sample and path')
    if optix_sample == '1':
        prepare()
    if args.tools == 'sanitizer':
        if args.option:
            sanitizer_optix_case(args.option)
        else:
            logger.info('please give correct option when use sanitizer to run optix case')
    elif args.tools == 'sanitizer_sanity':
        if args.option:
            sanitizer_optix_case_sanity(args.option)
        else:
            logger.info('please give correct option when use sanitizer to run optix case')
    elif args.tools == 'profile_sanity':
        if args.option:
            cupti_profile_injection_graphic_sanity()
        else:
            logger.info('please give correct function name to run cupti profile sanity case')
    elif args.tools == 'trace_sanity':
        if args.option == 'trace':
            cupti_trace_injection_sanity()
        elif args.option == 'trace_hes':
            cupti_trace_injection_sanity_hes()
        else:
            logger.info('please give correct function name to run cupti profile sanity case')
    elif args.tools == 'cupti':
        if args.option == 'profile':
            cupti_profile_injection_optix()
            # cupti_profile_injection_optix_basic()
        elif args.option == 'trace':
            cupti_trace_injection_optix()
        elif args.option == 'old_api':
            cupti_profile_injection_optix_old_api()
        else:
            cupti_trace_injection_optix()
            cupti_profile_injection_optix()
    elif args.tools == 'ncu':
        if args.option:
            ncu_optix(option=args.option)
        else:
            ncu_optix(option=None)
    elif args.tools == 'cuda-gdb':
        if args.option == 'graphic':
            cuda_gdb_graphic()
        else:
            cuda_gdb_optix()
    else:
        logger.info('please give correct tools to run optix')
