# -*- encoding: utf-8 -*-
import os.path
import re

try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
import argparse
import base64
from common_utils import *
import logging
import time
import random
import threading
from yaml_mgr import YamlManger

cupti_yaml = '{}/../yaml/cupti_case.yaml'.format(os.getcwd())
yaml_mgr = YamlManger(cupti_yaml)
case_config = yaml_mgr.load()
chip_list = [
    'ga100',
    'ga102',
    'ga104',
    'ga106',
    'ga107',
    'gv100',
    'gv11b',
    'tu102',
    'tu104',
    'tu106',
    'tu116',
    'tu117',
    'ad104',
    'gh100',
    'ga106'
]
cuda_version = case_config['global']['env']['CUDA_VERSION']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
driver_version = case_config['global']['env']['DRIVER_VERSION']
sample_bin_path = case_config['global']['env']['SAMPLE_BIN_PATH']
base_path = case_config['global']['env']['CUDA_PATH']
host_password = case_config['global']['env']['HOST_PASSWORD']
cuda_major = case_config['global']['env']['CUDA_MAJOR']
mig = case_config['global']['env']['MIG']
cuda_minor = case_config['global']['env']['CUDA_MINOR']
cuda_version = case_config['global']['env']['CUDA_VERSION']
cupti_extract_path = case_config['global']['env']['CUPTI_EXTRACT_PATH']
vgpu = case_config['global']['env']['VGPU'].lower()
home_path = case_config['global']['env']['HOST_HOME']
platform = case_config['global']['env']['PLATFORM'].lower()
installer = case_config['global']['env']['INSTALLER'].lower()
password1 = case_config['global']['env']['CQA_PASSWORD']
output_flag = case_config['global']['env']['OUTPUT_FLAG']
user1 = case_config['global']['env']['CQA_USER']
password = b64_strip_decode(password1).decode()
user = b64_strip_decode(user1).decode()
base_url = 'http://cqa-fs01.nvidia.com/Compute_Tools/ComputeToolsTest'
if cuda_short_version < '11.6':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH1']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH1']
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH1']
    cuhook_path = case_config['global']['env']['CUHOOK_PATH1']
elif '11.6' < cuda_short_version < '13.0':
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH']
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH']
    cuhook_path = case_config['global']['env']['CUHOOK_PATH']
else:
    sample_0_path = case_config['global']['env']['SAMPLE_0_PATH2']
    sample_1_path = case_config['global']['env']['SAMPLE_1_PATH2']
    sample_6_path = case_config['global']['env']['SAMPLE_6_PATH2']
    cuhook_path = case_config['global']['env']['CUHOOK_PATH2']
if cuda_short_version == 10.1:
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.1']
    cupti_addr_ppc64le = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.1_P9']
elif cuda_short_version == 10.2:
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.2']
    cupti_addr_ppc64le = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.2_P9']
    cupti_addr_arm = case_config['CUPTI_SMOKE_ADDR']['CUPTI_10.2_ARM']
elif cuda_major == '11':
    cupti_addr = case_config['CUPTI_SMOKE_ADDR']['CUPTI_11']
    cupti_addr_ppc64le = case_config['CUPTI_SMOKE_ADDR']['CUPTI_11_P9']
    cupti_addr_arm = case_config['CUPTI_SMOKE_ADDR']['CUPTI_11_ARM']
else:
    logger.info('will use the new dvs package')
date1 = time.strftime("%Y%m%d", time.localtime())
cupti_path = case_config['global']['env']['CUPTI_PATH1']
cupti_path_ppc64le = case_config['global']['env']['CUPTI_PATH2']
cupti_path_arm = case_config['global']['env']['CUPTI_PATH3']
tools_home = case_config['global']['env']['TOOLS_HOME']
mkdir(tools_home)
if platform == 'power':
    download_to_path = cupti_path_ppc64le
elif platform == 'arm':
    download_to_path = cupti_path_arm
else:
    download_to_path = cupti_path

logger.setLevel(level=logging.INFO)

# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/cupti_case.log' % tools_home)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger = logging.getLogger(__name__)

if platform == 'x86':
    download_to_path = case_config['global']['env']['CUPTI_PATH1']
    cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH1']
    cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH1']
    package = 'Cupti-Release-Public-Linux.tar.gz'
elif platform == 'power':
    download_to_path = case_config['global']['env']['CUPTI_PATH2']
    if cuda_short_version <= '11.4':
        cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH2']
        cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH2']
    else:
        cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH2_1']
        cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH2_1']
    package = 'Cupti-Release-Public-Linux_PPC64LE.tar.gz'
elif platform == 'arm':
    download_to_path = case_config['global']['env']['CUPTI_PATH3']
    cupti_run_path = case_config['global']['env']['CUPTI_RUN_PATH3']
    cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH3']
    package = 'Cupti-Release-Public-Armserver.tar.gz'
else:
    logger.error('please give the correct platform')
    sys.exit(2)

gpu_dict = {
    'V100': 'gv100',
    'v100': 'gv100',
    'RTX 2070': 'tu106',
    'RTX 2080': 'tu104',
    'RTX 2080 Ti': 'tu102',
    'RTX 3080 Ti': 'ga102',
    'GV100': 'gv100',
    'T4': 'tu104',
    'tu104': 'tu104',
    'TITAN V': 'tu102',
    'A100': 'ga100',
    'P100': 'gp100',
    'ga100': 'ga100',
    'gv100': 'gv100'
}
os.environ["PATH"] = "%s/bin" % base_path + ";" + os.environ["PATH"]
os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']


def prepare_gpu():
    build_cmd = case_config['PREPARE']['CMD1'] % sample_1_path
    gpu_cmd = case_config['PREPARE']['CMD2'] % sample_1_path
    sm_cmd = case_config['PREPARE']['CMD3'] % sample_1_path
    out1 = run_loc_cmd(build_cmd)
    if out1.succeeded:
        out2 = run_loc_cmd(gpu_cmd)
        out3 = run_loc_cmd(sm_cmd)
        for key, value in gpu_dict.items():
            if key in out2['output']:
                return value
        if 'Graphics Device' in out2['output'] and out3['output'] == '7.0':
            return 'gv100'
        elif 'Graphics Device' in out2['output'] and out3['output'] == '8.0':
            return 'ga100'
        else:
            pass


def run_uvm_sample():
    run_uvm_cmd = 'cd %s/UnifiedMemoryStreams; make clean; make; ./UnifiedMemoryStreams' % sample_0_path
    out = run_loc_cmd(run_uvm_cmd)
    if out.succeeded:
        return True
    else:
        return False


def verify_tools_enable(*args):
    # get gpu bus id
    cmd_bus = "nvidia-smi | grep 0000000 | awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'"
    bus_out = run_loc_cmd(cmd_bus)
    # get the dmatest tools
    cmd = 'cd ~; wget --user {} --password {} {}/vGPU/dmaTest'.format(user, password, base_url)
    out = run_loc_cmd(cmd)
    passed = 0
    if out.succeeded:
        logger.info('we get the dmaTest tools successful')
        cmd1 = 'cd ~; chmod u+x dmaTest; export __RM_NO_VERSION_CHECK=2; ./dmaTest -g {} -q'.format(bus_id_list[0])
        tool_out = run_loc_cmd(cmd1)
        print(tool_out)
        if args:
            for tool in args:
                if tool.lower() == 'debug':
                    pattern = 'Debugging capability: 1'
                    if pattern in tool_out['output']:
                        logger.info('debugger had enabled')
                        passed += 1
                elif tool.lower() == 'profile':
                    pattern = 'Profiling capability: 1'
                    if pattern in tool_out['output']:
                        logger.info('profile had enabled')
                        passed += 1
                elif tool.lower() == 'uvm':
                    pattern = 'UVM capability: 1'
                    if pattern in tool_out['output']:
                        logger.info('UVM had enabled')
                        passed += 1
                else:
                    continue
        if passed == len(args):
            logger.info('verify tools enabled successful')
            return True
        else:
            logger.info('there is no tools params enabled in vgpu')
            return False
    else:
        logger.info('we download the tool------dmaTest failed, please check')


def get_sm():
    sm_cmd = case_config['PREPARE']['CMD3'] % sample_1_path
    sm_out = run_loc_cmd(sm_cmd)
    print(sm_cmd)
    if sm_out.succeeded:
        sm = sm_out['output']
        if len(sm.split('\n')[-1].strip(' ')) >= 3:
            return float(sm.split('\n')[-1].strip(' '))
            logger.info('we get the sm of GPU is %s' % sm.split('\n')[-1].strip(' '))
        else:
            logger.info('we get the sm failed， please check deviceQuery sample')
            exit()
    else:
        logger.info('we get the sm failed， please check deviceQuery sample')
        exit()


def parse_cupti_page(arch=case_config['global']['env']['PLATFORM'].lower()):
    try:
        if arch == 'power':
            print(cupti_addr_ppc64le)
            website = urllib2.urlopen(cupti_addr_ppc64le)
        elif arch == 'arm':
            website = urllib2.urlopen(cupti_addr_arm)
        else:
            website = urllib2.urlopen(cupti_addr)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except httplib.HTTPException as e:
        print('HTTPException')
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False
    print(website)
    '''
    html = website.read().decode('utf-8')
    if arch == 'power':
        matchs = re.findall(r'SW_.*Release_Linux_Cupti_PPC64LE.tgz', html)
    elif arch == 'arm':
        matchs = re.findall(r'SW_.*Release_Armserver_Cupti.zip', html)
    else:
        matchs = re.findall(r'SW_.*Release_Linux_Cupti.tgz', html)
    matchs.sort()
    latest_build_name = matchs[-1].split('"')
    return latest_build_name
    '''
    html = website.read()
    print(html.split('"')[-2].strip(' '))
    return html.split('"')[-2].strip(' ')


def prepare_cupti():
    if platform == 'power':
        download_to_path = cupti_path_ppc64le
    elif platform == 'arm':
        download_to_path = cupti_path_arm
    else:
        download_to_path = cupti_path
    download_path = get_dvs_package('cupti', platform, cuda_short_version)
    print(download_path)
    cupti_package = download_path.split('/')[-1]
    logger.info('we will download the package from %s' % download_path)
    logger.info('we will download the package to %s ' % download_to_path)
    cmd2 = 'echo %s| sudo -S apt install -y bs4' % host_password
    mkdir(download_to_path)
    os.chdir(download_to_path)
    run_loc_cmd(cmd2)
    create_file(download_to_path, 'cupti.txt')
    if is_empty_file('%s/cupti.txt' % download_to_path) is False:
        logger.info('we had download the package, no need download again')
    else:
        cmd2 = "cd %s; lftp -c 'glob -- pget -n 80 %s'" % (download_to_path, download_path)
        logger.info('we will use the command to download package, the command is %s ' % cmd2)
        out2 = run_loc_cmd(cmd2)
        if out2.succeeded:
            if platform == 'arm':
                cmd3 = 'cd %s; unzip %s; for i in `ls *.tar.bz2`; do tar jxvf $i; done' % (download_to_path, cupti_package)
            else:
                cmd3 = 'cd %s; tar xzvf %s; for i in `ls *.tar.bz2`; do tar jxvf $i; done' % (download_to_path, cupti_package)
            logger.info('we will use the command to tar package, the command is %s ' % cmd3)
            out3 = run_loc_cmd(cmd3)
            if out3.succeeded:
                logger.info('extract the cupti package successful')
                with open('%s/cupti.txt' % download_to_path, 'a+') as f:
                    f.write(cupti_package)
                    f.write('\n')
            else:
                logger.info('extract the cupti package failed')
        else:
            logger.info('Failed to download cupti package')
    return download_to_path


def cupti_injection():
    if cuda_short_version < '11.7':
        result = {}
        passed, failed = 0, 0
        # prepare cupti dvs package
        # prepare_cupti()
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        if vgpu != 'none':
            if run_uvm_sample() is True:
                sample_list = case_config['CUPTI_INJECTION']['SAMPLE_LIST1'].split(',')
                sample_list.append('systemWideAtomics')
                sample_list.append('UnifiedMemoryStreams')
            else:
                sample_list = case_config['CUPTI_INJECTION']['SAMPLE_LIST1'].split(',')
        else:
            sample_list = case_config['CUPTI_INJECTION']['SAMPLE_LIST'].split(',')
        log_name = case_config['CUPTI_INJECTION']['LOG_NAME']
        log_path = case_config['global']['env']['INJECTION_LOG_PATH']
        mkdir(log_path)
        gpu_type = prepare_gpu()
        check_point = case_config['CUPTI_INJECTION']['CHECK_POINT']
        if platform == 'x86':
            cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH1']
        elif platform == 'power':
            cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH2']
        else:
            cupti_target_path = case_config['global']['env']['CUPTI_TARGET_PATH3']
        for sample in sample_list:
            if os.path.isfile("%s/%s" % (case_config['global']['env']['SAMPLE_BIN_PATH'], sample)):
                if cuda_version < '11.2':
                    # run step1
                    if platform.lower() == 'x86':
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD1'] % (gpu_type, sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD3'] % (gpu_type, sample, sample, sample)
                    else:
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD2'] % (gpu_type, sample, sample, sample)
                    # step1_point = case_config['CUPTI_INJECTION']['STEP1']['CHECK_POINT']
                    logname1 = log_path + '/step1_%s.log' % sample
                    check_result(step1_cmd, 'step1_%s' % step1_cmd, logname1, result, check_point, flag=2)
                    # run step2
                    if platform.lower() == 'x86':
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD1'] % (gpu_type, sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD3'] % (gpu_type, sample, sample, sample)
                    else:
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD2'] % (gpu_type, sample, sample, sample)
                    # step2_point = case_config['CUPTI_INJECTION']['STEP2']['CHECK_POINT']
                    logname2 = log_path + '/step2_%s.log' % sample
                    check_result(step2_cmd, 'step2_injection_%s' % step2_cmd, logname2, result, check_point, flag=2)
                    # run step3
                    if platform.lower() == 'x86':
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD1'] % (gpu_type, sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD3'] % (gpu_type, sample, sample, sample)
                    else:
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD2'] % (gpu_type, sample, sample, sample)
                    # step3_point = case_config['CUPTI_INJECTION']['STEP3']['CHECK_POINT']
                    print(step3_cmd)
                    logname3 = log_path + '/step3_%s.log' % sample
                    check_result(step3_cmd, 'step3_injection_%s' % step3_cmd, logname3, result, check_point, flag=2)
                    # run step4
                    if platform.lower() == 'x86':
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD1'] % (gpu_type, sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD3'] % (gpu_type, sample, sample, sample)
                    else:
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD2'] % (gpu_type, sample, sample, sample)
                    # step4_point = case_config['CUPTI_INJECTION']['STEP4']['CHECK_POINT']
                    logname4 = log_path + '/step4_%s.log' % sample
                    check_result(step4_cmd, 'step4_injection_%s' % step4_cmd, logname4, result, check_point, flag=2)
                else:
                    # run step1
                    if platform.lower() == 'x86':
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD1_1'] % (sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD3_1'] % (sample, sample, sample)
                    else:
                        step1_cmd = case_config['CUPTI_INJECTION']['STEP1']['CMD2_1'] % (sample, sample, sample)
                    # step1_point = case_config['CUPTI_INJECTION']['STEP1']['CHECK_POINT']
                    logname1 = log_path + '/step1_%s.log' % sample
                    check_result(step1_cmd, 'step1_%s' % step1_cmd, logname1, result, check_point, flag=2)
                    # run step2
                    if platform.lower() == 'x86':
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD1_1'] % (sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD3_1'] % (sample, sample, sample)
                    else:
                        step2_cmd = case_config['CUPTI_INJECTION']['STEP2']['CMD2_1'] % (sample, sample, sample)
                    # step2_point = case_config['CUPTI_INJECTION']['STEP2']['CHECK_POINT']
                    logname2 = log_path + '/step2_%s.log' % sample
                    check_result(step2_cmd, 'step2_injection_%s' % step2_cmd, logname2, result, check_point, flag=2)
                    # run step3
                    if platform.lower() == 'x86':
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD1_1'] % (sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD3_1'] % (sample, sample, sample)
                    else:
                        step3_cmd = case_config['CUPTI_INJECTION']['STEP3']['CMD2_1'] % (sample, sample, sample)
                    # step3_point = case_config['CUPTI_INJECTION']['STEP3']['CHECK_POINT']
                    print(step3_cmd)
                    logname3 = log_path + '/step3_%s.log' % sample
                    check_result(step3_cmd, 'step3_injection_%s' % step3_cmd, logname3, result, check_point, flag=2)
                    # run step4
                    if platform.lower() == 'x86':
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD1_1'] % (sample, sample, sample)
                    elif platform.lower() == 'arm':
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD3_1'] % (sample, sample, sample)
                    else:
                        step4_cmd = case_config['CUPTI_INJECTION']['STEP4']['CMD2_1'] % (sample, sample, sample)
                    # step4_point = case_config['CUPTI_INJECTION']['STEP4']['CHECK_POINT']
                    logname4 = log_path + '/step4_%s.log' % sample
                    check_result(step4_cmd, 'step4_injection_%s' % step4_cmd, logname4, result, check_point, flag=2)
            else:
                logger.info('the sample is not exist, please check it')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_injection.json' % case_config['global']['env']['INJECTION_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not run this case if cuda version more than 11.7')
        return WAIVED


def cupti_library():
    result = {}
    passed, failed = 0, 0
    if installer == 'runfile':
        cmd1 = 'cd %s/extras/CUPTI/lib64; ls libcupti.so.20*' % base_path
    else:
        cmd1 = 'cd %s/lib64; ls libcupti.so.20*' % base_path
    out1 = run_loc_cmd(cmd1)
    cupti = out1['output']
    print(cupti)
    log_name = case_config['CUPTI_LIBRARY']['LOG_NAME']
    log_path = case_config['global']['env']['LIBRARY_LOG_PATH']
    mkdir(log_path)
    arch = case_config['global']['env']['PLATFORM'].lower()
    if case_config['global']['env']['INSTALLER'] == 'runfile':
        check_path = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_PATH']
    else:
        check_path = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_PATH1']
    if arch == 'x86':
        check_list = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_X86'].split(',')
    elif arch == 'power':
        check_list = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_P9'].split(',')
    elif arch == 'arm':
        check_list = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_ARM'].split(',')
        if cuda_short_version > '12.5':
            check_list = case_config['CUPTI_LIBRARY']['STEP1']['CHECK_ARM1'].split(',')
    else:
        logger.error('please give correct platform to run case, exit......')
        exit()
    # check_list.append(cupti_dict['%s' % cuda_short_version])
    check_list.append(cupti)
    cmd = 'cd %s; ls' % check_path
    for i in check_list:
        check_result(cmd, 'step1_check_%s' % i, log_name, result, i)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_library.json' % case_config['global']['env']['LIBRARY_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_static():
    if platform == 'arm' and cuda_short_version < '12.6':
        logger.info('support this case since 12.6 on ARM')
    else:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_STATIC']['LOG_NAME']
        log_path = case_config['global']['env']['STATIC_LOG_PATH']
        mkdir(log_path)
        # prepare needed file
        if cuda_short_version < '11.3':
            cmd = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD'] % (
                user, password, base_url, 'cupti_static', 'Makefile_autorange_profiling_static')
        else:
            cmd = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD'] % (
                user, password, base_url, 'cupti_static', 'Makefile_autorange_profiling_static_new')
        cmd2 = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD1'] % (
            user, password, base_url, 'cupti_static', 'Makefile_cupti_query_static')
        check_result(cmd, 'we prepare the file----%s' % cmd, log_name, result)
        check_result(cmd2, 'we prepare the file----%s' % cmd2, log_name, result)
        if cuda_short_version < '11.0':
            cmd1 = case_config['CUPTI_STATIC']['PREPARE_FILE']['CMD1'] % (
                user, password, base_url, 'cupti_static', 'Makefile_activity_trace_static')
            check_result(cmd1, 'we prepare the file----%s' % cmd1, log_name, result)
        # prepare env
        sm = get_sm()
        pre_cmd2 = case_config['CUPTI_STATIC']['PREPARE']['CMD2']
        pre_cmd3 = case_config['CUPTI_STATIC']['PREPARE']['CMD3']
        pre_cmd4 = case_config['CUPTI_STATIC']['PREPARE']['CMD4']
        for cmd_pre in [pre_cmd4, pre_cmd2, pre_cmd3]:
            run_loc_cmd(cmd_pre)
        # step1
        if cuda_short_version >= '11.1':
            logger.info('now there is no this cupti sample')
        else:
            cmd1 = case_config['CUPTI_STATIC']['CMD1']
            check_result(cmd1, 'step1---%s' % cmd1, log_name, result)
        # step2 run cupti_query
        if cuda_short_version < '13.0':
            cmd2 = case_config['CUPTI_STATIC']['CMD2']
            if sm > 7.0:
                check_result(cmd2, 'run-step3-------%s' % cmd2, log_name, result, flag=1)
            else:
                check_result(cmd2, 'run-step3-------%s' % cmd2, log_name, result)
        if cuda_short_version <= '11.2':
            cmd3 = case_config['CUPTI_STATIC']['CMD3']
        else:
            cmd3 = case_config['CUPTI_STATIC']['CMD4']
        if sm < 6.0:
            check_result(cmd3, 'run-step3-------%s' % cmd3, log_name, result, flag=1)
        else:
            check_result(cmd3, 'run-step3-------%s' % cmd3, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_static.json' % case_config['global']['env']['STATIC_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_guard():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_GUARD']['LOG_NAME']
    log_path = case_config['global']['env']['GUARD_LOG_PATH']
    mkdir(log_path)
    if cuda_short_version <= '11.4':
        if installer == 'runfile':
            cmd1 = 'cd %s/extras/CUPTI/lib64; ls libcupti.so.20*' % base_path
        else:
            cmd1 = 'cd %s/lib64; ls libcupti.so.20*' % base_path
        out1 = run_loc_cmd(cmd1)
        cupti = out1['output']
        arch = case_config['global']['env']['PLATFORM'].lower()
        check_point1 = case_config['CUPTI_GUARD']['CHECK_POINT1']
        check_point2 = case_config['CUPTI_GUARD']['CHECK_POINT2']
        check_point3 = case_config['CUPTI_GUARD']['CHECK_POINT3']
        if case_config['global']['env']['INSTALLER'] == 'runfile':
            check_path = case_config['CUPTI_GUARD']['CHECK_PATH']
        else:
            check_path = case_config['CUPTI_GUARD']['CHECK_PATH1']
        if arch == 'x86':
            check_list = case_config['CUPTI_GUARD']['CHECK_X86'].split(',')
        elif arch == 'power' or arch == 'arm':
            check_list = case_config['CUPTI_GUARD']['CHECK_P9'].split(',')
        check_list.append(cupti)
        if case_config['global']['env']['CUDA_SHORT_VERSION'] == '10.2' or case_config['global']['env'][
                'CUDA_SHORT_VERSION'] == '10.1':
            for i in check_list:
                cmd1 = "cd %s; strings %s | grep -i %s" % (check_path, i, check_point1)
                check_result(cmd1, 'check_%s_%s' % (i, check_point1), log_name, result)
                cmd2 = "cd %s; strings %s | grep -i %s" % (check_path, i, check_point2)
                check_result(cmd2, 'check_%s_%s' % (i, check_point2), log_name, result)
                cmd3 = "cd %s; strings %s | grep %s" % (check_path, i, check_point3)
                check_result(cmd3, 'check_%s_%s' % (i, check_point3), 'failed', 'passed', log_name, result)
        elif case_config['global']['env']['CUDA_SHORT_VERSION'] > '10.2':
            for i in check_list:
                cmd1 = "cd %s; strings %s | grep -i %s" % (check_path, i, check_point1)
                check_result(cmd1, 'check_%s_%s' % (i, check_point1), 'passed', 'failed', log_name, result)
                cmd2 = "cd %s; strings %s | grep -i %s" % (check_path, i, check_point2)
                check_result(cmd2, 'check_%s_%s' % (i, check_point2), log_name, result)
                cmd3 = "cd %s; strings %s | grep %s" % (check_path, i, check_point3)
                check_result(cmd3, 'check_%s_%s' % (i, check_point3), log_name, result)
        else:
            logger.info('please give the specify cuda version to check')
    else:
        '''
        prepare_cupti()
        cmd1 = 'cd %s; tar zxvf %s' % (download_to_path, package)
        check_result(cmd1, 'extract package--%s' % package, log_name, result)
        '''
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        cmd2 = case_config['CUPTI_GUARD']['CMD'] % cupti_target_path
        check_point = case_config['CUPTI_GUARD']['CHECK_POINT10']
        print(cmd2)
        out = run_loc_cmd(cmd2)
        if out['output'] == '':
            print('-------------------------------+++++++++++++++++++++++++++++++++')
            result['run--{}'.format(cmd2)] = 'passed'
            logger.info('we run the command------"{}" passed, because the output is empty'.format(cmd2))
        else:
            if check_point not in out['output']:
                result['run--{}'.format(cmd2)] = 'failed'
                logger.error('we run the command------"{}" failed, because "{}"  not in output'.format(cmd2, check_point))
            else:
                result['run--{}'.format(cmd2)] = 'passed'
                logger.info('we run the command------"{}" passed, because "{}" is in output'.format(cmd2, check_point))
        save_log(log_name, cmd2, 'run cupti guard by {}'.format(cmd2), out.output)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_guard.json' % case_config['global']['env']['GUARD_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_trace():
    result = {}
    passed, failed = 0, 0
    # prepare cupti dvs package
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare sample
    prepare_cmd1 = case_config['CUPTI_TRACE']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['CUPTI_TRACE']['PREPARE']['CMD2']
    sample_list = case_config['CUPTI_TRACE']['SAMPLE_LIST'].split(',')
    for sample in sample_list:
        cmd1 = prepare_cmd1 % (sample_0_path, sample)
        run_test_cmd(cmd1)
        cmd = prepare_cmd2 % (sample, cupti_target_path)
        print(cmd)
        if os.path.exists('%s/trace_checker_data_files/%s' % (cupti_target_path, sample)):
            logger.info('no need copy sample')
        else:
            logger.info('copy the sample by {}'.format(cmd))
            run_loc_cmd(cmd)
    log_name = case_config['CUPTI_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['TRACE_LOG_PATH']
    mkdir(log_path)
    # run step1:
    sample_list = case_config['CUPTI_TRACE']['SAMPLE_LIST'].split(',')
    for sample in sample_list:
        if platform.lower() == 'x86':
            cmd = case_config['CUPTI_TRACE']['STEP1']['CMD1'] % sample
            cmd1 = case_config['CUPTI_TRACE']['STEP1']['CMD1_1'] % sample
        elif platform.lower() == 'arm':
            cmd = case_config['CUPTI_TRACE']['STEP1']['CMD3'] % sample
            cmd1 = case_config['CUPTI_TRACE']['STEP1']['CMD3_1'] % sample
        else:
            if cuda_short_version <= '11.4':
                cmd = case_config['CUPTI_TRACE']['STEP1']['CMD2'] % sample
                cmd1 = case_config['CUPTI_TRACE']['STEP1']['CMD2_1'] % sample
            else:
                cmd = case_config['CUPTI_TRACE']['STEP1']['CMD2-1'] % sample
                cmd1 = case_config['CUPTI_TRACE']['STEP1']['CMD2-1_1'] % sample
        check_result(cmd, 'step1_trace_off-on_%s---by %s' % (sample, cmd), log_name, result, 'FAILED', flag=2)
        check_result(cmd1, 'step1_trace_on-off_%s---by %s' % (sample, cmd1), log_name, result, 'FAILED', flag=2)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_trace.json' % case_config['global']['env']['TRACE_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_trace_coverage():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['TRACE_COVERAGE_LOG_PATH']
    mkdir(log_path)
    # prepare cupti dvs package
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare json file
    cmd1 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD1']
    cmd2 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD2']
    prepare_out1 = run_loc_cmd(cmd1)
    logger.info('we will copy host/target to bin')
    save_log(log_name, cmd1, 'prepare_step1', prepare_out1['output'])
    sample_list1 = prepare_out1['output'].split('\n')
    prepare_out2 = run_loc_cmd(cmd2)
    save_log(log_name, cmd2, 'prepare_step2', prepare_out2['output'])
    sample_list2 = prepare_out2['output']
    sample_list = [i for i in sample_list1 if i not in sample_list2]
    if vgpu != 'none':
        sample_list = case_config['CUPTI_TRACE_COVERAGE']['SAMPLE_LIST'].split(',')
        if run_uvm_sample() is True:
            sample_list.append('UnifiedMemoryStreams')
    else:
        if platform == 'arm':
            # vectorAddMMAP,cuHook not support on arm
            sample_list = case_config['CUPTI_TRACE_COVERAGE']['SAMPLE_LIST2'].split(',')
        else:
            sample_list = case_config['CUPTI_TRACE_COVERAGE']['SAMPLE_LIST1'].split(',')
    glibc_version = get_glic_version()
    logger.info('glibc version is {}'.format(glibc_version))
    if glibc_version > '2.33' or cuda_short_version > '12.9':
        sample_list = [i for i in sample_list if i != 'cuHook']
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    if platform.lower() == 'x86':
        cmd3 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD3']
        prepare_out3 = run_loc_cmd(cmd3)
        save_log(log_name, cmd3, 'prepare_step3', prepare_out3['output'])
        logger.info('we will generate the run json file')
        for i in sample_list:
            if i in [
                    'systemWideAtomics',
                    'UnifiedMemoryPerf',
                    'UnifiedMemoryStreams',
                    'vectorAddMMAP',
                    'streamOrderedAllocationIPC',
                    'streamOrderedAllocation',
                    'memMapIPCDrv',
                    'graphMemoryNodes',
                    'graphMemoryFootprint',
                    'conjugateGradientUM',
                    'conjugateGradientMultiBlockCG'
            ]:
                if vgpu != 'none':
                    if run_uvm_sample() is False:
                        logger.info('we do not support the sample--"%s" when UVM is not support or disabled on vgpu' % i)
                    else:
                        dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
                else:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
            elif 'cdp' in i:
                if vgpu != 'none':
                    logger.info('we do not support cdp sample ---"%s" in vgpu' % i)
                else:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['<70'], "exe": "%s" % i})
            elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                dict1["tests"].append({"name": "%s" % i, "CC": ['>=80'], "exe": "%s" % i})
            elif 'immaTensorCoreGemm' in i:
                dict1["tests"].append({"name": "%s" % i, "CC": ['>=72'], "exe": "%s" % i})
            elif i in [
                    'simpleAttributes',
                    'simpleCUFFT_2d_MGPU',
                    'simpleCUFFT_MGPU',
                    'simpleP2P',
                    'conjugateGradientMultiDeviceCG',
                    'cudaCompressibleMemory',
                    'cuHook',
                    'streamOrderedAllocationP2P'
            ]:
                pass
            elif 'cdp' in i:
                pass
            elif i in [
                    'bicubicTexture',
                    'simpleGLES',
                    'bilateralFilter',
                    'simpleGLES_EGLOutput',
                    'simpleVulkan',
                    'bindlessTexture',
                    'boxFilter',
                    'fluidsGL',
                    'FunctionPointers',
                    'imageDenoising',
                    'simpleVulkanMMAP',
                    'Mandelbrot',
                    'marchingCubes',
                    'nbody',
                    'oceanFFT',
                    'particles',
                    'postProcessGL',
                    'randomFog',
                    'recursiveGaussian',
                    'simpleCUDA2GL',
                    'simpleGL',
                    'simpleTexture3D',
                    'smokeParticles',
                    'SobelFilter',
                    'volumeFiltering',
                    'volumeRender',
                    'vulkanImageCUDA'
            ]:
                pass
            elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
            else:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (case_config['global']['env']['CUPTI_RUN_PATH1'] + '/' + 'test.json'))
    elif platform.lower() == 'power':
        if cuda_short_version <= '11.4':
            prepare_cmd4 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD4']
        else:
            prepare_cmd4 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD4_1']
        run_loc_cmd(prepare_cmd4)
        for i in sample_list:
            if 'cdp' in i:
                dict1["tests"].append({"name": "%s" % i, "CC": ["<70"], "exe": "%s" % i})
            elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                dict1["tests"].append({"name": "%s" % i, "CC": [">=80"], "exe": "%s" % i})
            elif 'immaTensorCoreGemm' in i:
                dict1["tests"].append({"name": "%s" % i, "CC": [">=72"], "exe": "%s" % i})
            elif i in [
                    'simpleAttributes',
                    'simpleCUFFT_2d_MGPU',
                    'simpleCUFFT_MGPU',
                    'simpleP2P',
                    'conjugateGradientMultiDeviceCG',
                    'cudaCompressibleMemory',
                    'cuHook'
            ]:
                pass
            elif i in [
                    'bicubicTexture',
                    'simpleGLES',
                    'bilateralFilter',
                    'simpleGLES_EGLOutput',
                    'simpleVulkan',
                    'bindlessTexture',
                    'boxFilter',
                    'fluidsGL',
                    'FunctionPointers',
                    'imageDenoising',
                    'simpleVulkanMMAP',
                    'Mandelbrot',
                    'marchingCubes',
                    'nbody',
                    'oceanFFT',
                    'particles',
                    'postProcessGL',
                    'randomFog',
                    'recursiveGaussian',
                    'simpleCUDA2GL',
                    'simpleGL',
                    'simpleTexture3D',
                    'smokeParticles',
                    'SobelFilter',
                    'volumeFiltering',
                    'volumeRender'
            ]:
                pass
            elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
            else:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        print(cupti_run_path)
        dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'test.json'))
    else:
        prepare_cmd5 = case_config['CUPTI_TRACE_COVERAGE']['PREPARE']['CMD5']
        run_loc_cmd(prepare_cmd5)
        for i in sample_list:
            if 'cdp' in i:
                dict1["tests"].append({"name": "%s" % i, "CC": ['<70'], "exe": "%s" % i})
            elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                dict1["tests"].append({"name": "%s" % i, "CC": ['>=80'], "exe": "%s" % i})
            elif 'immaTensorCoreGemm' in i:
                dict1["tests"].append({"name": "%s" % i, "CC": ['>=72'], "exe": "%s" % i})
            elif i in [
                    'simpleAttributes',
                    'simpleCUFFT_2d_MGPU',
                    'simpleCUFFT_MGPU',
                    'simpleP2P',
                    'conjugateGradientMultiDeviceCG',
                    'cudaCompressibleMemory',
                    'cuHook'
            ]:
                pass
            elif i in [
                    'bicubicTexture',
                    'simpleGLES',
                    'bilateralFilter',
                    'simpleGLES_EGLOutput',
                    'simpleVulkan',
                    'bindlessTexture',
                    'boxFilter',
                    'fluidsGL',
                    'FunctionPointers',
                    'imageDenoising',
                    'simpleVulkanMMAP',
                    'Mandelbrot',
                    'marchingCubes',
                    'nbody',
                    'oceanFFT',
                    'particles',
                    'postProcessGL',
                    'randomFog',
                    'recursiveGaussian',
                    'simpleCUDA2GL',
                    'simpleGL',
                    'simpleTexture3D',
                    'smokeParticles',
                    'SobelFilter',
                    'volumeFiltering',
                    'volumeRender'
            ]:
                pass
            elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
            else:
                dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        dict_to_json(dict2, '%s' % (case_config['global']['env']['CUPTI_RUN_PATH3'] + '/' + 'test.json'))
    # run step1
    logger.info('we will run sample coverage')
    if cuda_short_version <= '11.4':
        if platform.lower() == 'x86':
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD2']
        elif platform.lower() == 'power':
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD2']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD3']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD4']
        else:
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD3']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD5']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD6']
    else:
        if platform.lower() == 'x86':
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD1_1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD1_1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD2_1']
        elif platform.lower() == 'power':
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD2_1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD3_1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD4_1']
        else:
            cmd = case_config['CUPTI_TRACE_COVERAGE']['STEP1']['CMD3_1']
            cmd1 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD5_1']
            cmd2 = case_config['CUPTI_TRACE_COVERAGE']['STEP2']['CMD6_1']
    check_result(cmd, 'step1_trace by----%s' % cmd, log_name, result)
    # run steps2 ,get passed, failed
    out1 = run_loc_cmd(cmd1)
    if out1.succeeded:
        result['failed'] = int(out1['output'])
    else:
        logger.info('please check the result')
    out2 = run_loc_cmd(cmd2)
    if out2.succeeded:
        result['passed'] = int(out2['output'])
    else:
        logger.info('please check the result')
    dict_to_json(result, '%s/cupti_trace_coverage.json' % case_config['global']['env']['TRACE_COVERAGE_LOG_PATH'])
    return (PASSED if (result['failed'] == 0 and result['passed'] != 0) else FAILED)


def cupti_trace_coverage_hes():
    sm = get_sm()
    if cuda_short_version >= '13.0' and sm > 9.0 and vgpu.lower() == 'none':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_COVERAGE_HES']['LOG_NAME']
        log_path = case_config['global']['env']['TRACE_COVERAGE_HES_LOG_PATH']
        mkdir(log_path)
        # prepare cupti dvs package
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        # prepare json file
        cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD1']
        cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD2']
        prepare_out1 = run_loc_cmd(cmd1)
        logger.info('we will copy host/target to bin')
        save_log(log_name, cmd1, 'prepare_step1', prepare_out1['output'])
        sample_list1 = prepare_out1['output'].split('\n')
        prepare_out2 = run_loc_cmd(cmd2)
        save_log(log_name, cmd2, 'prepare_step2', prepare_out2['output'])
        sample_list2 = prepare_out2['output']
        sample_list = [i for i in sample_list1 if i not in sample_list2]
        if vgpu != 'none':
            sample_list = case_config['CUPTI_TRACE_COVERAGE_HES']['SAMPLE_LIST'].split(',')
            if run_uvm_sample() is True:
                sample_list.append('UnifiedMemoryStreams')
        else:
            if platform == 'arm':
                # vectorAddMMAP,cuHook not support on arm
                sample_list = case_config['CUPTI_TRACE_COVERAGE_HES']['SAMPLE_LIST2'].split(',')
            else:
                sample_list = case_config['CUPTI_TRACE_COVERAGE_HES']['SAMPLE_LIST1'].split(',')
        glibc_version = get_glic_version()
        logger.info('glibc version is {}'.format(glibc_version))
        if glibc_version > '2.33' or cuda_short_version > '12.9':
            sample_list = [i for i in sample_list if i != 'cuHook']
        if cuda_short_version <= '11.4':
            dict1 = {"tests": []}
            dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
        else:
            dict1 = {"tests": []}
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        if platform.lower() == 'x86':
            cmd3 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD3']
            prepare_out3 = run_loc_cmd(cmd3)
            save_log(log_name, cmd3, 'prepare_step3', prepare_out3['output'])
            logger.info('we will generate the run json file')
            for i in sample_list:
                if i in [
                        'systemWideAtomics',
                        'UnifiedMemoryPerf',
                        'UnifiedMemoryStreams',
                        'vectorAddMMAP',
                        'streamOrderedAllocationIPC',
                        'streamOrderedAllocation',
                        'memMapIPCDrv',
                        'graphMemoryNodes',
                        'graphMemoryFootprint',
                        'conjugateGradientUM',
                        'conjugateGradientMultiBlockCG'
                ]:
                    if vgpu != 'none':
                        if run_uvm_sample() is False:
                            logger.info('we do not support the sample--"%s" when UVM is not support or disabled on vgpu' % i)
                        else:
                            dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
                    else:
                        dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
                elif 'cdp' in i:
                    if vgpu != 'none':
                        logger.info('we do not support cdp sample ---"%s" in vgpu' % i)
                    else:
                        dict1["tests"].append({"name": "%s" % i, "CC": ['<70'], "exe": "%s" % i})
                elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['>=80'], "exe": "%s" % i})
                elif 'immaTensorCoreGemm' in i:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['>=72'], "exe": "%s" % i})
                elif i in [
                        'simpleAttributes',
                        'simpleCUFFT_2d_MGPU',
                        'simpleCUFFT_MGPU',
                        'simpleP2P',
                        'conjugateGradientMultiDeviceCG',
                        'cudaCompressibleMemory',
                        'cuHook',
                        'streamOrderedAllocationP2P'
                ]:
                    pass
                elif 'cdp' in i:
                    pass
                elif i in [
                        'bicubicTexture',
                        'simpleGLES',
                        'bilateralFilter',
                        'simpleGLES_EGLOutput',
                        'simpleVulkan',
                        'bindlessTexture',
                        'boxFilter',
                        'fluidsGL',
                        'FunctionPointers',
                        'imageDenoising',
                        'simpleVulkanMMAP',
                        'Mandelbrot',
                        'marchingCubes',
                        'nbody',
                        'oceanFFT',
                        'particles',
                        'postProcessGL',
                        'randomFog',
                        'recursiveGaussian',
                        'simpleCUDA2GL',
                        'simpleGL',
                        'simpleTexture3D',
                        'smokeParticles',
                        'SobelFilter',
                        'volumeFiltering',
                        'volumeRender',
                        'vulkanImageCUDA'
                ]:
                    pass
                elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
                else:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            dict_to_json(dict2, '%s' % (case_config['global']['env']['CUPTI_RUN_PATH1'] + '/' + 'test.json'))
        elif platform.lower() == 'power':
            if cuda_short_version <= '11.4':
                prepare_cmd4 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD4']
            else:
                prepare_cmd4 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD4_1']
            run_loc_cmd(prepare_cmd4)
            for i in sample_list:
                if 'cdp' in i:
                    dict1["tests"].append({"name": "%s" % i, "CC": ["<70"], "exe": "%s" % i})
                elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                    dict1["tests"].append({"name": "%s" % i, "CC": [">=80"], "exe": "%s" % i})
                elif 'immaTensorCoreGemm' in i:
                    dict1["tests"].append({"name": "%s" % i, "CC": [">=72"], "exe": "%s" % i})
                elif i in [
                        'simpleAttributes',
                        'simpleCUFFT_2d_MGPU',
                        'simpleCUFFT_MGPU',
                        'simpleP2P',
                        'conjugateGradientMultiDeviceCG',
                        'cudaCompressibleMemory',
                        'cuHook'
                ]:
                    pass
                elif i in [
                        'bicubicTexture',
                        'simpleGLES',
                        'bilateralFilter',
                        'simpleGLES_EGLOutput',
                        'simpleVulkan',
                        'bindlessTexture',
                        'boxFilter',
                        'fluidsGL',
                        'FunctionPointers',
                        'imageDenoising',
                        'simpleVulkanMMAP',
                        'Mandelbrot',
                        'marchingCubes',
                        'nbody',
                        'oceanFFT',
                        'particles',
                        'postProcessGL',
                        'randomFog',
                        'recursiveGaussian',
                        'simpleCUDA2GL',
                        'simpleGL',
                        'simpleTexture3D',
                        'smokeParticles',
                        'SobelFilter',
                        'volumeFiltering',
                        'volumeRender'
                ]:
                    pass
                elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
                else:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            print(cupti_run_path)
            dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'test.json'))
        else:
            prepare_cmd5 = case_config['CUPTI_TRACE_COVERAGE_HES']['PREPARE']['CMD5']
            run_loc_cmd(prepare_cmd5)
            for i in sample_list:
                if 'cdp' in i:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['<70'], "exe": "%s" % i})
                elif i in ['dmmaTensorCoreGemm', 'tf32TensorCoreGemm', 'bf16TensorCoreGemm']:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['>=80'], "exe": "%s" % i})
                elif 'immaTensorCoreGemm' in i:
                    dict1["tests"].append({"name": "%s" % i, "CC": ['>=72'], "exe": "%s" % i})
                elif i in [
                        'simpleAttributes',
                        'simpleCUFFT_2d_MGPU',
                        'simpleCUFFT_MGPU',
                        'simpleP2P',
                        'conjugateGradientMultiDeviceCG',
                        'cudaCompressibleMemory',
                        'cuHook'
                ]:
                    pass
                elif i in [
                        'bicubicTexture',
                        'simpleGLES',
                        'bilateralFilter',
                        'simpleGLES_EGLOutput',
                        'simpleVulkan',
                        'bindlessTexture',
                        'boxFilter',
                        'fluidsGL',
                        'FunctionPointers',
                        'imageDenoising',
                        'simpleVulkanMMAP',
                        'Mandelbrot',
                        'marchingCubes',
                        'nbody',
                        'oceanFFT',
                        'particles',
                        'postProcessGL',
                        'randomFog',
                        'recursiveGaussian',
                        'simpleCUDA2GL',
                        'simpleGL',
                        'simpleTexture3D',
                        'smokeParticles',
                        'SobelFilter',
                        'volumeFiltering',
                        'volumeRender'
                ]:
                    pass
                elif i in ['simpleAssert', 'simpleAssert_nvrtc']:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i, "verify": {"type": []}})
                else:
                    dict1["tests"].append({"name": "%s" % i, "exe": "%s" % i})
            if cuda_short_version <= '11.4':
                dict2.update(dict1)
            else:
                dict2["tracing-injection"].update(dict1)
            dict_to_json(dict2, '%s' % (case_config['global']['env']['CUPTI_RUN_PATH3'] + '/' + 'test.json'))
        # run step1
        logger.info('we will run sample coverage')
        if cuda_short_version <= '11.4':
            if platform.lower() == 'x86':
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD1']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD1']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD2']
            elif platform.lower() == 'power':
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD2']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD3']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD4']
            else:
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD3']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD5']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD6']
        else:
            if platform.lower() == 'x86':
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD1_1']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD1_1']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD2_1']
            elif platform.lower() == 'power':
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD2_1']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD3_1']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD4_1']
            else:
                cmd = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP1']['CMD3_1']
                cmd1 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD5_1']
                cmd2 = case_config['CUPTI_TRACE_COVERAGE_HES']['STEP2']['CMD6_1']
        check_result(cmd, 'step1_trace by----%s' % cmd, log_name, result)
        # run steps2 ,get passed, failed
        out1 = run_loc_cmd(cmd1)
        if out1.succeeded:
            result['failed'] = int(out1['output'])
        else:
            logger.info('please check the result')
        out2 = run_loc_cmd(cmd2)
        find_hes1 = search_keyword_in_file(log_path, 'Requested HES to be enabled')
        find_hes2 = search_keyword_in_file(log_path, 'Enabled HES tracing in CUPTI')
        if out2.succeeded and find_hes1 and find_hes2:
            result['passed'] = int(out2['output'])
        else:
            logger.info('please check the result')
        dict_to_json(result, '%s/cupti_trace_coverage_hes.json' % case_config['global']['env']['TRACE_COVERAGE_HES_LOG_PATH'])
        return (PASSED if (result['failed'] == 0 and result['passed'] != 0) else FAILED)
    else:
        logger.info('support this case since cuda 13.0 and blackwell++')
        return WAIVED


def cupti_nvprof():
    if vgpu == 'none':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_NVPROF']['LOG_NAME']
        log_path = case_config['global']['env']['NVPROF_LOG_PATH']
        mkdir(log_path)
        # prepare binary
        cmd = case_config['CUPTI_NVPROF']['PREPARE']['CMD']
        cmd1 = case_config['CUPTI_NVPROF']['PREPARE']['CMD1']
        cmd2 = case_config['CUPTI_NVPROF']['PREPARE']['CMD2']
        check_result(cmd, 'prepare_bak by %s' % cmd, log_name, result)
        check_result(cmd1, 'prepare_change by %s' % cmd1, log_name, result)
        check_result(cmd2, 'prepare_make by ---%s' % cmd2, log_name, result)
        # run case
        step_cmd = case_config['CUPTI_NVPROF']['STEP1']['CMD']
        step_cmd1 = case_config['CUPTI_NVPROF']['STEP1']['CMD1']
        out = run_loc_cmd(step_cmd)
        save_log(log_name, step_cmd, 'step1_0', out['output'])
        out1 = run_loc_cmd(step_cmd1)
        save_log(log_name, step_cmd1, 'step1_1', out1['output'])
        print(step_cmd)
        print(step_cmd1)
        print(out1)
        print(out)
        print(out['output'])
        print('====================')
        print(out1['output'])
        print('----------------------------------------------')
        if out.succeeded and out1.succeeded and out['output'] == out1['output']:
            result['step1'] = 'passed'
            logger.info('we run the step1 passed')
        else:
            result['step1'] = 'failed'
            logger.info('we run the step1 failed')
        exit()
        # recovery the env
        re_cmd = case_config['CUPTI_NVPROF']['RECOVERY']['CMD']
        re_cmd1 = case_config['CUPTI_NVPROF']['RECOVERY']['CMD1']
        check_result(re_cmd, 'recovery_file by %s' % re_cmd, log_name, result)
        check_result(re_cmd1, 'recovery_make_clean by %s' % re_cmd1, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_nvprof.json' % case_config['global']['env']['NVPROF_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we waive this case in vgpu env')
        return WAIVED


def build_openacc(log_name, result):
    pgi_version = case_config['global']['env']['PGI_VERSION']
    if vgpu != 'none' and platform == 'x86':
        if is_empty_dir(f'/opt/nvidia/hpc_sdk/Linux_x86_64/{pgi_version}') is True:
            # create '/opt/nvidia'
            prepare_cmd5 = case_config['CUPTI_OPENACC']['PREPARE']['CMD5']
            run_test_cmd(prepare_cmd5)
            time.sleep(6)
            # mount the HPC to vGPU vm
            cmd = 'cat /etc/issue'
            out_os = run_loc_cmd(cmd)
            if 'Ubuntu' in out_os['output']:
                prepare = 'apt-get install nfs-kernel-server -y'
            else:
                prepare = 'yum -y install nfs-utils'
            prepare_cmd4 = case_config['CUPTI_OPENACC']['PREPARE']['CMD4'] % prepare
            run_test_cmd(prepare_cmd4)
    if platform == 'x86':
        platform_str = 'x86_64'
    elif platform == 'power':
        platform_str = 'ppc64le'
    else:
        platform_str = 'aarch64'
    # prepare get the openacc version
    prepare_cmd1 = case_config['CUPTI_OPENACC']['PREPARE']['CMD1']
    check_result(prepare_cmd1, 'change write/read permission by %s' % prepare_cmd1, log_name, result)
    if pgi_version == '22.11':
        cuda_build = '11.8'
    elif pgi_version == '22.7' or pgi_version == '22.5':
        cuda_build = '11.7'
    elif pgi_version == '23.1' or pgi_version == '22.3':
        cuda_build = '12.0'
    elif pgi_version == '23.5':
        cuda_build = '12.1'
    else:
        prepare_cmd2 = case_config['CUPTI_OPENACC']['PREPARE']['CMD2'] % (platform_str, pgi_version)
        cmd2_out = run_loc_cmd(prepare_cmd2)
        cuda_build = cmd2_out['output']
    if platform == 'arm':
        prepare_cmd6 = case_config['CUPTI_OPENACC']['PREPARE']['CMD6']
        cmd6_out = run_loc_cmd(prepare_cmd6)
        pattern2 = cmd6_out['output']
        content2 = '        PGCPP_FLAGS += -mp=nonuma'
        replace_cmd1 = 'cd %s/extras/CUPTI/samples/openacc_trace; sed -i "s/%s/%s/g" Makefile' % (base_path, pattern2, content2)
        check_result(replace_cmd1, 'replace the line  %s on ARM' % replace_cmd1, log_name, result)
    prepare_cmd3 = case_config['CUPTI_OPENACC']['PREPARE']['CMD3']
    cmd3_out = run_loc_cmd(prepare_cmd3)
    pattern1 = cmd3_out['output']
    content1 = 'PGCPP_FLAGS = -acc -ta=nvidia:cuda%s -Mcuda=nordc -pgf90libs' % cuda_build
    if cuda_short_version >= '12.9':
        content1 = 'PGCPP_FLAGS = -acc -pgf90libs'
    replace_cmd = 'cd %s/extras/CUPTI/samples/openacc_trace; sed -i "s/%s/%s/g" Makefile' % (base_path, pattern1, content1)
    check_result(replace_cmd, 'replace the line %s' % replace_cmd, log_name, result)
    step1_cmd4 = case_config['CUPTI_OPENACC']['STEP1']['CMD4'] % (platform_str, pgi_version)
    check_result(step1_cmd4, 'change the lib by %s' % step1_cmd4, log_name, result)
    # build the openacc sample
    if cuda_short_version < '11.1':
        step2_cmd1 = case_config['CUPTI_OPENACC']['STEP2']['CMD1'] % pgi_version
    else:
        step2_cmd1 = case_config['CUPTI_OPENACC']['STEP2']['CMD2'] % (platform_str, pgi_version)
    check_result(step2_cmd1, 'build_openacc by---%s' % step2_cmd1, log_name, result)


def cupti_spec():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_SPECFIC']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_SPEC_LOG_PATH']
    mkdir(log_path)
    sm = get_sm()
    print('************************')
    print(sm)
    cmd1 = case_config['CUPTI_SPECFIC']['PREPARE']['CMD1']
    run_loc_cmd(cmd1)
    root_dir = '%s/extras/CUPTI/samples/' % base_path
    dir_list = os.listdir(root_dir)
    sudo_excute = case_config['global']['env']['SUDO_EXECUTE']
    ld_path = case_config['global']['env']['LD_PATH']
    print(dir_list)
    extension_path_list = [root_dir + i for i in dir_list if 'extension' in i]
    sample_path_list = [root_dir + i for i in dir_list if 'extension' not in i]
    cmd = 'cd %s/src/profilerhost_util/; %s make clean; %s make' % (extension_path_list[0], sudo_excute, sudo_excute)
    check_result(cmd, 'prepare_build-extensions by %s' % cmd, log_name, result)
    for i in sample_path_list:
        if 'openacc' in i:
            pass
            # build_openacc(log_name, result)
        else:
            cmd1 = 'cd %s; %s make clean; %s make' % (i, sudo_excute, sudo_excute)
            check_result(cmd1, 'build-sample by %s' % cmd1, log_name, result)
    # prepare env
    sm_cmd = case_config['CUPTI_SPECFIC']['PREPARE']['CMD']
    check_7 = case_config['CUPTI_SPECFIC']['PREPARE']['CHECK_SM1']
    check_5 = case_config['CUPTI_SPECFIC']['PREPARE']['CHECK_SM2']
    check_5_2 = case_config['CUPTI_SPECFIC']['PREPARE']['CHECK_SM3']
    platform = case_config['global']['env']['PLATFORM']

    # run step1, change the restrict value from 0 to 1

    def change_restrict(label):
        step1_cmd1 = case_config['CUPTI_SPECFIC']['STEP1']['CMD1']
        step1_cmd2 = case_config['CUPTI_SPECFIC']['STEP1']['CMD2']
        step1_cmd3 = case_config['CUPTI_SPECFIC']['STEP1']['CMD3']
        step1_cmd4 = case_config['CUPTI_SPECFIC']['STEP1']['CMD4'] % label
        step1_cmd5 = case_config['CUPTI_SPECFIC']['STEP1']['CMD5']
        step1_out1 = run_loc_cmd(step1_cmd1)
        save_log(log_name, step1_cmd1, 'step1_1', step1_out1['output'])
        if step1_out1.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd1)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd1)
        step1_out2 = run_loc_cmd(step1_cmd2)
        save_log(log_name, step1_cmd2, 'step1_2', step1_out2['output'])
        if step1_out2.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd2)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd2)
        step1_out3 = run_loc_cmd(step1_cmd3)
        step1_check_point = case_config['CUPTI_SPECFIC']['STEP1']['CHECK_POINT']
        save_log(log_name, step1_cmd3, 'step1_3', step1_out3['output'])
        if step1_out3.succeeded is False and step1_check_point not in step1_out3['output']:
            logger.info('we execute the command--%s successful' % step1_cmd3)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd3)
        step1_out4 = run_loc_cmd(step1_cmd4)
        save_log(log_name, step1_cmd4, 'step1_4', step1_out4['output'])
        if step1_out4.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd4)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd4)
        step1_out5 = run_loc_cmd(step1_cmd5)
        save_log(log_name, step1_cmd5, 'step1_5', step1_out5['output'])
        if step1_out5.succeeded and step1_out5['output'] == label:
            logger.info('we execute the command--%s successful' % step1_cmd5)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd5)
            exit()

    step1_cmd5 = case_config['CUPTI_SPECFIC']['STEP1']['CMD5']
    step1_out6 = run_loc_cmd(step1_cmd5)
    if step1_out6['output'] == '1':
        logger.info('now it is 1, no need to change it')
    else:
        change_restrict('1')
    # run step2, check the nvprof can't work under restrict mode
    if vgpu != 'none':
        logger.info('we do not support npvorf on vgpu, waive step2-nvprof testing')
    else:
        if sm < 7.5:
            cmd_list = case_config['CUPTI_SPECFIC']['STEP2']['CMD_LIST']
            step2_check = case_config['CUPTI_SPECFIC']['STEP2']['CHECK_POINT']
            i = 0
            for cmd in cmd_list:
                cmd6 = cmd.split('/')[-1]
                if 'out.pdm' not in cmd:
                    check_result(cmd, 'step2_%s_%s' % (i, cmd6), log_name, result, step2_check)
                else:
                    check_result(cmd, 'step2_%s_%s' % (i, cmd6), log_name, result, 'out.pdm')
                i = i + 1
        else:
            logger.info('we do not need run nvprof When the GPU SM more than 7.5')
    # run step3,check the cupti sample under restrict mode
    step3_cupti_query = case_config['CUPTI_SPECFIC']['STEP3']['CMD3']
    step3_nvlink_bandwidth = case_config['CUPTI_SPECFIC']['STEP3']['CMD6']
    step3_nvlink_bandwidth1 = case_config['CUPTI_SPECFIC']['STEP3']['CMD7']
    multi_gpu = case_config['CUPTI_SPECFIC']['STEP3']['CMD4']
    step3_check1 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT1']
    step3_check2 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT2']
    step3_check3 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT3']
    # run cupti_query
    out1 = run_loc_cmd(step3_cupti_query)
    cmd = step3_cupti_query
    cmd2 = cmd.split('/')[-1]
    save_log(log_name, cmd, 'step3_%s' % step3_cupti_query, out1['output'])
    if sm > 7.0:
        if out1.succeeded is True:
            result['step3_%s' % cmd2] = 'failed'
            logger.info('we run step3_%s failed' % cmd2)
        else:
            if step3_check1 in out1['output']:
                result['step3_%s' % cmd2] = 'passed'
                logger.info('we run step3_%s successful' % cmd2)
            else:
                result['step3_%s' % cmd2] = 'failed'
                logger.info('we run step3_%s  failed' % cmd)
    else:
        if out1.succeeded:
            result['step3_%s' % cmd2] = 'passed'
            logger.info('we run step3_%s  successful' % cmd)
        else:
            result['step3_%s' % cmd2] = 'failed'
            logger.info('we run step3_%s  failed' % cmd)
    # run event_multi_gpu
    cmd = "nvidia-smi -L | awk '{print NR}' | tail -n1"
    out = run_loc_cmd(cmd)
    print(out)
    if out['output'] != '1':
        if vgpu != 'none':
            logger.info('we do not run event_multi_gpu on vGPU')
        else:
            if sm < 7.5:
                pass
            else:
                check_result(multi_gpu, 'run--event_multi_gpu by %s' % multi_gpu, log_name, result, step3_check1, flag=1)
    else:
        logger.info(' there is only one gpu on the machine, do not run event_multi_gpu')
    # run nvlink_bandwidth
    if out['output'] == '1':
        logger.info('we will not run nvlink_bandwidth')
    else:
        if sm <= 7.0:
            check_result(step3_nvlink_bandwidth,
                         'run-nvlink--cpu_to_gpu by %s' % step3_nvlink_bandwidth,
                         log_name,
                         result,
                         step3_check2,
                         flag=1)
            check_result(step3_nvlink_bandwidth1,
                         'run-nvlink--cpu_to_gpu by %s' % step3_nvlink_bandwidth1,
                         log_name,
                         result,
                         step3_check2,
                         flag=1)
        else:
            check_result(step3_nvlink_bandwidth,
                         'run-nvlink--cpu_to_gpu by %s' % step3_nvlink_bandwidth,
                         log_name,
                         result,
                         step3_check3,
                         flag=1)
            check_result(step3_nvlink_bandwidth1,
                         'run-nvlink--cpu_to_gpu by %s' % step3_nvlink_bandwidth1,
                         log_name,
                         result,
                         step3_check2,
                         flag=1)
    nextgen_list = case_config['CUPTI_SPECFIC']['STEP3']['NEXTGEN_LIST'].split(',')
    legacy_list = case_config['CUPTI_SPECFIC']['STEP3']['LEGACY_LIST'].split(',')
    trace_list = case_config['CUPTI_SPECFIC']['STEP3']['TRACE_LIST'].split(',')

    def run_sample(sample_list, checkpoint=None):
        for i in sample_list:
            for j in sample_path_list:
                if i == j.split('/')[-1]:
                    if i == 'autorange_profiling':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, 'auto_range_profiling')
                    elif i == 'userrange_profiling':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, 'user_range_profiling')
                    elif i == 'cupti_nvtx':
                        cmd = 'cd %s; export NVTX_INJECTION64_PATH=%s/extras/CUPTI/lib64/libcupti.so; export LD_LIBRARY_PATH=%s; ./cupti_nvtx' % (
                            j, base_path, ld_path)
                    elif i == 'openacc_trace':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s/extras/CUPTI/samples/openacc_trace:%s; ./openacc_app' % (
                            j, base_path, ld_path)
                    else:
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, i)
                    if checkpoint:
                        check_result(cmd, 'run sample--%s' % i, log_name, result, checkpoint, flag=1)
                    else:
                        if i == 'cuda_memory_trace':
                            if vgpu != 'none':
                                check_point = 'operation not supported'
                                check_result(cmd, 'run sample--%s' % i, log_name, result, check_point, flag=1)
                            else:
                                check_result(cmd, 'run sample--%s' % i, log_name, result)
                        else:
                            check_result(cmd, 'run sample--%s' % i, log_name, result)

    step3_check1 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT1']
    step3_check2 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT2']
    step3_check3 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT3']
    step3_check4 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT4']
    step3_check5 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT5']
    if vgpu != 'none':
        run_sample(legacy_list, checkpoint=step3_check5)
    else:
        if sm <= 7.2:
            run_sample(legacy_list, checkpoint=step3_check2)
        else:
            print('===================================')
            print(step3_check4)
            print('++++++++++++++++++++++++++++++++++++')
            run_sample(legacy_list, checkpoint=step3_check4)
            print('++++++++++++++++++++++++++++++++++++')
            print(step3_check4)
    run_sample(nextgen_list, checkpoint=step3_check2)
    run_sample(trace_list)
    # run step4, change the restrict value from 0 to 1
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    change_restrict('0')
    dict_to_json(result1, '%s/cupti_spec.json' % case_config['global']['env']['CUPTI_SPEC_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_restrict():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_RESTRICT']['LOG_NAME']
    log_path = case_config['global']['env']['TRACE_RESTRICT_LOG_PATH']
    mkdir(log_path)

    # run step1, change the restrict value from 0 to 1

    def change_restrict(label):
        step1_cmd1 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD1']
        step1_cmd2 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD2']
        step1_cmd3 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD3']
        step1_cmd4 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD4'] % label
        step1_cmd5 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD5']
        step1_out1 = run_loc_cmd(step1_cmd1)
        save_log(log_name, step1_cmd1, 'step1_1', step1_out1['output'])
        if step1_out1.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd1)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd1)
            exit()
        step1_out2 = run_loc_cmd(step1_cmd2)
        save_log(log_name, step1_cmd2, 'step1_2', step1_out2['output'])
        if step1_out2.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd2)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd2)
        step1_out3 = run_loc_cmd(step1_cmd3)
        step1_check_point = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CHECK_POINT']
        save_log(log_name, step1_cmd3, 'step1_3', step1_out3['output'])
        if step1_out3.succeeded and step1_check_point not in step1_out3['output']:
            logger.info('we execute the command--%s successful' % step1_cmd3)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd3)
        step1_out4 = run_loc_cmd(step1_cmd4)
        save_log(log_name, step1_cmd4, 'step1_4', step1_out4['output'])
        if step1_out4.succeeded:
            logger.info('we execute the command--%s successful' % step1_cmd4)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd4)
            exit()
        step1_out5 = run_loc_cmd(step1_cmd5)
        save_log(log_name, step1_cmd5, 'step1_5', step1_out5['output'])
        if step1_out5.succeeded and step1_out5['output'] == label:
            logger.info('we execute the command--%s successful' % step1_cmd5)
        else:
            logger.info('we execute the command--%s failed' % step1_cmd5)
            exit()

    step1_cmd5 = case_config['CUPTI_TRACE_RESTRICT']['STEP1']['CMD5']
    step1_out6 = run_loc_cmd(step1_cmd5)
    if step1_out6['output'] == '1':
        logger.info('now it is 1, no need to change it')
    else:
        change_restrict('1')
    # step2 run nvprof cmd
    sm = get_sm()
    if sm < 7.5:
        cmd_list = case_config['CUPTI_TRACE_RESTRICT']['STEP2']['CMD_LIST']
        for i in range(0, len(cmd_list)):
            check_result(cmd_list[i], 'step2_%s' % cmd_list[i], log_name, result)
    else:
        logger.info('we need not to run on GPU that SM is more then 75')
    # step3 run cupti trace
    '''
    step3_activity_trace = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD1']
    step3_activity_trace_async = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD2']
    step3_callback_timestamp = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD3']
    step3_unified_memory = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD4']
    step3_sass_source_map = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD5']
    '''
    cmd_list1 = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD_LIST']
    for i in range(0, len(cmd_list1)):
        check_result(cmd_list1[i], 'step3_%s' % cmd_list1[i], log_name, result)
    if cuda_short_version <= '11.0':
        step3_cmd = case_config['CUPTI_TRACE_RESTRICT']['STEP3']['CMD']
        check_result(step3_cmd, 'step3_active_trace by %s' % step3_cmd, log_name, result)
    else:
        pass
        logger.info('now there is no activity_trace sample')

    change_restrict('0')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_trace_restrict.json' % case_config['global']['env']['TRACE_RESTRICT_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def openacc_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_OPENACC']['LOG_NAME']
    log_path = case_config['global']['env']['OPENACC_LOG_PATH']
    mkdir(log_path)
    # prepare pgi package, not ready, it download in the yaml file
    mkdir('%s/../../pgi' % tools_home)
    build_openacc(log_name, result)
    # run the sample
    step3_cmd1 = case_config['CUPTI_OPENACC']['STEP3']['CMD1']
    check_result(step3_cmd1,
                 'step3_run-openacc_trace',
                 log_name,
                 result,
                 'error',
                 'ERROR',
                 'fail',
                 'Fail',
                 'FAIL',
                 'Error',
                 flag=2)
    cmd_umount = "df |grep '173.11'"
    out = run_test_cmd(cmd_umount)
    if '173.11' in out.output:
        cmd_umount1 = 'echo {}|sudo -S umount /opt/nvidia'.format(host_password)
        run_test_cmd(cmd_umount1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_openacc.json' % case_config['global']['env']['OPENACC_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_SAMPLE_LOG_PATH']
    mkdir(log_path)
    # prepare get sm number
    sm = get_sm()
    print(sm)
    # step1 change the permission about autorange_profiling
    step1_cmd1 = case_config['CUPTI_SAMPLE']['STEP1']['CMD1']
    check_result(step1_cmd1, 'change permission by %s' % step1_cmd1, log_name, result)
    print(step1_cmd1)
    # build sample
    root_dir = '%s/extras/CUPTI/samples/' % base_path
    dir_list = os.listdir(root_dir)
    sudo_excute = case_config['global']['env']['SUDO_EXECUTE']
    ld_path = case_config['global']['env']['LD_PATH']
    print(dir_list)
    extension_path_list = [root_dir + i for i in dir_list if 'extension' in i]
    sample_path_list = [root_dir + i for i in dir_list if 'extension' not in i]
    cmd = 'cd %s/src/profilerhost_util/; %s make clean; %s make' % (extension_path_list[0], sudo_excute, sudo_excute)
    check_result(cmd, 'prepare_build-extensions by %s' % cmd, log_name, result)
    for i in sample_path_list:
        if 'openacc' in i:
            build_openacc(log_name, result)
        else:
            cmd1 = 'cd %s; %s make clean; %s make' % (i, sudo_excute, sudo_excute)
            check_result(cmd1, 'build-sample by %s' % cmd1, log_name, result)
    # run the cupti sample

    def run_sample(sample_list, checkpoint=None):
        for i in sample_list:
            for j in sample_path_list:
                if i == j.split('/')[-1]:
                    if i == 'autorange_profiling':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, 'auto_range_profiling')
                    elif i == 'userrange_profiling':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, 'user_range_profiling')
                    elif i == 'cupti_nvtx':
                        cmd = 'cd %s; export NVTX_INJECTION64_PATH=%s/extras/CUPTI/lib64/libcupti.so; export LD_LIBRARY_PATH=%s; ./cupti_nvtx' % (
                            j, base_path, ld_path)
                    elif i == 'openacc_trace':
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s/extras/CUPTI/samples/openacc_trace:%s; ./openacc_app' % (
                            j, base_path, ld_path)
                    else:
                        cmd = 'cd %s; export LD_LIBRARY_PATH=%s; ./%s' % (j, ld_path, i)
                    if checkpoint:
                        check_result(cmd, 'run sample--%s' % i, log_name, result, checkpoint, flag=1)
                    else:
                        if i == 'cuda_memory_trace':
                            if vgpu != 'none':
                                check_point = 'operation not supported'
                                check_result(cmd, 'run sample--%s' % i, log_name, result, check_point, flag=1)
                            else:
                                check_result(cmd, 'run sample--%s' % i, log_name, result)
                        else:
                            check_result(cmd, 'run sample--%s' % i, log_name, result)

    nextgen_list = case_config['CUPTI_SPECFIC']['STEP3']['NEXTGEN_LIST'].split(',')
    legacy_list = case_config['CUPTI_SPECFIC']['STEP3']['LEGACY_LIST'].split(',')
    trace_list = case_config['CUPTI_SPECFIC']['STEP3']['TRACE_LIST'].split(',')
    step3_check4 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT4']
    step3_check5 = case_config['CUPTI_SPECFIC']['STEP3']['CHECK_POINT5']
    nextgen_list = case_config['CUPTI_SPECFIC']['STEP3']['NEXTGEN_LIST'].split(',')
    legacy_list = case_config['CUPTI_SPECFIC']['STEP3']['LEGACY_LIST'].split(',')
    trace_list = case_config['CUPTI_SPECFIC']['STEP3']['TRACE_LIST'].split(',')
    if vgpu != 'none':
        if sm <= 7.2:
            run_sample(legacy_list, checkpoint=step3_check5)
        else:
            run_sample(legacy_list, checkpoint=step3_check4)
    else:
        if sm <= 7.2:
            run_sample(legacy_list)
        else:
            print(step3_check4)
            run_sample(legacy_list, checkpoint=step3_check4)
            print(step3_check4)
    run_sample(nextgen_list)
    run_sample(trace_list)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_sample.json' % case_config['global']['env']['CUPTI_SAMPLE_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_2nd_gpu():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_2ND_GPU']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_2ND_LOG_PATH']
    mkdir(log_path)
    # prepare file
    cmd1 = case_config['CUPTI_2ND_GPU']['PREPARE']['CMD1'] % (user, password, base_url, '2485834', 'timer.h')
    cmd2 = case_config['CUPTI_2ND_GPU']['PREPARE']['CMD2'] % (user, password, base_url, '2485834', 'simpleMultiGPU.h')
    cmd3 = case_config['CUPTI_2ND_GPU']['PREPARE']['CMD2'] % (user, password, base_url, '2485834', 'cudaTest_cupti_only.cu')
    check_result(cmd1, 'prepare_file1----%s' % cmd1, log_name, result)
    check_result(cmd2, 'prepare_file1----%s' % cmd2, log_name, result)
    check_result(cmd3, 'prepare_file1----%s' % cmd3, log_name, result)
    # build the sample
    step1_cmd = case_config['CUPTI_2ND_GPU']['STEP1']['CMD1']
    check_result(step1_cmd, 'step1 by %s' % step1_cmd, log_name, result)
    # step2, run cmd
    if platform.lower() == 'x86' or platform.lower() == 'arm':
        result['testing'] = 'passed'
        pass
    else:
        step2_cmd1 = case_config['CUPTI_2ND_GPU']['STEP2']['CMD1']
        step2_cmd2 = case_config['CUPTI_2ND_GPU']['STEP2']['CMD2']
        check_result(step2_cmd1, 'step2_cmd1 by %s' % step2_cmd1, log_name, result)
        check_result(step2_cmd2, 'step2_cmd2 by %s' % step2_cmd2, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_2nd_gpu.json' % case_config['global']['env']['CUPTI_2ND_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_nvperf2():
    if vgpu == 'none':
        result = {}
        passed, failed = 0, 0
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        log_name = case_config['CUPTI_NVPERF2']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_NVPERF2_LOG_PATH']
        mkdir(log_path)
        # run step1
        if platform.lower() == 'x86':
            step1_cmd = case_config['CUPTI_NVPERF2']['STEP1']['CMD']
        elif platform.lower() == 'arm':
            step1_cmd = case_config['CUPTI_NVPERF2']['STEP1']['CMD2']
        else:
            if cuda_short_version <= '11.4':
                step1_cmd = case_config['CUPTI_NVPERF2']['STEP1']['CMD1']
            else:
                step1_cmd = case_config['CUPTI_NVPERF2']['STEP1']['CMD1_1']
        check_list = case_config['CUPTI_NVPERF2']['STEP1']['CHECK_POINT'].split(',')
        for check in check_list:
            check_result(step1_cmd, 'step1_check by %s' % step1_cmd, log_name, result, check)
        # run step2
        gpu = prepare_gpu()
        gpu_arch = gpu_dict['%s' % gpu]
        print(gpu_arch)
        if platform.lower() == 'x86':
            step2_cmd = case_config['CUPTI_NVPERF2']['STEP2']['CMD'] % gpu_arch
        elif platform.lower() == 'arm':
            step2_cmd = case_config['CUPTI_NVPERF2']['STEP2']['CMD2'] % gpu_arch
        else:
            if cuda_short_version <= '11.4':
                step2_cmd = case_config['CUPTI_NVPERF2']['STEP2']['CMD1'] % gpu_arch
            else:
                step2_cmd = case_config['CUPTI_NVPERF2']['STEP2']['CMD1_1'] % gpu_arch
        print(step2_cmd)
        check_result(step2_cmd, 'step2 by %s' % step2_cmd, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_nvperf2.json' % case_config['global']['env']['CUPTI_NVPERF2_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we waive this case in vgpu env')
        return WAIVED


def cupti_injection_continuous():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_INJECTION_CONTINUOUS']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_INJECTION_CONTINUOUS_PATH']
    mkdir(log_path)
    # download the needed file
    download_cmd1 = case_config['CUPTI_INJECTION_CONTINUOUS']['DOWNLOAD']['CMD1'] % (user, password, base_url)
    download_cmd2 = case_config['CUPTI_INJECTION_CONTINUOUS']['DOWNLOAD']['CMD2'] % (user, password, base_url)
    run_loc_cmd(download_cmd1)
    run_loc_cmd(download_cmd2)
    # prepare Makefile
    cmd1 = case_config['CUPTI_INJECTION_CONTINUOUS']['PREPARE']['CMD1']
    cmd2 = case_config['CUPTI_INJECTION_CONTINUOUS']['PREPARE']['CMD2']
    cmd3 = case_config['CUPTI_INJECTION_CONTINUOUS']['PREPARE']['CMD3']
    check_result(cmd1, 'prepare1 makefile by %s' % cmd1, log_name, result)
    if case_config['global']['env']['INSTALLER'] == 'runfile':
        check_result(cmd2, 'prepare2 makefile by %s' % cmd2, log_name, result)
    else:
        check_result(cmd3, 'prepare2 makefile by %s' % cmd3, log_name, result)
    # run step1, prepare so file
    cmd = case_config['CUPTI_INJECTION_CONTINUOUS']['STEP1']['CMD']
    check = case_config['CUPTI_INJECTION_CONTINUOUS']['STEP1']['CHECK_POINT']
    check_result(cmd, 'run step1 by %s' % cmd, log_name, result, check)
    # prepare get sm number
    sm = get_sm()
    # run step2, run sample
    if sm < 8.6:
        sample_list = case_config['CUPTI_INJECTION_CONTINUOUS']['STEP2']['SAMPLE'].split(',')
        check1 = case_config['CUPTI_INJECTION_CONTINUOUS']['STEP2']['CHECK_POINT1']
        for sample in sample_list:
            cmd1 = case_config['CUPTI_INJECTION_CONTINUOUS']['STEP2']['CMD1'] % sample
            check_result(cmd1, 'run step2_%s by %s' % (sample, cmd1), log_name, result, check1)
    else:
        logger.info('now it dose not support the GPU whose SM is 8.6')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_injection_continuous.json' % case_config['global']['env']['CUPTI_INJECTION_CONTINUOUS_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_finalize():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_FINALIZE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_FINALIZE_LOG_PATH']
    mkdir(log_path)
    # build the cupti_finalize
    step1_cmd = case_config['CUPTI_FINALIZE']['STEP1']['CMD']
    check_point = case_config['CUPTI_FINALIZE']['STEP1']['CHECK_POINT']
    check_result(step1_cmd, 'step1_build sample by %s' % step1_cmd, log_name, result, check_point)
    # run the sample
    if cuda_short_version < '12.1':
        check_point1 = case_config['CUPTI_FINALIZE']['CHECK_POINT1']
        check_point2 = case_config['CUPTI_FINALIZE']['CHECK_POINT2']
    else:
        check_point1 = case_config['CUPTI_FINALIZE']['CHECK_POINT1_1']
        check_point2 = case_config['CUPTI_FINALIZE']['CHECK_POINT2_1']
        check_point3 = case_config['CUPTI_FINALIZE']['CHECK_POINT3']
    if platform == 'power':
        step2_cmd1 = case_config['CUPTI_FINALIZE']['STEP2']['CMD1_2'] % sample_0_path
    elif platform == 'arm':
        step2_cmd1 = case_config['CUPTI_FINALIZE']['STEP2']['CMD1_2'] % sample_0_path
    else:
        if cuda_short_version <= '11.4':
            # run the cupti sample--from cupti dvs packag
            prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
            target_path = case_config['global']['env']['CUPTI_TARGET_PATH1']
            step2_cmd1 = case_config['CUPTI_FINALIZE']['STEP2']['CMD1'] % (target_path, 'complex_target')
        elif '11.5' < cuda_short_version < '12.1':
            prepare_cmd1 = case_config['CUPTI_FINALIZE']['PREPARE']['CMD1']
            prepare_cmd2 = case_config['CUPTI_FINALIZE']['PREPARE']['CMD2']
            prepare_cmd3 = case_config['CUPTI_FINALIZE']['PREPARE']['CMD3']
            check_result(prepare_cmd1, 'prepare_cmd1------%s' % prepare_cmd1, log_name, result)
            check_result(prepare_cmd2, 'prepare_cmd2------%s' % prepare_cmd2, log_name, result)
            check_result(prepare_cmd3, 'prepare_cmd3------%s' % prepare_cmd3, log_name, result)
            step2_cmd1 = case_config['CUPTI_FINALIZE']['STEP2']['CMD1_1']
        else:
            step2_cmd1 = case_config['CUPTI_FINALIZE']['STEP2']['CMD1_2'] % sample_0_path
    print(step2_cmd1)
    if cuda_short_version < '12.1':
        check_result(step2_cmd1, 'run-step2_cmd1------%s' % step2_cmd1, log_name, result, check_point1, check_point2)
    else:
        check_result(step2_cmd1,
                     'run-step2_cmd1------%s' % step2_cmd1,
                     log_name,
                     result,
                     check_point1,
                     check_point2,
                     check_point3)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_finalize.json' % case_config['global']['env']['CUPTI_FINALIZE_LOG_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_callback_event():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_CALLBACK_EVENT']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_EVENT_LOG_PATH']
        mkdir(log_path)
        # prepare env
        sm = get_sm()
        # run step1 to get all the domain
        if sm <= 7.0:
            cmd1 = case_config['CUPTI_CALLBACK_EVENT']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    domain_list.append(i)
            else:
                result['step1'] = 'failed'
            # run step2 get events
            logger.info('we will get all the events')
            cmd2 = case_config['CUPTI_CALLBACK_EVENT']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j)
                else:
                    result['step2_%s' % i] = 'failed'
            # run step3, callback all the event
            cmd3 = case_config['CUPTI_CALLBACK_EVENT']['STEP3']['CMD']
            logger.info('we will run the command: ./callback_event device event')
            for i in range(0, len(event_list)):
                cmd4 = cmd3 % event_list[i]
                out3 = run_loc_cmd(cmd4)
                save_log(log_name, cmd2, 'step3_run_event%s' % i, out3['output'])
                if out3.succeeded:
                    result['step3_%s' % i] = 'passed'
                else:
                    result['step3_%s' % i] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_callback_event.json' % case_config['global']['env']['CUPTI_EVENT_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case in vgpu env and since cuda 13.0')
        return WAIVED


def cupti_callback_metric():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_CALLBACK_METRIC']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_METRIC_LOG_PATH']
        mkdir(log_path)
        # prepare env
        sm = get_sm()
        build_cmd = case_config['CUPTI_CALLBACK_METRIC']['BUILD']
        out0 = run_loc_cmd(build_cmd)
        if out0.succeeded:
            logger.info('build the cupti_query successful')
            result['build_cupti_query'] = 'passed'
        else:
            logger.info('build the cupti_query failed')
            result['build_cupti_query'] = 'failed'
        if sm <= 7.0:
            # run step1 to get all the metric
            cmd1 = case_config['CUPTI_CALLBACK_METRIC']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getmetrics', out1['output'])
            metric_list = []
            logger.info('we will get all the metrics')
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    metric_list.append(i)
            else:
                result['step1'] = 'failed'
            # run step2 run metric
            cmd2 = case_config['CUPTI_CALLBACK_METRIC']['STEP2']['CMD']
            logger.info('we will run the command: ./callback_metric device event')
            for i in range(0, len(metric_list)):
                cmd4 = cmd2 % metric_list[i]
                out3 = run_loc_cmd(cmd4)
                save_log(log_name, cmd2, 'step2_run_metric%s' % i, out3['output'])
                print(out3['output'])
                if out3.succeeded:
                    result['step2_%s_%s' % (i, metric_list[i])] = 'passed'
                else:
                    result['step2_%s_%s' % (i, metric_list[i])] = 'failed'
        else:
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_callback_metric.json' % case_config['global']['env']['CUPTI_METRIC_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case in vgpu env')
        return WAIVED


def cupti_event_sampling():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_EVENT_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_SAMPLING_LOG_PATH']
        mkdir(log_path)
        # prepare env
        sm = get_sm()
        # run step1 to get all the domain
        if sm <= 7.0:
            cmd1 = case_config['CUPTI_EVENT_SAMPLING']['STEP1']['CMD']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    domain_list.append(i)
            else:
                result['step1'] = 'failed'
            # run step2 get events
            logger.info('we will get all the events')
            cmd2 = case_config['CUPTI_EVENT_SAMPLING']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j)
                else:
                    result['step2_%s' % i] = 'failed'
            # run step3, event_sampling all the event
            cmd3 = case_config['CUPTI_EVENT_SAMPLING']['STEP3']['CMD']
            logger.info('we will run the command: ./event_sampling device event')
            for i in range(0, len(event_list)):
                cmd4 = cmd3 % event_list[i]
                out3 = run_loc_cmd(cmd4)
                print(out3['output'])
                save_log(log_name, cmd2, 'step3_run_event%s' % i, out3['output'])
                if out3.succeeded:
                    result['step3_%s' % i] = 'passed'
                else:
                    result['step3_%s' % i] = 'failed'
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_event_sampling.json' % case_config['global']['env']['CUPTI_SAMPLING_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case in vgpu env and since cuda 13.0')
        return WAIVED


def cupti_query():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_QUERY']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_QUERY_LOG_PATH']
        mkdir(log_path)
        # prepare env
        sm = get_sm()
        # run step1 to get all the domain
        cmd = case_config['CUPTI_QUERY']['STEP1']['CMD']
        # check_result(cmd, 'build_cupti_query---by %s' % cmd, log_name, result)
        out_cmd = run_loc_cmd(cmd)
        if out_cmd.succeeded:
            logger.info('build cupti query successful')
            result['build_cupti_query'] = 'passed'
        else:
            logger.info('build cupti query fail')
            result['build_cupti_query'] = 'failed'
        if sm <= 7.0:
            cmd1 = case_config['CUPTI_QUERY']['STEP1']['CMD1']
            out1 = run_loc_cmd(cmd1)
            save_log(log_name, cmd1, 'step1_getdomains', out1['output'])
            domain_list = []
            if out1.succeeded:
                result['step1'] = 'passed'
                for i in out1['output'].split('\n'):
                    domain_list.append(i)
            else:
                result['step1'] = 'failed'
            print(domain_list)
            # run step2 get events
            logger.info('we will get all the events')
            cmd2 = case_config['CUPTI_QUERY']['STEP2']['CMD']
            event_list = []
            for i in range(0, len(domain_list)):
                cmd = cmd2 % domain_list[i]
                out2 = run_loc_cmd(cmd)
                save_log(log_name, cmd2, 'step2_event%s' % i, out2['output'])
                if out2.succeeded:
                    result['step2_%s' % i] = 'passed'
                    for j in out2['output'].split('\n'):
                        event_list.append(j)
                else:
                    result['step2_%s' % i] = 'failed'
            # run step3 to get all the metric
            cmd3 = case_config['CUPTI_QUERY']['STEP3']['CMD']
            out3 = run_loc_cmd(cmd3)
            save_log(log_name, cmd3, 'step3_getmetrics', out3['output'])
            metric_list = []
            logger.info('we will get all the metrics')
            if out3.succeeded:
                result['step3'] = 'passed'
                for i in out3['output'].split('\n'):
                    metric_list.append(i)
            else:
                result['step3'] = 'failed'
            print(event_list)
            print(metric_list)
        else:
            pass
            logger.info('the SM more than 7.0, do not support this sample')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_query.json' % case_config['global']['env']['CUPTI_QUERY_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case---cupti_query in vgpu env and since cuda 13.0')
        return WAIVED


def cupti_extra_replay():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_EXTRA_REPLAY']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_EXTRA_REPLAY_PATH']
    mkdir(log_path)
    # prepare env
    cmd1 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD1']
    if cuda_short_version < '11.3':
        cmd2 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD2'] % (
            'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu')
        check_point = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CHECK_POINT']
        step1_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP1']['CMD'] % 'autoRangeSample'
        step2_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP2']['CMD'] % 'autoRangeSample'
        step3_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP3']['CMD'] % ('simplecuda.cu', 'simplecuda.cu', 'simplecuda.cu')
    else:
        if cuda_short_version < '12.3':
            cmd2 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD2'] % ('auto_range_profiling.cu',
                                                                           'auto_range_profiling.cu',
                                                                           'auto_range_profiling.cu',
                                                                           'auto_range_profiling.cu',
                                                                           'auto_range_profiling.cu',
                                                                           'auto_range_profiling.cu')
        else:
            cmd2 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD2_1'] % ('auto_range_profiling.cu',
                                                                             'auto_range_profiling.cu',
                                                                             'auto_range_profiling.cu',
                                                                             'auto_range_profiling.cu',
                                                                             'auto_range_profiling.cu',
                                                                             'auto_range_profiling.cu')
        step1_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP1']['CMD'] % 'auto_range_profiling'
        check_point = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CHECK_POINT1']
        step2_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP2']['CMD'] % 'auto_range_profiling'
        step3_cmd = case_config['CUPTI_EXTRA_REPLAY']['STEP3']['CMD'] % (
            'auto_range_profiling.cu', 'auto_range_profiling.cu', 'auto_range_profiling.cu')
    print(cmd2)
    cmd3 = case_config['CUPTI_EXTRA_REPLAY']['PREPARE']['CMD3']
    check_result(cmd1, 'prepare_cmd1 by %s' % cmd1, log_name, result)
    check_result(cmd2, 'prepare_cmd2 by %s' % cmd2, log_name, result)
    check_result(cmd3, 'prepare_cmd3 by %s' % cmd3, log_name, result, check_point)
    # run step1 to check single run
    logger.info('we will run the step1, the command is %s' % step1_cmd)
    out = run_loc_cmd(step1_cmd)
    save_log(log_name, step1_cmd, 'step1', out['output'])
    single_add, single_sub = [], []
    print(out['output'])
    for line in out['output'].split('came'):
        if 'vecAdd' in line:
            single_add.append(line)
        if 'vecSub' in line:
            single_sub.append(line)
    if out.succeeded and len(single_sub) == 1 and len(single_add) == 1:
        result['step1'] = 'passed'
        logger.info('we run cupti_extra_replay step1 successful')
    else:
        result['step1'] = 'failed'
        logger.info('we run cupti_extra_replay step1 failed')
    # run step2 to check multi run
    logger.info('we will run the step2, the command is %s' % step2_cmd)
    out = run_loc_cmd(step2_cmd)
    save_log(log_name, step2_cmd, 'step2', out['output'])
    multi_add, multi_sub = [], []
    for line in out['output'].split('came'):
        if 'vecAdd' in line:
            multi_add.append(line)
        if 'vecSub' in line:
            multi_sub.append(line)
    if out.succeeded and len(multi_sub) == len(multi_add) and len(multi_sub) > 1 and len(multi_add) > 1:
        result['step2'] = 'passed'
        logger.info('we run cupti_extra_replay step2 successful')
    else:
        result['step2'] = 'failed'
        logger.info('we run cupti_extra_replay step2 failed')
    # restore env
    check_result(step3_cmd, 'step3_restore_env by %s' % step3_cmd, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_extra_replay.json' % case_config['global']['env']['CUPTI_EXTRA_REPLAY_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_continous():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_SAMPLING_CONTINOUS']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_SAMPLING_CONTINOUS_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLING']
    cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLE'] % sample_0_path
    cmd3 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP1']['BUILD_SAMPLE1'] % sample_0_path
    check_result(cmd1, 'run step1-1_%s' % cmd1, log_name, result)
    check_result(cmd2, 'run step1-2_%s' % cmd2, log_name, result)
    check_result(cmd3, 'run step1-3_%s' % cmd3, log_name, result)
    # run pc_sampling command
    # number_list = [5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31]
    number_list = [5, 7, 9, 11, 13]
    i = random.sample(number_list, 1)[0]
    # i = random.randint(5, 31)
    step2_cmd = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD']
    step3_cmd = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD'] % sample_0_path
    step3_cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD2'] % sample_0_path
    check_point = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_POINT']
    check_point_list = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_POINT1'].split(',')
    check_list = case_config['CUPTI_SAMPLING_CONTINOUS']['CHECK_LIST'].split(',')
    if platform == 'power':
        logger.info('=============run the sample on P9==============')
        step2_cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD1'] % (
            'unset CUDA_VISIBLE_DEVICES', sample_0_path, 'simpleMultiGPU', i, 'simpleMultiGPU')
        step2_cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD2'] % (
            'unset CUDA_VISIBLE_DEVICES', sample_0_path, 'simpleMultiGPU', i, 'simpleMultiGPU')
        step2_cmd3 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD3'] % (
            'unset CUDA_VISIBLE_DEVICES', sample_0_path, 'simpleMultiGPU', i, 'simpleMultiGPU')
        check_result(step2_cmd1, 'step2_cmd1%s' % step2_cmd1, log_name, result)
        check_result(step3_cmd2,
                     'run step3_check_p9 by %s' % step3_cmd2,
                     log_name,
                     result,
                     check_point_list[0],
                     check_point_list[1],
                     check_point_list[2],
                     check_point_list[3])
        check_result(step2_cmd2, 'step2_cmd2%s' % step2_cmd2, log_name, result)
        check_result(step3_cmd2,
                     'run step3_check_file_p9-1 by %s' % step3_cmd2,
                     log_name,
                     result,
                     check_point_list[0],
                     check_point_list[1],
                     check_point_list[2],
                     check_point_list[3])
    else:
        step2_cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD1'] % (
            'echo "1"', sample_0_path, 'asyncAPI', i, 'asyncAPI')
        step2_cmd2 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD2'] % (
            'echo "1"', sample_0_path, 'asyncAPI', i, 'asyncAPI')
        step2_cmd3 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP2']['CMD3'] % (
            'echo "1"', sample_0_path, 'asyncAPI', i, 'asyncAPI')
        check_result(step2_cmd1, 'step2_cmd1----%s' % step2_cmd1, log_name, result, 'Initialize injection')
        check_result(step3_cmd, 'step3_cmd----%s' % step3_cmd, log_name, result, check_point)
        check_result(step2_cmd2, 'step2_cmd2---%s' % step2_cmd2, log_name, result, 'Initialize injection')
        check_result(step3_cmd, 'step3_%s_1' % step3_cmd, log_name, result, check_point)
        check_result(step2_cmd3, 'step2_cmd3-----%s' % step2_cmd3, log_name, result)
        check_result(step3_cmd,
                     'step3_cmd_2 to run the pc_sampling_continous  option-disable-file-dump ----%s' % step3_cmd,
                     log_name,
                     result,
                     flag=2)
        '''
        out = run_loc_cmd(step3_cmd)
        if out.succeeded:
            result['step3_cmd_2----%s' % step3_cmd] = 'failed'
            logger.info('the pc_sampling_continous  option-disable-file-dump  run fail')
        else:
            result['step3_cmd_2----%s' % step3_cmd] = 'passed'
            logger.info('the pc_sampling_continous  option-disable-file-dump  run pass')
        '''
    step3_cmd1 = case_config['CUPTI_SAMPLING_CONTINOUS']['STEP3']['CMD1']
    check_result(step2_cmd, 'step2_%s' % step2_cmd, log_name, result)
    out1 = run_loc_cmd(step3_cmd1)
    help_list = out1['output'].split('\n ')
    h_list = [i.strip(' ') for i in help_list]
    if set(check_list) == set(h_list):
        result['step2_cmd_help_list'] = 'passed'
        logger.info('the pc_sampling_continous --help run pass')
    else:
        result['step2_cmd_help_list'] = 'failed'
        logger.info('the pc_sampling_continous --help run fail')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_continous.json' % case_config['global']['env']['CUPTI_SAMPLING_CONTINOUS_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def autorange_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['AUTORANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['AUTORANGE_PROFILING_PATH']
    mkdir(log_path)
    sm = get_sm()
    check_point = case_config['AUTORANGE_PROFILING']['CHECK_POINT']
    # prepare sample
    cmd1 = case_config['AUTORANGE_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['AUTORANGE_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling by %s' % cmd1, log_name, result)
    check_result(cmd2, 'change_profiling_permission by %s' % cmd2, log_name, result)
    # build the profiling sample
    auto_range = case_config['AUTORANGE_PROFILING']['STEP1']['BUILD_AUTORANGE']
    check_result(auto_range, 'build_autorange_profiling by %s' % auto_range, log_name, result)
    # run autorange profiling sample
    if cuda_short_version < '11.3':
        run_auto = case_config['AUTORANGE_PROFILING']['STEP2']['RUN_AUTORANGE'] % 'autoRangeSample'
    else:
        run_auto = case_config['AUTORANGE_PROFILING']['STEP2']['RUN_AUTORANGE'] % 'auto_range_profiling'
    print(sm)
    if sm < 7.0:
        check_result(run_auto, 'run_autorange_profiling', log_name, result, check_point, flag=1)
    else:
        check_result(run_auto, 'run_autorange_profiling', log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/autorange_profiling.json' % case_config['global']['env']['AUTORANGE_PROFILING_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_nvtx():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_NVTX']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_NVTX_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the cupti nvtx sample
        cmd = case_config['CUPTI_NVTX']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_cupti_nvtx by %s' % cmd, log_name, result)
        # run cupti_nvtx sample
        run_cupti_nvtx = case_config['CUPTI_NVTX']['STEP1']['CMD']
        # check_result(run_cupti_nvtx, 'run_cupti_nvtx by %s' % run_cupti_nvtx, log_name, result)
        out = run_test_cmd(run_cupti_nvtx)
        print(out.output)
        ret = check_cupti_output(out.output, 'Calling CUPTI API')
        if out.rc == 0 and ret == 0:
            result['run {}'.format(run_cupti_nvtx)] = 'passed'
            logger.info('run cupti_external_correlation by {} successful'.format(run_cupti_nvtx))
        else:
            result['run {}'.format(run_cupti_nvtx)] = 'failed'
            logger.info('run cupti_external_correlation by {} fail'.format(run_cupti_nvtx))
        print('\n\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n\n')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_nvtx.json' % case_config['global']['env']['CUPTI_NVTX_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('it has not this sample, please check')
        return WAIVED


def cuda_graphs_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUDA_GRAPHS_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['CUDA_GRAPHS_TRACE_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the cuda_graphs_trace sample
        cmd = case_config['CUDA_GRAPHS_TRACE']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_cuda_graphs_trace', log_name, result)
        # run cuda_graphs_trace sample
        run_cuda_graphs_trace = case_config['CUDA_GRAPHS_TRACE']['STEP1']['CMD']
        out = run_test_cmd(run_cuda_graphs_trace)
        print(out.output)
        ret = check_cupti_output(out.output, 'Calling CUPTI API')
        if out.rc == 0 and ret == 0:
            result['run {}'.format(run_cuda_graphs_trace)] = 'passed'
            logger.info('run cuda_graphs_trace by {} successful'.format(run_cuda_graphs_trace))
            print('\n')
            logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
            print('\n')
        else:
            result['run {}'.format(run_cuda_graphs_trace)] = 'failed'
            logger.info('run cuda_graphs_trace by {} fail'.format(run_cuda_graphs_trace))
        save_log(log_name, run_cuda_graphs_trace, 'run {}'.format(run_cuda_graphs_trace), out.output)
        # check_result(run_cuda_graphs_trace, 'run_cuda_graphs_trace', log_name, result)
        cmd = 'nvidia-smi -L|grep MIG'
        out = run_loc_cmd(cmd)
        if out.succeeded:
            print('check mig env ')
            mig1, mig2 = [], []
            for j in out['output'].split('\n'):
                i = j.strip(' ')
                if i != ' ':
                    mig2.append(i.split(' ')[-1].split(')')[0])
                    mig1.append(i.split(' ')[1])
            dict_mig = dict(zip(mig2, mig1))
            cuda_devices1 = "echo $CUDA_VISIBLE_DEVICES"
            cuda_devices_out = run_loc_cmd(cuda_devices1)
            cuda_devices = cuda_devices_out['output'].split('\n')[0]
            if cuda_devices != '':
                check_mig = dict_mig[cuda_devices]
                check_result(run_cuda_graphs_trace, 'check_mig env--{}'.format(check_mig), log_name, result, check_mig)
            else:
                pass1 = 0
                for check_mig in mig1:
                    out1 = run_loc_cmd(run_cuda_graphs_trace)
                    if out1.succeeded and check_mig in out1['output']:
                        pass1 += 1
                if pass1 != 0:
                    logger.info('we check mig env successful')
                    result['check mig env'] = 'passed'
                else:
                    logger.info('we check mig env failed, please make sure one of {} in output '.format(mig1))
                    result['check mig env'] = 'failed'
    else:
        logger.info('now it has not this sample, please check')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cuda_graphs_trace.json' % case_config['global']['env']['CUDA_GRAPHS_TRACE_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def callback_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CALLBACK_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CALLBACK_PROFILING_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['CALLBACK_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['CALLBACK_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling', log_name, result)
    check_result(cmd2, 'change_profiling_permission', log_name, result)
    # build the profiling sample
    callback = case_config['CALLBACK_PROFILING']['STEP1']['BUILD_CALLBACK']
    check_result(callback, 'build_callback_profiling', log_name, result)
    # run callback profiling sample
    run_callback = case_config['CALLBACK_PROFILING']['STEP2']['RUN_CALLBACK']
    sm = get_sm()
    check_point = case_config['CALLBACK_PROFILING']['CHECK_POINT']
    if sm < 7.0:
        check_result(run_callback, 'run_callback_profiling', log_name, result, check_point, flag=1)
    else:
        check_result(run_callback, 'run_callback_profiling', log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/callback_profiling.json' % case_config['global']['env']['CALLBACK_PROFILING_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def userrange_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['USERRANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['USERRANGE_PROFILING_PATH']
    mkdir(log_path)
    sm = get_sm()
    check_point = case_config['USERRANGE_PROFILING']['CHECK_POINT']
    # prepare sample
    cmd1 = case_config['USERRANGE_PROFILING']['PREPARE']['CMD1']
    cmd2 = case_config['USERRANGE_PROFILING']['PREPARE']['CMD2']
    check_result(cmd1, 'prepare_build_profiling', log_name, result)
    check_result(cmd2, 'change_profiling_permission', log_name, result)
    # build the profiling sample
    user_range = case_config['USERRANGE_PROFILING']['STEP1']['BUILD_USERRANGE']
    check_result(user_range, 'build_userrange_profiling', log_name, result)
    # run userrange profiling sample
    if cuda_short_version < '11.3':
        run_user = case_config['USERRANGEE_PROFILING']['STEP2']['RUN_USERRANGE'] % 'userRangeSample'
    else:
        run_user = case_config['USERRANGE_PROFILING']['STEP2']['RUN_USERRANGE'] % 'user_range_profiling'
    if sm < 7.0:
        check_result(run_user, 'run_userrange_profiling', log_name, result, check_point, flag=1)
    else:
        check_result(run_user, 'run_userrange_profiling', log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/userrange_profiling.json' % case_config['global']['env']['USERRANGE_PROFILING_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def nested_range_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NESTED_RANGE_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['NESTED_RANGE_PROFILING_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['NESTED_RANGE_PROFILING']['PREPARE']['CMD1']
    check_result(cmd1, 'prepare_build_profiling', log_name, result)
    # build the profiling sample
    nest_range = case_config['NESTED_RANGE_PROFILING']['STEP1']['BUILD_SAMPLE']
    check_result(nest_range, 'build_nest_range_profiling', log_name, result)
    # run the nest range profiling sample
    run_nest_range = nest_range = case_config['NESTED_RANGE_PROFILING']['STEP2']['CMD']
    sm = get_sm()
    check_point = case_config['NESTED_RANGE_PROFILING']['CHECK_POINT']
    if sm < 7.0:
        check_result(run_nest_range, 'run_nest_range', log_name, result, check_point, flag=1)
    else:
        check_result(run_nest_range, 'run_nest_range', log_name, result)
    check_result(run_nest_range, 'Run_nest_range_profiling', log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/nested_range_profiling.json' % case_config['global']['env']['NESTED_RANGE_PROFILING_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_start_stop():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_START_STOP']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_START_STOP_PATH']
    mkdir(log_path)
    # prepare sample
    cmd = case_config['PC_SAMPLING_START_STOP']['STEP1']['BUILD_SAMPLING']
    check_result(cmd, 'step1_prepare_sample--%s' % cmd, log_name, result)
    sm = get_sm()
    check_point = case_config['NESTED_RANGE_PROFILING']['CHECK_POINT']
    # run pc_sampling
    step2_cmd = case_config['PC_SAMPLING_START_STOP']['STEP2']['CMD']
    if sm < 7.0:
        check_result(step2_cmd, 'run_pc_sampling_start_stop', log_name, result, check_point)
    else:
        if cuda_short_version >= '12.1':
            check_result(step2_cmd, 'run_pc_sampling_start_stop', log_name, result, 'ERROR', 'error', flag=2)
        else:
            out = run_loc_cmd(step2_cmd)
            save_log(log_name, step2_cmd, 'run-step2_cmd', out['output'])
            # account stallReasonCount is correct or wrong in every line
            list1, list2 = [], []
            with open('%s/pc_sampling_start_stop.txt' % home_path, 'r') as f:
                for line in f.readlines():
                    if 'pcOffset' in line or 'Launching VecMul' in line:
                        list1.append(line.strip(' ').strip('\n').split(','))
                    if 'Number of PCs' in line:
                        list2.append(int(line.strip(' ').strip('\n').split(',')[2].split(':')[1].strip(' ')))
            success, fail = 0, 0
            index_list, pc_list, function_list = [], [], []
            for i in range(0, len(list1)):
                stall_list = []
                for index, value in enumerate(list1[i]):
                    if 'stallReason' in value:
                        stall_list.append(value)
                    if 'Launching VecMul' in value:
                        index_list.append(i)
                    if 'pcOffset' in value:
                        pc_list.append(value.split(':')[1].strip(' '))
                    if 'functionName' in value:
                        function_list.append(value.split(':')[1].strip(' '))
                for stall in stall_list:
                    if 'stallReasonCount' in stall:
                        if int(stall.split(':')[1].strip(' ')) == len(stall_list) - 1:
                            success += 1
                        else:
                            fail += 1
            # account the sample number in every line, then accout the sum of all sample

            def get_sample_number(index1, index2, list3):
                sum_list, total_sample = [], []
                for i in range(index1, index2):
                    sample_list = []
                    for index, value in enumerate(list3[i]):
                        if 'samples' in value and 'not_issued' not in list3[i][index - 1]:
                            if int(value.split(':')[1].strip(' ')) != 0:
                                sample_list.append(int(value.split(':')[1].strip(' ')))
                            else:
                                exit(20)
                                logger.info('the sample number should not be 0, so exit')
                            total_sample.append(int(value.split(':')[1].strip(' ')))
                    sum_list.append(sum(sample_list))
                return total_sample, sum(sum_list)

            sample_list1, sum_number1 = get_sample_number(1, index_list[1], list1)
            sample_list2, sum_number2 = get_sample_number(index_list[2], index_list[-1], list1)
            print(list2)
            print(index_list)
            print(sample_list1, sum_number1)
            print(sample_list2, sum_number2)
            print(pc_list)
            print(function_list)
            check_list1 = sample_list1 + sample_list2
            check_list2 = pc_list + function_list
            if out.succeeded and 'None' not in check_list2 and 0 not in check_list1 and sum_number1 == list2[
                    0] and sum_number2 == list2[1] and fail == 0:
                result['run_pc_sampling_start_stop'] = 'passed'
                logger.info('we run pc_sampling_start_stop successful')
            else:
                result['run_pc_sampling_start_stop'] = 'failed'
                logger.info('we run pc_sampling_start_stop fail')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_start_stop.json' % case_config['global']['env']['PC_SAMPLING_START_STOP_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_count():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_COUNT']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_COUNT_PATH']
        mkdir(log_path)
        # prepare
        prepare_cmd = case_config['PC_SAMPLING_COUNT']['PREPARE']['CMD']
        prepare_out1 = run_loc_cmd(prepare_cmd)
        message = prepare_out1['stdout'].decode()
        str1 = 'configPC.samplingPeriod=CUPTI_ACTIVITY_PC_SAMPLING_PERIOD_MIN;'
        prepare_cmd1 = case_config['PC_SAMPLING_COUNT']['PREPARE']['CMD1'] % message
        if message.strip(' ') != str1:
            check_result(prepare_cmd1, 'restore_pc_sampling', log_name, result)
        # build sample
        cmd = case_config['PC_SAMPLING_COUNT']['STEP1']['BUILD_SAMPLING']
        check_result(cmd, 'step1_prepare_sample--%s' % cmd, log_name, result)
        # run pc_sampling
        step2_cmd = case_config['PC_SAMPLING_COUNT']['STEP2']['CMD']
        out = run_loc_cmd(step2_cmd)
        print(out['output'])
        out_list1, out_list2 = [], []
        with open('%s/pc_sampling.txt' % home_path, 'r') as f:
            for line in f.readlines():
                if 'samples ' in line:
                    out_list1.append(int(line.strip('\n').split(',')[4].split(' ')[2]))
                if 'totalSamples' in line:
                    out_list2.append(int(line.strip('\n').split(',')[1].split(' ')[2]))
        if out.succeeded and sum(out_list1) == sum(out_list2):
            result['step2_count'] = 'passed'
            logger.info('we run the pc_sampling and the count is same, successful')
        else:
            result['step2_count'] = 'passed'
            logger.info('we run the pc_sampling and the count is different, fail')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_count.json' % case_config['global']['env']['PC_SAMPLING_COUNT_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case since cuda 13.0')
        return WAIVED


def pc_sampling_utility():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_UTILITY']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_UTILITY_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLING']
    cmd2 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLING']
    cmd3 = case_config['PC_SAMPLING_UTILITY']['STEP1']['BUILD_SAMPLE'] % sample_0_path
    check_result(cmd1, 'step1_build_pc_sampling_continous by %s' % cmd1, log_name, result)
    check_result(cmd2, 'step1_build_pc_sampling_utility by %s' % cmd2, log_name, result)
    check_result(cmd3, 'step1_build_async by %s' % cmd3, log_name, result)
    # run pc_sampling command
    # number_list = [5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31]
    number_list = [5, 7, 9, 11, 13, 15]
    i = random.sample(number_list, 1)[0]
    step2_cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD1'] % (sample_0_path, i)
    step2_cmd2 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD2'] % sample_0_path
    step2_cmd3 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD3']
    step2_cmd4 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD4'] % sample_0_path
    step2_cmd5 = case_config['PC_SAMPLING_UTILITY']['STEP2']['CMD5'] % sample_0_path
    step3_cmd = case_config['PC_SAMPLING_UTILITY']['STEP3']['CMD'] % sample_0_path
    step3_cmd1 = case_config['PC_SAMPLING_UTILITY']['STEP3']['CMD1'] % sample_0_path
    check_point = case_config['PC_SAMPLING_UTILITY']['CHECK_POINT']
    # generate dat file
    check_result(step2_cmd1, 'step2_%s' % step2_cmd1, log_name, result)
    check_result(step3_cmd, 'step3_%s' % step3_cmd, log_name, result, check_point)
    # generate cubin file
    check_result(step2_cmd2, 'step2_%s' % step2_cmd2, log_name, result)
    cubin_out = run_loc_cmd(step3_cmd1)
    print(cubin_out)
    cubin_list = cubin_out['output'].split('\n')
    for i in range(0, len(cubin_list)):
        change_cmd = step2_cmd3 % (sample_0_path, cubin_list[i], i + 1)
        run_loc_cmd(change_cmd)
    # run pc_sampling_utility without --verbose option
    out = run_loc_cmd(step2_cmd4)
    save_log(log_name, step2_cmd4, 'step2_cmd4', out['output'])
    out_list = []
    time.sleep(2)
    with open('%s/pc_sampling_utility.txt' % home_path, 'r') as f:
        for line in f.readlines():
            out_list.append(line.strip('\n'))
    if cuda_short_version <= '11.4':
        check_list = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST'].split(',')
        check_list1 = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST1'].split(',')
    else:
        check_list = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST_1'].split(',')
        check_list1 = case_config['PC_SAMPLING_UTILITY']['CHECK_LIST1_1'].split(',')
    print(out_list)
    print('+++++++++++++++++++++++')

    def get_list(list1, list2):
        name_list, value_list = [], []
        for i in list1:
            name_list.append(i.split(':')[0].strip(' '))
        for m in range(1, len(list2)):
            for n in list2[m].split(','):
                value_list.append(n.split(':')[1].strip(' '))
        return name_list, value_list

    name_list1, value_list1 = get_list(out_list[1].split(','), out_list)
    name_list2, value_list2 = get_list(out_list[2].split(','), out_list)
    print('========')
    print(name_list1, value_list1)
    print('====================')
    print(name_list2, value_list2)
    print(set(check_list) == set(name_list1))
    print('=============')
    print(set(check_list))
    print(set(name_list1))
    if out.succeeded and 'error' not in out['output'] and 'ERROR' not in out['output'] and set(check_list) == set(
            name_list1) and 'NULL' not in value_list1 and set(check_list1).issubset(set(name_list2)):
        result['step2_step4_run_pc_sampling_utility'] = 'passed'
        logger.info('we run pc_sampling_utility successful')
    else:
        result['step2_step4_run_pc_sampling_utility'] = 'failed'
        logger.info('we run pc_sampling_utility fail')
    # run pc_sampling_utility with --version option
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_utility.json' % case_config['global']['env']['PC_SAMPLING_UTILITY_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_period():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_PERIOD']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PERIOD_PATH']
        mkdir(log_path)
        # prepare sample
        cmd1 = case_config['PC_SAMPLING_PERIOD']['PREPARE']['CMD']
        cmd2 = case_config['PC_SAMPLING_PERIOD']['PREPARE']['CMD1']
        check_point = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT']
        check_point1 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT1']
        check_point2 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT2']
        check_point3 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT3']
        check_point4 = case_config['PC_SAMPLING_PERIOD']['CHECK_POINT4']
        check_result(cmd1, 'prepare_pc_sampling', log_name, result)
        # prepare env
        sm = get_sm()

        def get_run_cmd():
            out = run_loc_cmd(cmd2)
            replace_message = out['output']
            return replace_message

        # run pc_sampling with different value
        step1_cmd = case_config['PC_SAMPLING_PERIOD']['STEP1']['CMD']
        step2_cmd = case_config['PC_SAMPLING_PERIOD']['STEP2']['CMD']
        step3_cmd = case_config['PC_SAMPLING_PERIOD']['STEP3']['CMD']
        step4_cmd = case_config['PC_SAMPLING_PERIOD']['STEP4']['CMD']
        step5_cmd = case_config['PC_SAMPLING_PERIOD']['STEP5']['CMD']

        def run_pc_sampling(label, cmd, check_point_1):
            cmd1 = cmd % get_run_cmd()
            out = run_loc_cmd(cmd1)
            save_log(log_name, cmd, label, out['output'])
            print(out['output'])
            import re
            match = re.search(r'samplingPeriodInCycles (\d+)', out['output'])
            if match:
                value = match.group(1)
            else:
                value = 'None'
                logger.info("cannot get the samplingPeriodInCycles value")
            print(value)
            logger.info('we will run the cmd ------%s' % cmd1)
            if out.succeeded and value == check_point_1:
                result['%s_min' % label] = 'passed'
                logger.info('we run the pc_sampling %s successful, the value is %s ' % (label, value))
            else:
                result['%s_min' % label] = 'failed'
                logger.info('we run the pc_sampling %s fail, the value is %s ' % (label, value))

        run_pc_sampling('step1_min', step1_cmd, check_point)
        run_pc_sampling('step2_low', step2_cmd, check_point)
        run_pc_sampling('step3_mid', step3_cmd, check_point)
        if sm < 7.0:
            run_pc_sampling('step4_high', step4_cmd, check_point1)
            run_pc_sampling('step5_max', step5_cmd, check_point2)
        else:
            run_pc_sampling('step4_high', step4_cmd, check_point3)
            run_pc_sampling('step5_max', step5_cmd, check_point4)
        # restore the pc sampling
        cmd6 = case_config['PC_SAMPLING_PERIOD']['STEP6']['CMD']
        check_result(cmd6, 'restore_pc_sampling', log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_period.json' % case_config['global']['env']['PC_SAMPLING_PERIOD_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case since cuda 13.0')
        return WAIVED


def concurrent_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CONCURRENT_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CONCURRENT_PROFILING_PATH']
    mkdir(log_path)
    if cuda_short_version >= '11.4':
        # build the concurrent_profiling sample
        cmd1 = case_config['CONCURRENT_PROFILING']['PREPARE']['CMD1']
        check_result(cmd1, 'preprare_build by %s' % cmd1, log_name, result)
        cmd = case_config['CONCURRENT_PROFILING']['PREPARE']['CMD']
        check_result(cmd, 'prepare_build_concurrent_profiling by %s' % cmd, log_name, result)
        # run concurrent_profiling sample
        run_concurrent_profiling = case_config['CONCURRENT_PROFILING']['STEP1']['CMD']
        check_result(run_concurrent_profiling,
                     'run_concurrent_profiling --- %s' % run_concurrent_profiling,
                     log_name,
                     result,
                     'n/a',
                     'N/A',
                     flag=2)
    else:
        logger.info('it has not this sample, please check')
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/concurrent_profiling.json' % case_config['global']['env']['CONCURRENT_PROFILING_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling():
    sm = get_sm()
    if sm < 9.0 and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PATH']
        mkdir(log_path)
        # get sm and devicename
        prepare_cmd1 = case_config['PREPARE']['CMD3'] % sample_1_path
        prepare_cmd2 = case_config['PREPARE']['CMD4'] % sample_1_path
        out1 = run_loc_cmd(prepare_cmd1)
        out2 = run_loc_cmd(prepare_cmd2)
        sm = get_sm()
        device_name = out2['output'].split('\n')[-1].strip('"')
        check_point1 = case_config['PC_SAMPLING']['CHECK_POINT1'] % device_name
        check_point2 = case_config['PC_SAMPLING']['CHECK_POINT2'] % sm
        print(check_point2)
        print(check_point1)
        # build the pc_sampling sample
        cmd = case_config['PC_SAMPLING']['STEP1']['CMD']
        check_result(cmd, 'build_pc_sampling', log_name, result)
        check_result(cmd, 'build_pc_sampling', log_name, result)
        # run pc_sampling sample
        run_pc_sampling = case_config['PC_SAMPLING']['STEP2']['CMD']
        check_result(run_pc_sampling, 'run_pc_sampling---%s' % run_pc_sampling, log_name, result, check_point1, check_point2)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling.json' % case_config['global']['env']['PC_SAMPLING_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this sample if sm more than 9.0 or since cuda 13.0')
        return WAIVED


def activity_trace_async():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['ACTIVITY_TRACE_ASYNC']['LOG_NAME']
    log_path = case_config['global']['env']['ACTIVITY_TRACE_ASYNC_PATH']
    mkdir(log_path)
    # build the activity_trace_async sample
    cmd = case_config['ACTIVITY_TRACE_ASYNC']['STEP1']['CMD']
    check_result(cmd, 'build_activity_trace_async', log_name, result)
    # run activity_trace_async sample
    run_activity_trace_async = case_config['ACTIVITY_TRACE_ASYNC']['STEP2']['CMD']
    # check_result(run_activity_trace_async, 'run_activity_trace_async', log_name, result)
    out = run_test_cmd(run_activity_trace_async)
    print(out.output)
    ret = check_cupti_output(out.output, 'Calling CUPTI API')
    if out.rc == 0 and ret == 0:
        result['run {}'.format(run_activity_trace_async)] = 'passed'
        logger.info('run cupti_external_correlation by {} successful'.format(run_activity_trace_async))
        print('\n\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n\n')
    else:
        result['run {}'.format(run_activity_trace_async)] = 'failed'
        logger.info('run cupti_external_correlation by {} fail'.format(run_activity_trace_async))
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/activity_trace_async.json' % case_config['global']['env']['ACTIVITY_TRACE_ASYNC_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def activity_trace():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['ACTIVITY_TRACE']['LOG_NAME']
    log_path = case_config['global']['env']['ACTIVITY_TRACE_PATH']
    mkdir(log_path)
    if cuda_short_version > '11.0':
        logger.info('now there is no activity_trace sample, no need to run it')
        return WAIVED
    else:
        # build the activity_trace sample
        cmd = case_config['ACTIVITY_TRACE']['STEP1']['CMD']
        check_result(cmd, 'build_activity_trace', log_name, result)
        # run activity_trace sample
        run_activity_trace = case_config['ACTIVITY_TRACE']['STEP2']['CMD']
        check_result(run_activity_trace, 'run_activity_trace', log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/activity_trace.json' % case_config['global']['env']['ACTIVITY_TRACE_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def callback_timestamp():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CALLBACK_TIMESTAMP']['LOG_NAME']
    log_path = case_config['global']['env']['CALLBACK_TIMESTAMP_PATH']
    mkdir(log_path)
    # build the callback_timestamp sample
    cmd = case_config['CALLBACK_TIMESTAMP']['STEP1']['CMD']
    check_result(cmd, 'build_callback_timestamp', log_name, result)
    # run callback_timestamp sample
    run_callback_timestamp = case_config['CALLBACK_TIMESTAMP']['STEP2']['CMD']
    check_result(run_callback_timestamp, 'run_callback_timestamp', log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/callback_timestamp.json' % case_config['global']['env']['CALLBACK_TIMESTAMP_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def sass_source_map():
    if cuda_short_version < '13.0':
        sm = get_sm()
        result = {}
        passed, failed = 0, 0
        log_name = case_config['SASS_SOURCE_MAP']['LOG_NAME']
        log_path = case_config['global']['env']['SASS_SOURCE_MAP_PATH']
        mkdir(log_path)
        # build the sass_source_map sample
        cmd = case_config['SASS_SOURCE_MAP']['STEP1']['CMD']
        check_result(cmd, 'build_sass_source_map', log_name, result)
        # run sass_source_map sample
        run_sass_source_map = case_config['SASS_SOURCE_MAP']['STEP2']['CMD']
        # check_result(run_sass_source_map, 'run_sass_source_map', log_name, result)
        out = run_test_cmd(run_sass_source_map)
        print(out.output)
        if sm < 10.0:
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.rc == 0 and ret == 0:
                result['run {}'.format(run_sass_source_map)] = 'passed'
                logger.info('run sass_source_map by {} successful'.format(run_sass_source_map))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(run_sass_source_map)] = 'failed'
                logger.info('run sass_source_map by {} fail'.format(run_sass_source_map))
        else:
            if 'not supported on Blackwell+ GPU architecture' in out.output:
                result['run {}'.format(run_sass_source_map)] = 'passed'
                logger.info('run sass_source_map on blackwell++ vGPU successful')
            else:
                result['run {}'.format(run_sass_source_map)] = 'failed'
                logger.info('run sass_source_map on blackwell++ vGPU successful')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/sass_source_map.json' % case_config['global']['env']['SASS_SOURCE_MAP_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this sample if cuda 13.0')
        return WAIVED


def unified_memory():
    sm = get_sm()
    if platform == 'arm' and str(sm) in ['12.1']:
        logger.info('not support this case in WOA')
        return WAIVED
    else:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['UNIFIED_MEMORY']['LOG_NAME']
        log_path = case_config['global']['env']['UNIFIED_MEMORY_PATH']
        mkdir(log_path)
        # build the unified_memory sample
        cmd = case_config['UNIFIED_MEMORY']['STEP1']['CMD']
        check_result(cmd, 'build_unified_memory', log_name, result)
        # run unified_memory sample
        check_point = "Test is waived, unified memory is not supported on the underlying platform"
        run_unified_memory = case_config['UNIFIED_MEMORY']['STEP2']['CMD']
        if run_uvm_sample() is True or vgpu == 'none':
            # check_result(run_unified_memory, 'run_unified_memory', log_name, result)
            out = run_test_cmd(run_unified_memory)
            print(out.output)
            ret = check_cupti_output(out.output, 'Calling CUPTI API')
            if out.rc == 0 and ret == 0:
                result['run {}'.format(run_unified_memory)] = 'passed'
                logger.info('run cupti_external_correlation by {} successful'.format(run_unified_memory))
                print('\n\n')
                logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                print('\n\n')
            else:
                result['run {}'.format(run_unified_memory)] = 'failed'
                logger.info('run cupti_external_correlation by {} fail'.format(run_unified_memory))
        else:
            check_result(run_unified_memory, 'run_unified_memory with UVM disabled', log_name, result, check_point, flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/unified_memory.json' % case_config['global']['env']['UNIFIED_MEMORY_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def event_multi_gpu():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['EVENT_MULTI_GPU']['LOG_NAME']
        log_path = case_config['global']['env']['EVENT_MULTI_GPU_PATH']
        mkdir(log_path)
        # build the event_multi_gpu sample
        cmd = case_config['EVENT_MULTI_GPU']['STEP1']['CMD']
        check_result(cmd, 'build_event_multi_gpu', log_name, result)
        # run event_multi_gpu sample
        run_event_multi_gpu = case_config['EVENT_MULTI_GPU']['STEP2']['CMD']
        check_point = case_config['EVENT_MULTI_GPU']['CHECK_POINT']
        check_point1 = case_config['EVENT_MULTI_GPU']['CHECK_POINT1']
        check_point2 = case_config['EVENT_MULTI_GPU']['CHECK_POINT2']
        out = run_test_cmd(run_event_multi_gpu)
        print(out.output)
        if out.rc == 0:
            result['run_event_multi_gpu'] = 'passed'
        else:
            result['run_event_multi_gpu'] = 'failed'
            waive_list = [check_point, check_point1, check_point2]
            for i in waive_list:
                if re.search(i, out.output, re.I):
                    logger.info('###########################################')
                    logger.info('we check --- "{}" passed in output'.format(i))
                    logger.info('###########################################')
                    result['run_event_multi_gpu'] = 'passed'
                    break
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/event_multi_gpu.json' % case_config['global']['env']['EVENT_MULTI_GPU_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this case if cuda 13.0')
        return WAIVED


def nvlink_bandwidth():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['NVLINK_BANDWIDTH']['LOG_NAME']
        log_path = case_config['global']['env']['NVLINK_BANDWIDTH_PATH']
        mkdir(log_path)
        check_point1 = case_config['NVLINK_BANDWIDTH']['CHECK_POINT']
        check_point2 = case_config['NVLINK_BANDWIDTH']['CHECK_POINT1']
        # build the nvlink_bandwidth sample
        cmd = case_config['NVLINK_BANDWIDTH']['STEP1']['CMD']
        check_result(cmd, 'build_nvlink_bandwidth', log_name, result)
        # run nvlink_bandwidth sample
        run_nvlink_bandwidth1 = case_config['NVLINK_BANDWIDTH']['STEP2']['CMD1']
        out = run_test_cmd(run_nvlink_bandwidth1)
        print(out.output)
        if out.rc == 0:
            result['run_nvlink_bandwidth'] = 'passed'
            logger.info('we run the nvlink-bandwidth passed')
        else:
            result['run_nvlink_bandwidth'] = 'failed'
            print('====================================================')
            waive_list = [check_point1, check_point2]
            for i in waive_list:
                print(i)
                print(re.search(i, out.output))
                if re.search(i, out.output):
                    logger.info('###########################################')
                    logger.info('we check --- "{}" passed in output'.format(i))
                    logger.info('###########################################')
                    result['run_nvlink_bandwidth'] = 'passed'
                    break
                print('000000000000000000000000000000000000000000000000')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/nvlink_bandwidth.json' % case_config['global']['env']['NVLINK_BANDWIDTH_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this case if cuda 13.0')
        return WAIVED


def profiling_injection():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PROFILING_INJECTION']['LOG_NAME']
    log_path = case_config['global']['env']['PROFILING_INJECTION_PATH']
    mkdir(log_path)
    # prepare sample
    cmd1 = case_config['PROFILING_INJECTION']['PREPARE']['CMD']
    check_result(cmd1, 'prepare_build_profiling', log_name, result)
    # build the profiling_injection sample
    cmd = case_config['PROFILING_INJECTION']['STEP1']['CMD']
    check_result(cmd, 'build_profiling_injection', log_name, result)
    # run profiling_injection sample
    if cuda_short_version < '11.8':
        run_profiling_injection1 = case_config['PROFILING_INJECTION']['STEP2']['CMD1']
        run_profiling_injection2 = case_config['PROFILING_INJECTION']['STEP2']['CMD2']
    elif cuda_short_version < '12.1':
        run_profiling_injection1 = case_config['PROFILING_INJECTION']['STEP2']['CMD1_1']
        run_profiling_injection2 = case_config['PROFILING_INJECTION']['STEP2']['CMD2_1']
    else:
        run_profiling_injection1 = case_config['PROFILING_INJECTION']['STEP2']['CMD1_2']
        run_profiling_injection2 = case_config['PROFILING_INJECTION']['STEP2']['CMD2_2']

    def get_result(output):
        result_list = []
        for line in output.split('\n'):
            if 'sm__' in line or 'smsp__' in line:
                result_list.append(line.split(' ')[-1])
        return result_list

    logger.info('we will run step2-cmd1----%s' % run_profiling_injection1)
    out1 = run_loc_cmd(run_profiling_injection1)
    result1_list = get_result(out1['output'])
    out2 = run_loc_cmd(run_profiling_injection2)
    result2_list = get_result(out2['output'])
    save_log(log_name, run_profiling_injection1, 'run-profiling_injection1', out1['output'])
    save_log(log_name, run_profiling_injection2, 'run-profiling_injection2', out2['output'])
    print(out1['output'])
    print(result1_list)
    logger.info('we will run step2-cmd2----%s' % run_profiling_injection2)
    print(out2['output'])
    print(result2_list)
    if out1.succeeded and 'n/a' not in result1_list and 'N/A' not in result1_list and 'nan' not in result1_list and 'error' not in result1_list:
        result['step2_cmd1-%s' % run_profiling_injection1] = 'passed'
    else:
        result['step2_cmd1-%s' % run_profiling_injection1] = 'failed'
    if out2.succeeded and 'n/a' not in result2_list and 'N/A' not in result2_list and 'nan' not in result1_list and 'error' not in result1_list:
        result['step2_cmd2-%s' % run_profiling_injection2] = 'passed'
    else:
        result['step2_cmd2-%s' % run_profiling_injection2] = 'failed'
    cmd = 'nvidia-smi -L|grep MIG'
    out = run_loc_cmd(cmd)
    if out.succeeded:
        print('check mig env ')
        mig1, mig2 = [], []
        for j in out['output'].split('\n'):
            i = j.strip(' ')
            if i != ' ':
                mig2.append(i.split(' ')[-1].split(')')[0])
                mig1.append(i.split(' ')[1])
        dict_mig = dict(zip(mig2, mig1))
        cuda_devices1 = "echo $CUDA_VISIBLE_DEVICES"
        cuda_devices_out = run_loc_cmd(cuda_devices1)
        cuda_devices = cuda_devices_out['output'].split('\n')[0]
        if cuda_devices != '':
            check_mig = dict_mig[cuda_devices]
            check_result(run_profiling_injection1, 'check_mig env--{}'.format(check_mig), log_name, result, check_mig)
        else:
            pass1 = 0
            for check_mig in mig1:
                out1 = run_loc_cmd(run_profiling_injection1)
                if out1.succeeded and check_mig in out1['output']:
                    pass1 += 1
            if pass1 != 0:
                logger.info('we check mig env successful')
                result['check mig env'] = 'passed'
            else:
                logger.info('we check mig env failed, please make sure one of {} in output '.format(mig1))
                result['check mig env'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/profiling_injection.json' % case_config['global']['env']['PROFILING_INJECTION_PATH'])
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def callback_event():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CALLBACK_EVENT']['LOG_NAME']
        log_path = case_config['global']['env']['CALLBACK_EVENT_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % sample_1_path
        run_loc_cmd(prepare_cmd)
        sm = get_sm()
        # build the callback_event sample
        cmd = case_config['CALLBACK_EVENT']['STEP1']['CMD']
        check_result(cmd, 'build_callback_event', log_name, result)
        # run callback_event sample
        run_callback_event = case_config['CALLBACK_EVENT']['STEP2']['CMD']
        if sm < 7.2:
            check_result(run_callback_event, 'run_callback_event', log_name, result)
        else:
            if cuda_short_version < '11.4':
                check_point = case_config['CALLBACK_EVENT']['CHECK_POINT1_1']
            else:
                check_point = case_config['CALLBACK_EVENT']['CHECK_POINT']
            check_result(run_callback_event,
                         'run_callback_event by %s' % run_callback_event,
                         log_name,
                         result,
                         check_point,
                         flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/callback_event.json' % case_config['global']['env']['CALLBACK_EVENT_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case ---callback_event in vgpu env and since cuda 13.0')
        return WAIVED


def callback_metric():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CALLBACK_METRIC']['LOG_NAME']
        log_path = case_config['global']['env']['CALLBACK_METRIC_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % sample_1_path
        run_loc_cmd(prepare_cmd)
        sm = get_sm()
        # build the callback_metric sample
        cmd = case_config['CALLBACK_METRIC']['STEP1']['CMD']
        check_result(cmd, 'build_callback_metric', log_name, result)
        # run callback_metric sample
        run_callback_metric = case_config['CALLBACK_METRIC']['STEP2']['CMD']
        if sm < 7.2:
            check_result(run_callback_metric, 'run_callback_metric by %s' % run_callback_metric, log_name, result)
        else:
            if cuda_short_version < '11.4':
                check_point = case_config['CALLBACK_METRIC']['CHECK_POINT_1']
            else:
                check_point = case_config['CALLBACK_METRIC']['CHECK_POINT']
            check_result(run_callback_metric,
                         'run_callback_metric by %s' % run_callback_metric,
                         log_name,
                         result,
                         check_point,
                         flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/callback_metric.json' % case_config['global']['env']['CALLBACK_METRIC_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case--callback_metric in vgpu env and since cuda 13.0')
        return WAIVED


def event_sampling():
    if vgpu == 'none' and cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['EVENT_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['EVENT_SAMPLING_LOG_PATH']
        mkdir(log_path)
        prepare_cmd = case_config['PREPARE']['CMD1'] % sample_1_path
        run_loc_cmd(prepare_cmd)
        sm = get_sm()
        # build the event_sampling sample
        cmd = case_config['EVENT_SAMPLING']['STEP1']['CMD']
        check_result(cmd, 'build_event_sampling', log_name, result)
        # run event_sampling sample
        run_event_sampling = case_config['EVENT_SAMPLING']['STEP2']['CMD']
        if sm < 7.2:
            check_result(run_event_sampling, 'run_event_sampling by %s' % run_event_sampling, log_name, result)
        else:
            if cuda_short_version < '11.4':
                check_point = case_config['EVENT_SAMPLING']['CHECK_POINT1_1']
            else:
                check_point = case_config['EVENT_SAMPLING']['CHECK_POINT']
            check_result(run_event_sampling,
                         'run_event_sampling by %s' % run_event_sampling,
                         log_name,
                         result,
                         check_point,
                         flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/event_sampling.json' % case_config['global']['env']['EVENT_SAMPLING_LOG_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case----event_sampling in vgpu env and since cuda 13.0')
        return WAIVED


def cupti_metric_properties():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_METRIC_PROPERTIES']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_METRIC_PROPERTIES_LOG_PATH']
    mkdir(log_path)
    sm = get_sm()
    # prepare sample
    prepare_cmd = case_config['CUPTI_METRIC_PROPERTIES']['PREPARE']['CMD1']
    prepare_cmd1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP1']['CMD']
    check_result(prepare_cmd, 'prepare_build_env', log_name, result)
    check_result(prepare_cmd1, 'build_cupti_metric_properties', log_name, result)
    # run the sample
    cmd1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD1']
    if cuda_short_version < '13.0':
        cmd2 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % chip_list[random.randrange(0, len(chip_list))]
    else:
        cmd2 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % chip_list[random.randrange(0, len(chip_list))].upper()
    cmd2_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD2'] % 'random'
    cmd3 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD3']
    cmd4 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD4']
    cmd4_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD4_1']
    cmd5 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD5']
    cmd5_1 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD5_1']
    cmd6 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD6']
    if cuda_short_version > '12.4':
        cmd6 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD6_1']
        cmd7 = case_config['CUPTI_METRIC_PROPERTIES']['STEP2']['CMD7']
    check_point = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT']
    if cuda_short_version < '13.0':
        check_point1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT1']
    else:
        check_point1 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT1_1']
    check_point2 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT2']
    check_point3 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT3']
    check_point4 = case_config['CUPTI_METRIC_PROPERTIES']['CHECK_POINT4']
    if sm < 7.0:
        check_result(cmd1, 'run-cupti_metric_properties', log_name, result, check_point, flag=1)
        check_result(cmd2, 'run-cupti_metric_properties-with exist chip option', log_name, result)
        check_result(cmd2_1, 'run-cupti_metric_properties--with no-exist chip', log_name, result, check_point1)
        check_result(cmd3, 'run-cupti_metric_properties--with device id 0', log_name, result, check_point, flag=1)
        if cuda_short_version < '12.5':
            check_result(cmd4, 'run-cupti_metric_properties --with filename test.log', log_name, result, check_point, flag=1)
            # need check cmd4_1
            check_result(cmd4_1, 'run-cupti_metric_properties--with filename empty', log_name, result, check_point4, flag=1)
            check_result(cmd5, 'run-cupti_metric_properties --with csv option', log_name, result, check_point, flag=1)
        check_result(cmd6, 'run-cupti_metric_properties --with metric option', log_name, result, check_point, flag=1)
        if cuda_short_version > '12.4':
            check_result(cmd7,
                         'run-cupti_metric_properties --with metrics option submetrices',
                         log_name,
                         result,
                         check_point,
                         flag=1)
    else:
        check_result(cmd1, 'run-cupti_metric_properties', log_name, result)
        check_result(cmd2, 'run-cupti_metric_properties-with exist chip option', log_name, result)
        if cuda_short_version >= '12.5':
            check_result(cmd2_1, 'run-cupti_metric_properties--with no-exsit chip', log_name, result, check_point1, flag=1)
        else:
            check_result(cmd2_1, 'run-cupti_metric_properties--with no-exsit chip', log_name, result, check_point1)
        check_result(cmd3, 'run-cupti_metric_properties--with device id 0', log_name, result)
        if cuda_short_version < '12.5':
            check_result(cmd4, 'run-cupti_metric_properties --with filename test.log', log_name, result, check_point2)
            # need check cmd4_1
            check_result(cmd4_1, 'run-cupti_metric_properties--with filename empty', log_name, result, check_point4, flag=1)
            check_result(cmd5, 'run-cupti_metric_properties --with csv option', log_name, result, check_point3)
            check_result(cmd5_1,
                         'run-cupti_metric_properties--with csv option and filename empty',
                         log_name,
                         result,
                         check_point4,
                         flag=1)
        check_result(cmd6,
                     'run-cupti_metric_properties --with metrics option',
                     log_name,
                     result,
                     'sm__mioc_inst_issued.sum',
                     'sm__sass_inst_executed.avg')
        check_result(cmd6,
                     'run-cupti_metric_properties --with metrics option to check no error message',
                     log_name,
                     result,
                     'error',
                     'fail', flag=6)
        if cuda_short_version > '12.4':
            if cuda_short_version < '13.0':
                check_point_submetrices = 'Submetrics'
            else:
                check_point_submetrices = 'Sub-metrics'
            check_result(cmd7,
                         'run-cupti_metric_properties --with metrics option submetrices',
                         log_name,
                         result,
                         'sm__mioc_inst_issued.sum',
                         'sm__sass_inst_executed.avg',
                         check_point_submetrices)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_metric_properties.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_period_increase():
    if cuda_short_version < '13.0':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_PERIOD_INCREASE']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_PERIOD_INCREASE_PATH']
        mkdir(log_path)
        # prepare sample
        cmd1 = case_config['PC_SAMPLING_PERIOD_INCREASE']['PREPARE']['CMD']
        cmd2 = case_config['PC_SAMPLING_PERIOD_INCREASE']['PREPARE']['CMD1']
        check_result(cmd1, 'prepare_pc_sampling', log_name, result)

        def get_run_cmd():
            out = run_loc_cmd(cmd2)
            replace_message = out['output']
            return replace_message

        # run pc_sampling with different value
        step1_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP1']['CMD']
        step2_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP2']['CMD']
        step3_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP3']['CMD']
        step4_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP4']['CMD']
        step5_cmd = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP5']['CMD']

        def run_pc_sampling(label, cmd):
            cmd1 = cmd % get_run_cmd()
            out = run_loc_cmd(cmd1)
            save_log(log_name, cmd, label, out['output'])
            print('==CMD==\n%s' % cmd)
            print('==RESULT==\n%s' % out['output'])
            if out.succeeded:
                import re
                match = re.search(r'samplingPeriodInCycles (\d+)', out['output'])
                if match:
                    value = match.group(1)
                else:
                    logger.error('cannot get the samplingPeriodInCycles value')
                    value = 'None'
                return value
            else:
                logger.error('we run this case fail')

        min_value = run_pc_sampling('step1_min', step1_cmd)
        low_value = run_pc_sampling('step2_low', step2_cmd)
        mid_value = run_pc_sampling('step3_mid', step3_cmd)
        high_value = run_pc_sampling('step4_high', step4_cmd)
        max_value = run_pc_sampling('step5_max', step5_cmd)
        value_list = [min_value, low_value, mid_value, high_value, max_value]
        value = ['min', 'low', 'mid', 'high', 'max']
        value_dict = dict(zip(value, value_list))
        print('the totalsamples value list is %s' % value_list)
        print('the totalsamples value dict is %s' % value_dict)
        value_list1 = sorted(value_list)
        print('sorted the totalSamples value list is %s' % value_list1)
        value_list2 = value_list[::-1]
        print('reverse the totalSamples value list is %s' % value_list2)
        if value_list1 == value_list2:
            logger.info('we run pc_sampling_period increase successful')
            result['pc_sampling_period_increase'] = 'passed'
        else:
            logger.error('we run pc_sampling_period increase fail, the totalsample value should be min>=low>=mid, please check it')
            result['pc_sampling_period_increase'] = 'failed'
        # restore the pc sampling
        cmd6 = case_config['PC_SAMPLING_PERIOD_INCREASE']['STEP6']['CMD']
        check_result(cmd6, 'restore_pc_sampling', log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pc_sampling_period_increase.json' % case_config['global']['env']['PC_SAMPLING_PERIOD_INCREASE_PATH'])
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('not support this case since cuda 13.0')
        return WAIVED


def collect_pc_sampling_data():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['COLLECT_PC_SAMPLING_DATA']['LOG_NAME']
    log_path = case_config['global']['env']['COLLECT_PC_SAMPLING_DATA_PATH']
    mkdir(log_path)
    # prepare sample
    file_list = ['Makefile', 'NsightEclipse.xml', 'readme.txt', 'simpleStreams.cu', 'Makefile-11.6']
    cmd = case_config['COLLECT_PC_SAMPLING_DATA']['PREPARE']['CMD'] % sample_0_path
    check_result(cmd, 'prepare_download_PATH---%s' % cmd, log_name, result)
    cmd1 = case_config['COLLECT_PC_SAMPLING_DATA']['PREPARE']['CMD1']
    cmd2 = case_config['COLLECT_PC_SAMPLING_DATA']['PREPARE']['CMD2'] % sample_0_path
    for index, file in enumerate(file_list):
        cmd1_1 = cmd1 % (sample_0_path, user, password, base_url, file)
        check_result(cmd1_1, 'prepare_file-%s' % file, log_name, result)
    if cuda_short_version >= '11.6':
        cmd3 = 'cd %s/simpleStreamsCupti; rm Makefile; mv Makefile-11.6 Makefile' % sample_0_path
        check_result(cmd3, 'change the Makefile for 11.6---%s' % cmd3, log_name, result)
    check_result(cmd2, 'build_and_run_sample', log_name, result)
    # step1 prepare pc_sampling_continuous
    step1_cmd = case_config['COLLECT_PC_SAMPLING_DATA']['STEP1']['BUILD_SAMPLING']
    check_result(step1_cmd, 'prepare------pc_sampling_continuous by cmd-----%s' % step1_cmd, log_name, result)
    # step2 collect the file
    step2_cmd = case_config['COLLECT_PC_SAMPLING_DATA']['STEP2']['CMD'] % sample_0_path
    check_result(step2_cmd, 'collect-pc_sampling_data--%s' % step2_cmd, log_name, result)
    # step3 check .dat file
    step3_cmd = case_config['COLLECT_PC_SAMPLING_DATA']['STEP3']['CMD'] % sample_0_path
    check_point = case_config['COLLECT_PC_SAMPLING_DATA']['STEP3']['CHECK_POINT']
    check_result(step3_cmd, 'check-result-------%s' % step3_cmd, log_name, result, check_point)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/collect_pc_sampling_data.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def checkpoint_kernels():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CHECKPOINT_KERNELS']['LOG_NAME']
    log_path = case_config['global']['env']['CHECKPOINT_KERNELS_PATH']
    mkdir(log_path)
    # build the checkpoint kernels sample
    step1_cmd = case_config['CHECKPOINT_KERNELS']['STEP1']['CMD']
    check_result(step1_cmd, 'build_checkpoint_kernals-------%s' % step1_cmd, log_name, result)
    # run checkpoint_kernels sample
    run_checkpoint_kernels = case_config['CHECKPOINT_KERNELS']['STEP2']['CMD']
    check_result(run_checkpoint_kernels, 'run_checkpoint_kernels------%s' % run_checkpoint_kernels, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/checkpoint_kernels.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def check_cupti_output(output, expect_message):
    print(output)
    new_output = [line for line in output.split('\n') if expect_message not in line]
    new_output_string = ''
    for line in new_output:
        new_output_string += line
    pattern = ['error', 'ERROR', 'fail', 'unsupport']
    ret = 0
    for pat in pattern:
        if re.search(pat, new_output_string):
            logger.error('check {} fail, {} is in output'.format(pat, pat))
            ret = 1
            break
    return ret


def cupti_external_correlation():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_EXTERNAL_CORRELATION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_EXTERNAL_CORRELATION_PATH']
    mkdir(log_path)
    # build the cupti_external_correlation sample
    cmd = case_config['CUPTI_EXTERNAL_CORRELATION']['STEP1']['CMD']
    check_result(cmd, 'build_cupti_external_correlation', log_name, result)
    # run cupti_external_correlation sample
    step1_cmd1 = case_config['CUPTI_EXTERNAL_CORRELATION']['STEP2']['CMD']
    # check_result(run_cupti_external_correlation, 'run_cupti_external_correlation------%s' % run_cupti_external_correlation, log_name, result, 'err', 'fail', flag=6)
    out = run_test_cmd(step1_cmd1)
    ret = check_cupti_output(out.output, 'Calling CUPTI API')
    if out.rc == 0 and ret == 0:
        result['run {}'.format(step1_cmd1)] = 'passed'
        logger.info('run cupti_external_correlation by {} successful'.format(step1_cmd1))
        print('\n\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n\n')
    else:
        result['run {}'.format(step1_cmd1)] = 'failed'
        logger.info('run cupti_external_correlation by {} fail'.format(step1_cmd1))
    save_log(log_name, step1_cmd1, 'run {}'.format(step1_cmd1), out.output)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_external_correlation.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_mps():
    if cuda_short_version > '11.4':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_MPS']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_MPS_PATH']
        mkdir(log_path)
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        if platform == 'arm':
            step1_cmd = case_config['CUPTI_MPS']['STEP1']['CMD_1'] % cupti_run_path
            step2_cmd = case_config['CUPTI_MPS']['STEP2']['CMD_1'] % cupti_run_path
        else:
            step1_cmd = case_config['CUPTI_MPS']['STEP1']['CMD'] % cupti_run_path
            step2_cmd = case_config['CUPTI_MPS']['STEP2']['CMD'] % cupti_run_path
        # run step1 mps L0 testing
        check_result(step1_cmd, 'we run MPS L0 testing by %s' % step1_cmd, log_name, result)
        # run step2 MPS L1 testing
        check_result(step2_cmd, 'we run MPS L1 testing by %s' % step2_cmd, log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_mps.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('no need run cupti_mps if cuda version is less than 11.5')
        return WAIVED


def cupti_memcheck_support():
    if cuda_short_version > '11.5':
        if platform == 'x86':
            result = {}
            passed, failed = 0, 0
            log_name = case_config['CUPTI_MEMCHECK_SUPPORT']['LOG_NAME']
            log_path = case_config['global']['env']['CUPTI_MEMCHECK_SUPPORT_PATH']
            mkdir(log_path)
            prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
            # prepare install virg
            prepare_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['PREPARE']['CMD']
            run_loc_cmd(prepare_cmd)
            if vgpu == '1':
                step1_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP1']['CMD_1'] % (cupti_run_path, '1:1')
                step2_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP2']['CMD_1'] % (cupti_run_path, '1:1')
                step3_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP3']['CMD_1'] % (cupti_run_path, '1:1')
            elif vgpu == 'n':
                step1_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP1']['CMD_1'] % (cupti_run_path, 'N:1')
                step2_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP2']['CMD_1'] % (cupti_run_path, 'N:1')
                step3_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP3']['CMD_1'] % (cupti_run_path, 'N:1')
            else:
                step1_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP1']['CMD'] % cupti_run_path
                step2_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP2']['CMD'] % cupti_run_path
                step3_cmd = case_config['CUPTI_MEMCHECK_SUPPORT']['STEP3']['CMD'] % cupti_run_path
            # run step1 leak check testing
            check_result(step1_cmd, 'we run leak check testing by %s' % step1_cmd, log_name, result)
            # run step2 memcheck check testing
            check_result(step2_cmd, 'we run memcheck check testing by %s' % step2_cmd, log_name, result)
            # run step3 race check testing
            check_result(step3_cmd, 'we run race check testing by %s' % step3_cmd, log_name, result)
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '%s/cupti_memcheck_support.json' % log_path)
            return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
        else:
            logger.info('cuda_memcheck_support only can run on X86, now the platform is %s' % platform)
            return WAIVED
    else:
        logger.info('no need run cupti_memcheck_support if cuda version is less than 11.6')
        return WAIVED


def cupti_2_profiling():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_2_PROFILING']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_2_PROFILING_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare samples
    prepare_cmd = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD'] % (cupti_target_path, cupti_target_path)
    prepare_cmd1 = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['CUPTI_2_PROFILING']['PREPARE']['CMD2']
    for cmd in [prepare_cmd, prepare_cmd1, prepare_cmd2]:
        print(cmd)
        run_loc_cmd(cmd)
    result1 = {}
    cmd1 = 'cd %s; rm *' % log_path
    run_loc_cmd(cmd1)
    step1_file = '%s/step1_alignedTypes_cmd.log' % log_path
    step1_file1 = '%s/step1_pc_sampling_cmd1.log' % log_path
    step2_file = '%s/step2_alignedTypes.log' % log_path
    step2_file1 = '%s/step2_matrixMul.log' % log_path
    # run step1
    if cuda_short_version < '11.7':
        step1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD'] % cupti_target_path
    else:
        step1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % cupti_target_path
    step1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1']['CMD1']
    print(step1_cmd1)
    print(step1_cmd)
    time.sleep(20)
    threads1 = []
    threads1.append(threading.Thread(target=check_result, args=(step1_cmd, 'run step1_cmd %s' % step1_cmd, step1_file, result1)))
    threads1.append(
        threading.Thread(target=check_result, args=(step1_cmd1, 'run step1_cmd1 %s' % step1_cmd1, step1_file1, result1)))
    for t1 in threads1:
        t1.start()
    time.sleep(30)
    # run step2
    if cuda_short_version < '11.7':
        step2_cmd = case_config['CUPTI_2_PROFILING']['STEP2']['CMD'] % cupti_target_path
        step2_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD1'] % cupti_target_path
    else:
        step2_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % cupti_target_path
        step2_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2'] % cupti_target_path
    threads2 = []
    threads2.append(threading.Thread(target=check_result, args=(step2_cmd, 'run step2_cmd %s' % step2_cmd, step2_file, result1)))
    threads2.append(
        threading.Thread(target=check_result, args=(step2_cmd1, 'run step2_cmd1 %s' % step2_cmd1, step2_file1, result1)))
    for t2 in threads2:
        t2.start()
    time.sleep(20)
    # run step1_1 and step2_1, assure single run the command can pass
    if cuda_short_version < '11.7':
        step1_1_cmd = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD'] % cupti_target_path
        step1_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD1']
        step2_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2_1']['CMD1'] % cupti_target_path
    else:
        step1_1_cmd = case_config['CUPTI_2_PROFILING']['STEP1']['CMD2'] % cupti_target_path
        step1_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP1_1']['CMD1']
        step2_1_cmd1 = case_config['CUPTI_2_PROFILING']['STEP2']['CMD2'] % cupti_target_path
    for cmd in [step1_1_cmd, step1_1_cmd1, step2_1_cmd1]:
        time.sleep(15)
        check_result(cmd, 'verify single command can run pass by %s' % cmd, log_name, result)
    check_point = case_config['CUPTI_2_PROFILING']['CHECK_POINT']
    step1_cmd_status = search_keyword_in_file('%s/step1_alignedTypes_cmd.log' % log_path, check_point)
    step1_cmd1_status = search_keyword_in_file('%s/step1_pc_sampling_cmd1.log' % log_path, check_point)
    step2_cmd_status = search_keyword_in_file('%s/step2_alignedTypes.log' % log_path, check_point)
    step2_cmd1_status = search_keyword_in_file('%s/step2_matrixMul.log' % log_path, check_point)
    status_list = [step1_cmd_status, step1_cmd1_status, step2_cmd1_status, step2_cmd_status]
    cmd_list = [step1_cmd, step1_cmd1, step2_cmd1, step2_cmd]
    cmd_dict = dict(zip(cmd_list, status_list))
    print(cmd_dict)
    for key, value in cmd_dict.items():
        if value is True:
            logger.info('we can find the file by run command--- %s' % key)
        else:
            logger.info('we can find the file by run command--- %s' % key)
    # check step1 result
    if step1_cmd1_status ^ step1_cmd_status is True:
        logger.info('we run step1 successful')
        result['step1_check_point---%s' % check_point] = 'passed'
    else:
        logger.info('we run the step1 fail')
        result['step1_check_point---%s' % check_point] = 'failed'
    # check step2 result
    if step2_cmd1_status ^ step2_cmd_status is True:
        logger.info('we run step2 successful')
        result['step2_check_point---%s' % check_point] = 'passed'
    else:
        logger.info('we run the step2 fail')
        result['step2_check_point---%s' % check_point] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_2_profiling.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cuda_memory_trace():
    if cuda_short_version > '11.5':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUDA_MEMORY_TRACE']['LOG_NAME']
        log_path = case_config['global']['env']['CUDA_MEMORY_TRACE_PATH']
        mkdir(log_path)
        logger.info('now the vgpu is %s' % vgpu)
        prepare_cmd = case_config['CUDA_MEMORY_TRACE']['PREPARE']['CMD']
        step1_cmd = case_config['CUDA_MEMORY_TRACE']['STEP1']['CMD']
        check_point = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT']
        check_point1 = case_config['CUDA_MEMORY_TRACE']['CHECK_POINT1']
        # build cupti_memory_trace sample
        check_result(prepare_cmd, 'we build cuda_memory_trace by %s' % prepare_cmd, log_name, result)
        if run_uvm_sample() is False and vgpu != 'none':
            check_point_uvm = 'failed with error operation not supported'
            check_result(step1_cmd,
                         'we run cuda_memory_trace with uvm disabled by %s' % step1_cmd,
                         log_name,
                         result,
                         check_point_uvm,
                         flag=1)
        else:
            # run cupti_memory_trace sample
            if cuda_short_version >= '12.1':
                out = run_test_cmd(step1_cmd)
                ret = check_cupti_output(out.output, 'Calling CUPTI API')
                check_list = ['memoryPoolOperation MEM_POOL_CREATED', 'memoryPoolOperation MEM_POOL_TRIMMED', 'MEMORY_POOL', 'memoryPool LOCAL', 'MEMORY2', 'memoryOperationType ALLOCATE', 'memoryKind DEVICE', 'memoryPoolAddress', 'memoryPoolThreshold', 'memoryPoolSize']

                def check_content(list1):
                    pass1, fail1 = 0, 0
                    for content in list1:
                        if content in out.output:
                            pass1 += 1
                        else:
                            fail1 += 1
                            logger.error('{} not in output, please check'.format(content))
                    return pass1
                pass_content_number = check_content(check_list)
                if out.rc == 0 and ret == 0 and pass_content_number == len(check_list):
                    result['run {}'.format(step1_cmd)] = 'passed'
                    logger.info('run cuda_memory_trace by {} successful'.format(step1_cmd))
                    print('\n\n')
                    logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
                    print('\n\n')
                else:
                    result['run {}'.format(step1_cmd)] = 'failed'
                    logger.info('run cuda_memory_trace by {} fail'.format(step1_cmd))
                save_log(log_name, step1_cmd, 'run {}'.format(step1_cmd), out.output)
                # check_result(step1_cmd, 'we run cuda_memory_trace by %s' % step1_cmd, log_name, result, 'memoryPoolOperation MEM_POOL_CREATED', 'memoryPoolOperation MEM_POOL_TRIMMED')
            else:
                check_result(step1_cmd, 'we run cuda_memory_trace by %s' % step1_cmd, log_name, result, check_point, check_point1)
                out = run_loc_cmd(step1_cmd)
                print(out['output'])
                print('+++++++++++++++++++++++++++++')
                index_list = []
                for index, value in enumerate(out['output'].split(',')):
                    if 'MEMORY_POOL' in value:
                        value1 = value.split(']')[0].split('[')[-1].strip(' ').lower()
                        if value1 != "None" or value1 != "N/A":
                            logger.info('we check the value scucessful, the MEMORY_POOL is %s' % value1)
                            result['check MEMORY_POOL is %s' % value1] = 'passed'
                        else:
                            logger.info('we check the value fail, the MEMORY_POOL is %s' % value1)
                            result['check MEMORY_POOL is %s' % value1] = 'failed'
                        index_list.append(index)
                fail = 0
                for i in index_list:
                    if out['output'].split(',')[int(i) + 1] != ' memoryPoolType LOCAL':
                        fail += 1
                if fail == 0:
                    logger.info('we check memoryPoolType LOCAL scucessful')
                    result['check memoryPoolType LOCAL after memoryPoolOperationType'] = 'passed'
                else:
                    logger.info('we check memoryPoolType LOCAL fail')
                    result['check memoryPoolType LOCAL after memoryPoolOperationType'] = 'failed'
            cmd = 'nvidia-smi -L|grep MIG'
            out = run_loc_cmd(cmd)
            if out.succeeded:
                print('check mig env ')
                mig1, mig2 = [], []
                for j in out['output'].split('\n'):
                    i = j.strip(' ')
                    if i != ' ':
                        mig2.append(i.split(' ')[-1].split(')')[0])
                        mig1.append(i.split(' ')[1])
                dict_mig = dict(zip(mig2, mig1))
                cuda_devices1 = "echo $CUDA_VISIBLE_DEVICES"
                cuda_devices_out = run_loc_cmd(cuda_devices1)
                cuda_devices = cuda_devices_out['output'].split('\n')[0]
                if cuda_devices != '':
                    check_mig = dict_mig[cuda_devices]
                    check_result(step1_cmd, 'check_mig env--{}'.format(check_mig), log_name, result, check_mig)
                else:
                    pass1 = 0
                    for check_mig in mig1:
                        out1 = run_loc_cmd(step1_cmd)
                        if out1.succeeded and check_mig in out1['output']:
                            pass1 += 1
                    if pass1 != 0:
                        logger.info('we check mig env successful')
                        result['check mig env'] = 'passed'
                    else:
                        logger.info('we check mig env failed, please make sure one of {} in output '.format(mig1))
                        result['check mig env'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cuda_memory_trace.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('no need run cuda_memory_trace if cuda version is less than 11.6')
        return WAIVED


def pc_sampling_stop_api():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_STOP_API']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_STOP_API_PATH']
    mkdir(log_path)
    # prepare samples
    prepare_cmd1 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD1']
    prepare_cmd2 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD2'] % (user, password, base_url)
    prepare_cmd3 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD3']
    prepare_cmd4 = case_config['PC_SAMPLING_STOP_API']['PREPARE']['CMD4']
    check_result(prepare_cmd1, 'create folder by %s' % prepare_cmd1, log_name, result)
    check_result(prepare_cmd2, 'download .cu file by %s' % prepare_cmd2, log_name, result)
    check_result(prepare_cmd3, 'run prepare_build sample cmd3 by %s' % prepare_cmd3, log_name, result)
    check_result(prepare_cmd4, 'run prepare_build sample cmd4 by %s' % prepare_cmd4, log_name, result)
    # run the sample
    step1_cmd1 = case_config['PC_SAMPLING_STOP_API']['STEP1']['CMD1']
    out = run_loc_cmd(step1_cmd1)
    print(out['output'])
    list_1 = [i.split(' ')[-1].strip('s') for i in out['output'].split('\n') if 'PC sampling stop' in i]
    save_log(log_name, step1_cmd1, 'run step1 by %s' % step1_cmd1, out['output'])
    if out.succeeded and list_1[0] < '0.01' and list_1[1] < '0.01':
        print(out.succeeded and list_1[0] < '0.01' and list_1[1] < '0.01')
        result['run step1 by %s' % step1_cmd1] = 'passed'
        logger.info('we run step1 by %s successful' % step1_cmd1)
    else:
        result['run step1 by %s' % step1_cmd1] = 'failed'
        logger.info('we run step1 by %s failed' % step1_cmd1)
    step2_cmd1 = case_config['PC_SAMPLING_STOP_API']['STEP2']['CMD1']
    check_result(step2_cmd1, 'delete the folder by {}'.format(step2_cmd1), log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_stop_api.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_query_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_QUERY_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_QUERY_SAMPLE_PATH']
    mkdir(log_path)
    prepare_cmd = case_config['PREPARE']['CMD1'] % sample_1_path
    run_loc_cmd(prepare_cmd)
    sm = get_sm()
    # build the event_sampling sample
    cmd = case_config['CUPTI_QUERY_SAMPLE']['STEP1']['CMD']
    check_result(cmd, 'build_cupti_query', log_name, result)
    # run event_sampling sample
    run_event_sampling = case_config['CUPTI_QUERY_SAMPLE']['STEP2']['CMD']
    if sm < 7.2:
        check_result(run_event_sampling, 'run_cupti_query by %s' % run_event_sampling, log_name, result)
    else:
        check_point = case_config['CUPTI_QUERY_SAMPLE']['CHECK_POINT']
        check_result(run_event_sampling, 'run_cupti_query by %s' % run_event_sampling, log_name, result, check_point, flag=1)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_query_sample.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_profile_coverage():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_PROFILE_COVERAGE_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare sample
    cmd = case_config['CUPTI_PROFILE_COVERAGE']['CMD'] % cupti_target_path
    check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
    if cuda_short_version < '13.0':
        cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['CMD1'] % (cupti_target_path, cuhook_path)
        check_result(cmd1, 'prepare_cuhook by %s' % cmd1, log_name, result)
    # profile sample list
    checkpoint = case_config['CUPTI_PROFILE_COVERAGE']['CHECK_POINT']
    if vgpu != 'none':
        sample_list = case_config['CUPTI_PROFILE_COVERAGE']['SAMPLE_LIST1'].split(',')
        if run_uvm_sample() is True:
            sample_list.append('UnifiedMemoryStreams')
    else:
        sample_list = case_config['CUPTI_PROFILE_COVERAGE']['SAMPLE_LIST'].split(',')
        if platform == 'arm':
            sample_list = [i for i in sample_list if i not in ['cuHook', 'vectorAddMMAP', 'UnifiedMemoryStreams']]
    glibc_version = get_glic_version()
    logger.info('glibc version is {}'.format(glibc_version))
    if glibc_version > '2.33' or cuda_short_version > '12.9':
        sample_list = [i for i in sample_list if i != 'cuHook']
    for sample in sample_list:
        if sample == 'simpleCudaGraphs':
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, 'Error', checkpoint, flag=2)
            continue
        step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
        step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
        step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)
        if sample in ['UnifiedMemoryStreams']:
            step2_cmd1 = "cd %s;LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -r auto -e application -j step2_%s.json -a ./%s" % (
                cupti_target_path, sample, sample)
        if cuda_short_version < '13.0':
            if sample == 'cuHook':
                step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP1']['CMD1_1'] % cupti_target_path
                step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP3']['CMD1_1'] % cupti_target_path
                step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP2']['CMD1_1'] % cupti_target_path
        check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, 'Error', checkpoint, flag=2)
        check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, 'Error', checkpoint, flag=2)
        check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, 'Error', checkpoint, flag=2)
    # run nvtx range
    step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD1'] % cupti_target_path
    step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE']['STEP4']['CMD2'] % cupti_target_path
    check_result(step4_cmd1, 'run step4_cmd1 by %s' % step4_cmd1, log_name, result, 'Error', checkpoint, flag=2)
    check_result(step4_cmd2, 'run step4_cmd1 by %s' % step4_cmd2, log_name, result, 'Error', checkpoint, flag=2)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_coverage.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_trace_injection():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_LOG_PATH']
    mkdir(log_path)
    # prepare sample
    cmd2 = case_config['CUPTI_TRACE_INJECTION']['PREPARE']['CMD2']
    check_result(cmd2, 'build---cupti_trace_injection by %s' % cmd2, log_name, result)
    # run sample
    step1_cmd1 = case_config['CUPTI_TRACE_INJECTION']['STEP1']['CMD1']
    print(step1_cmd1)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    cmd1 = case_config['CUPTI_TRACE_INJECTION']['PREPARE']['CMD1'] % cupti_target_path
    check_result(cmd1, 'prepare_sample by %s' % cmd1, log_name, result)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    dict1["tests"].append({"name": "asyncAPI", "exe": "asyncAPI"})
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    dict_to_json(dict2, '%s' % (cupti_run_path + '/' + 'asyncAPI.json'))
    out = run_loc_cmd(step1_cmd1)
    print(out['output'])
    ret = check_cupti_output(out['output'], 'Calling CUPTI API')
    if out.succeeded and ret == 0:
        result['run {}'.format(step1_cmd1)] = 'passed'
        logger.info('run cupti_trace_injection by {} successful'.format(step1_cmd1))
        print('\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n')
    else:
        result['run {}'.format(step1_cmd1)] = 'failed'
        logger.info('run cupti_trace_injection by {} fail'.format(step1_cmd1))
    save_log(log_name, step1_cmd1, 'run {}'.format(step1_cmd1), out.output)
    # run cupti trace injection by cupti dvs package
    cmd = 'cd %s; python CuptiSmoke.py --testconfig tracing-injection --testlist asyncAPI.json| tee asyncAPI_injection.txt' % cupti_run_path
    check_result(cmd, 'run_cupti-trace-injection---%s' % cmd, log_name, result)
    if cuda_short_version < '12.4':
        driver, runtime, memcpy, memset, kernel = 0, 0, 0, 0, 0
        for line in out['output'].split('\n'):
            if 'DRIVER' in line:
                driver += 1
            if 'RUNTIME' in line:
                runtime += 1
            if 'MEMCPY' in line:
                memcpy += 1
            if 'KERNEL' in line:
                kernel += 1
            if 'MEMSET' in line:
                memset += 1
        logger.info('we run cupti_trace_injection with asyncAPI successful')
        result_list = [driver, runtime, memcpy, kernel, memset]
        # run cupti trace injection by cupti dvs package
        cmd = 'cd %s; python CuptiSmoke.py --testconfig tracing-injection --testlist asyncAPI.json| tee asyncAPI_injection.txt' % cupti_run_path
        check_result(cmd, 'run_cupti-trace-injection---%s' % cmd, log_name, result)

        # get the count of api

        def get_count(path, pattern1, pattern2, file1):
            cmd = "cd %s; sed -n '/%s/,/%s/{//!p}' %s| awk '{print $5}'" % (path, pattern1, pattern2, file1)
            out1 = run_loc_cmd(cmd)
            count_list = []
            print('the output of the cmd---%s is %s' % (cmd, out1['output']))
            for i in out1['output'].split('\n'):
                if i != 'Count' and i != ' ':
                    count_list.append(int(i))
            return sum(count_list)

        print('-------------------------------------------')
        driver1 = get_count(cupti_run_path, 'Driver API', 'NVTX Range', 'asyncAPI_injection.txt')
        runtime1 = get_count(cupti_run_path, 'Runtime API', 'Driver API', 'asyncAPI_injection.txt')
        kernel1 = get_count(cupti_run_path, 'Kernel(s)', 'Memcopies', 'asyncAPI_injection.txt')
        memcpy1 = get_count(cupti_run_path, 'Memcopies', 'Memsets', 'asyncAPI_injection.txt')
        memset1 = get_count(cupti_run_path, 'Memsets', 'CUDA API', 'asyncAPI_injection.txt')
        result_list1 = [driver1, runtime1, memcpy1, kernel1, memset1]
    else:
        driver, runtime, memcpy, memset, kernel = 0, 0, 0, 0, 0
        for line in out['output'].split('\n'):
            if 'DRIVER' in line[:6]:
                driver += 1
            if 'RUNTIME' in line[:7]:
                runtime += 1
            if 'MEMCPY' in line[:6]:
                memcpy += 1
            if 'KERNEL' in line and 'Disabling' not in line and 'Enabling' not in line:
                kernel += 1
            if 'MEMSET' in line and 'Disabling' not in line and 'Enabling' not in line:
                memset += 1
        result_list = [driver, runtime, memcpy, kernel, memset]
        # get number from cupti trace injection script
        cmd_trace = 'grep -A 6 "Kernels  :" {}/asyncAPI_injection.txt'.format(cupti_run_path)
        trace_out = run_loc_cmd(cmd_trace)
        driver1, runtime1, memcpy1, memset1, kernel1 = 0, 0, 0, 0, 0
        for line in trace_out['output'].split('\n'):
            print(line)
            if 'Driver' in line:
                driver1 += int(line.split(':')[-1].strip(' '))
            if 'Runtime' in line:
                runtime1 += int(line.split(':')[-1].strip(' '))
            if 'Memcopies' in line:
                memcpy1 += int(line.split(':')[-1].strip(' '))
            if 'Kernels' in line:
                kernel1 += int(line.split(':')[-1].strip(' '))
            if 'Memset' in line:
                memset1 += int(line.split(':')[-1].strip(' '))
        result_list1 = [driver1, runtime1, memcpy1, kernel1, memset1]
    print('Driver: %s; Runtime: %s; Memcpy: %s; Memset: %s; Kernel: %s' % (driver, runtime, memcpy, memset, kernel))
    print('-------------------------------------------')
    print('use cupti_trace_injection run asyncAPI------the DriverTime: %s; Runtime: %s, Kernel: %s; Memcpy: %s; Memset: %s' %
          (driver, runtime, kernel, memcpy, memset))
    print('use cupti smoke run asyncAPI------the DriverTime: %s; Runtime: %s, Kernel: %s; Memcpy: %s; Memset: %s' %
          (driver1, runtime1, kernel1, memcpy1, memset1))
    print('++++++++++++++++++++++++++++++++++++++++++++')
    if result_list1 == result_list:
        logger.info('we compare_with_smoke result passed')
        result['compare_with_smoke result'] = 'passed'
    else:
        logger.info('we compare_with_smoke result failed')
        result['compare_with_smoke result'] = 'failed'
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_trace_injection.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_correlation():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_CORRELATION']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_CORRELATION_LOG_PATH']
    mkdir(log_path)
    # prepare
    cmd1 = case_config['CUPTI_CORRELATION']['PREPARE']['CMD1']
    check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
    # run the sample
    step1_cmd1 = case_config['CUPTI_CORRELATION']['STEP1']['CMD1']
    # check_result(step1_cmd1, 'run_sample by %s' % step1_cmd1, log_name, result, 'ERROR', 'error', flag=2)
    out = run_test_cmd(step1_cmd1)
    ret = check_cupti_output(out.output, 'Calling CUPTI API')
    if out.rc == 0 and ret == 0:
        result['run {}'.format(step1_cmd1)] = 'passed'
        logger.info('run cupti_correlation by {} successful'.format(step1_cmd1))
        print('\n')
        logger.info('there is no ERR0R, error, fail, unsupport in output, except CUPTI_CBID_STATE_FATAL_ERROR')
        print('\n')
    else:
        result['run {}'.format(step1_cmd1)] = 'failed'
        logger.info('run cupti_correlation by {} fail'.format(step1_cmd1))
    save_log(log_name, step1_cmd1, 'run {}'.format(step1_cmd1), out.output)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_correlation.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def pc_sampling_coverage_2804521():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_COVERAGE']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_COVERAGE_LOG_PATH']
    mkdir(log_path)
    # prepare
    cmd1 = case_config['PC_SAMPLING_COVERAGE']['PREPARE']['CMD1']
    check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
    cmd = 'cd {}, rm *.dat'.format(sample_bin_path)
    run_loc_cmd(cmd)
    # run test
    if vgpu != 'none':
        sample_list = case_config['PC_SAMPLING_COVERAGE']['SAMPLE_LIST'].split(',')
        if run_uvm_sample() is True:
            sample_list.append('UnifiedMemoryStreams')
    else:
        if platform == 'arm':
            # vectorAddMMAP,cuHook not support on arm
            sample_list = case_config['PC_SAMPLING_COVERAGE']['SAMPLE_LIST2'].split(',')
        else:
            sample_list = case_config['PC_SAMPLING_COVERAGE']['SAMPLE_LIST1'].split(',')
    glibc_version = get_glic_version()
    logger.info('glibc version is {}'.format(glibc_version))
    if glibc_version > '2.33' or cuda_short_version > '12.9':
        sample_list = [i for i in sample_list if i != 'cuHook']
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    print(sample_list)
    sm = get_sm()
    for sample in sample_list:
        if os.path.exists('%s/%s' % (sample_bin_path, sample)):
            if sample == 'cudaOpenMP':
                step1_cmd1 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD1'] % (
                    sample_0_path + '/cudaOpenMP', 5, sample, 5, sample, sample, 5)
                check_result(step1_cmd1, 'run cmd by {}'.format(step1_cmd1), log_name, result)
                step1_cmd2 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD2'] % (
                    sample_0_path + '/cudaOpenMP', cupti_target_path, cupti_target_path, sample, 5)
                out = run_loc_cmd(step1_cmd2)
                if out.succeeded:
                    print(out['output'])
                    drop_num = out['output'].split('Total Dropped Samples')[1].split(',')[0].strip(':').strip(' ')
                    print('>>>>>>>>>>>>>>>{}<<<<<<<<<<<<'.format(drop_num))
                    print(type(drop_num))
                    total_num = out['output'].split('Total Samples')[1].split(',')[0].strip(':').strip(' ')
                    no_user = out['output'].split('Non User Kernels Total Samples')[1].split('Sum')[0].strip(':').strip(' ')
                    sum_of_non = out['output'].split(':')[-1].strip(' ')
                    total_num1 = int(drop_num) + int(no_user) + int(sum_of_non)
                    if drop_num != '0' and int(total_num) == total_num1:
                        result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                        logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                    else:
                        result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                        logger.error('we run step1_cmd2 ---{} failed;  drop number should be 0, now is {}'.format(
                            step1_cmd2, drop_num))
            else:
                for i in [5, 15]:
                    if sample == 'vectorAddMMAP':
                        if i == 15 and sm < 9.0:
                            period = 7
                        else:
                            period = 5
                        step1_cmd1 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD1'] % (
                            sample_0_path + '/vectorAddMMAP', period, sample, period, sample, sample, period)
                        step1_cmd2 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD2'] % (
                            sample_0_path + '/vectorAddMMAP', cupti_target_path, cupti_target_path, sample, period)
                    elif sample == 'batchCUBLAS' and sm >= 9.0:
                        step1_cmd1 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD1_1'] % (
                            sample_bin_path, i, sample, i, sample, sample, i)
                        step1_cmd2 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD2_1'] % (
                            sample_bin_path, cupti_target_path, cupti_target_path, sample, i)
                    else:
                        step1_cmd1 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD1'] % (
                            sample_bin_path, i, sample, i, sample, sample, i)
                        step1_cmd2 = case_config['PC_SAMPLING_COVERAGE']['STEP1']['CMD2'] % (
                            sample_bin_path, cupti_target_path, cupti_target_path, sample, i)
                    check_result(step1_cmd1, 'run cmd by {}'.format(step1_cmd1), log_name, result)
                    out = run_loc_cmd(step1_cmd2)
                    if out.succeeded:
                        print(out['output'])
                        drop_num = out['output'].split('Total Dropped Samples')[1].split(',')[0].strip(':').strip(' ')
                        print('>>>>>>>>>>>>>>>{}<<<<<<<<<<<<'.format(drop_num))
                        print(type(drop_num))
                        total_num = out['output'].split('Total Samples')[1].split(',')[0].strip(':').strip(' ')
                        no_user = out['output'].split('Non User Kernels Total Samples')[1].split('Sum')[0].strip(':').strip(' ')
                        sum_of_non = out['output'].split(':')[-1].strip(' ')
                        total_num1 = int(drop_num) + int(no_user) + int(sum_of_non)
                        if i == 5:
                            if sample == 'simpleCooperativeGroups':
                                if drop_num == '0' and int(total_num) == total_num1:
                                    result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                                    logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                                else:
                                    result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                                    logger.error('we run step1_cmd2 ---{} failed, drop num should be 0'.format(step1_cmd2))
                            else:
                                if drop_num != '0' and int(total_num) == total_num1:
                                    result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                                    logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                                else:
                                    result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                                    logger.error('we run step1_cmd2 ---{} failed, drop num should not be 0'.format(step1_cmd2))
                        elif i == 15 and drop_num == '0' and int(total_num) == total_num1:
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                            logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                        else:
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                            logger.error('we run step1_cmd2 ---{} failed, drop num is '.format(step1_cmd2, drop_num))
        else:
            logger.warning('the sample --- {} does not exist'.format(sample))
            result['pc_sampling-------{}'.format(sample)] = 'failed'
    # calculate result
    result1 = calculate_result(result)
    final_result = FAILED
    dict_output(result1, flag=output_flag)
    if result1['passed'] / (len(sample_list) * 4) >= 0.78:
        logger.info('*************we get correct result, pass more than 80%**************')
        final_result = PASSED
    else:
        logger.info('/\n\n*************we get fail result or sample does not exist, please check the result!!!!!!!!!!!!!!!!')
        logger.info(
            'the sample longth is {}, so the result pass number should be more than {}, because cudaOpenMP only run 2 cmds, others run 4 cmds'.
            format(len(sample_list), len(sample_list) * 4 - 7))
        logger.info('the actually result number is {}'.format(result1['passed'] + result1['failed']))
    dict_to_json(result1, '%s/pc_sampling_coverage.json' % log_path)
    return final_result


def run_graphic(cmd_11, log_name2, result2):
    check_result(cmd_11, 'run_cmd by %s' % cmd_11, log_name2, result2)


def pc_sampling_graphic_coverage_2941255():
    sm = get_sm()
    if vgpu.lower() == 'none' and sm not in [8.0, 9.0, 10.0, 10.3]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['LOG_NAME']
        log_path = case_config['global']['env']['PC_SAMPLING_GRAPHIC_COVERAGE_LOG_PATH']
        mkdir(log_path)
        # prepare
        cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['PREPARE']['CMD1']
        check_result(cmd1, 'build_sample by %s' % cmd1, log_name, result)
        # run test
        graphic_sample_list = ['postProcessGL', 'oceanFFT', 'simpleGL']
        sample_dict = {
            'postProcessGL': 'CUDA GL Post Processing', 'oceanFFT': 'CUDA FFT Ocean Simulation', 'simpleGL': 'Cuda GL Interop'
        }
        optix_flag = case_config['global']['env']['OPTIX_FLAG']
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        cmd = 'cd {}; rm *.dat'.format(sample_bin_path)
        run_loc_cmd(cmd)
        optix_flag = '1'
        if optix_flag == '1':
            optix_sample_list = ['optixPathTracer']
            cmd_copy = 'cp -r %s/* %s' % (optix_bin_path, sample_bin_path)
            run_loc_cmd(cmd_copy)
            cmd_rm = 'cd {}; rm *.dat'.format(optix_bin_path)
            run_loc_cmd(cmd_rm)
        else:
            optix_sample_list = []
        # 0
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        sample_list = graphic_sample_list
        for sample in sample_list:
            if os.path.exists('%s/%s' % (sample_bin_path, sample)):
                for i in [5, 13]:
                    if 'optix' in sample:
                        step1_cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (
                            optix_bin_path, i, sample, i, sample)
                        close_cmd = "sleep 15; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % sample
                    else:
                        step1_cmd1 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD1'] % (
                            sample_bin_path, i, sample, i, sample)
                        close_cmd = "sleep 15; wmctrl -i -c `wmctrl -l |grep -i '%s'|awk '{print $1}'`" % sample_dict[sample]
                    # cmd_list = [step1_cmd1, close_cmd]
                    # multi_process(cmd_list, log_name, result)
                    manager = Manager()
                    mp_result = manager.dict()
                    pool = Pool(2)
                    try:
                        logging.debug('start run %d' % os.getpid())
                        pool.apply_async(run_graphic, args=(step1_cmd1, log_name, mp_result))
                        pool.apply_async(run_graphic, args=(close_cmd, log_name, mp_result))
                        pool.close()
                        pool.join()
                        for dt in mp_result:
                            if 'wmctrl' not in dt:
                                result.update({dt: mp_result[dt]})
                    except:
                        logging.debug('Process terminating')
                        pool.terminate()
                        pool.join()
                        time.sleep(1)
                        raise
                    if 'optix' in sample:
                        step1_cmd2 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD2'] % (
                            optix_bin_path, cupti_target_path, cupti_target_path, sample, i)
                    else:
                        step1_cmd2 = case_config['PC_SAMPLING_GRAPHIC_COVERAGE']['STEP1']['CMD2'] % (
                            sample_bin_path, cupti_target_path, cupti_target_path, sample, i)
                    out = run_loc_cmd(step1_cmd2)
                    if out.succeeded:
                        print('we run cmd {}'.format(step1_cmd2))
                        print(out['output'])
                        drop_num = out['output'].split('Total Dropped Samples')[1].split(',')[0].strip(':').strip(' ')
                        print('+++++++++++++++++++++++++++++++++')
                        print('+++                           +++')
                        print('>>>>>>>>>>>drop_number:{}<<<<<<<'.format(drop_num))
                        print('+++                           +++')
                        print('+++++++++++++++++++++++++++++++++')
                        print(type(drop_num))
                        if i == 5 and drop_num != '0':
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                            logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                        elif i == 13 and drop_num == '0':
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'passed'
                            logger.info('we run step1_cmd2 ---{} successful'.format(step1_cmd2))
                        else:
                            result['run step1_cmd2----{}'.format(step1_cmd2)] = 'failed'
                            logger.error('we run step1_cmd2 ---{} failed'.format(step1_cmd2))
            else:
                logger.warning('the sample --- {} does not exist'.format(sample))
                result['run pc_sampling with grahic----{}'.format(sample)] = 'failed'
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        if result1['passed'] / (len(sample_list) * 4) >= 0.78:
            logger.info('*************we get correct result, pass more than 80%**************')
        else:
            logger.info('/\n\n*************we get fail result, please check the result!!!!!!!!!!!!!!!!')
            logger.info('the sample length is {}, so the result number should be more than {}'.format(
                len(sample_list), len(sample_list) * 4 - 7))
            logger.info('the actually result number is {}'.format(result1['passed'] + result1['failed']))
        dict_to_json(result1, '%s/pc_sampling_graphic_coverage.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    logger.info('we need not run this case in vGPU and tesla GPU, Ampere/Hopper/Blackwell')
    return WAIVED


def pc_sampling_user_control():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['PC_SAMPLING_USER_CONTROL']['LOG_NAME']
    log_path = case_config['global']['env']['PC_SAMPLING_USER_CONTROL_PATH']
    mkdir(log_path)
    sm = get_sm()
    # prepare samples
    prepare_cmd1 = case_config['PC_SAMPLING_USER_CONTROL']['PREPARE']['CMD1'] % (user, password, base_url)
    prepare_cmd2 = case_config['PC_SAMPLING_USER_CONTROL']['PREPARE']['CMD2']
    check_result(prepare_cmd1, 'download Makefile %s' % prepare_cmd1, log_name, result)
    check_result(prepare_cmd2, 'extract the file by %s' % prepare_cmd2, log_name, result)
    # build the sample
    step1_cmd1 = case_config['PC_SAMPLING_USER_CONTROL']['STEP1']['CMD1'] % sm
    check_result(step1_cmd1, 'run step1 by %s' % step1_cmd1, log_name, result)
    # profile the sample
    step2_cmd1 = case_config['PC_SAMPLING_USER_CONTROL']['STEP2']['CMD1']
    check_result(step2_cmd1, 'run step2 by %s' % step2_cmd1, log_name, result)
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/pc_sampling_user_control.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_trace_injection_optix():
    sm = get_sm()
    if vgpu.lower() == 'none' and sm not in [8.0, 9.0, 10.0, 10.3]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_INJECTION_OPTIX']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_OPTIX_LOG_PATH']
        mkdir(log_path)
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            cupti_run_path = '%s/host/%s' % (cupti_path, cupti_extract_path)
            cupti_target_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/host/linux-desktop-glibc_2_19_0-arm" % cupti_path_arm
                cupti_target_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path_arm
            else:
                cupti_run_path = "%s/host/linux-desktop-t210-a64" % cupti_path_arm
                cupti_target_path = "%s/target/linux-desktop-t210-a64" % cupti_path_arm
        else:
            logger.info('now we do not support the platform---%s' % platform)
        # build optix sample
        prepare_optix_sample(log_path, user, password, base_url, cuda_short_version, platform, build=1)
        # prepare graphic sample for cupti testing
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['PREPARE']['CMD'] % (optix_bin_path, cupti_target_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_OPTIX']['PREPARE']['CMD1'] % (cupti_target_path, user, password, base_url)
        run_loc_cmd(prepare_cmd1)
        # run cupti trace injection testing
        sample_list = ['optixDenoiser', 'optixPathTracer', 'optixTriangle', 'optixMeshViewer', 'optixNVLink']
        run_sample_list = ['optixDenoiser color.exr', 'optixPathTracer', 'optixTriangle', 'optixMeshViewer', 'optixNVLink']
        sample_dict = dict(zip(run_sample_list, sample_list))
        create_json_file = 'cd {}; rm test_trace_injection.json; touch test_trace_injection.json; rm cupti_trace_injection.txt'.format(
            cupti_run_path)
        run_loc_cmd(create_json_file)
        if cuda_short_version <= '11.4':
            dict1 = {"tests": []}
            dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
        else:
            dict1 = {"tests": []}
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        for sample, value in sample_dict.items():
            if len(sample.split(' ')) != 1:
                if cuda_short_version <= '11.4':
                    dict1["tests"].append({
                        "name": "%s" % sample.split(' ')[0],
                        "exe": "%s" % sample.split(' ')[0],
                        "duration": 30,
                        "exe_args": sample.split(' ')[1::]
                    })
                else:
                    dict1["tests"].append({
                        "name": "%s" % sample.split(' ')[0],
                        "exe": "%s" % sample.split(' ')[0],
                        "duration": 30,
                        "exe-args": sample.split(' ')[1::]
                    })
            else:
                dict1["tests"].append({"name": "%s" % sample.split(' ')[0], "exe": "%s" % sample.split(' ')[0], "duration": 30})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        print(dict2)
        dict_to_json(dict2, '{}.json'.format(cupti_run_path + '/test_trace_injection'))
        if cuda_short_version <= '11.4':
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1']['CMD'] % cupti_run_path
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_1']['CMD'] % cupti_run_path
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_2']['CMD'] % cupti_run_path
        else:
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1']['CMD1'] % cupti_run_path
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_1']['CMD1'] % cupti_run_path
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP1_2']['CMD1'] % cupti_run_path
        check_result(step1_cmd, 'run cupti trace injection--optix by {}'.format(step1_cmd), log_name, result)
        # check result
        if cuda_short_version <= '11.4':
            step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP2']['CMD1'] % (cupti_run_path, log_path)
            step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP2']['CMD2'] % (cupti_run_path, log_path)
        else:
            step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP2']['CMD1_1'] % (cupti_run_path, log_path)
            step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_OPTIX']['STEP2']['CMD2_1'] % (cupti_run_path, log_path)
        out1 = run_loc_cmd(step2_cmd1)
        out2 = run_loc_cmd(step2_cmd2)
        print(out1)
        print('==================')
        if out1['output'] == '0' and out2['output'] == str(len(sample_list)):
            result['check_trace_injection'] = 'passed'
        else:
            result['check_trace_injection'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_trace_injection_optix.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we need not run this case in vGPU and tesla gpu, ampere/hopper/blackwell')
        return WAIVED


def cupti_trace_injection_graphic():
    sm = get_sm()
    if vgpu.lower() == 'none' and sm not in [8.0, 9.0, 10.0, 10.3]:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_GRAPHIC_LOG_PATH']
        mkdir(log_path)
        optix_bin_path = case_config['global']['env']['OPTIX_BIN_PATH']
        optix_lib_path = case_config['global']['env']['OPTIX_LIB_PATH']
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        if platform == 'x86':
            cupti_run_path = '%s/host/%s' % (cupti_path, cupti_extract_path)
            cupti_target_path = '%s/target/%s' % (cupti_path, cupti_extract_path)
            host_run_path = '%s/../../host/%s' % (sample_bin_path, cupti_extract_path)
        elif platform == 'arm':
            if cuda_short_version < '11.5':
                cupti_run_path = "%s/host/linux-desktop-glibc_2_19_0-arm" % cupti_path_arm
                cupti_target_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path_arm
            else:
                cupti_run_path = "%s/host/linux-desktop-t210-a64" % cupti_path_arm
                cupti_target_path = "%s/target/linux-desktop-t210-a64" % cupti_path_arm
                host_run_path = "%s/../../host/linux-desktop-t210-a64" % sample_bin_path
        else:
            logger.info('now we do not support the platform---%s' % platform)
        # prepare graphic sample for cupti testing
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['PREPARE']['CMD'] % (optix_bin_path, cupti_target_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
        prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['PREPARE']['CMD1'].format(sample_bin_path, cupti_target_path)
        print(prepare_cmd1)
        run_test_cmd(prepare_cmd1)
        # run cupti trace injection testing
        sample_list = ['simpleGL', 'oceanFFT', 'postProcessGL', 'optixPathTracer']
        create_json_file = 'cd {}; rm test_trace_injection.json; touch test_trace_injection.json; rm cupti_trace_injection1.txt'.format(
            cupti_run_path)
        run_loc_cmd(create_json_file)
        if cuda_short_version <= '11.4':
            dict1 = {"tests": []}
            dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
        else:
            dict1 = {"tests": []}
            dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        for sample in sample_list:
            if len(sample.split(' ')) != 1:
                if cuda_short_version <= '11.4':
                    dict1["tests"].append({"name": "%s" % sample, "exe": "%s" % sample, "duration": 30})
                else:
                    dict1["tests"].append({"name": "%s" % sample, "exe": "%s" % sample, "duration": 30})
            else:
                dict1["tests"].append({"name": "%s" % sample, "exe": "%s" % sample, "duration": 30})
        if cuda_short_version <= '11.4':
            dict2.update(dict1)
        else:
            dict2["tracing-injection"].update(dict1)
        print(dict2)
        dict_to_json(dict2, '{}.json'.format(cupti_run_path + '/test_trace_injection'))
        if cuda_short_version <= '11.4':
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1']['CMD'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1_1']['CMD'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1_2']['CMD'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
        else:
            if platform == 'x86':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1']['CMD1'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
            elif platform == 'arm':
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1_1']['CMD1'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
            else:
                step1_cmd = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP1_2']['CMD1'].format(
                    sample_bin_path, sample_bin_path, sample_bin_path)
        print(step1_cmd)
        check_result(step1_cmd, 'run cupti trace injection--optix by {}'.format(step1_cmd), log_name, result)
        # check result
        if cuda_short_version <= '11.4':
            step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP2']['CMD1'] % (host_run_path, log_path)
            step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP2']['CMD2'] % (host_run_path, log_path)
        else:
            step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP2']['CMD1_1'] % (host_run_path, log_path)
            step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_GRAPHIC']['STEP2']['CMD2_1'] % (host_run_path, log_path)
        print(step2_cmd1)
        print(step2_cmd2)
        print('================================')
        out1 = run_loc_cmd(step2_cmd1)
        out2 = run_loc_cmd(step2_cmd2)
        print(out1)
        print('==================')
        if out1['output'] == '0' and out2['output'] == str(len(sample_list)):
            result['check_trace_injection'] = 'passed'
        else:
            result['check_trace_injection'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_trace_injection_graphic.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we need not run this case in vGPU and tesla GPU, Ampere/Hopper/Blackwell')
        return WAIVED


def cupti_trace_injection_spec_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_SPEC_SAMPLE_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    if platform == 'x86':
        cupti_run_path = '{}/host/{}'.format(cupti_path, cupti_extract_path)
        cupti_target_path = '{}/target/{}'.format(cupti_path, cupti_extract_path)
        host_run_path = '{}/../../host/{}'.format(sample_bin_path, cupti_target_path)
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "{}/host/linux-desktop-glibc_2_19_0-arm".format(cupti_path_arm)
            cupti_target_path = "{}/target/linux-desktop-glibc_2_19_0-arm".format(cupti_path_arm)
        else:
            cupti_run_path = "{}/host/linux-desktop-t210-a64".format(cupti_path_arm)
            cupti_target_path = "{}/target/linux-desktop-t210-a64".format(cupti_path_arm)
            host_run_path = "{}/../../host/linux-desktop-t210-a64".format(sample_bin_path)
    else:
        logger.info('now we do not support the platform---{}'.format(platform))
    # prepare sample for cupti testing
    prepare_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['PREPARE']['CMD'].format(
        sample_bin_path, cupti_target_path, sample_bin_path, cupti_target_path)
    print(prepare_cmd)
    run_test_cmd(prepare_cmd)
    # run cupti trace injection testing
    sample_list = ['conjugateGradientPrecond', 'memMapIPCDrv']
    create_json_file = 'cd {}; rm test_trace_injection.json; touch test_trace_injection.json; rm cupti_trace_injection1.txt'.format(
        cupti_run_path)
    run_loc_cmd(create_json_file)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    for sample in sample_list:
        if len(sample.split(' ')) != 1:
            if cuda_short_version <= '11.4':
                dict1["tests"].append({"name": "{}".format(sample), "exe": "{}".format(sample), "duration": 30})
            else:
                dict1["tests"].append({"name": "{}".format(sample), "exe": "{}".format(sample), "duration": 30})
        else:
            dict1["tests"].append({"name": "{}".format(sample), "exe": "{}".format(sample), "duration": 30})
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    print(dict2)
    dict_to_json(dict2, '{}.json'.format(cupti_run_path + '/test_trace_injection'))
    if cuda_short_version <= '11.4':
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1']['CMD'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1_1']['CMD'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1_2']['CMD'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
    else:
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1']['CMD1'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1_1']['CMD1'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP1_2']['CMD1'].format(
                sample_bin_path, sample_bin_path, sample_bin_path)
    print(step1_cmd)
    check_result(step1_cmd, 'run cupti trace injection--optix by {}'.format(step1_cmd), log_name, result)
    # check result
    if cuda_short_version <= '11.4':
        step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP2']['CMD1'].format(host_run_path, log_path)
        step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP2']['CMD2'].format(host_run_path, log_path)
    else:
        step2_cmd1 = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP2']['CMD1_1'].format(host_run_path, log_path)
        step2_cmd2 = case_config['CUPTI_TRACE_INJECTION_SPEC_SAMPLE']['STEP2']['CMD2_1'].format(host_run_path, log_path)
    print(step2_cmd1)
    print(step2_cmd2)
    print('================================')
    out1 = run_loc_cmd(step2_cmd1)
    out2 = run_loc_cmd(step2_cmd2)
    print(out1)
    print('==================')
    if out1['output'] == '0' and out2['output'] == str(len(sample_list)):
        result['check_trace_injection'] = 'passed'
    else:
        result['check_trace_injection'] = 'failed'
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/cupti_trace_injection_spec_sample.json'.format(log_path))
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_profile_injection_spec_sample():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare sample
    cmd = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['CMD'].format(cupti_target_path)
    check_result(cmd, 'prepare_sample by {}'.format(cmd), log_name, result)
    checkpoint = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['CHECK_POINT']
    step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['STEP1']['CMD1'].format(cupti_target_path)
    step2_cmd1 = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['STEP2']['CMD1'].format(cupti_target_path)
    step3_cmd1 = case_config['CUPTI_PROFILE_INJECTION_SPEC_SAMPLE']['STEP3']['CMD1'].format(cupti_target_path)
    check_result(step1_cmd1, 'run step1_simpleAWBarrier by {}'.format(step1_cmd1), log_name, result, 'Error', checkpoint, flag=2)
    check_result(step2_cmd1, 'run step2_simpleAWBarrier by {}'.format(step2_cmd1), log_name, result, 'Error', checkpoint, flag=2)
    check_result(step3_cmd1, 'run step3_simpleAWBarrier by {}'.format(step3_cmd1), log_name, result, 'Error', checkpoint, flag=2)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/cupti_profile_injection_spec_sample.json'.format(log_path))
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_rcca_3292084():
    if cuda_short_version >= '12.1':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_RCCA_3292084']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_RCCA_3292084_PATH']
        mkdir(log_path)
        # prepare samples
        prepare_cmd1 = case_config['CUPTI_RCCA_3292084']['PREPARE']['CMD1']
        prepare_cmd2 = case_config['CUPTI_RCCA_3292084']['PREPARE']['CMD2'].format(user, password, base_url)
        os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']
        os.environ["PATH"] = '{}/cuda-injection-library:'.format(log_path) + os.environ['PATH']
        check_result(prepare_cmd1, 'prepare env  by  {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'prepare sample by {}'.format(prepare_cmd2), log_name, result)
        # run the sample
        os.chdir('{}/cuda-injection-library/'.format(log_path))
        # os.popen('cd {}/cuda-injection-library;python3 test.py'.format(log_path))
        step1_cmd1 = case_config['CUPTI_RCCA_3292084']['STEP1']['CMD1']
        # os.environ["CUDA_INJECTION64_PATH"] = '{}/cuda-injection-library/libToolsInjectionCuda.so'.format(log_path)
        check_result1(step1_cmd1, 'run the sample by {}'.format(step1_cmd1), log_name, result)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_rcca_3292084.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this case if cuda version less than 12.1')
        return WAIVED


def cupti_rcca_3452563():
    if cuda_short_version >= '12.2':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_RCCA_3452563']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_RCCA_3452563_PATH']
        mkdir(log_path)
        # prepare samples
        prepare_cmd1 = case_config['CUPTI_RCCA_3452563']['PREPARE']['CMD1'].format(user, password, base_url)
        check_result(prepare_cmd1, 'prepare env  by  {}'.format(prepare_cmd1), log_name, result)
        # run the sample
        step1_cmd1 = case_config['CUPTI_RCCA_3452563']['STEP1']['CMD1']
        # os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']
        check_result(step1_cmd1, 'run the sample by {}'.format(step1_cmd1), log_name, result, 'FAILED', flag=6)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_rcca_3452563.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this case if cuda version less than 12.2')
        return WAIVED


def sass_metrics():
    if cuda_short_version < '12.2':
        logger.info('we support the sample since cuda 12.2, waive......')
    else:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['SASS_METRICS']['LOG_NAME']
        log_path = case_config['global']['env']['SASS_METRICS_LOG_PATH']
        mkdir(log_path)
        # prepare
        cmd1 = case_config['SASS_METRICS']['PREPARE']['CMD1']
        check_result(cmd1, 'build_sample by {}'.format(cmd1), log_name, result)

        def get_device_name_sm():
            cmd = case_config['PREPARE']['CMD5'] % sample_1_path
            out = run_test_cmd(cmd)
            sm_list, device_name = [], []
            for line in out.output.split('\n'):
                if 'Device ' in line and '"' in line:
                    print(line)
                    device_name.append(line.split('"')[1].strip('u'))
                if "CUDA Capability Major/Minor version number" in line:
                    sm_list.append(line.split(':')[1].strip(' ').strip('\n').strip('u'))
            return sm_list, device_name

        sm_list, device_name = get_device_name_sm()
        for index, sm in enumerate(sm_list):
            if sm < '7.0' and '10.' not in sm:
                logger.info('we do not support this sample if sm less than 7.0')
            else:
                # run the sample
                check_point1 = "Device Num: {}".format(index)
                if cuda_short_version >= '12.3':
                    check_point2 = "Lazy Patching Disabled"
                    check_point5 = "Lazy Patching Enabled"
                else:
                    check_point2 = "Lazy  Disabled"
                    check_point5 = "Lazy  Enabled"
                check_point3 = "Device Name: {}".format(device_name[index])
                check_point4 = "Device compute capability: {}".format(sm)
                os.chdir('{}/extras/CUPTI/samples/sass_metrics'.format(base_path))
                step1_cmd1 = case_config['SASS_METRICS']['STEP1']['CMD1']
                check_result(step1_cmd1,
                             'step1-run_sample option help with sm {} by {}'.format(sm, step1_cmd1),
                             log_name,
                             result,
                             'nan',
                             'N/A',
                             'ERROR',
                             'error',
                             flag=2)
                step2_cmd1 = case_config['SASS_METRICS']['STEP2']['CMD1']
                check_result(step2_cmd1,
                             'step2-run_sample option list  with sm {} by {}'.format(sm, step2_cmd1),
                             log_name,
                             result,
                             'nan',
                             'N/A',
                             'ERROR',
                             'error',
                             flag=2)
                step3_cmd1 = case_config['SASS_METRICS']['STEP3']['CMD1'].format(index)
                check_result(step3_cmd1,
                             'step3-run_sample with no error information with sm {} by {}'.format(sm, step3_cmd1),
                             log_name,
                             result,
                             'nan',
                             'N/A',
                             'ERROR',
                             'error',
                             flag=2)
                check_result(step3_cmd1,
                             'step3-run_sample with sm/device name with sm {} by {}'.format(sm, step3_cmd1),
                             log_name,
                             result,
                             check_point1,
                             check_point3,
                             check_point4)
                step4_cmd1 = case_config['SASS_METRICS']['STEP4']['CMD1'].format(index)
                check_result(step4_cmd1,
                             'step4-run_sample with no error information with sm {} by {}'.format(sm, step4_cmd1),
                             log_name,
                             result,
                             'nan',
                             'N/A',
                             'ERROR',
                             'error',
                             flag=2)
                check_result(step4_cmd1,
                             'step4-run_sample with sm/device name with sm {} by {}'.format(sm, step4_cmd1),
                             log_name,
                             result,
                             check_point1,
                             check_point2,
                             check_point3,
                             check_point4)
                step5_cmd1 = case_config['SASS_METRICS']['STEP5']['CMD1'].format(index)
                check_result(step5_cmd1,
                             'step5-run_sample with no error information with sm {} by {}'.format(sm, step5_cmd1),
                             log_name,
                             result,
                             'nan',
                             'N/A',
                             'ERROR',
                             'error',
                             flag=2)
                check_result(step5_cmd1,
                             'step5-run_sample with enableLazyPatching 1 with sm {} by {}'.format(sm, step5_cmd1),
                             log_name,
                             result,
                             check_point1,
                             check_point5,
                             check_point3,
                             check_point4)
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/sass_metrics.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def support_collect_sass_3481156():
    if cuda_short_version >= '12.2':
        sm = get_sm()
        if sm < 7.0:
            logger.info('we do not support this sample if sm less than 7.0')
        else:
            result = {}
            passed, failed = 0, 0
            log_name = case_config['SUPPORT_COLLECT_SASS']['LOG_NAME']
            log_path = case_config['global']['env']['SUPPORT_COLLECT_SASS_PATH']
            mkdir(log_path)
            prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
            prepare_cmd1 = case_config['SUPPORT_COLLECT_SASS']['PREPARE']['CMD1'].format(cupti_target_path, log_path)
            check_result(prepare_cmd1, 'prepare env  by  {}'.format(prepare_cmd1), log_name, result)
            # run the sample
            step1_cmd1 = case_config['SUPPORT_COLLECT_SASS']['STEP1']['CMD1'].format(cupti_target_path)
            step1_cmd2 = case_config['SUPPORT_COLLECT_SASS']['STEP1']['CMD2'].format(cupti_target_path)
            # os.environ["LD_LIBRARY_PATH"] = case_config['global']['env']['LD_PATH']
            check_result(step1_cmd1, 'run the sample by {}'.format(step1_cmd1), log_name, result, 'error', 'ERROR', 'FAILED', flag=2)
            check_result(step1_cmd2, 'run the sample by {}'.format(step1_cmd2), log_name, result, 'error', 'ERROR', 'FAILED', flag=2)
            result1 = calculate_result(result)
            dict_output(result1, flag=output_flag)
            dict_to_json(result1, '{}/support_collect_sass.json'.format(log_path))
            return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we do not support this case if cuda version less than 12.2')
        return WAIVED


def support_cuda_sw_stack_3365796():
    if cuda_short_version >= '12.3':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['SUPPORT_CUDA_SW_STACK']['LOG_NAME']
        log_path = case_config['global']['env']['SUPPORT_CUDA_SW_STACK_PATH']
        mkdir(log_path)
        # prepare samples
        prepare_cmd1 = case_config['SUPPORT_CUDA_SW_STACK']['PREPARE']['CMD1']
        prepare_output = run_test_cmd(prepare_cmd1)
        print(prepare_output.output)
        libcupti = prepare_output.output.split('\n')[-1]
        # run the sample
        step1_cmd1 = case_config['SUPPORT_CUDA_SW_STACK']['STEP1']['CMD1'].format(libcupti)
        step1_output = run_test_cmd(step1_cmd1)
        save_log(log_name, step1_cmd1, 'check cuda sw stack', step1_output.output)
        value = int(int(step1_output.output.split('\n')[-1]))
        logger.info('the value is {}'.format(value))
        if 3000 < value < 10000:
            result['check_cuda_sw_value'] = 'passed'
            logger.info('check cuda sw stack successful')
        else:
            result['check_cuda_sw_value'] = 'failed'
            logger.info('check cuda sw stack fail, the value should more than 3000, check it in ')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/support_cuda_sw_stack.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('only support this case since 12.2')
        return WAIVED


def pm_sampling():
    sm = get_sm()
    if cuda_short_version > '12.5':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['PM_SAMPLING']['LOG_NAME']
        log_path = case_config['global']['env']['PM_SAMPLING_PATH']
        mkdir(log_path)
        check_point1 = case_config['PM_SAMPLING']['CHECK_POINT1']
        check_point2 = case_config['PM_SAMPLING']['CHECK_POINT2']
        check_point3 = case_config['PM_SAMPLING']['CHECK_POINT3']
        check_point4 = case_config['PM_SAMPLING']['CHECK_POINT4']
        # build the checkpoint kernels sample
        step1_cmd = case_config['PM_SAMPLING']['STEP1']['CMD']
        check_result(step1_cmd, 'build_checkpoint_kernals-------%s' % step1_cmd, log_name, result)
        run_pm_sampling = case_config['PM_SAMPLING']['STEP2']['CMD']
        if sm >= 7.5:
            # run pm_sampling
            check_result(run_pm_sampling, 'run_pm_sampling------%s' % run_pm_sampling, log_name, result, check_point1, check_point2, check_point3)
            check_result(run_pm_sampling, 'check result of pm_sampling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'error', 'n/a', 'fail', flag=6)
            # run pm_sampling with metric
            pm_sampling_with_metric = case_config['PM_SAMPLING']['STEP3']['CMD']
            check_result(pm_sampling_with_metric, 'run_checkpoint_kernels------%s' % pm_sampling_with_metric, log_name, result, check_point4, flag=1)
        else:
            check_result(run_pm_sampling, 'run_pm_sampling------%s' % run_pm_sampling, log_name, result, 'CUPTI_ERROR_NOT_SUPPORTED', flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/pm_sampling.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logging.warning('support pm_sampling since 12.6 and turing++')
        return WAIVED


def cupti_support_hide_rtcore_3730300():
    sm = get_sm()
    wls = is_WSL()
    if vgpu != 'none' or wls is True or platform.lower() == 'arm' or sm in [8.0, 9.0, 10.0, 10.3]:
        logger.info('we do not support this case on vgpu and WSL or tesla GPU, skip on arm')
    else:
        logger.info('we will run case http://hqnvwb01/DTResults/showTask.aspx?id=P1072_T3730300')
        result = {}
        passed, failed = 0, 0
        log_path = case_config['global']['env']['CUPTI_SUPPORT_HIDE_RTCORE_PATH']
        log_name = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['LOG_NAME']
        mkdir(log_path)
        # prepare package
        prepare_optix_sample(log_path, user, password, base_url, cuda_short_version, platform)
        prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
        # prepare sample
        prepare_cmd1 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['PREPARE']['CMD1']
        prepare_cmd2 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['PREPARE']['CMD2'].format(cupti_target_path)
        check_result(prepare_cmd1, 'build optix sample by {}'.format(prepare_cmd1), log_name, result)
        check_result(prepare_cmd2, 'copy optixRaycasting via {}'.format(prepare_cmd2), log_name, result)
        check_point1 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['CHECK_POINT1']
        check_point2 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['CHECK_POINT2']
        check_point3 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['CHECK_POINT3']
        # run cupti profile injection
        profile_cmd1 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP1']['CMD1'].format(cupti_target_path)
        profile_cmd2 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP1']['CMD2'].format(cupti_target_path)
        profile_cmd3 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP1']['CMD3'].format(cupti_target_path)
        check_result(profile_cmd1, 'check ttu/rtcore/megakernel_simple not in output via ---{}'.format(profile_cmd1), log_name, result, check_point1, check_point2, check_point3, flag=2)
        # check_result(profile_cmd1, 'check raygen and nvidia internal in output via ---{}'.format(profile_cmd1), log_name, result, check_point4, check_point5, flag=1)
        if cuda_short_version < '13.1':
            check_result(profile_cmd2, 'check ttu/rtcore/megakernel_simple not in output via ---{}'.format(profile_cmd2), log_name, result, check_point1, check_point2, check_point3, flag=2)
            # check_result(profile_cmd2, 'check raygen and nvidia internal in output via ---{}'.format(profile_cmd2), log_name, result, check_point1, check_point2, check_point3, flag=4)
            check_result(profile_cmd3, 'check ttu/rtcore/megakernel_simple not in output via ---{}'.format(profile_cmd3), log_name, result, check_point1, check_point2, check_point3, flag=2)
            # check_result(profile_cmd3, 'check raygen and nvidia internal in output via ---{}'.format(profile_cmd3), log_name, result, check_point1, check_point2, check_point3, flag=4)

        def check_range_number(run_cmd):
            number1, number2, number3, number4 = 0, 0, 0, 0
            out = run_loc_cmd(run_cmd)
            for i in out['output'].split('\n'):
                if 'NVIDIA Internal' in i:
                    number1 += 1
                if 'createRaysOrthoKernel' in i:
                    number2 += 1
                if 'translateRaysKernel' in i:
                    number3 += 1
                if 'shadeHitsKernel' in i:
                    number4 += 1
            return number1, number2, number3, number4
        cmd1_num1, cmd1_num2, cmd1_num3, cmd1_num4 = check_range_number(profile_cmd1)
        if cmd1_num1 == 5 and cmd1_num2 == 1 and cmd1_num3 == 1 and cmd1_num4 == 2:
            logger.info('run range name and number of {} successful'.format(profile_cmd1))
            result['check range name and number of {}'.format(profile_cmd1)] = 'passed'
        else:
            logger.info('run range name and number of {} fail'.format(profile_cmd1))
            result['check range name and number of {}'.format(profile_cmd1)] = 'failed'
        if cuda_short_version < '13.1':
            cmd2_num1, cmd2_num2, cmd2_num3, cmd2_num4 = check_range_number(profile_cmd2)
            if cmd2_num1 == 5 and cmd2_num2 == 1 and cmd2_num3 == 1 and cmd2_num4 == 2:
                logger.info('run range name and number of {} successful'.format(profile_cmd2))
                result['check range name and number of {}'.format(profile_cmd2)] = 'passed'
            else:
                logger.info('run range name and number of {} fail'.format(profile_cmd2))
                result['check range name and number of {}'.format(profile_cmd2)] = 'failed'
            cmd3_num1, cmd3_num2, cmd3_num3, cmd3_num4 = check_range_number(profile_cmd3)
            if cmd3_num1 != 0 and cmd3_num2 == 1 and cmd3_num3 == 1 and cmd3_num4 == 2:
                logger.info('run range name and number of {} successful'.format(profile_cmd3))
                result['check range name and number of {}'.format(profile_cmd3)] = 'passed'
            else:
                logger.info('run range name and number of {} fail'.format(profile_cmd3))
                result['check range name and number of {}'.format(profile_cmd3)] = 'failed'
        # run trace check
        trace_cmd1 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP2']['CMD1'].format(cupti_run_path)
        trace_cmd2 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP2']['CMD2'].format(cupti_run_path)
        trace_cmd3 = case_config['CUPTI_SUPPORT_HIDE_RTCORE']['STEP2']['CMD3'].format(cupti_run_path)
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
        dict1["tests"].append({"name": "optixRaycasting", "exe": "optixRaycasting"})
        dict2["tracing-injection"].update(dict1)
        print(dict2)
        print('{}'.format(cupti_run_path + '/' + 'test_{}.json'.format('optixRaycasting')))
        dict_to_json(dict2, '{}'.format(cupti_run_path + '/' + 'test_{}.json'.format('optixRaycasting')))
        check_result(trace_cmd1, 'run trace check on cbl2 via {}'.format(trace_cmd1), log_name, result)
        if cuda_short_version < '13.1':
            check_result(trace_cmd2, 'run trace check on cbl1 on via {}'.format(trace_cmd2), log_name, result)
            check_result(trace_cmd3, 'run trace check on cbl1 off via {}'.format(trace_cmd3), log_name, result)

        def check_trace_result(file_name, check_tring):
            pass1, fail1 = 0, 0
            with open(file_name, 'r') as f:
                for line in f.readlines():
                    if 'ttu' in line or 'rtcore' in line or 'megakernel_simple' in line:
                        logger.error('ttu/rtcore/megakernel_simple is in output')
                        fail1 += 1
                    if check_tring in line:
                        logger.info('{} is in output'.format(check_tring))
                        pass1 += 1
                    if '1 PASSED.' in line:
                        pass1 += 1
                if fail1 == 0 and pass1 == 2:
                    return True
                else:
                    return False
        if cuda_short_version < '12.8':
            if check_trace_result('{}/{}'.format(cupti_run_path, 'cupti_trace_injection1.txt'), '-- null --') is True:
                logger.info('check trace injection with CBL2 successful')
                result['check trace injection with CBL2,ttu/rtcore/megakernel_simple not in output,-- null -- in output'] = 'passed'
            else:
                logger.error('check trace injection with CBL2 fail')
                result['check trace injection with CBL2,ttu/rtcore/megakernel_simple in output, -- null -- not in output'] = 'failed'
        else:
            if check_trace_result('{}/{}'.format(cupti_run_path, 'cupti_trace_injection1.txt'), 'NVIDIA internal') is True:
                logger.info('check trace injection with CBL2 successful')
                result['check trace injection with CBL2,ttu/rtcore/megakernel_simple not in output,NVIDIA internal in output'] = 'passed'
            else:
                logger.error('check trace injection with CBL2 fail')
                result['check trace injection with CBL2,ttu/rtcore/megakernel_simple in output, NVIDIA internal not in output'] = 'failed'
        if cuda_short_version < '13.1':
            if check_trace_result('{}/{}'.format(cupti_run_path, 'cupti_trace_injection3.txt'), 'NVIDIA internal') is True:
                logger.info('check trace injection with CBL1 off successful')
                result['check trace injection with CBL1 off, ttu/rtcore/megakernel_simple not in output, NVIDIA internal in output'] = 'passed'
            else:
                logger.error('check trace injection with CBL1 off fail')
                result['check trace injection with CBL1 off, ttu/rtcore/megakernel_simple in output, NVIDIA internal not in output'] = 'failed'
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/cupti_support_hide_rtcore.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def cupti_rcca_3816918():
    if cuda_short_version >= '12.7':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CUPTI_RCCA_3816918']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_RCCA_3816918_PATH']
        mkdir(log_path)
        # prepare samples
        for file1 in ['test.cu', 'wip.cubin', 'wip.ptx']:
            prepare_cmd1 = case_config['CUPTI_RCCA_3816918']['PREPARE']['CMD1'].format(user, password, base_url, file1)
            run_test_cmd(prepare_cmd1)
        prepare_cmd2 = case_config['CUPTI_RCCA_3816918']['PREPARE']['CMD2']
        check_result(prepare_cmd2, 'prepare env  by  {}'.format(prepare_cmd2), log_name, result)
        # run the sample
        step1_cmd1 = case_config['CUPTI_RCCA_3816918']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run the sample by {}'.format(step1_cmd1), log_name, result, 'First kernel execution (traced)', 'Second kernel execution (untraced)', 'Third kernel execution (traced)')
        result1 = calculate_result(result)
        out = run_loc_cmd(step1_cmd1)
        if out['output'].split('\n') == '8':
            result['check the third kernel execution (traced) is 8'] = 'passed'
        else:
            result['check the third kernel execution (traced) is 8'] = 'failed'
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_rcca_3816918.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('we support this case since 12.7')
        return WAIVED


def range_profiling():
    sm = get_sm()
    if cuda_short_version > '12.6' and sm >= 7.0:
        result = {}
        passed, failed = 0, 0
        log_name = case_config['RANGE_PROFILING']['LOG_NAME']
        log_path = case_config['global']['env']['RANGE_PROFILING_PATH']
        mkdir(log_path)
        check_point1 = case_config['RANGE_PROFILING']['CHECK_POINT1']
        check_point2 = case_config['RANGE_PROFILING']['CHECK_POINT2']
        check_point3 = case_config['RANGE_PROFILING']['CHECK_POINT3']
        check_point4 = case_config['RANGE_PROFILING']['CHECK_POINT4']
        check_point5 = case_config['RANGE_PROFILING']['CHECK_POINT5']
        if cuda_short_version > '12.9':
            check_point5 = case_config['RANGE_PROFILING']['CHECK_POINT5_1']
        check_point6 = case_config['RANGE_PROFILING']['CHECK_POINT6']
        check_point7 = case_config['RANGE_PROFILING']['CHECK_POINT7']
        # build the checkpoint kernels sample
        step1_cmd = case_config['RANGE_PROFILING']['STEP1']['CMD1']
        check_result(step1_cmd, 'build_checkpoint_kernals-------%s' % step1_cmd, log_name, result)
        run_range_profiling1 = case_config['RANGE_PROFILING']['STEP2']['CMD1']
        run_range_profiling2 = case_config['RANGE_PROFILING']['STEP2']['CMD2']
        run_range_profiling3 = case_config['RANGE_PROFILING']['STEP2']['CMD3']
        run_range_profiling4 = case_config['RANGE_PROFILING']['STEP2']['CMD4']
        run_range_profiling5 = case_config['RANGE_PROFILING']['STEP2']['CMD5']
        run_range_profiling6 = case_config['RANGE_PROFILING']['STEP2']['CMD6']
        if sm >= 7.0:
            # run range_profiling
            check_result(run_range_profiling1, 'for step2_cmd1, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling2, 'for step2_cmd2, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling3, 'for step2_cmd3, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            # check_result(run_range_profiling4, 'for step2_cmd4, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling5, 'for step2_cmd5, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling6, 'for step2_cmd6, check result of range_profiling, there is no error/nan/fail and n/a', log_name, result, 'nan', 'err', 'n/a', 'fail', flag=6)
            check_result(run_range_profiling1, 'run step2_cmd1------{}'.format(run_range_profiling1), log_name, result, check_point1, check_point3)
            check_result(run_range_profiling2, 'run step2_cmd2------{}'.format(run_range_profiling2), log_name, result, check_point1, check_point4)
            check_result(run_range_profiling3, 'run step2_cmd3------{}'.format(run_range_profiling3), log_name, result, check_point2, check_point3)
            check_result(run_range_profiling4, 'run step2_cmd4------{}'.format(run_range_profiling4), log_name, result, check_point2, check_point4, 'CUPTI_ERROR_INVALID_OPERATION', flag=1)
            check_result(run_range_profiling5, 'run step2_cmd5------{}'.format(run_range_profiling5), log_name, result, check_point5, check_point6, check_point7)
            check_result(run_range_profiling6, 'run step2_cmd6------{}'.format(run_range_profiling6), log_name, result, check_point1, check_point3)

        else:
            check_result(run_range_profiling1, 'for step2_cmd7, run_range_profiling on gpu whose sm less than 70------%s' % run_range_profiling1, log_name, result, 'device architecture is not supported', flag=1)
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '%s/range_profiling.json' % log_path)
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logging.warning('support range_profiling since 12.7 and volta++')
        return WAIVED


def cupti_profile_coverage_old_api_3896463():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['LOG_NAME']
    log_path = case_config['global']['env']['CUPTI_PROFILE_COVERAGE_OLD_API_PATH']
    mkdir(log_path)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    # prepare sample
    cmd = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CMD'] % cupti_target_path
    check_result(cmd, 'prepare_sample by %s' % cmd, log_name, result)
    if cuda_short_version < '13.0':
        cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CMD1'] % (cupti_target_path, cuhook_path)
        check_result(cmd1, 'prepare_cuhook by %s' % cmd1, log_name, result)
    # profile sample list
    checkpoint = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['CHECK_POINT']
    if vgpu != 'none':
        sample_list = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['SAMPLE_LIST1'].split(',')
        if run_uvm_sample() is True:
            sample_list.append('UnifiedMemoryStreams')
    else:
        sample_list = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['SAMPLE_LIST'].split(',')
        if platform == 'arm':
            sample_list = [i for i in sample_list if i not in ['cuHook', 'vectorAddMMAP', 'UnifiedMemoryStreams']]
    glibc_version = get_glic_version()
    logger.info('glibc version is {}'.format(glibc_version))
    if glibc_version > '2.33' or cuda_short_version > '12.9':
        sample_list = [i for i in sample_list if i != 'cuHook']
    for sample in sample_list:
        if sample == 'simpleCudaGraphs':
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
            check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, 'Error', checkpoint, flag=2)
            continue
        step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP1']['CMD1'] % (cupti_target_path, sample, sample)
        step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP2']['CMD1'] % (cupti_target_path, sample, sample)
        step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP3']['CMD1'] % (cupti_target_path, sample, sample)
        if sample in ['UnifiedMemoryStreams']:
            step2_cmd1 = "cd %s;LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH ./profiler_injection_test -m smsp__warps_launched.sum -n 5 -r auto -e application -j step2_%s.json -a ./%s" % (
                cupti_target_path, sample, sample)
        if sample == 'cuHook':
            step1_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP1']['CMD1_1'] % cupti_target_path
            step3_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP3']['CMD1_1'] % cupti_target_path
            step2_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP2']['CMD1_1'] % cupti_target_path
        check_result(step1_cmd1, 'run step1_%s by %s' % (sample, step1_cmd1), log_name, result, 'Error', checkpoint, flag=2)
        check_result(step2_cmd1, 'run step2_%s by %s' % (sample, step2_cmd1), log_name, result, 'Error', checkpoint, flag=2)
        check_result(step3_cmd1, 'run step3_%s by %s' % (sample, step3_cmd1), log_name, result, 'Error', checkpoint, flag=2)
    # run nvtx range
    step4_cmd1 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD1'] % cupti_target_path
    step4_cmd2 = case_config['CUPTI_PROFILE_COVERAGE_OLD_API']['STEP4']['CMD2'] % cupti_target_path
    check_result(step4_cmd1, 'run step4_cmd1 by %s' % step4_cmd1, log_name, result, 'Error', checkpoint, flag=2)
    check_result(step4_cmd2, 'run step4_cmd1 by %s' % step4_cmd2, log_name, result, 'Error', checkpoint, flag=2)
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '%s/cupti_profile_coverage_old_api.json' % log_path)
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def check_cupti_libstatic_bss_4068303():
    if cuda_short_version > '12.8':
        result = {}
        passed, failed = 0, 0
        log_name = case_config['CHECK_LIBCUPTI_STATIC_BSS']['LOG_NAME']
        log_path = case_config['global']['env']['CHECK_LIBCUPTI_STATIC_BSS_PATH']
        mkdir(log_path)
        # prepare sample
        prepare_cmd1 = case_config['CHECK_LIBCUPTI_STATIC_BSS']['PREPARE']['CMD1'].format(host_password, host_password)
        run_loc_cmd(prepare_cmd1)
        prepare_cmd2 = case_config['CHECK_LIBCUPTI_STATIC_BSS']['PREPARE']['CMD2']
        check_result(prepare_cmd2, 'prepare_sample by {}'.format(prepare_cmd2), log_name, result)
        step1_cmd1 = case_config['CHECK_LIBCUPTI_STATIC_BSS']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run step1_cmd1 by %s' % step1_cmd1, log_name, result)
        step1_cmd2 = case_config['CHECK_LIBCUPTI_STATIC_BSS']['STEP1']['CMD2']
        out = run_test_cmd(step1_cmd2)
        if 'Mi' in out.output.split('\n')[0] and float(out.output.split('\n')[0].strip('Mi')) < 10.0:
            result['step1_cmd2-check the file size'] = 'passed'
        else:
            result['step1_cmd2-check the file size'] = 'failed'
        # calculate result
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/check_cupti_libstatic_bss.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('support since 12.9')
        return WAIVED


def check_cupti_finalize_memory_leak_4069127():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CHECK_CUPTI_FINALIZE_MEMORY_LEAK']['LOG_NAME']
    log_path = case_config['global']['env']['CHECK_CUPTI_FINALIZE_MEMORY_LEAK_PATH']
    mkdir(log_path)
    # prepare sample
    prepare_cmd1 = case_config['CHECK_CUPTI_FINALIZE_MEMORY_LEAK']['PREPARE']['CMD1'].format(host_password, host_password)
    run_loc_cmd(prepare_cmd1)
    prepare_tools_package('cupti', download_to_path, platform, cuda_short_version)
    prepare_cmd2 = case_config['CHECK_CUPTI_FINALIZE_MEMORY_LEAK']['PREPARE']['CMD2'].format(cupti_target_path)
    check_result(prepare_cmd2, 'prepare_sample by {}'.format(prepare_cmd2), log_name, result)
    step1_cmd1 = case_config['CHECK_CUPTI_FINALIZE_MEMORY_LEAK']['STEP1']['CMD1'].format(cupti_target_path)
    check_result(step1_cmd1, 'run step1_cmd1 by %s' % step1_cmd1, log_name, result)
    step1_cmd2 = case_config['CHECK_CUPTI_FINALIZE_MEMORY_LEAK']['STEP1']['CMD2']
    out = run_test_cmd(step1_cmd2)
    print(out.output)
    leak_size = int(out.output.split('\n')[0].split(',')[0] + out.output.split('\n')[0].split(',')[1])
    logger.info(f'the definitely lost size is {leak_size}')
    if leak_size < 200000:
        result['step1_cmd2-check the leak memory size'] = 'passed'
    else:
        result['step1_cmd2-check the leak memory size'] = 'failed'
    # calculate result
    result1 = calculate_result(result)
    dict_output(result1, flag=output_flag)
    dict_to_json(result1, '{}/check_cupti_finalize_memory_leak.json'.format(log_path))
    return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)


def check_cupti_api_5118033():
    if cuda_short_version >= '13.0':
        result = {}
        log_name = case_config['CHECK_CUPTI_API']['LOG_NAME']
        log_path = case_config['global']['env']['CHECK_CUPTI_API_PATH']
        mkdir(log_path)
        # prepare file
        prepare_cmd1 = case_config['CHECK_CUPTI_API']['PREPARE']['CMD1'].format(user, password, base_url)
        run_loc_cmd(prepare_cmd1)
        cupti_api_list = []
        with open(f'{log_path}/cupti_master.def', 'r') as f:
            for line in f.readlines():
                if 'cupti' in line and '#' not in line:
                    cupti_api_list.append(line.split(' ')[0].strip(' ').strip('\n'))
                else:
                    continue

        def get_cupti_api(api_file, cupti_api):
            in_dx12_scg_block = False
            cupti_api_list = []
            with open(api_file, 'r') as f:
                for line in f.readlines():
                    if cupti_api in line:
                        in_dx12_scg_block = True
                        continue
                    if in_dx12_scg_block and '#endif' in line:
                        in_dx12_scg_block = False

                        # Extract API if within the block
                    if in_dx12_scg_block and line and not line.startswith('#'):
                        api_name = line.strip(' ').strip('\n')
                        cupti_api_list.append(api_name)
            return cupti_api_list
        cupti_api_list1 = get_cupti_api(f'{log_path}/cupti_master.def', 'CUPTI_ENABLE_MULTI_SUBSCRIBERS')
        cupti_api_list2 = get_cupti_api(f'{log_path}/cupti_master.def', ' NV_FEATURE_CUPTI_MEASURE_CUPTI_PERF_WITH_NVTX_INTERNAL')
        cupti_api_list3 = get_cupti_api(f'{log_path}/cupti_master.def', 'NV_FEATURE_CUPTI_ENABLE_PM_TRIGGER_API')
        cupti_api_list = set(cupti_api_list) - set((cupti_api_list1 + cupti_api_list2 + cupti_api_list3))
        cupti_api_list_1 = [i for i in list(set(cupti_api_list)) if i != '']
        # for bug check_cupti_api_5118033 remove cuptiDisableNonOverlappingMode and
        cupti_api_list_1.remove('cuptiDisableNonOverlappingMode')
        cupti_api_list_1.remove('cuptiEnableNonOverlappingMode')
        for cupti_api in cupti_api_list_1:
            step1_cmd1 = case_config['CHECK_CUPTI_API']['STEP1']['CMD1'].format(cupti_api)
            step1_cmd2 = case_config['CHECK_CUPTI_API']['STEP1']['CMD2'].format(cupti_api)
            check_result(step1_cmd1, f'check_api-- "{cupti_api}" by {step1_cmd1}', log_name, result, 'DEFAULT')
            check_result(step1_cmd2, f'check_api-- "{cupti_api}" by {step1_cmd2}', log_name, result, 'DEFAULT')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/check_cupti_api.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('support this case since cuda 13.0')
        return WAIVED


def cupti_nvtx_ext_payload():
    if cuda_short_version >= '13.0':
        result = {}
        log_name = case_config['CUPTI_NVTX_EXT_PAYLOAD']['LOG_NAME']
        log_path = case_config['global']['env']['CUPTI_NVTX_EXT_PAYLOAD_PATH']
        mkdir(log_path)
        # prepare sample
        prepare_cmd1 = case_config['CUPTI_NVTX_EXT_PAYLOAD']['PREPARE']['CMD1']
        check_result(prepare_cmd1, 'prepare_sample by {}'.format(prepare_cmd1), log_name, result)
        step1_cmd1 = case_config['CUPTI_NVTX_EXT_PAYLOAD']['STEP1']['CMD1']
        check_result(step1_cmd1, 'run cmd to check no error information by {}'.format(step1_cmd1), log_name, result, 'fail', flag=6)
        check_result(step1_cmd1, 'run step1_cmd1 to check the expected message by {}'.format(step1_cmd1), log_name, result, 'Content of nvtx_payload_blob.txt', 'Parsing NVTX extended payload address', 'CUPTI_ACTIVITY_KIND_MARKER', 'CUPTI_ACTIVITY_KIND_MARKER_DATA', 'MARKER_DATA id')
        result1 = calculate_result(result)
        dict_output(result1, flag=output_flag)
        dict_to_json(result1, '{}/cupti_nvtx_ext_payload.json'.format(log_path))
        return (PASSED if (result1['failed'] == 0 and result1['passed'] != 0) else FAILED)
    else:
        logger.info('support this case since cuda 13.0')
        return WAIVED


example_text = """
Example:
python run_cupti_case.py # run all the function
python run_cupti_case.py -e cupti_static # run single case
python run_cupti_case.py -el cupti_static,cupti_spec,cupti_smoke # run multi cases
"""
reversion = '1.0'
check_list = [
    'activity_trace_async',
    'pc_sampling',
    'concurrent_profiling',
    'cupti_finalize',
    'sass_source_map',
    'cupti_trace_injection',
    'cupti_metric_properties',
    'unified_memory',
    'profiling_injection',
    'pc_sampling_continous',
    'pc_sampling_utility',
    'pc_sampling_start_stop',
    'cuda_graphs_trace',
    'cupti_nvtx',
    'sass_metrics',
    'userrange_profiling',
    'callback_profiling',
    'autorange_profiling',
    'nested_range_profiling',
    'callback_timestamp',
    'checkpoint_kernels',
    'cupti_external_correlation',
    'cuda_memory_trace',
    'pm_sampling',
    'range_profiling',
    'cupti_correlation',
    'cupti_query_sample',
    'openacc_trace',
    'cupti_nvtx_ext_payload',
    'cupti_trace_coverage_hes',
    'callback_metric',
    'callback_event',
    'event_sampling',
    'nvlink_bandwidth',
    'event_multi_gpu',
    'cupti_query',
    'cupti_trace_coverage',
    'cupti_profile_coverage',
    'pc_sampling_coverage_2804521',
    'pc_sampling_graphic_coverage_2941255',
    'cupti_extra_replay',
    'cupti_profile_injection_spec_sample',
    'cupti_mps',
    'range_profiling',
    'check_cupti_api_5118033',
    'cupti_support_hide_rtcore_3730300',
    'cupti_profile_coverage_old_api_3896463',
    'cupti_memcheck_support',
    'cupti_2_profiling',
    'cupti_rcca_3292084',
    'cupti_rcca_3816918',
    'check_cupti_libstatic_bss_4068303',
    'check_cupti_finalize_memory_leak_4069127',
    'pc_sampling_user_control',
    'support_collect_sass_3481156',
    'cupti_library',
    'cupti_trace',
    'cupti_guard',
    'cupti_static',
    'cupti_trace_injection_optix',
    'cupti_sample',
    'collect_pc_sampling_data',
    'cupti_trace_injection_graphic',
    'support_cuda_sw_stack_3365796',
]
dvs_list = [
    'activity_trace_async',
    'pc_sampling',
    'concurrent_profiling',
    'cupti_finalize',
    'sass_source_map',
    'cupti_trace_injection',
    'cupti_metric_properties',
    'unified_memory',
    'profiling_injection',
    'pc_sampling_continous',
    'pc_sampling_utility',
    'pc_sampling_start_stop',
    'cuda_graphs_trace',
    'cupti_nvtx',
    'sass_metrics',
    'userrange_profiling',
    'callback_profiling',
    'autorange_profiling',
    'nested_range_profiling',
    'callback_timestamp',
    'checkpoint_kernels',
    'cupti_external_correlation',
    'cuda_memory_trace',
    'pm_sampling',
    'range_profiling',
    'cupti_nvtx_ext_payload',
    'cupti_trace_coverage_hes',
    'cupti_correlation',
    'cupti_query_sample',
    'callback_metric',
    'callback_event',
    'event_sampling',
    'nvlink_bandwidth',
    'event_multi_gpu',
    'cupti_query',
    'cupti_trace_coverage',
    'cupti_profile_coverage',
    'cupti_extra_replay',
    'cupti_profile_injection_spec_sample',
    'range_profiling',
    'cupti_rcca_3816918',
    'check_cupti_finalize_memory_leak_4069127',
    'pc_sampling_user_control',
    'support_collect_sass_3481156',
    'cupti_library',
    'cupti_trace',
    'cupti_guard',
    'cupti_static',
    'support_cuda_sw_stack_3365796',
]

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description=None, epilog=example_text, formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument('-l', '--list_cases', action='store_true', default=False, help='print test cases')
    parser.add_argument("-c", "--config-file", dest="config_file", required=False, help='Specify test index file. e.g. cases')
    parser.add_argument("-a", "--arch", dest="arch", required=False, help='Specify smoke test on P9 or x86 or ARM')
    parser.add_argument("-e", "--excute", action='append', required=False, choices=check_list, help="Specify function to be run.")
    parser.add_argument("-el", "--excute_list", action='store', required=False, help="Specify multi function to be run.")
    parser.add_argument("-dvs", "--dvs_cases", action='store_true', default=False, help="Specify DVS cases to be run.")
    args = parser.parse_args()
    case_str = args.excute_list
    case_list_single = args.excute
    list_cases = args.list_cases
    if case_str:
        case_list = case_str.split(',')
        print(case_list)
        for case in case_list:
            mod = sys.modules["__main__"]
            getattr(mod, case)()
    elif case_list_single:
        ret = EXCEPT
        mod = sys.modules["__main__"]
        ret = getattr(mod, case_list_single[0])()
        print("---ret is {}".format(ret))
        # import sys
        sys.exit(ret)
    elif list_cases:
        print(str(check_list))
    elif args.dvs_cases:
        print(str(dvs_list))
    else:
        logger.info('please give correct case to run')
