# -*- encoding: utf-8 -*-
import base64
import os.path

try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
import argparse
import re
import urllib.request
import requests
from requests.exceptions import RequestException
try:
    import colorlog
except ImportError:
    os.popen('pip3 install colorlog')
import logging
import time
import os
import random
import paramiko
from scp import SCPClient

log_colors_config = {
    'DEBUG': 'cyan',
    'INFO': 'green',
    'WARNING': 'yellow',
    'ERROR': 'red',
    'CRITICAL': 'red',
}


class Log:
    def __init__(self, logname='server_config.log'):
        self.logname = logname
        self.log = logging.getLogger()
        self.log.setLevel(logging.INFO)
        self.formatter = colorlog.ColoredFormatter('%(log_color)s[%(asctime)s] [%(filename)s:%(lineno)d] [%(module)s:%(funcName)s] [%(levelname)s]- %(message)s', log_colors=log_colors_config)

    def console(self, level, message):
        fh = logging.FileHandler(self.logname, encoding='utf-8')
        fh.setLevel(logging.INFO)
        fh.setFormatter(self.formatter)
        self.log.addHandler(fh)
        ch = logging.StreamHandler()
        ch.setLevel(logging.DEBUG)
        ch.setFormatter(self.formatter)
        self.log.addHandler(ch)
        if level == 'info':
            self.log.info(message)
        elif level == 'debug':
            self.log.debug(message)
        elif level == 'warning':
            self.log.warning(message)
        elif level == 'error':
            self.log.error(message)
        self.log.removeHandler(ch)
        self.log.removeHandler(fh)
        fh.close()

    def debug(self, message):
        self.console('debug', message)

    def info(self, message):
        self.console('info', message)

    def warning(self, message):
        self.console('warning', message)

    def error(self, message):
        self.console('error', message)


log = Log()


def get_args():
    parser = argparse.ArgumentParser(description='Arguments for operate vgpu server or vm')
    parser.add_argument('-s', '--server', required=True, action='store', help='Specify the vgpu server ip')
    parser.add_argument('-p', '--platform', required=True, action='store',
                        help='specify the platform to operate, kvm or esxi')
    parser.add_argument('-hu', '--host_user', required=True, action='store', help='the user of host')
    parser.add_argument('-hp', '--host_password', required=True, action='store',
                        help='the password of host, If your password has special charactor, please use "password"')
    parser.add_argument('-hd', '--host_driver', required=False, action='store', help='specify host driver to use')
    parser.add_argument('-so', '--server_operate', required=False, action='store',
                        help='upgrade/install/uninstall driver, then reboot or reboot/reset the host')
    parser.add_argument('-vo', '--vm_operate', required=False, action='store',
                        help='power on/off vm who specify by --vm_name')
    parser.add_argument('-vm', '--vm_name', required=False, action='store', help='name of the vm')
    parser.add_argument('-a', '--add_params', required=False, action='store',
                        help='add vgpu params to vgpu, all->debug/profile/uvm, debug_profile, debug, profile')
    parser.add_argument('-mig', '--enable_mig', required=False, action='store',
                        help='enable mig support, 1 is enable, 0 is disable, must work with -vg option together')
    parser.add_argument('-vg', '--vgpu', required=False, action='store',
                        help='assign vgpu to vm, must has vm_name option')
    parser.add_argument('-vgl', '--vgpu_list', required=False, action='store',
                        help='crete vgpu, then assign/add multi vgpu to vm, must has vm_name option')
    parser.add_argument('-cg', '--create_vgpu', required=False, action='store',
                        help='specify vgpu or vgpu list to create')
    parser.add_argument('-bn', '--bus_number', required=False, action='store', help='specify bus_number to create vgpu or vgpu list')
    parser.add_argument('-vh', '--vgpu_heterogeneous', required=False, action='store',
                        help='specify vgpu heterogeneous mode to create different size vgpu, 1:enable, 0:disable')
    parser.add_argument('-rg', '--release_vgpu', required=False, action='store', help='specify vgpu to release on kvm')
    parser.add_argument('-gu', '--guest_user', required=False, action='store',
                        help='guest password to login the guest, If your password has special charactor, please use "password"')
    parser.add_argument('-gp', '--guest_password', required=False, action='store',
                        help='guest password to login the guest')
    parser.add_argument('-gd', '--guest_driver', required=False, action='store', help='specify guest driver to use')
    parser.add_argument('-gip', '--guest_ip', required=False, action='store',
                        help='guest ip address to ssh, default we use script to get ip address, if get fail, you can specify the ip address')
    parser.add_argument('-d', '--delete_params', required=False, action='store',
                        help='delete vgpu params to vgpu, all->debug/profile/uvm, debug_profile, debug, profile')
    parser.add_argument('-si', '--show_infor', required=False, action='store',
                        help='if show_information=vm, it show all the vm in the host, if vgpu, will show all vgpu information')
    parser.add_argument('-dr', '--driver_openRM', required=False, action='store',
                        help='if driver_rm mode is not 0, install RM mode driver')
    parser.add_argument('-ct', '--cuda_type', required=False, action='store',
                        help='cuda type,  kitpicks or kitbundles')
    parser.add_argument('-cv', '--cuda_version', required=False, action='store',
                        help='cuda version,  12.3 or 12.4')
    parser.add_argument('-cd', '--cuda_date', required=False, action='store',
                        help='cuda toolkit build date,  for example:  kitpicks 012 or kitbundles 20231112, download or install specify date toolkit')
    parser.add_argument('-ctd', '--cuda_toolkit_download', required=False, action='store',
                        help='only download latest cuda toolkit or specify cuda toolkit, not install')
    parser.add_argument('-fwd', '--fwd_driver', required=False, action='store',
                        help='if fwd=1, download forward compatibility needed driver and configure fwd env')
    parser.add_argument('-cut', '--cuda_url_type', required=False, action='store',
                        help='if cuda_url_type == 0, download cuda toolkit from internal, default download from kitmaker')
    parser.add_argument('-dt', '--download_tool', required=False, action='store',
                        help='default use lftp download cuda toolkit, if -dt= wget, use wget download cuda toolkit')
    parser.add_argument('-rct', '--delete_toolkit', required=False, action='store',
                        help='if -rt value exist, rm existed cuda toolkit')
    args = parser.parse_args()
    return args


server_args = get_args()
platform = server_args.platform.lower()
server_ip = server_args.server
server_user = server_args.host_user
server_password = server_args.host_password
fwd_driver_version = server_args.fwd_driver
cuda_url_type = server_args.cuda_url_type


mig_cmd1 = {'a30-1-6c': 'nvidia-smi mig -cgi 14&&nvidia-smi mig -cci 0 -gi 0',
            'a30-2-12c': 'nvidia-smi mig -cgi 5&&nvidia-smi mig -cci 1 -gi 0',
            'a30-4-24c': 'nvidia-smi mig -cgi 0&&nvidia-smi mig -cci 3 -gi 0',
            'a100-7-40c': 'nvidia-smi mig -cgi 0&&nvidia-smi mig -cci 4 -gi 0',
            'a100-1-5c': 'nvidia-smi mig -cgi 19&&nvidia-smi mig -cci 0 -gi 0',
            'a100-2-10c': 'nvidia-smi mig -cgi 15&&nvidia-smi mig -cci 1 -gi 0',
            'a100-3-20c': 'nvidia-smi mig -cgi 9&&nvidia-smi mig -cci 2 -gi 0',
            'a100-4-20c': 'nvidia-smi mig -cgi 5&&nvidia-smi mig -cci 3 -gi 0',
            'a100d-7-80c': 'nvidia-smi mig -cgi 0&&nvidia-smi mig -cci 4 -gi 0',
            'a100d-1-10c': 'nvidia-smi mig -cgi 19&&nvidia-smi mig -cci 0 -gi 0',
            'a100d-2-20c': 'nvidia-smi mig -cgi 15&&nvidia-smi mig -cci 1 -gi 0',
            'a100d-3-40c': 'nvidia-smi mig -cgi 9&&nvidia-smi mig -cci 2 -gi 0',
            'a100d-4-40c': 'nvidia-smi mig -cgi 5&&nvidia-smi mig -cci 3 -gi 0',
            'h100-1-10c': 'nvidia-smi mig -cgi 19&&nvidia-smi mig -cci 0 -gi 9',
            'h100-2-20c': 'nvidia-smi mig -cgi 14&&nvidia-smi mig -cci 1 -gi 3',
            'h100-3-40c': 'nvidia-smi mig -cgi 9&&nvidia-smi mig -cci 2 -gi 1',
            'h100-4-40c': 'nvidia-smi mig -cgi 5&&nvidia-smi mig -cci 3 -gi 2',
            'h100-7-80c': 'nvidia-smi mig -cgi 0&&nvidia-smi mig -cci 4 -gi 0'
            }

mig_cmd = {
    'a30-1-6c': 'nvidia-smi mig -cgi 14 -C',
    'a30-2-12c': 'nvidia-smi mig -cgi 5 -C',
    'a30-4-24c': 'nvidia-smi mig -cgi 0 -C',
    'a100-7-40c': 'nvidia-smi mig -cgi 0 -C',
    'a100-1-5c': 'nvidia-smi mig -cgi 19 -C',
    'a100-2-10c': 'nvidia-smi mig -cgi 14 -C',
    'a100-3-20c': 'nvidia-smi mig -cgi 9 -C',
    'a100-4-20c': 'nvidia-smi mig -cgi 5 -C',
    'a100d-7-80c': 'nvidia-smi mig -cgi 0 -C',
    'a100d-1-10c': 'nvidia-smi mig -cgi 19 -C',
    'a100d-2-20c': 'nvidia-smi mig -cgi 14 -C',
    'a100d-3-40c': 'nvidia-smi mig -cgi 9 -C',
    'a100d-4-40c': 'nvidia-smi mig -cgi 5 -C',
    'h100-1-10c': 'nvidia-smi mig -cgi 19 -C',
    'h100-2-20c': 'nvidia-smi mig -cgi 14 -C',
    'h100-3-40c': 'nvidia-smi mig -cgi 9 -C',
    'h100-4-40c': 'nvidia-smi mig -cgi 5 -C',
    'h100-7-80c': 'nvidia-smi mig -cgi 0 -C',
    'h20-1-12c': 'nvidia-smi mig -cgi 19 -C',
    'h20-1-24c': 'nvidia-smi mig -cgi 15 -C',
    'h20-2-24c': 'nvidia-smi mig -cgi 14 -C',
    'h20-3-48c': 'nvidia-smi mig -cgi 9 -C',
    'h20-4-48c': 'nvidia-smi mig -cgi 5 -C',
    'h20-7-96c': 'nvidia-smi mig -cgi 0 -C',
    'gh200-1-12c': 'nvidia-smi mig -cgi 19 -C',
    'gh200-1-24c': 'nvidia-smi mig -cgi 15 -C',
    'gh200-2-24c': 'nvidia-smi mig -cgi 14 -C',
    'gh200-3-48c': 'nvidia-smi mig -cgi 9 -C',
    'gh200-4-48c': 'nvidia-smi mig -cgi 5 -C',
    'gh200-7-96c': 'nvidia-smi mig -cgi 0 -C',
    'b100-1-23c': 'nvidia-smi mig -cgi 19 -C',
    'b100-1-45c': 'nvidia-smi mig -cgi 15 -C',
    'b100-2-45c': 'nvidia-smi mig -cgi 14 -C',
    'b100-3-90c': 'nvidia-smi mig -cgi 9 -C',
    'b100-7-180c': 'nvidia-smi mig -cgi 0 -C',
    'b200x-1-23c': 'nvidia-smi mig -cgi 19 -C',
    'b200x-1-45c': 'nvidia-smi mig -cgi 15 -C',
    'b200x-2-45c': 'nvidia-smi mig -cgi 14 -C',
    'b200x-3-90c': 'nvidia-smi mig -cgi 9 -C',
    'b200x-7-180c': 'nvidia-smi mig -cgi 0 -C',
    'gb200-1-24c': 'nvidia-smi mig -cgi 19 -C',
    'gb200-1-47c': 'nvidia-smi mig -cgi 15 -C',
    'gb200-2-47c': 'nvidia-smi mig -cgi 14 -C',
    'gb200-3-95c': 'nvidia-smi mig -cgi 9 -C',
    'gb200-4-95c': 'nvidia-smi mig -cgi 5 -C',
    'gb200-7-189c': 'nvidia-smi mig -cgi 0 -C',
    'dc-1-24c': 'nvidia-smi mig -cgi 47 -C',
    'dc-1-12c': 'nvidia-smi mig -cgi 47 -C',
    'dc-1-8c': 'nvidia-smi mig -cgi 47 -C',
    'dc-2-48c': 'nvidia-smi mig -cgi 35 -C',
    'dc-2-16c': 'nvidia-smi mig -cgi 35 -C',
    'dc-2-12c': 'nvidia-smi mig -cgi 35 -C',
    'dc-4-96c': 'nvidia-smi mig -cgi 32 -C',
    'dc-4-48c': 'nvidia-smi mig -cgi 32 -C',
    'dc-4-32c': 'nvidia-smi mig -cgi 32 -C',
    'dc-4-24c': 'nvidia-smi mig -cgi 32 -C'
}

ip_map_mac = {
    '************': 'fa:16:3e:81:0c:a3',
    '*************': 'fa:16:3e:85:fb:02',
    '***********': 'fa:16:3e:47:a4:5f',
    '************': 'fa:16:3e:89:8c:ae',
    '************': 'fa:16:3e:08:90:35',
    '************': 'fa:16:3e:b4:89:16',
    '*************': 'fa:16:3e:83:e4:5a',
    '************': 'fa:16:3e:d3:36:44',
    '************': 'fa:16:3e:cb:ba:6b',
    '***********': 'fa:16:3e:33:ca:98',
    '************': 'fa:16:3e:bf:26:ed',
    '***********': 'fa:16:3e:0b:85:0c',
    '************': 'fa:16:3e:7b:5c:f3',
    '************': 'fa:16:3e:1d:ca:31',
    '***********': 'fa:16:3e:f2:f7:fc',
    '************': 'fa:16:3e:e7:82:79',
    '************': 'fa:16:3e:a9:56:a0',
    '*************': 'fa:16:3e:36:17:b0',
    '*************': 'fa:16:3e:36:78:03',
    '***********81': 'fa:16:3e:bb:f1:c0',
    '************': 'fa:16:3e:d2:0e:03',
    '************': 'fa:16:3e:56:c9:99',
    '************': 'fa:16:3e:9b:5c:f6'
}


def fetch_service_account():
    import requests
    if "CQASYSTEM_TOKEN" in os.environ:
        api_token = os.environ["CQASYSTEM_TOKEN"]
    else:
        api_token_list = ['8c913a613f', '88b6f1ad91a', '815f13fd732']
        api_token = ''.join(api_token_list)
    response = requests.get('http://cqasystem.nvidia.com/api/credential/?ctype=service_account',
                            headers={'accept': 'application/json',
                                     'api-token': api_token}).json()
    return response['username'], urllib2.quote(response['password'])


def get_download_path():
    cmd = 'echo ~'
    out = os.popen(cmd)
    return out.read().split('\n')[0]


user, password = fetch_service_account()
# user = base64.decode('c3ZjY3Fh')
# password = base64.decode('Q1A1djp0VzNfK1ZNIUBK')
download_path = get_download_path()


def trans_connect(vm_ip, vm_user, vm_password):
    trans = paramiko.Transport((vm_ip, 22))
    trans.connect(username=vm_user, password=vm_password)
    return trans


def ssh_connect(trans):
    ssh = paramiko.SSHClient()
    ssh._transport = trans
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    return ssh


def ssh_disconnect(trans):
    return trans.close()


def check_file_exist(vm_ip, vm_user, vm_password, checked_file):
    try:
        trans_con = trans_connect(vm_ip, vm_user, vm_password)
        ssh = ssh_connect(trans_con)
        cmd = 'ls ~/{}*'.format(checked_file)
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
        file_list = ssh_stdout.read().decode('unicode_escape').split('\n')
        log.info('the exist file is {}'.format(file_list))
        if re.search(checked_file, file_list[0], re.IGNORECASE) is not None:
            if len(file_list) != 2:
                log.info('the file {} not download successful, please check'.format(checked_file))
                return False
            else:
                log.info('the driver file exist, no need download again')
                return True
        else:
            log.info('no driver file, need download')
            return False
    except Exception as e:
        log.error(f"Check file exist error: {str(e)}")
        return False


def prepare_driver_esxi_zip():
    try:
        log.info('will download the zip mode driver to {}'.format(download_path))
        if os.path.exists('{}/{}'.format(download_path, host_driver_zip)):
            cmd_del = "cd {};rm {}*".format(download_path, host_driver_zip)
            os.popen(cmd_del)
        cmd = "cd {}; lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}'".format(download_path, user, password, host_driver_url, host_driver_zip)
        os.popen(cmd)
        time.sleep(35)
        if os.path.exists('{}/{}'.format(download_path, host_driver_zip)):
            cmd1 = "cd {}; rm -r vib* *.xml *metadata.zip; unzip {}; sleep 5; cp vib20/NVD-*/* .".format(download_path, host_driver_zip)
            print(cmd1)
            print('extract the zip driver file')
            out = os.popen(cmd1)
            print(out.read())
            out.close()
        else:
            logging.error('download zip driver file fail, please check......')
            exit()
    except Exception as e:
        log.error(f"Prepare driver error: {str(e)}")
        exit(2)


def prepare_driver_esxi(driver):
    try:
        log.info('will download the driver to {}'.format(download_path))
        if driver == 'host':
            if host_driver:
                if os.path.exists('{}/{}'.format(download_path, host_driver)):
                    cmd = 'ls {}/{}'.format(download_path, host_driver)
                else:
                    prepare_driver_esxi_zip()
                    cmd = 'cd {}'.format(download_path)
                    # cmd = "cd {}; lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}'".format(download_path, user, password, host_driver_url,  host_driver)
            else:
                log.error('there is no host driver, please check vmware version, then make sure host driver exist')
        elif driver == 'guest':
            if os.path.exists('{}/{}'.format(download_path, guest_driver)):
                cmd = 'ls {}/{}'.format(download_path, guest_driver)
            else:
                cmd = "cd {}; lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}'".format(download_path, user, password, guest_drive_url, guest_driver)
        else:
            log.warning('please give correct params to download driver')
        os.popen(cmd)
        time.sleep(6)
        if driver == 'host':
            cmd1 = 'ls {}/{}'.format(download_path, host_driver)
        if driver == 'guest':
            cmd1 = 'ls {}/{}'.format(download_path, guest_driver)
        out = os.popen(cmd1)
        if driver == 'host':
            print(host_driver)
            if host_driver in out.read().split('\n')[0]:
                log.info('we prepare host driver --{} successful'.format(host_driver))
                return host_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
        if driver == 'guest':
            print(guest_driver)
            if guest_driver in out.read().split('\n')[0]:
                log.info('we prepare guest driver --{} successful'.format(guest_driver))
                return guest_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
    except Exception as e:
        log.error(f"Prepare driver error: {str(e)}")
        exit(2)


def prepare_driver(vm_user, vm_password, ip_addr, driver):
    try:
        log.info('we will download the {} driver '.format(driver))
        if driver == 'host':
            lftp_check(ip_addr, vm_user, vm_password, vm_type='host')
            file_status = check_file_exist(ip_addr, vm_user, vm_password, host_driver)
            # if os.path.exists('{}'.format(host_driver)):
            if file_status is True:
                cmd = 'ls {}'.format(host_driver)
            else:
                cmd = "lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}'".format(user, password, host_driver_url, host_driver)
        elif driver == 'guest':
            lftp_check(ip_addr, vm_user, vm_password, vm_type='host')
            file_status = check_file_exist(ip_addr, vm_user, vm_password, guest_driver)
            # if os.path.exists('{}'.format(guest_driver)):
            if file_status is True:
                cmd = 'ls {}'.format(guest_driver)
            else:
                cmd = "lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}; sleep 10s'".format(user, password, guest_driver_url, guest_driver)
        else:
            log.warning('please give correct params to download driver')
        trans = paramiko.Transport((ip_addr, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.exec_command(cmd)
        time.sleep(30)
        if driver == 'host':
            cmd1 = 'ls {}'.format(host_driver)
        if driver == 'guest':
            cmd1 = 'ls {}'.format(guest_driver)
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        out = ssh_stdout1.read().decode('unicode_escape')
        time.sleep(1)
        if driver == 'host':
            log.info('the host driver is {}'.format(host_driver))
            if host_driver in out.split('\n')[0] and 'lftp' not in out.split('\n')[0]:
                log.info('we prepare host driver --{} successful'.format(host_driver))
                return host_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
        if driver == 'guest':
            log.info('the guest driver is {}'.format(guest_driver))
            if guest_driver in out.split('\n')[0] and 'lftp' not in out.split('\n')[0]:
                log.info('we prepare guest driver --{} successful'.format(guest_driver))
                return guest_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
    except Exception as e:
        log.error(f"Prepare driver error: {str(e)}")
        exit(2)


def prepare_driver1(vm_user, vm_password, ip_addr, driver):
    try:
        log.info('we will download the driver ')
        if driver == 'host':
            lftp_check(ip_addr, vm_user, vm_password, vm_type='host')
            file_status = check_file_exist(ip_addr, vm_user, vm_password, host_driver)
            # if os.path.exists('{}'.format(host_driver)):
            if file_status is True:
                cmd = 'ls {}'.format(host_driver)
            else:
                cmd = "lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no; glob -- pget -n 20 {}/{}'".format(user, password, host_driver_url, host_driver)
        elif driver == 'guest':
            lftp_check(ip_addr, vm_user, vm_password, vm_type='host')
            file_status = check_file_exist(ip_addr, vm_user, vm_password, guest_driver)
            # if os.path.exists('{}'.format(guest_driver)):
            if file_status is True:
                cmd = 'ls {}'.format(guest_driver)
            else:
                cmd = "lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no; glob -- pget -n 20 {}/{}'".format(user, password, guest_driver_url, guest_driver)
        else:
            log.warning('please give correct params to download driver')
        trans = paramiko.Transport((ip_addr, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.exec_command(cmd)
        time.sleep(30)
        if driver == 'host':
            cmd1 = 'ls {}'.format(host_driver)
        if driver == 'guest':
            cmd1 = 'ls {}'.format(guest_driver)
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        out = ssh_stdout1.read().decode('unicode_escape')
        time.sleep(1)
        if driver == 'host':
            print(host_driver)
            if host_driver in out.split('\n')[0]:
                log.info('we prepare host driver --{} successful'.format(host_driver))
                return host_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
        if driver == 'guest':
            print(guest_driver)
            if guest_driver in out.split('\n')[0]:
                log.info('we prepare guest driver --{} successful'.format(guest_driver))
                return guest_driver
            else:
                log.info('we prepare {} driver fail'.format(driver))
                exit(2)
    except Exception as e:
        log.error(f"Prepare driver error: {str(e)}")
        exit(2)


def scp_file(target_ip, target_user, target_password, source_file, target_path):
    try:
        log.info('used for ESXi: copy driver from local to ESXi server')
        trans = paramiko.Transport((target_ip, 22))
        trans.connect(username=target_user, password=target_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        scp = SCPClient(ssh.get_transport())
        scp.put(source_file, recursive=True, remote_path=target_path)
        print('*******************************************************************')
        trans.close()
    except Exception as e:
        log.error(f"SCP file error: {str(e)}")
        exit(2)


def uninstall_driver(vm_ip, vm_user, vm_password):
    try:
        trans = paramiko.Transport((vm_ip, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        cmd1 = 'ls /usr/bin/nvidia-smi'
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        cmd1_out = ssh_stdout1.read().decode('unicode_escape')
        if 'nvidia-smi' in cmd1_out:
            cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S nvidia-uninstall --silent; sleep 2'.format(vm_password, vm_password)
            log.info('we uninstall the driver by --- {}'.format(cmd))
            ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
            print(ssh_stdout.read())
        else:
            log.info('no need uninstall the driver, because there is no driver in vm')
        trans.close()
    except Exception as e:
        log.error(f"Uninstall driver error: {str(e)}")
        exit(2)


def lftp_check(vm_ip, vm_user, vm_password, vm_type='host'):
    trans_con = trans_connect(vm_ip, vm_user, vm_password)
    ssh = ssh_connect(trans_con)
    cmd = 'ls /etc/lftp.conf'
    ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
    if ssh_stdout.read().decode('unicode_escape').split('\n')[0] == '/etc/lftp.conf':
        log.info('lftp exist, no need reinstall')
    else:
        log.warning('please install lftp in your vGPU host or vm, we will use it to download driver')
        cmd = 'uname -a'
        ssh_stdin, ssh_stdout1, ssh_stderr = ssh.exec_command(cmd)
        if 'ubuntu' in ssh_stdout1.read().decode('unicode_escape').split('\n')[0]:
            cmd = "echo {}|sudo -S apt -y install lftp".format(vm_password)
            if vm_type == 'host':
                cmd = "apt -y install lftp"
        else:
            cmd = "echo {}|sudo -S dnf -y install lftp".format(vm_password)
            if vm_type == 'host':
                cmd = "dnf -y install lftp"
        log.info('no lftp in OS, need install lftp')
        ssh.exec_command(cmd)
    ssh.close()


def get_guest_driver(vm_ip, vm_user, vm_password):
    try:
        trans_con = trans_connect(vm_ip, vm_user, vm_password)
        ssh = ssh_connect(trans_con)
        cmd1 = "nvidia-smi | grep 'Driver Version:'|awk -F ' ' '{print $3}'"
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        cmd1_out = ssh_stdout1.read().decode('unicode_escape')
        if cmd1_out.split('\n')[0]:
            return cmd1_out.split('\n')[0]
        else:
            return None
    except Exception as e:
        log.error(f"Get guest driver error: {str(e)}")
        return None


def install_driver1(vm_ip, vm_user, vm_password, vm_driver, driver_rm=None):
    try:
        trans = paramiko.Transport((vm_ip, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        if driver_rm:
            cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S bash {} -m=kernel-open --silent; sleep 6'.format(vm_password, vm_password, vm_driver)
        else:
            cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S bash {} --silent; sleep 2'.format(vm_password, vm_password, vm_driver)
        log.info('we install driver by --- {}'.format(cmd))
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
        print(ssh_stdout.read().decode('unicode_escape'))
        cmd1 = "nvidia-smi | grep 'Driver Version:'|awk -F ' ' '{print $3}'"
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        cmd1_out = ssh_stdout1.read().decode('unicode_escape')
        print(cmd1_out.split('\n'))
        if len(cmd1_out.split('\n')) != 1 and cmd1_out.split('\n')[0] in vm_driver:
            log.info('we install the driver successful')
        else:
            log.info('we install the driver failed')
        trans.close()
    except Exception as e:
        log.error(f"Install driver error: {str(e)}")
        exit(2)


def install_driver(vm_ip, vm_user, vm_password, vm_driver, driver_rm=None):
    try:
        trans = paramiko.Transport((vm_ip, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        if driver_rm:
            cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S bash {} -m=kernel-open --silent; sleep 6'.format(vm_password, vm_password, vm_driver)
        else:
            cmd = 'echo {}|sudo -S init 3; echo {}| sudo -S bash {} --silent; sleep 2'.format(vm_password, vm_password, vm_driver)
        log.info('we install driver by --- {}'.format(cmd))
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
        print(ssh_stdout.read().decode('unicode_escape'))
        cmd1 = "nvidia-smi | grep 'Driver Version:'|awk -F ' ' '{print $3}'"
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd1)
        cmd1_out = ssh_stdout1.read().decode('unicode_escape')
        print(cmd1_out.split('\n'))
        if len(cmd1_out.split('\n')) != 1 and cmd1_out.split('\n')[0] in vm_driver:
            log.info('we install the driver successful')
        else:
            log.info('we install the driver failed')
        trans.close()
    except Exception as e:
        log.error(f"Install driver error: {str(e)}")
        exit(2)


def prepare_fwd_env(vm_ip, vm_user, vm_password, fwd_driver):
    try:
        # if os.path.exists('{}'.format(guest_driver)):
        fwd_driver_full = "NVIDIA-Linux-x86_64-{}-grid.run".format(fwd_driver)
        file_status = check_file_exist(vm_ip, vm_user, vm_password, fwd_driver_full)
        if file_status is True:
            down_cmd = 'ls {}'.format(fwd_driver_full)
        else:
            down_cmd = "lftp -c 'open {}:{}@hqnvhwy02; set ftp:use-allo false; set ftp:passive-mode true; set ftp:prefer-epsv false; set ssl:verify-certificate no;  glob -- pget -n 20 {}/{}; sleep 10s'".format(user, password, guest_driver_url, fwd_driver_full)
        trans = paramiko.Transport((vm_ip, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.exec_command(down_cmd)
        if 'lftp' in down_cmd:
            time.sleep(30)
        fwd_driver_path = fwd_driver_full.split('.run')[0]
        cmd = 'echo {}|sudo -S rm -r {}; bash {} -x; sleep 2'.format(vm_password, fwd_driver_path, fwd_driver_full)
        log.info('we extract driver by --- {}'.format(cmd))
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd)
        print(ssh_stdout.read().decode('unicode_escape'))
        file1 = "libcuda.so.{}".format(fwd_driver)
        file2 = 'libcudadebugger.so.{}'.format(fwd_driver)
        file3 = 'libnvidia-ptxjitcompiler.so.{}'.format(fwd_driver)
        file4 = 'libnvidia-nvvm.so.{}'.format(fwd_driver)
        cmd1 = "cd {}; rm -r compat; mkdir compat; cp {} {} {} {} compat".format(fwd_driver_path, file1, file2, file3, file4)
        cmd2 = "echo {}|sudo -S echo '/usr/local/cuda/compat' > /etc/ld.so.conf.d/00-cuda-compat.conf".format(vm_password)
        cmd3 = "echo {}|sudo -S rm -r /usr/local/cuda/compat; cd {}; echo {}|sudo -S cp -r compat /usr/local/cuda/; echo {}|sudo -S ldconfig".format(vm_password, fwd_driver_path, vm_password, vm_password)
        cmd3_1 = 'cd /usr/local/cuda/compat; echo {}|sudo -S ln -s libcuda.so.1 libcuda.so; echo {}|sudo -S ln -s libnvidia-nvvm.so.4 libnvidia-nvvm.so'.format(vm_password, vm_password)
        cmd4 = 'ls /usr/local/cuda/compat'
        for cmd in [cmd1, cmd2, cmd3, cmd3_1]:
            print(cmd)
            log.warning('will run command--{}'.format(cmd))
            ssh.exec_command(cmd)
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd4)
        output = ssh_stdout.read().decode('unicode_escape').split('\n')[:-1]
        log.info('the /usr/local/cuda/compat has following content')
        print(output)
        if file1 in output and file2 in output and file3 in output and file4 in output:
            log.info('prepare forward Compatibility successful')
        else:
            log.error('prepare forward Compatibility fail, exit....')
            exit()
        trans.close()
    except Exception as e:
        log.error(f"Prepare forward compatibility error: {str(e)}")
        exit(2)


def get_vm_detail_info(vm_ip, vm_user, vm_password):
    try:
        trans = paramiko.Transport((vm_ip, 22))
        trans.connect(username=vm_user, password=vm_password)
        ssh = paramiko.SSHClient()
        ssh._transport = trans
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        vm_info_dict = {}
        get_cuda_version = "/usr/local/cuda/bin/nvcc --version|grep release|awk -F ' ' '{print $4,$5}'"
        get_driver_version = "nvidia-smi | grep NVIDIA-SMI|awk -F ' ' '{print $4,$6}'"
        get_cuda = "nvidia-smi | grep NVIDIA-SMI|awk -F ' ' '{print $7,$9}'"
        get_vgpu = "nvidia-smi |grep 0000000|awk -F ' ' '{print $4}'"
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(get_cuda_version)
        output = ssh_stdout.read().decode('unicode_escape').split('\n')[:-1]
        print(output)
        if 'release' in output[0] and output != []:
            vm_info_dict['cuda_version'] = output[0].strip(',').split(' ')[1]
        else:
            vm_info_dict['cuda_version'] = 'not install cuda toolkit'
        ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(get_driver_version)
        output1 = ssh_stdout1.read().decode('unicode_escape').split('\n')[:-1]
        print(output1)
        if output1 != []:
            if 'Driver' in output1[0]:
                vm_info_dict['driver version'] = output1[0].split(' ')[1]
            else:
                vm_info_dict['driver version'] = 'not install driver or guest driver not match host driver'
        else:
            vm_info_dict['driver version'] = 'not install driver or guest driver not match host driver'
        ssh_stdin2, ssh_stdout2, ssh_stderr2 = ssh.exec_command(get_cuda)
        output2 = ssh_stdout2.read().decode('unicode_escape').split('\n')[:-1]
        print(output2)
        if output2 != []:
            if 'CUDA' in output2[0]:
                if vm_info_dict['cuda_version'] == output2[0].split(' ')[1]:
                    vm_info_dict['driver mode'] = 'normal or FWD'
                else:
                    vm_info_dict['driver mode'] = 'minor/Enhance'
            else:
                vm_info_dict['driver mode'] = 'undermined'
        else:
            vm_info_dict['driver mode'] = 'undermined'
        ssh_stdin3, ssh_stdout3, ssh_stderr3 = ssh.exec_command(get_vgpu)
        output3 = ssh_stdout3.read().decode('unicode_escape').split('\n')[:-1]
        print(output3)
        if output3 != []:
            if 'C' in output3[0] and output3 != []:
                vm_info_dict['vGPU'] = output3[0]
            else:
                vm_info_dict['vGPU'] = 'not vgpu or driver has issue'
        else:
            vm_info_dict['vGPU'] = 'not vgpu or driver has issue'
        return vm_info_dict
    except Exception as e:
        log.error(f"Get vm detail info error: {str(e)}")
        return False


if server_args.cuda_version:
    cuda_version = server_args.cuda_version.lower()
    cuda_major = cuda_version.split('.')[0]
    cuda_min = cuda_version.split('.')[1]
    cuda_short_version = cuda_major + '.' + cuda_min


def get_latest_build_date():
    try:
        if server_args.cuda_type.lower() in 'kitpicks':
            print('cuda version is {}'.format(cuda_version))
            if len(cuda_version.split('.')) == 2:
                cuda_addr = "http://cuda-internal.nvidia.com/release-candidates/kitpicks/cuda-r{}-{}/{}.0/".format(cuda_major, cuda_min, cuda_short_version)
            else:
                cuda_addr = "http://cuda-internal.nvidia.com/release-candidates/kitpicks/cuda-r{}-{}/{}/".format(cuda_major, cuda_min, cuda_version)
        if server_args.cuda_type.lower() in 'kitbundles':
            cuda_addr = "http://cuda-internal.nvidia.com/release-candidates/kitbundles/r{}/".format(cuda_short_version)
        website = urllib2.urlopen(cuda_addr)
        html = website.read().decode('utf-8')
        if server_args.cuda_type.lower() in 'kitpicks':
            matchs = re.findall('"0.*', html)
            latest_build_version = matchs[-1].split('/')[0].strip('"')
        if server_args.cuda_type.lower() in 'kitbundles':
            mathcs = re.findall('href=".*', html)
            build_version = [i.split('"')[1].strip('/') for i in mathcs]
            build_version1 = [i for i in build_version if len(i) == 8]
            latest_build_version = build_version1[-1]
        log.info('the latest cuda build is {}'.format(latest_build_version))
        return latest_build_version
    except Exception as e:
        log.error("Get latest build date error: {}".format(str(e)))
        return False


def get_latest_build_date1():
    try:
        if server_args.cuda_type.lower() in 'kitpicks':
            print('cuda version is {}'.format(cuda_version))
            if len(cuda_version.split('.')) == 2:
                cuda_addr = "https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}-{}/{}.0/".format(cuda_major, cuda_min, cuda_short_version)
            else:
                cuda_addr = "https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}-{}/{}/".format(cuda_major, cuda_min, cuda_version)
        if server_args.cuda_type.lower() in 'kitbundles':
            cuda_addr = "https://kitmaker-web.nvidia.com/kitbundles/r{}/".format(cuda_short_version)
            
        log.info('Accessing CUDA download page: {}'.format(cuda_addr))
        try:
            response = requests.get(cuda_addr, verify=False)  # verify=False 用于忽略SSL证书验证
            response.raise_for_status()  # 检查响应状态
            html = response.text
        except requests.exceptions.HTTPError as e:
            log.error('Failed to access CUDA download page: HTTP Error {}'.format(e))
            return False
        except requests.exceptions.RequestException as e:
            log.error('Failed to access CUDA download page: Error {}'.format(e))
            return False
            
        if server_args.cuda_type.lower() in 'kitpicks':
            matchs = re.findall('name">.*', html)
            if not matchs:
                log.error('No matching build versions found in the page')
                return False
            latest_build_version = matchs[-1].split('>')[1].split('<')[0]
        if server_args.cuda_type.lower() in 'kitbundles':
            mathcs = re.findall('href=".*', html)
            if not mathcs:
                log.error('No matching build versions found in the page')
                return False
            build_version = [i.split('"')[1].strip('.').strip('/') for i in mathcs]
            build_version1 = [i for i in build_version if len(i) == 8]
            if not build_version1:
                log.error('No valid build versions found')
                return False
            latest_build_version = build_version1[-1]
            
        log.info('The latest CUDA build is {}'.format(latest_build_version))
        return latest_build_version
        
    except Exception as e:
        log.error("Get latest build date error: {}".format(str(e)))
        return False


def parse_cuda_page(cuda_date=None):
    latest_build_version = cuda_date if cuda_date is not None else get_latest_build_date()
    if server_args.cuda_type.lower() in 'kitpicks':
        if len(cuda_version.split('.')) == 2:
            cuda_addr = "http://cuda-internal.nvidia.com/release-candidates/kitpicks/cuda-r{}-{}/{}.0/{}/local_installers/".format(cuda_major, cuda_min, cuda_short_version, latest_build_version)
        else:
            cuda_addr = "http://cuda-internal.nvidia.com/release-candidates/kitpicks/cuda-r{}-{}/{}/{}/local_installers/".format(cuda_major, cuda_min, cuda_version, latest_build_version)
    elif server_args.cuda_type.lower() in 'kitbundles':
        cuda_addr = " http://cuda-internal.nvidia.com/release-candidates/kitbundles/r{}/{}/local_installers/".format(cuda_short_version, latest_build_version)
    else:
        log.error('please give correct cuda type to download')
    # log.info('will download the cuda toolkit from {}'.format(cuda_addr))
    try:
        website = urllib2.urlopen(cuda_addr)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except httplib.HTTPException as e:
        print('HTTPException')
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False
    html = website.read().decode('utf-8')
    matchs = re.findall('cuda.*_linux.run', html)
    latest_build_name = matchs[-1].split('>')[1]
    cuda_download_path = cuda_addr + latest_build_name
    return cuda_download_path


def parse_cuda_page1(cuda_date=None):
    latest_build_version = cuda_date if cuda_date is not None else get_latest_build_date1()
    if server_args.cuda_type.lower() in 'kitpicks':
        if len(cuda_version.split('.')) == 2:
            cuda_addr = "https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}-{}/{}.0/{}/local_installers/".format(cuda_major, cuda_min, cuda_short_version, latest_build_version)
        else:
            cuda_addr = "https://kitmaker-web.nvidia.com/kitpicks/cuda-r{}-{}/{}/{}/local_installers/".format(cuda_major, cuda_min, cuda_version, latest_build_version)
    elif server_args.cuda_type.lower() in 'kitbundles':
        cuda_addr = " https://kitmaker-web.nvidia.com/kitbundles/r{}/{}/local_installers/".format(cuda_short_version, latest_build_version)
    else:
        log.error('please give correct cuda type to download')
    # log.info('will download the cuda toolkit from {}'.format(cuda_addr))
    try:
        website = urllib.request.urlopen(cuda_addr)
    except urllib2.HTTPError as e:
        print('HTTPError = ' + str(e.code))
        return False
    except urllib2.URLError as e:
        print('URLError = ' + str(e.reason))
        return False
    except httplib.HTTPException as e:
        print('HTTPException')
        return False
    except Exception:
        import traceback
        print('generic exception: ' + traceback.format_exc())
        return False
    html = website.read().decode('utf-8')
    matchs = re.findall('cuda.*_linux.run', html)
    latest_build_name = matchs[-1]
    # latest_build_name = matchs[-1].strip('.').strip('/')
    cuda_download_path = cuda_addr + latest_build_name
    return cuda_download_path


def prepare_cuda(vm_ip, vm_user, vm_password, cuda_date=None, cuda_toolkit_download=None):
    try:
        cuda_package_url = parse_cuda_page1(cuda_date) if cuda_date is not None else parse_cuda_page1()
        if cuda_url_type == '0':
            cuda_package_url = parse_cuda_page(cuda_date) if cuda_date is not None else parse_cuda_page()
        cuda_package = cuda_package_url.split('/')[-1]
        trans_con = trans_connect(vm_ip, vm_user, vm_password)
        ssh = ssh_connect(trans_con)
        if server_args.delete_toolkit:
            rm_cmd = 'rm -r {}*'.format(cuda_package)
            log.warning('delete the download or exist cuda toolkit')
            ssh.exec_command(rm_cmd)
        cmd_ls = 'ls {}*'.format(cuda_package)
        ssh_stdin, ssh_stdout, ssh_stderr = ssh.exec_command(cmd_ls)
        out = ssh_stdout.read().decode('unicode_escape')
        print(out)
        time.sleep(1)
        if cuda_package in out.split('\n')[0] and len(out.split('\n')) == 2:
            if 'lftp' not in out.split('\n')[0]:
                log.info('we had download the package, no need download again')
            else:
                log.info('does not download {} successful, please check, exit.....'.format(cuda_package))
                exit()
        else:
            log.warning('Set the max download time is 10 mins, please make sure your download speed is enough for Download cuda package, otherwise manually download the cuda package')
            if server_args.download_tool:
                cmd = 'wget {}'.format(cuda_package_url)
            else:
                cmd = "lftp -c 'pget -c -n 10 {}'".format(cuda_package_url)
            log.info('will download the latest cuda package via---{}'.format(cmd))
            ssh.exec_command(cmd)
            if '10.2' in guest_ip or '10.19' in guest_ip or '10.23' in guest_ip:
                time.sleep(800)
            else:
                time.sleep(20)
        if cuda_toolkit_download is not None:
            log.warning('we only download the cuda toolkit---{} from {}'.format(cuda_package, cuda_package_url))
        else:
            ssh_stdin1, ssh_stdout1, ssh_stderr1 = ssh.exec_command(cmd_ls)
            out1 = ssh_stdout1.read().decode('unicode_escape')
            log.warning('the exist file is {}'.format(out1.split('\n')))
            if cuda_package in out1.split('\n')[0] and len(out1.split('\n')) == 2 and 'lftp' not in out1.split('\n')[0]:
                cmd1 = 'echo {}|sudo -S rm -r /usr/local/cuda*; echo {}|sudo -S bash {} --silent --toolkit'.format(vm_password, vm_password, cuda_package)
                log.info('install toolkit via {}'.format(cmd1))
                ssh_stdin2, ssh_stdout2, ssh_stderr2 = ssh.exec_command(cmd1)
                log.info('install output is {}'.format(ssh_stdout2.read().decode('unicode_escape')))
                time.sleep(6)
            else:
                log.error('please check the cuda package ---{} exist or not, {}.lftp-pget-status should not exist, exit.....'.format(cuda_package, cuda_package))
                exit(2)
            cmd3 = 'ls /usr/local/cuda-{}'.format(cuda_short_version)
            ssh_stdin3, ssh_stdout3, ssh_stderr3 = ssh.exec_command(cmd3)
            out3 = ssh_stdout3.read().decode('unicode_escape')
            log.info('Under cuda-{} folder, the child file and child folder are {}'.format(cuda_short_version, out3.split('\n')))
            if len(out3.split('\n')) > 20:
                log.info('nice, install cuda toolkit successful')
            else:
                log.info('install cuda toolkit fail, please check, exit.....')
                exit()
        ssh_disconnect(trans_con)
    except Exception as e:
        log.error(f"Prepare cuda error: {str(e)}")
        exit(2)


class Vgpu_server:
    def __init__(self):
        self.user = server_user
        self.password = server_password
        self.ip = server_ip
        self.platform = platform
        self.vgpu_list = ['a30', 'a16', 'a40', 'h100', 'l40', 'l4']  # used by create vgpu on ESXi,such as: nvidia_h100-80c or grid_v100-8c
        try:
            self.trans = paramiko.Transport((self.ip, 22))
            self.trans.connect(username=self.user, password=self.password)
            self.ssh = paramiko.SSHClient()
            self.ssh._transport = self.trans
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        except:
            log.error('ssh connect server fail')
            log.warning('please check server is power on or not')
            exit(3)

    def connect_server(self):
        try:
            trans = paramiko.Transport((self.ip, 22))
            trans.connect(username=self.user, password=self.password)
            ssh = paramiko.SSHClient()
            ssh._transport = trans
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            return trans, ssh
        except Exception as e:
            log.error(f"Connect server error: {str(e)}")
            return False, False

    def disconnect_server(self):
        try:
            self.trans.close()
        except Exception as e:
            log.error(f"Disconnect server error: {str(e)}")

    def excute_cmd_on_server(self, cmd):
        try:
            ssh_stdin, ssh_stdout, ssh_stderr = self.ssh.exec_command(cmd)
            return ssh_stdout.read().decode('unicode_escape'), ssh_stderr.read().decode('unicode_escape')
        # return ssh_stdout.read(), ssh_stderr.read()
        except Exception as e:
            log.error(f"Execute command on server error: {str(e)}")
            return False, False

    def get_all_vm(self):
        try:
            if platform.lower() == 'kvm':
                cmd = "virsh list --all | awk '{print $2}'"
            else:
                cmd = "vim-cmd vmsvc/getallvms | awk '{print $1,$2}'"
            cmd_out, ssh_stderr = self.excute_cmd_on_server(cmd)
            if platform.lower() == 'kvm':
                vm_list1 = cmd_out.split('\n')[2:-1]
                vm_list = [i for i in vm_list1 if i != '']
                return dict(zip(vm_list, vm_list))
            else:
                vm_list, vm_id = [], []
                for line in cmd_out.split('\n')[0:-1]:
                    if 'vCLS' in line or 'Vmid' in line:
                        continue
                    else:
                        vm_list.append(line.split(' ')[1].strip(' '))
                        vm_id.append(int(line.split(' ')[0].strip(' ')))
                return dict(zip(vm_list, vm_id))
        except Exception as e:
            log.error(f"Get all vm error: {str(e)}")
            return False

    def power_off_all_vm(self):
        try:
            vm_dict = self.get_all_vm()
            for vm_name, vm_id in vm_dict.items():
                if self.get_vm_status(vm_name) == 'power_off':
                    log.info('vm ---{} is power off, no need shutdown'.format(vm_name))
                else:
                    if self.vm_power_off(vm_name):
                        log.info('vm --- {} was shut down'.format(vm_name))
                    else:
                        log.info('vm ---{} is not shutdown, exit.....'.format(vm_name))
                        exit(3)
        except Exception as e:
            log.error(f"Power off all vm error: {str(e)}")
            exit(3)

    def power_off_all_vm_cg(self):
        try:
            cmd = "ps -ef | grep qemu|awk '{print $2}'|xargs kill -9"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            cmd1 = "ps -ef | grep qemu|awk '{print $2}'"
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd1)
            if len(cmd_out1.split('\n')) <= 3:
                log.info('close vm of CG successful')
            else:
                log.error('close vm of CG fail, exit........')
                exit()
        except Exception as e:
            log.error(f"Power off all vm of CG error: {str(e)}")
            exit(3)

    def get_vm_status(self, vm_name):
        try:
            if self.platform == 'kvm':
                cmd = 'virsh list --all | grep -w {}'.format(vm_name)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                '''
                for line in cmd_out.split('\n'):
                    if vm_name in line:
                        if 'running' in line:
                            log.info('the vm ----{} is power on now'.format(vm_name))
                            return 'power_on'
                        else:
                            log.warn('the vm ----{} is power off now'.format(vm_name))
                            return 'power_off'
                    else:
                        continue
                '''
                if 'running' in cmd_out:
                    log.info('the vm ----{} is power on now'.format(vm_name))
                    return 'power_on'
                else:
                    log.warning('the vm ----{} is power off now'.format(vm_name))
                    return 'power_off'
            if self.platform == 'esxi':
                vm_dict = self.get_all_vm()
                cmd = 'vim-cmd vmsvc/power.getstate {}'.format(vm_dict[vm_name])
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                time.sleep(10)
                if 'Powered on' in cmd_out:
                    log.info('{} is power_on'.format(vm_name))
                    return 'power_on'
                else:
                    return 'power_off'
        except Exception as e:
            log.error(f"Get vm status error: {str(e)}")

    def vm_power_off(self, vm_name):
        try:
            log.info('we will power_off the vm ---{}'.format(vm_name))
            if self.platform == 'kvm':
                cmd = 'virsh shutdown {}'.format(vm_name)
            if self.platform == 'esxi':
                vm_dict = self.get_all_vm()
                cmd = 'vim-cmd vmsvc/power.off {}'.format(vm_dict[vm_name])
            self.excute_cmd_on_server(cmd)
            time.sleep(30)
            if self.platform == 'kvm':
                time.sleep(90)
            vm_status = self.get_vm_status(vm_name)
            print('========================================')
            print('the vm {} status is {}'.format(vm_name, vm_status))
            print('++++++++++++++++++++++++++++++++++++++++')
            if vm_status == 'power_off':
                log.info('we power_off the vm ---{} successful'.format(vm_name))
                return True
            else:
                self.force_power_off(vm_name)
                time.sleep(30)
                if self.get_vm_status(vm_name) == 'power_off':
                    log.info('vm ---{} is power off, no need shutdown'.format(vm_name))
                    return True
                else:
                    log.info('vm ---{} is not shutdown, exit.....'.format(vm_name))
                    return False
        except Exception as e:
            log.error(f"Vm power off error: {str(e)}")
            return False

    def force_power_off(self, vm_name):
        try:
            if platform == 'esxi':
                world_id = []
                cmd = "esxcli vm process list|grep -A 1 {}|grep 'World ID'".format(vm_name)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                for line in cmd_out.split('\n'):
                    if 'World ID' in line:
                        world_id.append(line.split(":")[1].strip(' '))
                if len(world_id) != 0:
                    for i in world_id:
                        cmd1 = "esxcli vm process kill -t hard -w {}".format(i)
                        self.excute_cmd_on_server(cmd1)
                        time.sleep(2)
        except Exception as e:
            log.error(f"Force power off error: {str(e)}")

    def vm_power_on(self, vm_name):
        try:
            if self.get_vm_status(vm_name) == 'power_on':
                log.info('the vm is power on now, no need run power on command')
                return True
            else:
                if self.platform == 'kvm':
                    cmd = 'virsh start {}'.format(vm_name)
                if self.platform == 'esxi':
                    vm_dict = self.get_all_vm()
                    cmd = 'vim-cmd vmsvc/power.on {}'.format(vm_dict[vm_name])
                self.excute_cmd_on_server(cmd)
                if platform == 'esxi':
                    time.sleep(30)
                else:
                    time.sleep(60)
                vm_status = self.get_vm_status(vm_name)
                print('========================================')
                print('the vm {} status is {}'.format(vm_name, vm_status))
                print('++++++++++++++++++++++++++++++++++++++++')
                if vm_status == 'power_on':
                    log.info('we power on the vm--{} successful'.format(vm_name))
                    return True
                else:
                    log.info('we power on the vm--{} fail'.format(vm_name))
                    return False
        except Exception as e:
            log.error(f"Vm power on error: {str(e)}")
            return False

    def get_vm_mac_addr(self, vm_name):
        try:
            if platform == 'kvm':
                vm_file = '/etc/libvirt/qemu/{}.xml'.format(vm_name)
                get_mac_cmd = "grep 'mac address' {}|awk -F '=' '{print $2}'|awk -F '/' '{print $1}'".format(vm_file)
            elif platform == 'esxi':
                vm_path = self.get_vm_path(vm_name)
                vm_file = "{}/{}.vmx".format(vm_path, vm_name)
                get_mac_cmd = "grep Address {}|awk -F '=' '{print $2}'".format(vm_file)
            else:
                log.error('please give correct platform, exit......')
                exit()
            cmd_out, cmd_err = self.excute_cmd_on_server(get_mac_cmd)
            if cmd_out.split('\n')[0].strip(' ').strip('"').strip("'"):
                return cmd_out.split('\n')[0].strip(' ').strip('"').strip("'")
            else:
                return False
        except Exception as e:
            log.error(f"Get vm mac address error: {str(e)}")
            return False

    def get_vm_ip(self, vm_name):
        try:
            ip_list = []
            try_time = 0
            log.warning('we will get the vm ip address via run command on host')
            if 'win' not in vm_name:
                if self.get_vm_status(vm_name) == 'power_on':
                    if platform == 'esxi':
                        vm_dict = self.get_all_vm()
                        cmd = 'vim-cmd vmsvc/get.guest {} | grep -m 1 "ipAddress = \"'.format(vm_dict[vm_name])
                        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                        for line in cmd_out.split('\n'):
                            if 'ipAddress' in line:
                                try:
                                    ip_list.append(line.split('"')[1])
                                except IndexError:
                                    log.warning('get ip address of {} fail, please check'.format(vm_name))
                            else:
                                continue
                        log.debug('the {} ip is {}'.format(vm_name, ip_list))
                        return ip_list
                    elif platform == 'kvm':
                        cmd = "virsh domifaddr --source agent {}| grep enp".format(vm_name)
                        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                        for line in cmd_out.split('\n'):
                            if 'ipv4' in line:
                                try:
                                    ip_list.append(line.split(' ')[-1].split('/')[0])
                                except IndexError:
                                    log.warning('get ip address of {} fail, please check'.format(vm_name))
                        log.debug('the {} ip is {}'.format(vm_name, ip_list))
                        return ip_list
                else:
                    self.vm_power_on(vm_name)
                    try_time += 1
                    self.get_vm_ip(vm_name)
                    log.info('the vm is shutdown, can not get ip address')
                    log.info('so power on the vm, then try to get the ip address')
                    # exit(3)
                if try_time == 3:
                    exit(3)
                    log.info('we try 3 time, but can not power on the {}, please take a look'.format(vm_name))
            else:
                log.warning('can not get windows vm ip address')
        except Exception as e:
            log.error(f"Get vm ip error: {str(e)}")

    # get current host driver version
    def get_host_driver_version(self):
        try:
            cmd = "nvidia-smi| grep 'Driver Version'| awk -F : '{print $2}'|awk -F ' ' '{print $1}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if cmd_out:
                log.info('we can get the current driver version----{}'.format(cmd_out.split('\n')[0]))
                return cmd_out.split('\n')[0]
            else:
                log.warning('there is no driver on host')
                return False
        except Exception as e:
            log.error(f"Get host driver version error: {str(e)}, exit....")
            exit(1)
    
    # get host driver, used by uninstall host driver
    def get_host_driver(self):
        try:
            if platform == 'kvm':
                cmd = 'rpm -qa |grep NV'
            else:
                cmd = "esxcli software vib list | grep -i NV|awk -F ' ' '{print $1}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            return cmd_out.split('\n')[0].strip(' ')
        except Exception as e:
            log.error(f"Get host driver error: {str(e)}")
            return False

    def get_used_vgpu_info(self):
        '''
        :return: for exsi: {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
                for kvm: {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0', 'vgpu_uuid': '11d111f2-3927-49c4-8661-7c5a1f180f8a'}
        '''
        try:
            vgpu_info = {}
            vgpu_device, vm, vgpu_name, vgpu_uuid, vgpu_id = [], [], [], [], []
            if platform == 'kvm':
                cmd = "nvidia-smi vgpu -q |grep -B 10 'Guest Driver Version'"
            else:
                cmd = "nvidia-smi vgpu -q |grep -B 5 'vGPU Name'"
            cmd_out, cmd_err1 = self.excute_cmd_on_server(cmd)
            for line in cmd_out.split('\n'):
                if platform == 'kvm':
                    if 'GPU 00' in line:
                        vgpu_device.append(line.split(' ')[-1].strip(' '))
                    elif 'VM Name' in line:
                        vm.append(line.split(':')[-1].strip(' '))
                    elif 'vGPU Name' in line:
                        vgpu_name.append(line.split(':')[-1].strip(' ').split(' ')[1])
                    elif 'MDEV UUID' in line:
                        vgpu_uuid.append(line.split(":")[-1].strip(' '))
                    elif 'vGPU ID' in line:
                        vgpu_id.append(line.split(":")[-1].strip(' '))
                    else:
                        continue
                else:
                    if 'GPU 00' in line:
                        vgpu_device.append(line.split(' ')[-1].strip(' '))
                    elif 'VM Name' in line:
                        vm.append(line.split(':')[-1].strip(' '))
                    elif 'vGPU Name' in line:
                        vgpu_name.append(line.split(':')[-1].strip(' ').split(' ')[1])
                    elif 'vGPU ID' in line:
                        vgpu_id.append(line.split(":")[-1].strip(' '))
                    else:
                        continue
            if len(vgpu_id) != 0:
                if platform == 'kvm':
                    for i in range(0, len(vgpu_id)):
                        vgpu_info[vgpu_id[i]] = {'vgpu': vgpu_name[i], 'vm_name': vm[i], 'vgpu_uuid': vgpu_uuid[i]}
                else:
                    for i in range(0, len(vgpu_id)):
                        vgpu_info[vgpu_id[i]] = {'vgpu': vgpu_name[i], 'vm_name': vm[i]}
                log.info('the used vgpu information is {}'.format(vgpu_info))
                return vgpu_name, vgpu_info
            else:
                return False, False
        except Exception as e:
            log.error(f"Get used vgpu info error: {str(e)}")
            return False, False

    def judge_esxi_version(self):
        try:
            cmd = "vmware --version|awk -F ' ' '{print $3}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if len(cmd_out.split('\n')) == 2:
                return cmd_out.split('\n')[0]
            else:
                log.error('please check the platform is ESXi or not, exit.....')
                exit(2)
        except Exception as e:
            log.error(f"Judge ESXi version error: {str(e)}")
            exit(2)

    def reboot_server(self):
        try:
            cmd = 'rm vgpu_info.txt'
            self.excute_cmd_on_server(cmd)
            cmd1 = 'reboot'
            log.info('reboot the host')
            self.excute_cmd_on_server(cmd1)
        except Exception as e:
            log.error(f"Reboot server error: {str(e)}")

    def upgrade_host_driver(self, driver_path, host_driver):
        try:
            log.debug('will upgrade the host driver to {}'.format(host_driver))
            self.power_off_all_vm()
            current_driver_version = self.get_host_driver()
            if platform == 'kvm':
                if current_driver_version < '535.36':
                    cmd_list = ['rpm -Uv {}/{}'.format(driver_path, host_driver)]
                    for cmd in cmd_list:
                        log.info('install driver by ---{}'.format(cmd))
                        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                        print(cmd_out)
                        time.sleep(60)
                else:
                    self.uninstall_host_driver()
                    self.install_host_driver(driver_path, host_driver)
                self.reboot_server()
            else:
                # cmd_list = ['vim-cmd hostsvc/maintenance_mode_enter', 'localcli software acceptance set --level=CommunitySupported', 'localcli software vib install --no-sig-check -v {}/{}'.format(driver_path, host_driver), 'vim-cmd hostsvc/maintenance_mode_exit', 'reboot']
                cmd_list = ['esxcli software vib update --maintenance-mode --no-sig-check -v {}/{}'.format(driver_path, host_driver)]
                for cmd in cmd_list:
                    log.info('install driver by ---{}'.format(cmd))
                    cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                    print(cmd_out)
                    time.sleep(60)
                self.reboot_server()
        except Exception as e:
            log.error(f"Upgrade host driver error: {str(e)}")

    # install the driver, step1, uninstall current driver, step2, install new driver
    def install_host_driver(self, driver_path, host_driver, kvm_type=None):
        try:
            log.debug('will install the host driver --- {}'.format(host_driver))
            current_driver = self.get_host_driver_version()
            print('current driver is {}'.format(current_driver))

            def run_install_cmd(platform, driver_path, host_driver):
                if platform == 'kvm':
                    if 'rpm' in host_driver:
                        cmd_list = ['rpm -iv {}/{}'.format(driver_path, host_driver)]
                    else:
                        cmd_list = ['init 3&&bash {}/{} --silent'.format(driver_path, host_driver)]
                    for cmd in cmd_list:
                        log.info('install driver by ---{}'.format(cmd))
                        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                        print(cmd_out)
                        time.sleep(60)
                    self.reboot_server()
                    # how to check install the driver successful or fail on KVM platform? in the main_function--re-connect host, then check it
                else:
                    cmd_list = ['esxcli software vib install --maintenance-mode --no-sig-check -v {}/{}'.format(driver_path, host_driver)]
                    print(cmd_list)
                    for cmd in cmd_list:
                        log.info('install driver by ---{}'.format(cmd))
                        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                        print(cmd_out)
                        time.sleep(60)
                    self.reboot_server()
            if current_driver is not False:
                if platform == 'kvm':
                    host_driver1 = host_driver.split('-')[3]
                else:
                    host_driver1 = host_driver.split('_')[-1].split('-')[0]
                if re.search(host_driver1, current_driver, re.IGNORECASE) is not None:
                    log.info('current driver is {}, no need re-install it'.format(current_driver))
                else:
                    if kvm_type == 'cg':
                        self.power_off_all_vm_cg()
                    else:
                        self.power_off_all_vm()
                    self.uninstall_host_driver()
                    run_install_cmd(platform, driver_path, host_driver)
            else:
                if kvm_type == 'cg':
                    self.power_off_all_vm_cg()
                else:
                    self.power_off_all_vm()
                run_install_cmd(platform, driver_path, host_driver)
        except Exception as e:
            log.error(f"Install host driver error: {str(e)}")

    def uninstall_host_driver(self):
        try:
            log.debug('will uninstall the host driver')
            self.power_off_all_vm()
            driver_version = self.get_host_driver_version()
            if platform == 'kvm':
                if driver_version.split('.')[0] <= '525':
                    current_driver = self.get_host_driver()
                    cmd_list = ['rpm -ev {}'.format(current_driver)]
                elif driver_version < '535.36' and len(driver_version) <= 6:
                    current_driver = self.get_host_driver()
                    cmd_list = ['rpm -ev {}'.format(current_driver)]
                else:
                    cmd_list = ['nvidia-uninstall --silent']
                for cmd in cmd_list:
                    log.info('uninstall driver by ---{}'.format(cmd))
                    cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                    print(cmd_out)
                    time.sleep(60)
            else:
                current_driver = self.get_host_driver()
                cmd_list = ['esxcli software vib remove --maintenance-mode -n  {}'.format(current_driver)]
                for cmd in cmd_list:
                    log.info('uninstall driver by ---{}'.format(cmd))
                    cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                    print(cmd_out)
        except Exception as e:
            log.error(f"Uninstall host driver error: {str(e)}")

    def get_vm_path(self, vm_name):
        try:
            if platform != 'kvm':
                cmd = 'find / -name {}'.format(vm_name)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print(cmd_out)
                if cmd_out:
                    log.info('the {} path is {}'.format(vm_name, cmd_out.split('\n')[0].strip(' ')))
                    print(cmd_out.split('\n')[0].strip(' '))
                    return cmd_out.split('\n')[0].strip(' ')
                else:
                    log.info('please check {} exist or not'.format(vm_name))
                    exit()
            else:
                # cmd = 'echo ~'
                cmd = 'find / -name {}'.format(vm_name)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                if cmd_out:
                    log.info('For KVM, the path is /{}/{}'.format(cmd_out.split('\n')[-2].split('/')[1].strip(' '), cmd_out.split('\n')[-2].split('/')[2].strip(' ')))
                else:
                    log.info('please check {} exist or not'.format(vm_name))
                    return False
        except Exception as e:
            log.error(f"Get vm path error: {str(e)}")
            return False

    def get_gpu_bus_id(self):
        '''
        :return: a list, such as: ['6+A100+00000000:24:00.0+On', '7+A100+00000000:3D:00.0+0']
        '''
        '''
        try:
            gpu_bus_id, bus_id, gpu_name = [], [], []
            cmd = "nvidia-smi |grep 0000000|awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for i in cmd_out.split('\n')[0:-1]:
                bus_id.append(i.split(' ')[0].split('-')[0])
            cmd2 = "nvidia-smi |grep 0000000|awk -F ' ' '{print $4}'"
            cmd2_out, cmd_err = self.excute_cmd_on_server(cmd2)
            cmd_out1 = cmd2_out.read()
            for i in cmd_out1.split('\n')[0:-1]:
                gpu_name.append(i.split(' ')[0])
            for index, i in enumerate(bus_id):
                gpu_bus_id.append(gpu_name[index] + '-' + i)
            log.info('we get the gpu bus id successful')
            return gpu_bus_id
        '''
        try:
            gpu_bus_id = []
            cmd = "nvidia-smi | grep 000000| awk -F '|' '{print $2, $3, $4}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for line in cmd_out.split('\n')[0:-1]:
                gpu_info = [i for i in line.strip(' ').split(' ') if i != '']
                gpu_bus_id.append(gpu_info[0] + '+' + gpu_info[2] + '+' + gpu_info[-3] + '+' + gpu_info[-1])
            log.info(f'get the pgpu bus information: {gpu_bus_id}')
            return gpu_bus_id
        except:
            log.info('we get the gpu name:bus id failed ')
            return False

    def host_support_vgpu(self, vgpu):
        try:
            cmd = 'nvidia-smi vgpu -s'
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            vgpu_list = []
            for line in cmd_out.split('\n'):
                vgpu_list.append(line.strip(' ').split(' ')[-1])
            if vgpu.upper() in vgpu_list:
                log.info('host has the vgpu--{}'.format(vgpu))
                return True
            else:
                log.info('the vgpu---{} is not in host'.format(vgpu))
                return False
        except Exception as e:
            log.error(f"Host support vgpu error: {str(e)}")
            return False

    def get_host_gpu_info(self):
        '''
        :return: [{'cuda_visible_devices:': '0', 'bus_id': '00000000:1A:00.0', 'uuid': 'GPU-64d6486a-8fa5-0268-f713-8c339b8a83d3', 'gpu': 'V100-PCIE-16GB'},
        {'cuda_visible_devices:': '1', 'bus_id': '00000000:1B:00.0', 'uuid': 'GPU-b39ca232-f56c-5fbb-dc17-388d2a1ce579', 'gpu': 'A100-PCIE-40GB'}]
        '''
        try:
            gpu_bus_id = self.get_gpu_bus_id()
            cmd = 'nvidia-smi -L'
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            host_gpu_info = []
            for index, line1 in enumerate(cmd_out.split('\n')[0:-1]):
                vgpu_dict = {}
                for line in line1.split(':'):
                    if 'GPU ' in line:
                        vgpu_dict["cuda_visible_devices:"] = line.split(' ')[-1]
                    elif 'UUID' in line:
                        vgpu_dict["gpu"] = line.split(" ")[2]
                    elif 'GPU-' in line:
                        vgpu_dict["uuid"] = line.strip(')').strip(' ')
                    else:
                        continue
                if gpu_bus_id is not False:
                    vgpu_dict['bus_id'] = gpu_bus_id[index].split('+')[-2]
                host_gpu_info.append(vgpu_dict)
            log.info('we get the host gpu info successful')
            return host_gpu_info
        except:
            log.info('we get the host gpu info failed')
            return False

    def get_gpu_uuid(self, vgpu):
        '''
        use cmd ------ nvidia-smi -L
        :param vgpu:
        :return: GPU-a528d4cf-ede9-ec1d-264f-a02821803c4d
        '''
        try:
            cmd = 'nvidia-smi -L'
            gpu = vgpu.split('-')[0].upper()
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for line in cmd_out.split('\n')[0:-1]:
                if gpu in line:
                    log.info('the vgpu map with {}'.format(line.split("UUID: ")[1].strip(')')))
                    return line.split("UUID: ")[1].strip(')')
                else:
                    continue
        except:
            log.info('get the vgpu--{}  uuid failed'.format(vgpu))
            exit(2)

    def verify_vgpu_params(self, param, config_file):
        try:
            cmd = 'grep {} {}'.format(param, config_file)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if param in cmd_out.split('\n')[0]:
                log.info('{} had been enabled'.format(param))
                return True
            else:
                log.info('{} was not be enabled'.format(param))
                return False
        except Exception as e:
            log.error(f"Verify vgpu params error: {str(e)}")
            return False

    def get_mig_available_gpu_list(self, vgpu):
        try:
            if vgpu.split('-')[0].upper() in ['A100', 'A30', 'A100D']:
                # get used vgpu info {'T4-4C': {'vm_name': 'ubuntu20.04-desktop-auto', 'vgpu': '00000000:40:00.0'}}
                vgpu_name, used_vgpu = self.get_used_vgpu_info()
                # get host gpu info
                '''
                [{'cuda_visible_devices:': '0', 'bus_id': '00000000:1A:00.0', 'uuid': 'GPU-64d6486a-8fa5-0268-f713-8c339b8a83d3', 'gpu': 'V100-PCIE-16GB'}]
                '''
                gpu_info = self.get_host_gpu_info()
                A100_list, A30_list, A100D_list = [], [], []
                A100_used, A30_used, A100D_list = [], [], []
                for gpu in gpu_info:
                    if 'A30' in gpu['gpu']:
                        A30_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                    elif 'A100' in gpu['gpu']:
                        A100D_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                    elif 'A100-PCIE' in gpu['gpu']:
                        A100_list.append(gpu['cuda_visible_devices'] + '-' + gpu['bus_id'])
                    else:
                        continue
                for key, value in used_vgpu.items():
                    if 'A30' in key:
                        A30_used.append(value(vgpu))
                    elif '100' in key:
                        A100_used.append(value(vgpu))
                    else:
                        continue
                A100_available = [i for i in A100_list if i.split('-')[-1] not in A100_used]
                A100D_available = [i for i in A100D_list if i.split('-')[-1] not in A100_used]
                A30_available = [i for i in A30_list if i.split('-')[-1] not in A30_used]
                return A30_available, A100_available, A100D_available
            else:
                log.info('{} not support mig'.format(vgpu))
                return False
        except Exception as e:
            log.error(f"Get mig available gpu list error: {str(e)}")
            return False

    def get_gpu_list(self):
        try:
            cmd = "nvidia-smi -L|awk -F  ' ' '{print $4}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            gpu_list = []
            for i in cmd_out.split('\n'):
                if "0:" not in i:
                    gpu_list.append(i.strip(' '))
            log.info('get the host gpu list')
            return gpu_list
        except Exception as e:
            log.error(f"Get gpu list error: {str(e)}")
            return False

    def judge_mig_enable(self, vgpu, pgpu=server_args.bus_number):
        flag = False
        try:
            pgpu_info = self.get_gpu_bus_id()
            log.info('pgpu information is {}'.format(pgpu_info))
            if pgpu:
                pgpu_info = [i for i in pgpu_info if pgpu.upper() in i]
            for i in pgpu_info:
                if 'On' in i:
                    log.info('we had enable mig on host for vgpu--{}, but maybe we need enable all mig for testing, please make sure'.format(vgpu))
                    flag = True
                    break
                else:
                    continue
            return flag
        except Exception as e:
            log.error(f"Judge mig enable error: {str(e)}, the mig status is {flag}")
            return flag

    def judge_mig_enable1(self, vgpu):
        try:
            cmd = 'nvidia-smi |grep -A 2 {}'.format(vgpu.upper().split('-')[0].strip('D'))
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if 'Enabled' in cmd_out:
                log.info('we had enable mig on host for vgpu--{}, but maybe we need enable all mig for testing, please make sure'.format(vgpu))
                return True
            else:
                return False
        except Exception as e:
            log.error(f"Judge mig enable error: {str(e)}")
            return False

    def get_kvm_version(self):
        try:
            cmd = 'cat /etc/redhat-release'
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            print(cmd_out)
            return cmd_out.split('\n')[0].split(' ')[-2].strip(' ')
        except Exception as e:
            log.error(f"Get kvm version error: {str(e)}")
            exit()

    def check_kvm_type(self):
        try:
            cmd = 'uname -a'
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if 'Ubuntu' in cmd_out.split('\n')[0]:
                if 'x86' in cmd_out.split('\n')[0]:
                    if '22.04' in cmd_out.split('\n')[0]:
                        kvm1 = 'redhat'
                    else:
                        kvm1 = 'ubuntu'
                elif 'aarch64' in cmd_out.split('\n')[0]:
                    kvm1 = 'cg'
                else:
                    log.error('please check the kvm host platform, exit.....')
                    exit()
            elif 'aarch64' in cmd_out.split('\n')[0]:
                kvm1 = 'cg'
            else:
                kvm1 = 'redhat'
            return kvm1
        except Exception as e:
            log.error(f"Check kvm type error: {str(e)}")

    # for kvm platform
    def enable_vgpu_support_kvm(self):
        try:
            # enable vgpu
            cmd_vgpu = "/usr/lib/nvidia/sriov-manage -e all"
            kvm_type = self.check_kvm_type()
            if kvm_type == 'redhat':
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
                redhat_version = self.get_kvm_version()
                log.info('now redhat version is {}'.format(redhat_version))
                if cmd_err == '':
                    log.info('enable vgpu support on kmv successful or vgpu had enabled')
                    return True
                else:
                    log.info('enable vgpu support on kmv fail')
                    return False
                '''
                if redhat_version > '9.4':
                    log.info('now redhat version is {}'.format(redhat_version))
                    if cmd_err == '':
                        log.info('enable vgpu support on kmv successful or vgpu had enabled')
                        return True
                    else:
                        log.info('enable vgpu support on kmv fail')
                        return False
                else:
                    if 'VFs' not in cmd_out:
                        log.info('enable vgpu support on kmv fail')
                        return False
                    else:
                        log.info('enable vgpu support on kmv successful or vgpu had enabled')
                        return True
                '''
            else:
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
                if 'VFs' not in cmd_out:
                    log.info('enable vgpu support on kmv fail')
                    return False
                else:
                    log.info('enable vgpu support on kmv successful or vgpu had enabled')
                    return True
        except Exception as e:
            log.error(f"Unexpected error in enable_vgpu_support_kvm: {str(e)}")
            return False

    # can enable or disable all mig for vgpu if gpu is more than 1
    def enable_mig(self, vgpu, action=1, gpu_bus=None):
        log.warning('will enalbe mig or disable mig, 1:enable; 0:disable')
        if vgpu.split('-')[0].split(' ')[-1].upper().strip('D') in ['A100', 'A30', 'H100', 'GH200', 'H20', 'H200X'] or vgpu.split('_')[0].upper().strip('D') in ['A100', 'A30', 'H100', 'GH200', 'H20'] or len(vgpu.split(' ')[-1].split('-')) == 3 or 'dc' in vgpu or 'DC' in vgpu:
            gpu_info3 = self.get_gpu_bus_id()
            print(gpu_info3)
            if gpu_bus:
                for i in gpu_info3:
                    if gpu_bus.upper() in i:
                        gpu_index = i.split('+')[0]
                        break
                specify_gpu = '-i {}'.format(gpu_index)
            else:
                specify_gpu = ''
            if action == 1:
                log.info('enable mig on host')
                cmd = 'nvidia-smi -mig 1 {}'.format(specify_gpu)
            else:
                if platform == 'kvm':
                    log.info('destory GI&CI then disable MIG')
                    destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                    self.excute_cmd_on_server(destory_ci_gi)
                cmd = 'nvidia-smi -mig 0 {}'.format(specify_gpu)
            log.info('will run the command---{}'.format(cmd))
            self.excute_cmd_on_server(cmd)
            if platform == 'esxi':
                log.warning('will reboot the ESXi host')
                self.reboot_server()
            # kvm H100 reboot will disable mig
            if platform == 'kvm' and vgpu.split('-')[0].upper().strip('D') in ['A100', 'A30']:
                log.warning('will reboot the kvm host if gpu is ampere')
                self.reboot_server()
        else:
            log.info('{} does not support mig, please check......, exit'.format(vgpu.split('-')[0].upper()))
            exit(1)

    # used for ESXi platform add vgpu to vm
    def get_pgpu_uuid(self, vgpu):
        try:
            if len(vgpu.split('-')) == 2:
                gpu = vgpu.split('-')[0].lower()
            else:
                gpu = vgpu.split('-')[0].lower() + '-'
            if gpu in ['t4', 'v100']:
                pgpu_uuid = self.get_gpu_uuid(vgpu)
            elif gpu in ['a100', 'a100-']:
                pgpu_uuid = "20F1145F0707080808080800000003"  # A100-7-40c  20F1145F0707080808080800000003
            elif gpu in ['a16', 'a10']:
                pgpu_uuid = "25B614A90500000003"  # A16
            elif gpu in ['a40']:
                pgpu_uuid = "2235145A0606060606060600000003"  # A40-48C
            elif gpu in ['a30', 'a30-']:
                pgpu_uuid = "20B715320707070700000003"  # A30
            elif gpu in ['h100', 'h100-']:
                pgpu_uuid = "2331162600000003"  # H100
            elif gpu in ['a100d', 'a100d-']:
                pgpu_uuid = "20B515330707070707070700000003"
            elif gpu in ['l40']:
                pgpu_uuid = "26B5169D05060606060606060606060600000003"
            elif gpu in ['l4']:
                pgpu_uuid = "27B816EE050606060600000003"
            elif gpu.split('-')[0].lower() in ['dc', 'DC']:
                pgpu_uuid = '2BB5204E00000003'
            else:
                log.info('please give correct vgpu to deploy')
                exit(1)
            return pgpu_uuid
        except Exception as e:
            log.error(f"Get pgpu uuid error: {str(e)}")
            exit(1)

    # for esxi platform
    def get_vgpu_memsize_vmware(self, vgpu):
        log.info('For ESXi platform, get the vgpu memsize')
        mem_size = {'h100-80c': '81920', 'h100-1-10c': '10240', 'h100-2-20c': '20480', 'h100-3-40c': '40960',
                    'h100-4-40c': '81920', 'h100-7-80c': '81920',
                    'h100-40c': '40960', 'h100-20c': '20480', 'h100-10c': '10240', 'h100-5c': '5120', 'a100d-80c': '81920', 'a100d-7-80c': '81920',
                    'a100d-1-10c': '10240', 'a100d-2-20c': '20480', 'a100d-3-40c': '40960', 'a100d-4-40c': '81920',
                    'a100d-40c': '40960', 'a100d-20c': '20480', 'a100d-10c': '10240', 'a100-40c': '40960',
                    'a100-20c': '20480', 'a100-7-40c': '40960', 'a100-10c': '10240',
                    'a100-1-5c': '10240', 'l40-12c': '12288', 'l40-24c': '24576', 'l40-48c': '49152', 'l40-4c': '4096', 'l4-24c': '24576',
                    'l4-12c': '12288', 'l4-6c': '6144', 't4-16c': '12288', 't4-8c': '6144', 't4-4c': '3072', 'a30-1-6c': '6144', 'a30-12c': '12288',
                    'a30-2-12c': '12288', 'a30-4-24c': '24576', 'a30-24c': '24576', 'a40-48c': '49152', 'a30-6c': '6144',
                    'a40-24c': '24576', 'a40-12c': '12288', 'a40-6c': '6144', 'v100-8c': '8192',
                    'v100-16c': '16384', 'a16-8c': '16384', 'a16-16c': '32768', 'a16-4c': '8192', 'v100-4c': '4096',
                    'a10-12c': '12288', 'a10-24c': '24576', 'a10-6c': '6144', 'dc-96c': '98304', 'dc-48c': '49152', 'dc-24c': '24576',
                    'dc-12c': '12288', 'dc-8c': '8192', 'dc-4-96c': '98304', 'dc-2-48c': '49152', 'dc-1-24c': '24576'
                    }
        if mem_size.get(vgpu.lower()):
            log.info('For ESXi platform, get the vgpu memsize--{}'.format(mem_size.get(vgpu.lower())))
            return mem_size.get(vgpu.lower())
        else:
            log.info('please give correct vgpu or make sure the dict has the correct vgpu value. exit.....')
            exit(1)

    def is_1_1_vgpu(self, vgpu):
        if vgpu.lower() in ['a40-48c', 'a100-40c', 'a100d-80c', 'a30-24c', 'a10-24c', 'a16-16c', 'h100-80c', 'l40-48c', 'l4-24c',
                            't4-16c', 'v100-16c', 'a100-7-40c', 'a100d-7-80c', 'h100-7-80c', 'a30-4-24c', 'h20-96c',
                            'gh200-96c', 'gh200-7-96c', 'gh200-3-48c' 'b100-80c', 'gb200-189c', 'gb200-7-189c', 'dc-96c', 'dc-4-96c', 'dc-2-48c', 'dc-1-24c']:
            log.warning('the {} is 1:1 vGPU'.format(vgpu.lower()))
            return True
        else:
            log.warning('the {} is N:1 vGPU'.format(vgpu.lower()))
            return False

    # used for add vgpu to vm, pciSlotNumber
    def get_slot_number_vmware(self, vm_name):
        try:
            log.debug('get the slot number for ESXi platform')
            vm_path = self.get_vm_path(vm_name)
            cmd_uuid = "cd {}; grep pciSlotNumber {}.vmx".format(vm_path, vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd_uuid)
            splot_list = []
            for i in cmd_out.split('\n'):
                if 'ethernet' not in i or ' ' not in i:
                    splot_list.append(i)
            log.info('the split list is {}'.format(splot_list))
            splot_list1 = [i for i in splot_list if i != '']
            splot_list2 = [i.split('"')[1] for i in splot_list1]
            return int(max(splot_list2)) + 1
        except Exception as e:
            log.error(f"Get slot number error: {str(e)}")
            exit(1)

    def check_vgpu_in_vm(self, vm_name):
        try:
            vm_path = self.get_vm_path(vm_name)
            cmd = 'cat {}/{}.vmx|grep vgpu'.format(vm_path, vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            vgpu_list1 = []
            print(cmd_out)
            vgpu_list = cmd_out.split('\n')[:-1]
            print(vgpu_list)
            if vgpu_list:
                for i in vgpu_list:
                    print(i)
                    vgpu_list1.append(i.split('_')[1].strip('"'))
            print(vgpu_list1)
            return vgpu_list1
        except Exception as e:
            log.error(f"Check vgpu in vm error: {str(e)}")
            exit(1)

    def check_tools_option_in_exsi_vm(self, vm_name):
        try:
            vm_path = self.get_vm_path(vm_name)
            cmd = 'cat {}/{}.vmx|grep cfg'.format(vm_path, vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            option_list1 = []
            option_list = cmd_out.split('\n')[:-1]
            if option_list:
                print(option_list)
                return option_list
            else:
                return option_list1
        except Exception as e:
            log.error(f"Check tools option in esxi vm error: {str(e)}")
            exit(1)

    def add_vgpu_to_vm_vmware(self, add_vgpu_list, vm_name):
        try:
            log.warning('For ESXi platform, add {} to {}'.format(add_vgpu_list, vm_name))
            add2_vgpu_list_1 = add_vgpu_list.split(',')
            add2_vgpu_list = [i for i in add2_vgpu_list_1 if i != '']
            add1_vgpu_list = [i.lower() for i in add2_vgpu_list]
            esxi_version = self.judge_esxi_version()
            if len(add1_vgpu_list) > 1 and len(set(add1_vgpu_list)) != 1 and len(add1_vgpu_list[0].split('-')) == 2:
                if esxi_version >= '8.0.3':
                    self.vgpu_heterogeneous_support(platform='esxi', operate='1')
                else:
                    log.err('do not support vgpu_heterogeneous on vmware{}, exit.....'.format(esxi_version))
                    exit()
            '''
            else:
                if esxi_version >= '8.0.3':
                    self.vgpu_heterogeneous_support(platform='esxi', operate='1')
                else:
                    log.err('do not support vgpu_heterogeneous on vmware{}, no need disable'.format(esxi_version))
            '''
            vgpu_list = self.judge_pgpu_in_vm(vm_name)
            for index, vgpu in enumerate(add1_vgpu_list):
                log.info('the ESXi version is {}'.format(esxi_version))
                if self.host_support_vgpu(vgpu) is True:
                    if esxi_version == '7.0' and vgpu.split('-')[0].lower() != 'a40' and len(vgpu_list) != 1:
                        log.info('we only can support add 1 vGPU in esxi 7.0 platform')
                        exit(2)
                    else:
                        vgpu_number = 0 + index
                        # host_vgpu_info = self.get_host_gpu_info()
                        vm_status = self.get_vm_status(vm_name)
                        if vm_status == 'power_on':
                            self.vm_power_off(vm_name)
                        if platform != 'kvm':
                            vm_path = self.get_vm_path(vm_name)
                            config_file = '{}/{}.vmx'.format(vm_path, vm_name)
                            if vgpu.split('-')[0].lower() in self.vgpu_list or vgpu.split('-')[0].lower() in ['dc']:
                                target_vgpu = 'nvidia_' + vgpu
                                if vgpu.split('-')[0].lower() == 'dc':
                                    target_vgpu = 'nvidia_' + 'rtx_pro_6000_blackwell_' + vgpu
                            else:
                                target_vgpu = 'grid_' + vgpu
                            # add vgpu to vm
                            pgpu_uuid = self.get_pgpu_uuid(vgpu)
                            vgpu_size = self.get_vgpu_memsize_vmware(vgpu)
                            pci_number = self.get_slot_number_vmware(vm_name)
                            virtual_dev = 'pciPassthru{}.virtualDev = "vmiop"'.format(vgpu_number)
                            add_vm_vgpu = 'pciPassthru{}.vgpu = "{}"'.format(vgpu_number, target_vgpu)
                            fb_size = 'pciPassthru{}.fbSizeMB = "{}"'.format(vgpu_number, vgpu_size)
                            vm_vgpu_ppid = 'pciPassthru{}.pgpu = "{}"'.format(vgpu_number, pgpu_uuid)
                            pci_present = 'pciPassthru{}.present = "TRUE"'.format(vgpu_number)
                            vgpu_pci_number = 'pciPassthru{}.pciSlotNumber = "{}"'.format(vgpu_number, pci_number)
                            if vgpu_number == '0':  # vm has no vgpu, add it whether is 1:1 or n:1 vgpu
                                option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number, vm_vgpu_ppid]
                            else:
                                if self.is_1_1_vgpu(vgpu.lower()) is True:  # if is 1:1 vGPU, it must add vgpu_ppid and vgpu_pci_number
                                    log.info('we wil add 1:1 vgpu---{}'.format(vgpu))
                                    option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number, vm_vgpu_ppid]
                                else:
                                    log.info('we will add n:1 vgpu, the vgpu is --{}'.format(vgpu))
                                    option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number, vm_vgpu_ppid]
                            for option in option_list:
                                cmd = "cd {}; sed -i '$a {}' {}.vmx".format(vm_path, option, vm_name)
                                self.excute_cmd_on_server(cmd)
                                time.sleep(1)
                            # add tools option
                            debug_param = 'pciPassthru{}.cfg.enable_debugging = "1"'.format(vgpu_number)
                            uvm_param = 'pciPassthru{}.cfg.enable_uvm = "1"'.format(vgpu_number)
                            profile_param = 'pciPassthru{}.cfg.enable_profiling = "1"'.format(vgpu_number)
                            enable_debug = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, debug_param, config_file)
                            enable_uvm = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, uvm_param, config_file)
                            enable_profiling = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, profile_param, config_file)
                            # judge whether add uvm option
                            if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                tool_option = [enable_uvm, enable_profiling, enable_debug]
                            else:
                                if len(vgpu.strip(' ').split('-')) != 3:
                                    tool_option = [enable_profiling, enable_debug]
                                else:
                                    tool_option = [enable_uvm, enable_profiling, enable_debug]
                            for option in tool_option:
                                self.excute_cmd_on_server(option)
                                time.sleep(1)
                else:
                    log.info('the vgpu is not in host,please check......')
                    exit(2)
            self.vm_power_on(vm_name)
        except Exception as e:
            log.error(f"Add vgpu to vm error: {str(e)}")
            exit(1)

    '''
    def add_vgpu_to_vm_vmware(self, add_vgpu_list, vm_name):
        log.warning('For ESXi platform, add {} to {}'.format(add_vgpu_list, vm_name))
        add2_vgpu_list = add_vgpu_list.split(',')
        add1_vgpu_list = [i.lower() for i in add2_vgpu_list]
        esxi_version = self.judge_esxi_version()
        if len(add1_vgpu_list) > 1 and len(set(add1_vgpu_list)) != 1 and len(add1_vgpu_list[0].split('-')) == 2:
            if esxi_version >= '8.0.3':
                self.vgpu_heterogeneous_support(platform='esxi', operate='1')
            else:
                log.err('do not support vgpu_heterogeneous on vmware{}, exit.....'.format(esxi_version))
                exit()
        else:
            if esxi_version >= '8.0.3':
                self.vgpu_heterogeneous_support(platform='esxi', operate='1')
            else:
                log.err('do not support vgpu_heterogeneous on vmware{}, no need disable'.format(esxi_version))
        vgpu_list = self.judge_pgpu_in_vm(vm_name)
        for index, vgpu in enumerate(add1_vgpu_list):
            log.info('the ESXi version is {}'.format(esxi_version))
            if self.host_support_vgpu(vgpu) is True:
                if esxi_version == '7.0' and vgpu.split('-')[0].lower() != 'a40' and len(vgpu_list) != 1:
                    log.info('we only can support add 1 vGPU in esxi 7.0 platform')
                    exit(2)
                else:
                    vgpu_number = 0 + index
                    # host_vgpu_info = self.get_host_gpu_info()
                    vm_status = self.get_vm_status(vm_name)
                    if vm_status == 'power_on':
                        self.vm_power_off(vm_name)
                    if platform != 'kvm':
                        vm_path = self.get_vm_path(vm_name)
                        config_file = '{}/{}.vmx'.format(vm_path, vm_name)
                        if vgpu.split('-')[0].lower() in self.vgpu_list:
                            target_vgpu = 'nvidia_' + vgpu
                        else:
                            target_vgpu = 'grid_' + vgpu
                        # add vgpu to vm
                        pgpu_uuid = self.get_pgpu_uuid(vgpu)
                        vgpu_size = self.get_vgpu_memsize_vmware(vgpu)
                        pci_number = self.get_slot_number_vmware(vm_name)
                        virtual_dev = 'pciPassthru{}.virtualDev = "vmiop"'.format(vgpu_number)
                        add_vm_vgpu = 'pciPassthru{}.vgpu = "{}"'.format(vgpu_number, target_vgpu)
                        fb_size = 'pciPassthru{}.fbSizeMB = "{}"'.format(vgpu_number, vgpu_size)
                        vm_vgpu_ppid = 'pciPassthru{}.pgpu = "{}"'.format(vgpu_number, pgpu_uuid)
                        pci_present = 'pciPassthru{}.present = "TRUE"'.format(vgpu_number)
                        vgpu_pci_number = 'pciPassthru{}.pciSlotNumber = "{}"'.format(vgpu_number, pci_number)
                        if vgpu_number == '0':  # vm has no vgpu, add it whether is 1:1 or n:1 vgpu
                            option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number,
                                           vm_vgpu_ppid]
                        else:
                            if self.is_1_1_vgpu(
                                    vgpu) is True:  # if is 1:1 vGPU, it must add vgpu_ppid and vgpu_pci_number
                                log.info('we wil add 1:1 vgpu---{}'.format(vgpu))
                                option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number,
                                               vm_vgpu_ppid]
                            else:
                                log.info('we will add n:1 vgpu, the vgpu is --{}'.format(vgpu))
                                option_list = [virtual_dev, add_vm_vgpu, fb_size, pci_present, vgpu_pci_number,
                                               vm_vgpu_ppid]
                        for option in option_list:
                            cmd = "cd {}; sed -i '$a {}' {}.vmx".format(vm_path, option, vm_name)
                            self.excute_cmd_on_server(cmd)
                            time.sleep(1)
                        # add tools option
                        debug_param = 'pciPassthru{}.cfg.enable_debugging = "1"'.format(vgpu_number)
                        uvm_param = 'pciPassthru{}.cfg.enable_uvm = "1"'.format(vgpu_number)
                        profile_param = 'pciPassthru{}.cfg.enable_profiling = "1"'.format(vgpu_number)
                        enable_debug = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, debug_param,
                                                                                         config_file)
                        enable_uvm = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, uvm_param,
                                                                                       config_file)
                        enable_profiling = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_number, profile_param,
                                                                                             config_file)
                        # judge whether add uvm option
                        if self.is_1_1_vgpu(vgpu) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                            tool_option = [enable_uvm, enable_profiling, enable_debug]
                        else:
                            if len(vgpu.strip(' ').split('-')) != 3:
                                tool_option = [enable_profiling, enable_debug]
                            else:
                                tool_option = [enable_uvm, enable_profiling, enable_debug]
                        for option in tool_option:
                            self.excute_cmd_on_server(option)
                            time.sleep(1)
            else:
                log.info('the vgpu is not in host,please check......')
                exit(2)
        self.vm_power_on(vm_name)
    '''

    def del_all_vgpu_vm_vmware(self, vm_name):
        try:
            log.warning('For ESXi platform, delete all vgpu of {}'.format(vm_name))
            vm_status = self.get_vm_status(vm_name)
            if vm_status == 'power_on':
                self.vm_power_off(vm_name)
            vm_path = self.get_vm_path(vm_name)
            vgpu_list = self.judge_pgpu_in_vm(vm_name)
            if len(vgpu_list) == 1:
                log.info('there is no vgpu in vm------{}'.format(vm_name))
                return True
            else:
                del_cmd = "cd {}; sed -i '/pciPassthru/d' {}.vmx".format(vm_path, vm_name)
                self.excute_cmd_on_server(del_cmd)
                time.sleep(2)
                self.vm_power_on(vm_name)
                vgpu_list = self.judge_pgpu_in_vm(vm_name)
                if len(vgpu_list) == 1:
                    log.info('we delete all vgpu in vm--{} successful'.format(vm_name))
                    return True
                else:
                    log.info('we delete all vgpu in vm--{} fail'.format(vm_name))
                    return False
        except Exception as e:
            log.error(f"Delete all vgpu in vm error: {str(e)}")
            exit(1)

    # for ESXi platform
    def judge_pgpu_in_vm(self, vm_name):
        try:
            vm_path = self.get_vm_path(vm_name)
            # cmd = 'grep pgpu {}/{}.vmx'.format(vm_path, vm_name)
            cmd = 'grep pciPassthru {}/{}.vmx'.format(vm_path, vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            pgpu_list = cmd_out.split('\n')
            print(pgpu_list)
            return pgpu_list
        except Exception as e:
            log.error(f"Judge pgpu in vm error: {str(e)}")
            exit(1)

    def add_vgpu_params_vmware(self, vm_name, *args):
        try:
            vgpu_list = self.judge_pgpu_in_vm(vm_name)
            if len(vgpu_list) == 1:
                log.info('there is no vgpu in vm, no need add tools option')
            else:
                if args:
                    vm_path = self.get_vm_path(vm_name)
                    config_file = '{}/{}.vmx'.format(vm_path, vm_name)
                    vm_status = self.get_vm_status(vm_name)
                    if vm_status == 'power_on':
                        self.vm_power_off(vm_name)
                        time.sleep(12)
                        vm_status = self.get_vm_status(vm_name)
                        if vm_status == 'power_on':
                            log.info('can not add option to power on vm')
                    for i in range(0, len(vgpu_list) - 1):
                        for arg in args:
                            if arg == 'debug':
                                log.info('we will try to enable debugging on vm ---{}'.format(vm_name))
                                param = 'pciPassthru{}.cfg.enable_debugging = "1"'.format(i)
                                if self.verify_vgpu_params(param, config_file) is True:
                                    log.info('no need add debug, it has been enabled')
                                else:
                                    cmd_debug = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(i, param, config_file)
                                    self.excute_cmd_on_server(cmd_debug)
                                    time.sleep(3)
                                    if self.verify_vgpu_params(param, config_file) is True:
                                        log.info('add debugger successful')
                                    else:
                                        log.info('add debugger fail')
                                        exit(2)
                            elif arg == 'profile':
                                log.info('we will try to enable profile on vm ---{}'.format(vm_name))
                                param = 'pciPassthru{}.cfg.enable_profiling = "1"'.format(i)
                                if self.verify_vgpu_params(param, config_file) is True:
                                    log.info('no need add profile, it has been enabled')
                                else:
                                    cmd_profile = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(i, param, config_file)
                                    self.excute_cmd_on_server(cmd_profile)
                                    time.sleep(3)
                                    if self.verify_vgpu_params(param, config_file) is True:
                                        log.info('add profiling successful')
                                    else:
                                        log.info('add profiling fail')
                                        exit(2)
                            elif arg == 'uvm':
                                log.info('we will try to enable UVM on vm ---{}'.format(vm_name))
                                param = 'pciPassthru{}.cfg.enable_uvm = "1"'.format(i)
                                if self.verify_vgpu_params(param, config_file) is True:
                                    log.info('no need add uvm, it has been enabled')
                                else:
                                    cmd_uvm = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(i, param, config_file)
                                    self.excute_cmd_on_server(cmd_uvm)
                                    time.sleep(3)
                                    if self.verify_vgpu_params(param, config_file) is True:
                                        log.info('add uvm successful')
                                    else:
                                        log.info('add uvm fail')
                                        exit(2)
                            else:
                                continue
                    self.vm_power_on(vm_name)
                else:
                    log.info('please give option or no need add tools option to vm---{}'.format(vm_name))
        except Exception as e:
            log.error(f"Add vgpu params to vm error: {str(e)}")
            exit(1)
            
    # add tools option to specify vgpu(vgpu locate number, fox example: 0, 1, 2)
    def add_params_to_specify_vgpu_vmware(self, vm_name, vgpu_locate_number, *args):
        try:
            if args:
                vm_path = self.get_vm_path(vm_name)
                config_file = '{}/{}.vmx'.format(vm_path, vm_name)
                vm_status = self.get_vm_status(vm_name)
                if vm_status == 'power_on':
                    self.vm_power_off(vm_name)
                    time.sleep(10)
                    vm_status = self.get_vm_status(vm_name)
                    if vm_status == 'power_on':
                        log.info('can not add option to power on vm')
                        exit(1)
                for arg in args:
                    if arg == 'debug':
                        log.info(
                            'we will try to enable debugging on vm ---{} for vgpu{}'.format(vm_name, vgpu_locate_number))
                        param = 'pciPassthru{}.cfg.enable_debugging = "1"'.format(vgpu_locate_number)
                        if self.verify_vgpu_params(param, config_file) is True:
                            log.info('no need add debug, it has been enabled')
                        else:
                            cmd_debug = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_locate_number, param, config_file)
                            self.excute_cmd_on_server(cmd_debug)
                            time.sleep(3)
                            if self.verify_vgpu_params(param, config_file) is True:
                                log.info('add debugger successful')
                            else:
                                log.info('add debugger fail')
                                exit(2)
                    elif arg == 'profile':
                        log.info('we will try to enable profile on vm ---{}'.format(vm_name))
                        param = 'pciPassthru{}.cfg.enable_profiling = "1"'.format(vgpu_locate_number)
                        if self.verify_vgpu_params(param, config_file) is True:
                            log.info('no need add profile, it has been enabled')
                        else:
                            cmd_profile = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_locate_number, param, config_file)
                            self.excute_cmd_on_server(cmd_profile)
                            time.sleep(3)
                            if self.verify_vgpu_params(param, config_file) is True:
                                log.info('add profiling successful')
                            else:
                                log.info('add profiling fail')
                                exit(2)
                    elif arg == 'uvm':
                        log.info('we will try to enable UVM on vm ---{} for vgpu{}'.format(vm_name, vgpu_locate_number))
                        param = 'pciPassthru{}.cfg.enable_uvm = "1"'.format(vgpu_locate_number)
                        if self.verify_vgpu_params(param, config_file) is True:
                            log.info('no need add uvm, it has been enabled')
                        else:
                            cmd_uvm = "sed -i '/pciPassthru{}.present/a\{}' {} ".format(vgpu_locate_number, param, config_file)
                            self.excute_cmd_on_server(cmd_uvm)
                            time.sleep(3)
                            if self.verify_vgpu_params(param, config_file) is True:
                                log.info('add uvm successful')
                            else:
                                log.info('add uvm fail')
                                exit(2)
                    else:
                        continue
            else:
                log.info('no need add params to vm---{}'.format(vm_name))
        except Exception as e:
            log.error(f"Add vgpu params to vm error: {str(e)}")
            exit(1)

    def delete_vgpu_params_vmware_from_vm(self, vm_name, *args):
        try:
            log.warning('will delete the tools option of vm_name')
            vgpu_list = self.judge_pgpu_in_vm(vm_name)
            if len(vgpu_list) == 1:
                log.info('there is no vgpu in vm, no need delete tools option')
            else:
                if args:
                    vm_path = self.get_vm_path(vm_name)
                    config_file = '{}/{}.vmx'.format(vm_path, vm_name)
                    vm_status = self.get_vm_status(vm_name)
                    if vm_status == 'power_on':
                        self.vm_power_off(vm_name)
                    for arg in args:
                        if arg.lower() == 'debug':
                            log.info('we will try to delete debugging on vm ---{}'.format(vm_name))
                            param = 'cfg.enable_debugging'
                            if self.verify_vgpu_params(param, config_file) is False:
                                log.info('no need delete debug, it has not been enabled')
                            else:
                                cmd_debug = "sed -i '/{}/d' {} ".format(param, config_file)
                                self.excute_cmd_on_server(cmd_debug)
                                time.sleep(3)
                                if self.verify_vgpu_params(param, config_file) is False:
                                    log.info('delete debugger successful')
                                else:
                                    log.info('delete debugger fail')
                                    exit(2)
                        elif arg.lower() == 'profile':
                            log.info('we will try to delete profile on vm ---{}'.format(vm_name))
                            param = 'cfg.enable_profiling'
                            if self.verify_vgpu_params(param, config_file) is False:
                                log.info('no need delete profile, it has not been enabled')
                            else:
                                cmd_profile = "sed -i '/{}/d' {} ".format(param, config_file)
                                self.excute_cmd_on_server(cmd_profile)
                                time.sleep(3)
                                if self.verify_vgpu_params(param, config_file) is False:
                                    log.info('delete profiling successful')
                                else:
                                    log.info('delete profiling fail')
                                    exit(2)
                        elif arg.lower() == 'uvm':
                            log.info('we will try to delete UVM on vm ---{}'.format(vm_name))
                            param = 'cfg.enable_uvm'
                            if self.verify_vgpu_params(param, config_file) is False:
                                log.info('no need delete uvm, it has not been enabled')
                            else:
                                cmd_uvm = "sed -i '/{}/d' {} ".format(param, config_file)
                                self.excute_cmd_on_server(cmd_uvm)
                                time.sleep(3)
                                if self.verify_vgpu_params(param, config_file) is False:
                                    log.info('delete uvm successful')
                                else:
                                    log.info('delete uvm fail')
                                    exit(2)
                        else:
                            continue
                    self.vm_power_on(vm_name)
                else:
                    log.info('no need del params to vm---{}'.format(vm_name))
        except Exception as e:
            log.error(f"Delete vgpu params from vm error: {str(e)}")
            exit(1)

    def get_exist_vgpu_kvm(self):
        """
         get exist  vgpu list
        :return: dict such as :  'asdfs-dsaf-sdfsd-fsdfsdf-sdfsdf': T4-4c
        """
        try:
            log.info('get the exist vgpu on kvm host')
            if platform == 'kvm':
                gpu_list, vgpu_name_list = [], []
                cmd = 'ls /sys/bus/mdev/devices/'
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                for i in cmd_out.split('\n')[0:-1]:
                    gpu_list.append(i.strip(' '))
                for j in gpu_list:
                    cmd1 = 'cat /sys/bus/mdev/devices/{}/mdev_type/name'.format(j)
                    cmd1_out, cmd_err = self.excute_cmd_on_server(cmd1)
                    vgpu_name_list.append(cmd1_out.split(' ')[-1].strip('\n'))
                if len(vgpu_name_list) != 0:
                    return dict(zip(gpu_list, vgpu_name_list))
                else:
                    return False
        except Exception as e:
            log.error(f"Get exist vgpu on kvm host error: {str(e)}")
            exit(1)

    def get_available_vgpu_uuid_kvm(self, vgpu):
        """
        :param vgpu:
        :return: vgpu_uuid or False
        """
        try:
            log.debug('get the available vgpu on kvm host')
            vgpu_name, used_gpu_info = self.get_used_vgpu_info()
            exist_gpu = self.get_exist_vgpu_kvm()
            available = 0
            uuid_list = []
            log.info('the used vgpu info ---{}'.format(used_gpu_info))
            used_gpu_list = []
            if used_gpu_info is not False:
                for key, value in used_gpu_info.items():
                    used_gpu_list.append(value['vgpu_uuid'])
            if exist_gpu is not False:
                for uuid, vgpu1 in exist_gpu.items():
                    if vgpu.upper() == vgpu1:
                        if used_gpu_info is not False and vgpu1 in vgpu_name:
                            for vgpu2, value in used_gpu_info.items():
                                if vgpu1 == value['vgpu']:
                                    if uuid not in used_gpu_list and len(used_gpu_list) != 0:
                                        available += 1
                                        uuid_list.append(uuid)
                                        log.warning('the uuid---{} exist and was not used'.format(uuid))
                                    else:
                                        continue
                        else:
                            available += 1
                            print('add uuid---{} to uuidlist'.format(uuid))
                            uuid_list.append(uuid)
                    else:
                        continue
                if available == 0:
                    log.info('there is no available uuid for {}'.format(vgpu))
                    return False
                else:
                    return uuid_list
            else:
                log.info('there is no available uuid now, please create')
                return False
        except Exception as e:
            log.error(f"Get available vgpu uuid on kvm host error: {str(e)}")
            exit(1)

    # get vgpu map id , such as H100-20c is 830. we can get 830
    def get_vgputype_id(self, vgpu):
        try:
            if vgpu.split('-')[0].lower() != 't4' and len(vgpu) > 5:
                cmd = "cat /usr/share/nvidia/vgpu/vgpuConfig.xml | grep {}".format(vgpu.upper())
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                for gpu_info in cmd_out.split('\n')[0:-1]:
                    # if vgpu.upper() in gpu_info.split(' ')[5].strip('"'):
                    if vgpu.upper() in gpu_info.split('"')[3]:
                        try:
                            vgputype_id = gpu_info.split('"')[1].strip('b').strip('\n')
                        except IndexError:
                            log.error('get vgpu type id fail, please check, exit......')
                            exit()
                        break
                    else:
                        continue
                log.warning('get the vgpu type id, {} is mapping to {}'.format(vgpu, vgputype_id))
                return vgputype_id
            elif 5 >= len(vgpu) > 2:
                if vgpu.split('-')[0].lower() == 't4' or vgpu.split('-')[0].lower() == 'l4' or vgpu.split('-')[0].lower() == 'dc':
                    cmd = "cat /usr/share/nvidia/vgpu/vgpuConfig.xml | grep {}".format(vgpu.split('-')[0].upper())
                    cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                    for gpu_info in cmd_out.split('\n')[0:-1]:
                        if vgpu.split('-')[0].lower() == 'dc':
                            if vgpu.upper() in gpu_info:
                                try:
                                    vgputype_id = gpu_info.split('"')[1].strip('b').strip('\n')
                                except IndexError:
                                    log.error('get vgpu type id fail, please check, exit.....')
                                    exit()
                                break
                            else:
                                continue
                        else:
                            if vgpu.upper() == gpu_info.split(' ')[5].strip('"'):
                                try:
                                    vgputype_id = gpu_info.split('"')[1].strip('b').strip('\n')
                                except IndexError:
                                    log.error('get vgpu type id fail, please check, exit.....')
                                    exit()
                                break
                            else:
                                continue
                    log.warning('get the vgpu type id, {} is mapping to {}'.format(vgpu, vgputype_id))
                    return vgputype_id
            else:
                log.error('please check the vgpu--{}, it should be invalid vgpu, exit....'.format(vgpu))
                exit()
        except Exception as e:
            log.error(f"Get vgpu type id error: {str(e)}")
            exit(1)

    def get_bus_list(self, pgpu_bus=None):
        try:
            cmd = 'ls /sys/class/mdev_bus'
            bus_list = []
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for i in cmd_out.split('\n')[0:-1]:
                bus_list.append(i.strip(' '))
            if pgpu_bus is not None:
                bus_list = [i for i in bus_list if i.split(':')[1] == pgpu_bus.lower().split(':')[1]]
            bus_list = random.sample(bus_list, len(bus_list))
            if len(bus_list) != 0:
                return bus_list
            else:
                log.error('can not obtain bus list, please check, exit.........')
                exit()
        except Exception as e:
            log.error(f"Get bus list error: {str(e)}")
            exit(1)

    '''
    def get_bus_list_cg(self):
        get_bus_cmd = "nvidia-smi |grep 00000|awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'"
        cmd_out, cmd_err = self.excute_cmd_on_server(get_bus_cmd)
        bus_id = cmd_out.split('\n')[0]
        bus_id_number = bus_id.strip('')
        bus_id_list1, bus_id_list2 = [], []
        bus_id_number_list = bus_id_number.split(':')
        for i in ['2', '3', '4', '5', '6', '7']:
            bus_id_list1.append(bus_id_number_list[0][-4:] + ':' + bus_id_number_list[1] + ':' + bus_id_number_list[2].split('.')[0] + '.' + i)
            bus_id_list2.append(bus_id_number_list[0][-4:] + '\:' + bus_id_number_list[1] + '\:' + bus_id_number_list[2].split('.')[0] + '.' + i)
        # uuid_list = ['88dbb03d-d40e-4dea-834a-7c42ee80f100', '88dbb03d-d40e-4dea-834a-7c42ee80f101', '88dbb03d-d40e-4dea-834a-7c42ee80f102', '88dbb03d-d40e-4dea-834a-7c42ee80f103', '88dbb03d-d40e-4dea-834a-7c42ee80f104', '88dbb03d-d40e-4dea-834a-7c42ee80f105']
        vgpu_list = ['gh200-96c', 'gh200-7-96c', 'gh200-4-48c', 'gh200-3-48c', 'gh200-2-24c', 'gh200-1-24c', 'gh200-1-12c']
        vgpu_bus_dict = dict(zip(vgpu_list, bus_id_list2))
        log.info('vgpu and bus_id mapping as follows')
        return vgpu_bus_dict

    def create_vgpu_for_cg(self, vgpu):
        log.warning('we usually use this script only create one grace-hopper vGPU')
        vgpu_bus_dict = self.get_bus_list_cg()
        if len(vgpu.split('-')) == 3:
            if self.judge_mig_enable(vgpu) is True:
                pass
            else:
                self.enable_mig(vgpu, action=1)
            self.create_ci_for_vgpu_kvm(vgpu)
        else:
            if self.judge_mig_enable(vgpu) is True:
                cmd = "nvidia-smi mig -dci && nvidia-smi mig -dgi"
                self.excute_cmd_on_server(cmd)
                self.enable_mig(vgpu, action=0)
        if vgpu.lower() in cg_vgpu_dict.keys():
            uuid = cg_vgpu_dict[vgpu.lower()]
        self.enable_vgpu_support_kvm()
        bus_id = vgpu_bus_dict[vgpu.lower()]
        vgpu_type_id = self.get_vgputype_id(vgpu)
        cmd = "echo {} > /sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(vgpu_type_id, bus_id)
        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
        config_cmd = "cat /sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(bus_id)
        cmd_out1, cmd_err1 = self.excute_cmd_on_server(config_cmd)
        if re.search(vgpu_type_id, cmd_out1, re.IGNORECASE) is not None:
            log.info('create {} successful'.format(vgpu))
        else:
            log.info('create {} fail, exit.....'.format(vgpu))
            exit()
        cmd1 = 'echo "enable_debugging=1,enable_profiling=1,enable_uvm=1" > /sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(bus_id)
        cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd1)
        log.info('add tools support for cg, uvm/debug/profile')
        log.warning('we should launch the vm in host via following commnad')
        print("qemu-system-aarch64 -object iommufd,id=iommufd0 -machine hmat=on -machine virt,accel=kvm,gic-version=3,iommu=nested-smmuv3,iommufd=iommufd0,ras=on -cpu host -smp cpus=8 -m size=8G,slots=2,maxmem=66G -monitor pty -display none  -k en-us -object memory-backend-ram,size=4G,id=m0 -object memory-backend-ram,size=4G,id=m1 -numa node,memdev=m0,cpus=0-3,nodeid=0 -numa node,memdev=m1,cpus=4-7,nodeid=1 -device vfio-pci,host=0009:01:00.2,rombar=0,id=dev0,iommufd=iommufd0  -uuid {} -object acpi-generic-initiator,id=gi0,pci-dev=dev0,host-nodes=2-9 -bios /usr/share/AAVMF/AAVMF_CODE.fd -device nvme,drive=nvme0,serial=deadbeaf1,bus=pcie.0 -drive file=/home/<USER>/Images/myvm_disk.qcow2,index=0,media=disk,format=qcow2,if=none,id=nvme0 -cdrom /home/<USER>/ISOs/BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso -device e1000,romfile=/home/<USER>/Images/efi-e1000.rom,netdev=net0 -netdev user,id=net0,hostfwd=tcp::5558-:22,hostfwd=tcp::5900-:5900 &".format(uuid))
        print(
        """
        a. qemu-img create -f qcow2 myvm_disk.qcow2 350G \n
        b. download tot ISO file from https://urm.nvidia.com/artifactory/sw-dgx-platform-generic-local/ghvirt-iso/ \n
        c. download rom file(efi-e1000.rom) from  https://github.com/qemu/qemu/blob/master/pc-bios/efi-e1000.rom \n
        d. select you used base os version, such as BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso \n
        e. On host machine, use — 'ssh -p 5558 user@127.0.0.1' to login guest
        """
        )

    def show_vgpu_info_cg(self):
        vgpu_bus_dict = self.get_bus_list_cg()
        for key, value in vgpu_bus_dict.items():
            vgpu_id = self.get_vgputype_id(key)
            config_cmd = "cat /sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(value)
            cmd_out, cmd_err = self.excute_cmd_on_server(config_cmd)
            if re.search(vgpu_id, cmd_out, re.IGNORECASE) is not None:
                print('++++++++++++++++++++++++++++++++++++')
                print('+++++++++had created vgpu {}++++++++++++'.format(key))
                print('++++++++++++++++++++++++++++++++++++')
    '''
    # get according vgpu to get bus create vgpu, such as ,vgpu is H100-20c, to get "0000\:41\:03.3" to create
    def get_available_bus_id(self, bus_list, vgpu):
        try:
            vgpu_type_id = self.get_vgputype_id(vgpu)
            for bus in bus_list:
                # print(bus)
                cmd1 = 'ls /sys/class/mdev_bus/{}/mdev_supported_types'.format(bus)
                cmd_out2, cmd_err2 = self.excute_cmd_on_server(cmd1)
                if vgpu_type_id in cmd_out2:
                    log.info('we can use ---{} to create {}'.format(bus, vgpu))
                    break
                else:
                    continue
            available_bus_list = bus.split(':')
            available_bus = available_bus_list[0] + '\:' + available_bus_list[1] + '\:' + available_bus_list[2]
            log.warning('get the available bus for create vgpu--{}'.format(vgpu))
            return bus, available_bus
        except Exception as e:
            log.error(f"Get available bus id error: {str(e)}")
            exit(1)

    def get_mig_device_id(self, vgpu):
        try:
            gpu_info = []
            cmd1 = "nvidia-smi |grep {}".format(vgpu.split('-')[0].upper())
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd1)
            for i in cmd_out1.split('\n'):
                if i != '':
                    gpu_info.append(i.split('|')[1] + '+' + i.split(' ')[7])
                    continue
            mig_list = []
            cmd2 = "nvidia-smi |grep -A 2 {}".format(vgpu.split('-')[0].upper())
            cmd_out2, cmd_err2 = self.excute_cmd_on_server(cmd2)
            for j in cmd_out2.split('\n'):
                if 'Enabled' in j or 'Disabled' in j:
                    mig_list.append(j.split('|')[-2].strip(' '))
                    continue
        except Exception as e:
            log.error(f"Get mig device id error: {str(e)}")
            exit(1)

    def create_ci_for_vgpu_kvm(self, vgpu, pgpu=None):
        try:
            if pgpu == server_args.bus_number:
                if self.judge_mig_enable(vgpu, pgpu=server_args.bus_number) is False:
                    self.enable_mig(vgpu, action=1, gpu_bus=server_args.bus_number)
            else:
                self.release_all_vgpu_on_kvm()
                cmd1 = 'nvidia-smi -mig 1'
                log.info('=============================================')
                log.info('For create mig vgpu, need enable mig support')
                log.info('=============================================')
                self.excute_cmd_on_server(cmd1)
            cmd = mig_cmd[vgpu.lower()]
            if pgpu:
                pgpu_info = self.get_gpu_bus_id()
                for i in pgpu_info:
                    if pgpu.upper() in i:
                        pgpu_index = i.split('+')[0]
                        break
                cmd = mig_cmd[vgpu.lower()] + ' -i {}'.format(pgpu_index)
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
            if 'Successfully created compute instance' not in cmd_out1:
                log.error('use cmd--{} to create CI fail, please check, exit........,if gpu is RTX Pro 6000, can ingore this message'.format(cmd))
            else:
                log.info('use cmd--{} to create CI successful'.format(cmd))
                return True
        except Exception as e:
            log.error(f"Create CI for vgpu kvm error: {str(e)}")
            exit(1)

    def create_vgpu_kvm_bak(self, bus_list, vgpu):
        try:
            log.info('create vgpu----{} on kvm host'.format(vgpu))
            if len(vgpu.split('-')) == 2:
                if self.judge_mig_enable(vgpu, pgpu=server_args.bus_number) is True:
                    self.release_all_vgpu_on_kvm()
                    self.enable_mig(vgpu, action=0, gpu_bus=server_args.bus_number)
            if len(vgpu.split('-')) == 3:
                self.create_ci_for_vgpu_kvm(vgpu)
            vgpu_uuid_cmd = 'uuidgen'
            uuid_out, uuid_err1 = self.excute_cmd_on_server(vgpu_uuid_cmd)
            vgpu_uuid = uuid_out.split('\n')[0].strip(' ')
            bus, available_bus = self.get_available_bus_id(bus_list, vgpu)
            if vgpu.split('-')[0].lower() not in ['t4', 'v100']:
                bus_list.remove(bus)
            vgputype_id = self.get_vgputype_id(vgpu)
            cmd_vgpu = 'echo {} >/sys/bus/pci/devices/{}/mdev_supported_types/nvidia-{}/create'.format(vgpu_uuid, available_bus, vgputype_id)
            log.info('create vgpu by --- {}'.format(cmd_vgpu))
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
            if cmd_err.split('\n')[0] != '':
                log.info('generate vgpu failed')
                return False
            else:
                log.warning('generate vgpu successful')
                if (self.is_1_1_vgpu(vgpu.lower()) and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']) or len(
                        vgpu.split('-')) == 3:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                else:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                print(cmd)
                self.excute_cmd_on_server(cmd)
                return vgpu_uuid
        except Exception as e:
            log.error(f"Create vgpu kvm error: {str(e)}")
            exit(1)

    # create single vgpu on kvm
    def create_vgpu_kvm_with_specify_bus(self, vgpu, bus_id):
        try:
            log.warning('if you use sepcify pgpu to create vGPU, please make sure you have enough resource to create')
            bus_list = self.get_bus_list(pgpu_bus=bus_id)
            log.info('create vgpu----{} on kvm host'.format(vgpu))
            if len(vgpu.split('-')) == 2:
                if self.judge_mig_enable(vgpu, pgpu=server_args.bus_number) is True:
                    self.release_all_vgpu_on_kvm()
                    self.enable_mig(vgpu, action=0, gpu_bus=server_args.bus_number)
            if len(vgpu.split('-')) == 3:
                self.create_ci_for_vgpu_kvm(vgpu)
            vgpu_uuid_cmd = 'uuidgen'
            uuid_out, uuid_err1 = self.excute_cmd_on_server(vgpu_uuid_cmd)
            vgpu_uuid = uuid_out.split('\n')[0].strip(' ')
            bus, available_bus = self.get_available_bus_id(bus_list, vgpu)
            if vgpu.split('-')[0].lower() not in ['t4', 'v100']:
                bus_list.remove(bus)
            vgputype_id = self.get_vgputype_id(vgpu)
            cmd_vgpu = 'echo {} >/sys/bus/pci/devices/{}/mdev_supported_types/nvidia-{}/create'.format(vgpu_uuid, available_bus, vgputype_id)
            log.info('create vgpu by --- {}'.format(cmd_vgpu))
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
            if cmd_err.split('\n')[0] != '':
                log.info('generate vgpu failed')
                vgpu_uuid = 'fail'
                return bus, vgpu_uuid
            else:
                log.warning('generate vgpu successful')
                if (self.is_1_1_vgpu(vgpu.lower()) and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']) or len(
                        vgpu.split('-')) == 3:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                else:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                print(cmd)
                self.excute_cmd_on_server(cmd)
                return bus, vgpu_uuid
        except Exception as e:
            log.error(f"Create vgpu kvm with specify bus error: {str(e)}")
            exit(1)

    # create single vgpu on kvm
    def create_vgpu_kvm(self, bus_list, vgpu):
        try:
            log.info('create vgpu----{} on kvm host'.format(vgpu))
            if len(vgpu.split('-')) == 2 and vgpu.split('-')[0].upper() in ['A100', 'A100D', 'A30', 'H100', 'GH200', 'H20', 'B100', 'DC', 'B200', 'GB200']:
                if self.judge_mig_enable(vgpu, pgpu=server_args.bus_number) is True:
                    # self.release_all_vgpu_on_kvm()
                    self.enable_mig(vgpu, action=0, gpu_bus=server_args.bus_number)
            if len(vgpu.split('-')) == 3:
                self.create_ci_for_vgpu_kvm(vgpu)
            vgpu_uuid_cmd = 'uuidgen'
            uuid_out, uuid_err1 = self.excute_cmd_on_server(vgpu_uuid_cmd)
            vgpu_uuid = uuid_out.split('\n')[0].strip(' ')
            bus, available_bus = self.get_available_bus_id(bus_list, vgpu)
            if vgpu.split('-')[0].lower() not in ['t4', 'v100']:
                bus_list.remove(bus)
            vgputype_id = self.get_vgputype_id(vgpu)
            cmd_vgpu = 'echo {} >/sys/bus/pci/devices/{}/mdev_supported_types/nvidia-{}/create'.format(vgpu_uuid, available_bus, vgputype_id)
            log.info('create vgpu by --- {}'.format(cmd_vgpu))
            cmd_out1, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
            if cmd_err.split('\n')[0] != '':
                log.info('generate vgpu failed')
                vgpu_uuid = 'fail'
                return bus, vgpu_uuid
            else:
                log.warning('generate vgpu successful')
                if (self.is_1_1_vgpu(vgpu.lower()) and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']) or len(
                        vgpu.split('-')) == 3:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                else:
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(vgpu_uuid)
                print(cmd)
                self.excute_cmd_on_server(cmd)
                cmd = 'touch vgpu_info.txt'
                os.popen(cmd)
                with open('vgpu_info.txt', 'a+') as f:
                    f.write("On host {}, {}-- create {} + {} on {} under redhat kvm platform".format(server_args.server, time.strftime("%Y%m%d--%H:%M", time.localtime()), vgpu, vgpu_uuid, bus))
                    f.write("\n")
                self.excute_cmd_on_server(cmd)
                cmd1 = "echo 'On host {}, {}-- use {} + {} create {} on redhat kvm' >> vgpu_info.txt".format(server_args.server, time.strftime("%Y%m%d--%H:%M", time.localtime()), available_bus, vgpu_uuid, vgpu)
                self.excute_cmd_on_server(cmd1)
                return bus, vgpu_uuid
        except Exception as e:
            log.error(f"Create vgpu kvm error: {str(e)}")
            exit(1)

    # generate multi vgpu for kvm
    def generate_vgpu_kvm(self, vgpu_list1, bus_list):
        """
        # generate uuid, will assign it to vgpu, only use it on kvm platform, if it exist and not be used , return it
        :param vgpu:
        :return: vgpu_uuid or False
        """
        try:
            if self.enable_vgpu_support_kvm() is True:
                uuid_vgpu_list = []
                vgpu_list2 = vgpu_list1.strip(',').split(',')
                vgpu_list = [i.lower().strip(' ') for i in vgpu_list2]
                exist_vgpu = self.get_exist_vgpu_kvm()
                if len(vgpu_list) > 1 and len(set(vgpu_list)) != 1 and len(vgpu_list[0].split('-')) == 2:
                    self.vgpu_heterogeneous_support(platform='kvm', operate='1')
                '''
                else:
                    if exist_vgpu:
                        for key, value in exist_vgpu.items():
                            if vgpu_list[0].upper() != value and vgpu_list[0].split('-')[0].upper() == value.split('-')[0]:
                                self.vgpu_heterogeneous_support(platform='kvm', operate='1')
                            else:
                                self.vgpu_heterogeneous_support(platform='kvm', operate='0')
                    else:
                        self.vgpu_heterogeneous_support(platform='kvm', operate='0')
                '''
                # bus_list = self.get_bus_list()
                all_available_uuid = self.get_exist_vgpu_kvm()
                for vgpu in vgpu_list:
                    log.warning('create vgpu or use exist vgpu, checking {}..........'.format(vgpu))
                    available_uuid_list = self.get_available_vgpu_uuid_kvm(vgpu)
                    if isinstance(available_uuid_list, list):  # host has exist vgpu list
                        if len(available_uuid_list) == len(vgpu_list) and len(set(vgpu_list)) == 1:  # vgpu had created and not be used
                            print('now we can use {}'.format(available_uuid_list))
                            return available_uuid_list
                        # if isinstance(all_available_uuid, dict) and len(all_available_uuid) != 0:  # exist many vgpu
                        if isinstance(all_available_uuid, dict) and len(available_uuid_list) != 0:  # exist many vgpu
                            log.debug('check --if vgpu.upper() in list(all_available_uuid.values())  is {}'.format(
                                vgpu.upper() in list(all_available_uuid.values())))
                            if vgpu.upper() in list(all_available_uuid.values()):
                                for i in available_uuid_list:
                                    if isinstance(all_available_uuid, dict):
                                        if i in list(
                                                all_available_uuid.keys()) and i not in uuid_vgpu_list:  # if vgpu is in exist vgpu list and not be used, return it
                                            vgpu_uuid = i
                                            print('use the exist vgpu----{}'.format(i))
                                            all_available_uuid.pop(i)
                                            log.debug('use exist and unused vgpu, no need re-create')
                                            uuid_vgpu_list.append(vgpu_uuid)
                                            break
                            else:
                                if len(set(vgpu_list)) == 1 and self.is_1_1_vgpu(vgpu_list[0].lower()):
                                    log.warning('hahahahahahaha-11--create new vgpu')
                                    for i in range(0, len(vgpu_list)):
                                        used_bus, vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                                        if vgpu_uuid != 'fail':
                                            uuid_vgpu_list.append(vgpu_uuid)
                                            break
                                        else:
                                            log.warning('use the exist bus of vgpu to creat 1:1 vpu, so it failed, will use another bus to try again')
                                            log.warning('hahahahahahaha-12--create new vgpu again')
                                            available_bus_list = used_bus.split(":")
                                            if len(vgpu_list[0].split('-')) == 2:
                                                remove_bus = available_bus_list[0] + ':' + available_bus_list[1]
                                            else:
                                                remove_bus = available_bus_list[0] + ':' + available_bus_list[1] + ':' + available_bus_list[2]
                                            # remove_bus = available_bus_list[0] + ':' + available_bus_list[1]
                                            bus_list = [bus for bus in bus_list if remove_bus not in bus]
                                            used_bus1, vgpu_uuid1 = self.create_vgpu_kvm(bus_list, vgpu)
                                            uuid_vgpu_list.append(vgpu_uuid1)
                                            break
                                else:
                                    log.warning('hahahahahahaha-13--create new vgpu')
                                    used_bus, vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                                    uuid_vgpu_list.append(vgpu_uuid)
                        else:
                            log.warning('hahahahahahaha-14--create new vgpu')
                            used_bus, vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                            uuid_vgpu_list.append(vgpu_uuid)
                    else:  # if vgpu dose not exist, create a new vgpu
                        log.info('hahahahahahaha-12 create new vgpu')
                        used_bus, vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                        uuid_vgpu_list.append(vgpu_uuid)
                    if len(set(vgpu_list2)) == 1 and self.is_1_1_vgpu(vgpu_list2[0].lower()) is True:
                        available_bus_list = used_bus.split(":")
                        if len(vgpu_list2[0].split('-')) == 2:
                            remove_bus = available_bus_list[0] + ':' + available_bus_list[1]
                        else:
                            remove_bus = available_bus_list[0] + ':' + available_bus_list[1] + ':' + available_bus_list[2]
                        bus_list = [bus for bus in bus_list if remove_bus not in bus]
                print('now we can use {}'.format(uuid_vgpu_list))
                return uuid_vgpu_list
            else:
                log.info('please enable the vgpu support on kvm platform')
                exit(1)
        except Exception as e:
            log.error(f"Generate vgpu kvm error: {str(e)}")
            exit(1)

    def get_exist_avaiable_vgpu(self):
        """
        :param vgpu:
        :return: vgpu_uuid or False
        """
        try:
            vgpu_name, used_gpu_info = self.get_used_vgpu_info()
            exist_gpu = self.get_exist_vgpu_kvm()
            available = 0
            available_uuid_dict = {}
            if exist_gpu is not False:
                for uuid, vgpu1 in exist_gpu.items():
                    if used_gpu_info is not False:
                        for vgpu2, value in used_gpu_info.items():
                            if vgpu1 != vgpu2:
                                available += 1
                                available_uuid_dict['uuid'] = vgpu1
                            else:
                                continue
                    else:
                        available += 1
                        print('add uuid---{} to uuidlist'.format(uuid))
                        available_uuid_dict['uuid'] = vgpu1
                if available == 0:
                    log.warning('there is no available uuid now, please create')
                    return False
                else:
                    return available_uuid_dict
            else:
                log.warning('there is no available uuid now, please create')
                return False
        except Exception as e:
            log.error(f"Get exist avaiable vgpu error: {str(e)}")
            exit(1)

    # generate vgpu for kvm
    def generate_vgpu_kvm_bak(self, vgpu_list1, bus_list):
        """
        # generate uuid, will assign it to vgpu, only use it on kvm platform, if it exist and not be used , return it
        :param vgpu:
        :return: vgpu_uuid or False
        """
        try:
            if self.enable_vgpu_support_kvm() is True:
                uuid_vgpu_list = []
                vgpu_list2 = vgpu_list1.strip(',').split(',')
                vgpu_list = [i.strip(' ') for i in vgpu_list2]
                # bus_list = self.get_bus_list()
                all_available_uuid = self.get_exist_vgpu_kvm()
                for vgpu in vgpu_list:
                    log.warning('create vgpu or use exist vgpu, checking {}..........'.format(vgpu))
                    available_uuid_list = self.get_available_vgpu_uuid_kvm(vgpu)
                    log.info('now the available uuid is {}'.format(available_uuid_list))
                    print('for create vGPU, the all exist vgpu infor is : {}'.format(all_available_uuid))
                    if isinstance(available_uuid_list, list):  # host has exist vgpu list
                        if len(available_uuid_list) == len(vgpu_list) and len(set(vgpu_list)) == 1:  # vgpu had created and not be used
                            print('now we can use {}'.format(available_uuid_list))
                            return available_uuid_list
                        if isinstance(all_available_uuid, dict) and len(all_available_uuid) != 0:  # exist many vgpu
                            log.debug('check --if vgpu.upper() in list(all_available_uuid.values())  is {}'.format(
                                vgpu.upper() in list(all_available_uuid.values())))
                            if vgpu.upper() in list(all_available_uuid.values()):
                                for i in available_uuid_list:
                                    if isinstance(all_available_uuid, dict):
                                        if i in list(
                                                all_available_uuid.keys()) and i not in uuid_vgpu_list:  # if vgpu is in exist vgpu list and not be used, return it
                                            vgpu_uuid = i
                                            print('use the exist vgpu----{}'.format(i))
                                            all_available_uuid.pop(i)
                                            log.debug('use exist and unused vgpu, no need re-create')
                                            uuid_vgpu_list.append(vgpu_uuid)
                                            break
                            else:
                                log.warning('hahahahahahaha-1--create new vgpu')
                                vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                                uuid_vgpu_list.append(vgpu_uuid)
                        else:
                            log.warning('hahahahahahaha-2--create new vgpu')
                            vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                            uuid_vgpu_list.append(vgpu_uuid)
                    else:  # if vgpu dose not exist, create a new vgpu
                        vgpu_uuid = self.create_vgpu_kvm(bus_list, vgpu)
                        uuid_vgpu_list.append(vgpu_uuid)
                print('now we can use {}'.format(uuid_vgpu_list))
                return uuid_vgpu_list
            else:
                log.info('please enable the vgpu support on kvm platform')
                exit(1)
        except Exception as e:
            log.error(f"Generate vgpu kvm error: {str(e)}")
            exit(1)

    def delete_param_kvm_vm(self, vgpu, param):
        """
         delete params of not used vgpu (exist)
        :param vgpu:
        :param param:
        :return: True or False
        """
        try:
            log.info('delete the tools option in {}'.format(vgpu))
            kvm_type = self.check_kvm_type()
            if kvm_type == 'ubuntu':
                uuid_list = []
                used, un_used_info = self.get_used_vgpu_ubuntu_kvm()
                if un_used_info is not None:
                    for key, value in un_used_info.items():
                        if vgpu.upper() in value:
                            uuid_list.append(key)
                if uuid_list:
                    i = 0
                    for uuid in uuid_list:
                        if param:
                            if param == 'all':
                                cmd = 'echo "1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = '1'
                            elif param == 'debug_profile':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                                    pattern = "enable_uvm=0x1"
                                else:
                                    cmd = 'echo "1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                                    pattern = '1'
                            elif param == 'debug_uvm':
                                cmd = 'echo "enable_profiling=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = 'enable_profiling=0x1'
                            elif param == 'profile_uvm':
                                cmd = 'echo "enable_debugging=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = 'enable_debugging=0x1'
                            elif param == 'debug':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1,enable_profiling=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_uvm=0x1,enable_profiling=0x1'
                                else:
                                    cmd = 'echo "enable_profiling=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_profiling=0x1'
                            elif param == 'profile':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1,enable_debugging=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_uvm=0x1,enable_debugging=0x1'
                                else:
                                    cmd = 'echo "enable_debugging=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_debugging=0x1'
                            elif param == 'uvm':
                                cmd = 'echo "enable_debugging=0x1,enable_profiling=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(
                                    uuid)
                                pattern = 'enable_debugging=0x1,enable_profiling=0x1'
                            else:
                                log.info('please give correct param to delete')
                            log.warning('remain the tools option by {}'.format(cmd))
                            self.excute_cmd_on_server(cmd)
                            config_file = '/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(uuid)
                            if self.verify_vgpu_params(pattern, config_file):
                                log.info('delete params successful')
                                i += 1
                            else:
                                log.info('delete params fail')
                    if i == len(uuid_list):
                        log.info('delete all vgpu params successful')
                        return True
                    else:
                        log.warning('some params of vgup delete fail, please check')
                        return False
                else:
                    log.error('there is no vgpu to delete params or vgpu is used by vm, please power off vm, then re-run')
            else:
                if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
                    uuid_list = self.get_available_vgpu_uuid_kvm(vgpu)
                    count = 0
                    for uuid in uuid_list:
                        config_file = '/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                        if param:
                            if param == 'all':
                                cmd = 'echo "1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = '1'
                            elif param == 'debug_profile':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                                    pattern = "enable_uvm=0x1"
                                else:
                                    cmd = 'echo "1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                                    pattern = '1'
                            elif param == 'debug_uvm':
                                cmd = 'echo "enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = 'enable_profiling=0x1'
                            elif param == 'profile_uvm':
                                cmd = 'echo "enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(uuid)
                                pattern = 'enable_debugging=0x1'
                            elif param == 'debug':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1,enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_uvm=0x1,enable_profiling=0x1'
                                else:
                                    cmd = 'echo "enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_profiling=0x1'
                            elif param == 'profile':
                                if self.is_1_1_vgpu(vgpu.lower()) is True and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']:
                                    cmd = 'echo "enable_uvm=0x1,enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_uvm=0x1,enable_debugging=0x1'
                                else:
                                    cmd = 'echo "enable_debugging=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                                        uuid)
                                    pattern = 'enable_debugging=0x1'
                            elif param == 'uvm':
                                cmd = 'echo "enable_debugging=0x1,enable_profiling=0x1" >/sys/bus/mdev/devices/{}/nvidia/vgpu_params'.format(
                                    uuid)
                                pattern = 'enable_debugging=0x1,enable_profiling=0x1'
                            else:
                                log.info('please give correct param to delete')
                            log.warning('remain the tools option by {}'.format(cmd))
                            self.excute_cmd_on_server(cmd)
                            if self.verify_vgpu_params(pattern, config_file):
                                count += 1
                    if count == len(uuid_list):
                        log.info('we delete the param---{} successful'.format(param))
                        return True
                    else:
                        log.info('we delete the param---{} fail'.format(param))
                        return False
                else:
                    log.info('there is no {} exit in the kvm platform, can not delete the vgpu params')
                    exit()
        except Exception as e:
            log.error(f"Delete param kvm error: {str(e)}")
            exit(1)

    def get_available_bus_number_kvm(self, vm_name):
        try:
            cmd = "grep 'bus=' /etc/libvirt/qemu/{}.xml".format(vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            exist_bus_number = []
            for i in cmd_out.split('\n'):
                if "bus='0x" in i:
                    exist_bus_number.append(i.split("bus='0x")[1].split("'")[0])
                else:
                    continue
            log.info('the exist number is {}'.format(exist_bus_number))
            exist_number = [int(i) for i in exist_bus_number]
            log.info('now the vm has the bus number list is {}'.format(exist_number))
            available_number = max(exist_number) + 1
            log.info('now we can use the {} for add vGPU'.format(available_number))
            return available_number
        except Exception as e:
            log.error(f"Get available bus number kvm error: {str(e)}")
            exit(1)

    def add_vgpu_kvm_vm(self, vgpu_list1, vm_name, bus_list):
        """
        add param to new vgpu or existed vgpu, then assign vgpu to vm
        :param vgpu:
        :param vm_name:
        :param param:
        :return: True or False
        """
        try:
            if self.get_vm_status(vm_name) == 'power_on':
                self.vm_power_off(vm_name)
            # delete vgpu information in vm.xml file
            cmd = "sed -i '/hostdev/,/hostdev>/d' /etc/libvirt/qemu/{}.xml; virsh define /etc/libvirt/qemu/{}.xml".format(
                vm_name, vm_name)
            self.excute_cmd_on_server(cmd)
            # create vgpu
            uuid_list = self.generate_vgpu_kvm(vgpu_list1, bus_list)
            available_bus_number = self.get_available_bus_number_kvm(vm_name)
            vgpu_list = vgpu_list1.split(',')
            if isinstance(vgpu_list, list):
                # add multi or single vgpu to vm
                for index, uuid in enumerate(uuid_list):
                    available_bus_number += index
                    insert_cmd_list = ["    </hostdev>",
                                       "        <address type='pci' domain='0x0000' bus='0x0{}' slot='0x00' function='0x0'/>".format(available_bus_number),
                                       "      </source>", "        <address uuid='{}'/>".format(uuid), "      <source>",
                                       "    <hostdev mode='subsystem' type='mdev' managed='no' model='vfio-pci' display='off'>"]
                    for insert_cmd in insert_cmd_list:
                        add_cmd = 'sed -i "/<\/video>/a{}" /etc/libvirt/qemu/{}.xml'.format(insert_cmd, vm_name)
                        self.excute_cmd_on_server(add_cmd)
            else:
                insert_cmd_list = ["    </hostdev>",
                                   "        <address type='pci' domain='0x0000' bus='0x0{}' slot='0x00' function='0x0'/>".format(available_bus_number),
                                   "      </source>", "        <address uuid='{}'/>".format(uuid_list[0]), "      <source>",
                                   "    <hostdev mode='subsystem' type='mdev' managed='no' model='vfio-pci' display='off'>"]
                for insert_cmd in insert_cmd_list:
                    add_cmd = 'sed -i "/<\/video>/a{}" /etc/libvirt/qemu/{}.xml'.format(insert_cmd, vm_name)
                    self.excute_cmd_on_server(add_cmd)
            refine_cmd = "virsh define /etc/libvirt/qemu/{}.xml".format(vm_name)
            self.excute_cmd_on_server(refine_cmd)
        except Exception as e:
            log.error(f"Generate vgpu kvm error: {str(e)}")
            exit(1)

    def modify_vgpu_kvm_vm(self, vgpu, vm_name, bus_list):
        """
        for KVM: modify vgpu of vm_name,  change vgpu of vm_name is "param: vgpu"
        :param vgpu: new vgpu
        :param vm_name:
        :return: vpug_uuid  # will use for add tools params to vgpu or not add
        """
        if platform == 'kvm':
            if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
                vgpu_uuid = self.get_available_vgpu_uuid_kvm(vgpu)
            else:
                if self.generate_vgpu_kvm(vgpu, bus_list) is not False:
                    vgpu_uuid = self.generate_vgpu_kvm(vgpu, bus_list)
                else:
                    log.info('there is no vgpu to use, exist.......')
                    exit(2)
            cmd = "grep 'uuid=' /etc/libvirt/qemu/{}_auto.xml |awk -F = '{print $2}'"
            cmd_out, cmd_err1 = self.excute_cmd_on_server(cmd)
            old_uuid = cmd_out.split('\n')[0].split("'")[1]
            replace_cmd = 'sed -i "s#{}#{}#g" /etc/libvirt/qemu/{}.xml; virsh define /etc/libvirt/qemu/{}.xml'.format(
                old_uuid, vgpu_uuid, vm_name, vm_name)
            cmd_out2, cmd1_err = self.excute_cmd_on_server(replace_cmd)
            if cmd1_err.split('\n')[0]:
                log.info('modify the vgpu --{} from {} to {} fail'.format(vgpu, old_uuid, vgpu_uuid))
                return False
            else:
                log.info('modify the vgpu --{} from {} to {} successful'.format(vgpu, old_uuid, vgpu_uuid))
                return vgpu_uuid  # will use for add params to vgpu or not add
        else:
            log.info('please give correct platform, now platform is {}'.format(platform))
            return False

    def judge_vgpu_heterogeneous(self):
        try:
            cmd = "nvidia-smi -q |grep 'vGPU Heterogeneous Mode'|awk -F ':' '{print $2}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if 'Enabled' in cmd_out.split('\n')[0]:
                return True
            else:
                return False
        except Exception as e:
            log.error(f"Judge vgpu heterogeneous error: {str(e)}")
            return False

    def vgpu_heterogeneous_support(self, platform, operate=None):
        try:
            current_driver = self.get_host_driver_version()
            if current_driver.split('.')[0] < '550':
                log.warning('support vgpu_heterogeneous since 550 brance, now driver is {}'.format(current_driver))
                exit()
            else:
                if platform.lower() == 'kvm':
                    if operate == '1':
                        if self.judge_vgpu_heterogeneous() is True:
                            log.info('host already enable vgpu_heterogeneous support, no need re-enable ')
                            return True
                        else:
                            enable_cmd = 'nvidia-smi vgpu -shm 1'
                            self.excute_cmd_on_server(enable_cmd)
                            if self.judge_vgpu_heterogeneous() is True:
                                log.info('enable vgpu_heterogeneous support successful')
                                return True
                            else:
                                log.info('enable vgpu_heterogeneous support fail, exit.....')
                                exit(1)
                    elif operate == '0':
                        if self.judge_vgpu_heterogeneous() is not True:
                            log.info('host already disable vgpu_heterogeneous support, no need re-disable ')
                            return True
                        else:
                            disable_cmd = 'nvidia-smi vgpu -shm 0'
                            self.excute_cmd_on_server(disable_cmd)
                            if self.judge_vgpu_heterogeneous() is not True:
                                log.info('disable vgpu_heterogeneous support successful')
                                return True
                            else:
                                log.info('disable vgpu_heterogeneous support fail, exit.....')
                                exit(1)
                    else:
                        log.warning('please give correct value to enable or disable vgpu heterogeneous')
                elif platform.lower() == 'esxi':
                    get_bus_cmd = "nvidia-smi |grep '000000'|awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'|tail -1"
                    get_bus_cmd_out, get_bus_cmd_err = self.excute_cmd_on_server(get_bus_cmd)
                    bus_id = '00' + get_bus_cmd_out.split('\n')[0].lstrip('0')
                    if operate == '1':
                        if self.judge_vgpu_heterogeneous() is True:
                            log.info('host already enable vgpu_heterogeneous support, no need re-enable ')
                            return True
                        else:
                            enable_cmd = "esxcli graphics device set --device-id {} --type SharedPassthru --vgpu-mode MixedSize; esxcli graphics host refresh".format(bus_id)
                            self.excute_cmd_on_server(enable_cmd)
                            print(enable_cmd)
                            if self.judge_vgpu_heterogeneous() is True:
                                log.info('enable vgpu_heterogeneous support successful')
                                return True
                            else:
                                log.info('enable vgpu_heterogeneous support fail, exit.....')
                                exit(1)
                    elif operate == '0':
                        if self.judge_vgpu_heterogeneous() is not True:
                            log.info('host already disable vgpu_heterogeneous support, no need re-disable ')
                            return True
                        else:
                            disable_cmd = "esxcli graphics device set --device-id {} --type SharedPassthru --vgpu-mode SameSize; esxcli graphics host refresh".format(bus_id)
                            self.excute_cmd_on_server(disable_cmd)
                            if self.judge_vgpu_heterogeneous() is not True:
                                log.info('disable vgpu_heterogeneous support successful')
                                return True
                            else:
                                log.info('disable vgpu_heterogeneous support fail, exit.....')
                                exit(1)
                    else:
                        log.warning('please give correct value to enable or disable vgpu heterogeneous')
                else:
                    log.info('please give correct platform to create vGPU')
        except Exception as e:
            log.error(f"Vgpu heterogeneous support error: {str(e)}")
            exit(1)

    def add_param_kvm_vgpu1(self, vgpu, param):
        """
        add param to new vgpu or not used vgpu(existed)
        :param vgpu:
        :param param:
        :return: True or False
        """
        try:
            if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
                uuid = random.sample(self.get_available_vgpu_uuid_kvm(vgpu), 1)[0]
            else:
                log.info('there is no vgpu to add tools option, please check, exit.....')
                exit(2)
            type = 'mdev'
            kvm_type = self.check_kvm_type()
            if kvm_type == 'ubuntu' or vgpu.split('-')[0].lower() not in ['t4', 'v100']:
                vgpu_bus_dict = self.get_exist_vgpu_ubuntu_kvm()
                for key, value in vgpu_bus_dict.items():
                    if vgpu.split('-')[0].upper() == 'value':
                        uuid = value
                        break
                type = 'pci'
            if param:
                if param.lower() == 'debug':
                    cmd = 'echo "enable_debugging=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'profile':
                    cmd = 'echo "enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'uvm':
                    cmd = 'echo "enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'debug_profile':
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'debug_uvm':
                    cmd = 'echo "enable_debugging=0x1, enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'profile_uvm':
                    cmd = 'echo "enable_uvm=0x1, enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                elif param.lower() == 'all':
                    cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                else:
                    log.info('please give correct param ---- {} to config'.format(param))
            cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
            cmd_err = cmd_err1.read()
            if cmd_err.read().split('\n')[0]:
                log.error('add the tools option  by {} ----fail'.format(cmd))
                return False
            else:
                log.info('add the tools option  by {} ----successful'.format(cmd))
                return True
        except Exception as e:
            log.error(f"Add param kvm vgpu error: {str(e)}")
            return False

    def add_param_kvm_vgpu(self, vgpu, param):
        """
        add param to new vgpu or not used vgpu(existed)
        :param vgpu:
        :param param:
        :return: True or False
        """
        try:
            if self.get_available_vgpu_uuid_kvm(vgpu) is not False:
                uuid_list = self.get_available_vgpu_uuid_kvm(vgpu)
            else:
                log.info('there is no vgpu to add tools option, please check, exit.....')
                exit(2)
            type = 'mdev'
            kvm_type = self.check_kvm_type()
            if kvm_type == 'ubuntu' or vgpu.split('-')[0].lower() not in ['t4', 'v100']:
                vgpu_bus_dict = self.get_exist_vgpu_ubuntu_kvm()
                uuid_list = []
                for key, value in vgpu_bus_dict.items():
                    if vgpu.split('-')[0].upper() == 'value':
                        uuid_list.append(value)
                type = 'pci'
            i = 0
            for uuid in uuid_list:
                if param:
                    if param.lower() == 'debug':
                        cmd = 'echo "enable_debugging=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'profile':
                        cmd = 'echo "enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'uvm':
                        cmd = 'echo "enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'debug_profile' or param.lower() == 'profile_debug':
                        cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'debug_uvm':
                        cmd = 'echo "enable_debugging=0x1, enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'profile_uvm':
                        cmd = 'echo "enable_uvm=0x1, enable_profiling=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    elif param.lower() == 'all':
                        cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/{}/devices/{}/nvidia/vgpu_params'.format(type, uuid)
                    else:
                        log.info('please give correct param ---- {} to config'.format(param))
                cmd_out1, cmd_err1 = self.excute_cmd_on_server(cmd)
                log.info('add the tools option  by {}'.format(cmd))
                cmd_err = cmd_err1.read()
                if cmd_err.read().split('\n')[0]:
                    log.error('add the tools option  by {} ----fail'.format(cmd))
                else:
                    log.info('add the tools option  by {} ----successful'.format(cmd))
                    i += 1
            if i == len(uuid_list):
                log.info('add the tools option to all vGPU successful')
                return True
            else:
                log.error('add the tools option to all vGPU successful fail, please check vgpu is used or not')
                return False
        except Exception as e:
            log.error(f"Add param kvm vgpu error: {str(e)}")
            return False

    def release_vgpu_on_kvm(self, vgpu, vm=None, **kwargs):
        """
        delete vgpu
        :param vgpu: such as T4-8C
        :param kwargs: can not power off the vm, if the vm is using the vgpu, such as : vm='ubuntu20.04-desktop-108' is using T4-8c
        :return:
        """
        try:
            vgpu_name, used_vgpu = self.get_used_vgpu_info()
            exist_vgpu = self.get_exist_vgpu_kvm()
            print('the exist vgpu infor is {}'.format(exist_vgpu))
            if exist_vgpu is False:
                log.warning('there is no vgpu to delete')
                return False
            else:
                uuid_list = []
                for key, value in exist_vgpu.items():
                    if value.lower() == vgpu.lower():
                        uuid_list.append(key)
                    else:
                        continue
                if len(uuid_list) == 0:
                    log.info('there is no {} to delete'.format(vgpu))
                    return False
                elif len(uuid_list) == 1:
                    if used_vgpu is not False:
                        used_vgpu_list = []
                        for key, value in used_vgpu.items():
                            used_vgpu_list.append(value['vgpu'])
                        if vgpu.upper() in used_vgpu_list:
                            for vgpu1, value1 in used_vgpu.items():
                                if vgpu.upper() == value1['vgpu']:
                                    uuid = used_vgpu[vgpu1]['vgpu_uuid']
                                    if vm is not None and vm == value1['vm_name']:
                                        log.warning('now {} use the vgpu--{}, please confirm release this vgpu'.format(vm, vgpu))
                                        log.info('if you want release it, not use --vm {} option'.format(vm))
                                        exit(0)
                                    else:
                                        vm_name = value1['vm_name']
                                        log.info('vgpu ---{} was used by {}, First close {}'.format(vgpu, vm_name, vm_name))
                                        self.vm_power_off(vm_name)
                                        cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                                        log.warning('delete vgpu by {}'.format(cmd))
                                        self.excute_cmd_on_server(cmd)
                                        cmd1 = 'ls /sys/bus/mdev/devices/'
                                        cmd1_out, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                                        if uuid not in cmd1_out.split('\n')[0]:
                                            log.warning('delete vgpu successful')
                                            if len(vgpu.split('-')) == 3:
                                                destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                                                log.warning('we will destory mig CI and GI')
                                                self.excute_cmd_on_server(destory_ci_gi)
                                            return True
                                        else:
                                            log.error('delete vgpu fail')
                                            return False
                                else:
                                    continue
                        else:
                            log.info('we will delete the vgpu ----- {}'.format(uuid_list[0]))
                            uuid = uuid_list[0]
                            cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                            log.warning('delete vgpu by {}'.format(cmd))
                            self.excute_cmd_on_server(cmd)
                            cmd1 = 'ls /sys/bus/mdev/devices/'
                            cmd1_out, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                            if uuid not in cmd1_out.split('\n')[0]:
                                log.warning('delete vgpu successful')
                                if len(vgpu.split('-')) == 3:
                                    destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                                    log.warning('we will destory mig CI and GI')
                                    self.excute_cmd_on_server(destory_ci_gi)
                                return True
                            else:
                                log.error('delete vgpu fail')
                                return False
                    else:
                        log.info('we will delete the vgpu ----- {}'.format(uuid_list[0]))
                        uuid = uuid_list[0]
                        cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(uuid)
                        log.warning('delete vgpu by {}'.format(cmd))
                        self.excute_cmd_on_server(cmd)
                        cmd1 = 'ls /sys/bus/mdev/devices/'
                        cmd1_out, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                        if uuid not in cmd1_out.split('\n')[0]:
                            log.warning('delete vgpu successful')
                            if len(vgpu.split('-')) == 3:
                                destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                                log.warning('we will destory mig CI and GI')
                                self.excute_cmd_on_server(destory_ci_gi)
                            return True
                        else:
                            log.error('delete vgpu fail')
                            return False
                else:
                    uuid_list = []
                    for key, value in exist_vgpu.items():
                        if value.lower() == vgpu.lower():
                            uuid_list.append(key)
                        else:
                            continue
                    if used_vgpu is not False:
                        uuid_list1, vm_list1 = [], []
                        for vgpu2, value2 in used_vgpu.items():
                            if vgpu.upper() == value2['vgpu']:
                                uuid_list1.append(value2['vgpu_uuid'])
                                vm_list1.append(value2['vm_name'])
                            else:
                                continue
                        vgpu_info = dict(zip(vm_list1, uuid_list1))
                        remove_uuid_list = []
                        if kwargs:  # do not power off this vm
                            closed_vm = [i for i in vm_list1 if i not in kwargs.values()]
                            open_vm_uuid = [vgpu_info[vm] for vm in kwargs.values()]
                            for vm in closed_vm:
                                self.vm_power_off(vm)
                            remove_uuid_list = [i for i in uuid_list if i not in open_vm_uuid]
                        else:
                            closed_vm = vm_list1
                            for vm in closed_vm:
                                self.vm_power_off(vm)
                            remove_uuid_list = [i for i in uuid_list]
                    else:
                        remove_uuid_list = [i for i in uuid_list]
                    passed = 0
                    for remove_uuid in remove_uuid_list:
                        remove_cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(remove_uuid)
                        self.excute_cmd_on_server(remove_cmd)
                        log.warning('delete vgpu by {}'.format(remove_cmd))
                        cmd1 = 'ls /sys/bus/mdev/devices/'
                        cmd1_out, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                        if remove_uuid not in cmd1_out.split('\n')[0]:
                            log.warning('delete vgpu {}: {} successful'.format(vgpu, remove_uuid))
                            passed += 1
                        else:
                            log.error('delete vgpu {}: {} fail'.format(vgpu, remove_uuid))
                    if passed == len(remove_uuid_list):
                        log.debug('remove all vgpu successful')
                        if len(vgpu.split('-')) == 3:
                            destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                            log.warning('we will destory mig CI and GI')
                            self.excute_cmd_on_server(destory_ci_gi)
                        return True
                    else:
                        log.error('can not remove all vgpu')
                        return False
        except Exception as e:
            log.error(f"Release vgpu on kvm error: {str(e)}")
            return False

    def release_all_vgpu_on_kvm(self):
        """
        delete vgpu
        :param vgpu: such as T4-8C
        :param kwargs: can not power off the vm, if the vm is using the vgpu, such as : vm='ubuntu20.04-desktop-108' is using T4-8c
        :return:
        """
        try:
            self.power_off_all_vm()
            exist_vgpu = self.get_exist_vgpu_kvm()
            if exist_vgpu is False:
                log.warning('there is no vgpu to delete')
                return False
            else:
                uuid_list = []
                for key, value in exist_vgpu.items():
                    uuid_list.append(key)
                passed = 0
                for remove_uuid in uuid_list:
                    remove_cmd = "echo 1 > /sys/bus/mdev/devices/{}/remove".format(remove_uuid)
                    log.warning('delete vgpu by {}'.format(remove_cmd))
                    self.excute_cmd_on_server(remove_cmd)
                    cmd1 = 'ls /sys/bus/mdev/devices/'
                    cmd1_out, cmd1_err1 = self.excute_cmd_on_server(cmd1)
                    if remove_uuid not in cmd1_out.split('\n')[0]:
                        log.warning('delete vgpu {} successful'.format(remove_uuid))
                        passed += 1
                    else:
                        log.error('delete vgpu {} fail'.format(remove_uuid))
                if passed == len(uuid_list):
                    log.info('remove all vgpu successful')
                    for key, value in exist_vgpu.items():
                        if len(value.split('-')) == 3:
                            destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                            log.warning('we will destory mig CI and GI')
                            cmd_out, cmd_err = self.excute_cmd_on_server(destory_ci_gi)
                            print(cmd_out)
                            break
                        else:
                            continue
                    cmd = 'rm vgpu_info.txt'
                    self.excute_cmd_on_server(cmd)
                    return True
                else:
                    log.info('can not remove all vgpu')
                    return False
        except Exception as e:
            log.error(f"Release all vgpu on kvm error: {str(e)}")
            return False

    def gsp_operation_kvm(self, operate):
        if operate == 'disable':
            cmd1 = 'cat /etc/modprobe.d/gsp.conf'
            cmd1_out, cmd2_err = self.excute_cmd_on_server(cmd1)
            if 'EnableGpuFirmware=0;RMSetVgpuGspPluginOffloadMode=0' in cmd1_out.split('\n')[0]:
                log.info('the host is not GSP mode')
                return True
            else:
                disable_gsp = 'options nvidia NVreg_RegistryDwords="EnableGpuFirmware=0;RMSetVgpuGspPluginOffloadMode=0"'
                cmd = "cd /etc/modprobe.d&&rm gsp.conf&&touch gsp.conf; echo {} /etc/modprobe.d/gsp.conf; reboot".format(disable_gsp)
                self.excute_cmd_on_server(cmd)
        if operate == 'enable':
            if self.gsp_verify() is True:
                return True
            else:
                cmd = 'ls /etc/modprobe.d/gsp.conf'
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                if 'gsp.conf' in cmd_out.split('\n')[0]:
                    cmd = 'mv /etc/modprobe.d/gsp.conf /etc/modprobe.d/gsp.conf_bak; reboot'
                    self.excute_cmd_on_server(cmd)

    def gsp_operation_exsi(self, operate):
        if operate == 'disable':
            if self.gsp_verify() is False:
                return True
            else:
                disable_gsp = 'esxcli system module parameters set -p "EnableGpuFirmware=0;RMSetVgpuGspPluginOffloadMode=0" -m nvidia&&reboot'
                self.excute_cmd_on_server(disable_gsp)
        if operate == 'enable':
            if self.gsp_verify() is True:
                return True
            else:
                cmd = 'esxcli system module parameters set -p "" -m nvidia&&reboot'
                self.excute_cmd_on_server(cmd)

    def gsp_verify(self):
        cmd = 'nvidia-smi -q|grep -i gsp'
        cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
        if 'N/A' in cmd_out.split('\n')[0].split(':').strip(' '):
            log.info('host is no GSP mode')
            return False
        else:
            log.info('host is GSP mode, default it is GSP mode')
            return True

    def backup_vm_file(self, vm_name):
        try:
            vm_path = self.get_vm_path(vm_name)
            cmd = 'ls {}'.format(vm_path)
            if platform.lower() == 'esxi':
                vm_file = vm_path + '/' + vm_name + '.vmx'
                cmd1 = 'cp vm_file {}'.format(vm_file + 'bak')
                message = vm_name + '.vmx_bak'
            if platform.lower() == 'kvm':
                vm_file = '/etc/libvirt/qemu/{}.xml'.format(vm_name)
                message = vm_name + '.xml_bak'
                cmd1 = 'cp vm_file {}'.format(vm_file + '_bak')
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if message in cmd_out.split('\n'):
                log.info('had do vm file backup, no need do it again')
                return True
            else:
                self.excute_cmd_on_server(cmd1)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                if message in cmd_out.split('\n'):
                    log.info('we backup vm file successful')
                    return True
                else:
                    return False
        except Exception as e:
            log.error(f"Backup vm file error: {str(e)}")
            return False

    def restore_vm(self, vm_name):
        try:
            vm_path = self.get_vm_path(vm_name)
            cmd = 'ls {}'.format(vm_path)
            if platform.lower() == 'esxi':
                vm_file = vm_path + '/' + vm_name + '.vmx'
                cmd1 = 'rm {}; cp {} {}'.format(vm_file, vm_file + '_bak', vm_file)
            if platform.lower() == 'kvm':
                vm_file = '/etc/libvirt/qemu/{}.xml'.format(vm_name)
                cmd1 = 'rm {}; cp {} {}'.format(vm_file, vm_file + '_bak', vm_file)
            self.excute_cmd_on_server(cmd1)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            if vm_file in cmd_out.split('\n')[0]:
                log.info('restore the vm file successful')
                return True
            else:
                log.warning('restore the vm file fail')
                return False
        except Exception as e:
            log.error(f"Restore vm file error: {str(e)}")
            return False

    def get_mig_support(self, vgpu):
        try:
            gpu = vgpu.split('-')[0].upper()
            cmd = 'nvidia-smi |grep -A 2 {}'.format(gpu)
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            for i in cmd_out.split('\n'):
                pass
        except Exception as e:
            log.error(f"Get mig support error: {str(e)}")
            return False

    def enable_heterogeneous_support(self):
        try:
            if self.platform.lower() == 'kvm':
                cmd = 'nvidia-smi vgpu -shm 1'
            elif self.platform.lower() == 'esxi':
                cmd = 'export device_id=`esxcli graphics device list|grep 0000`; esxcli graphics device set --device-id $device_id --type SharedPassthru --vgpu-mode MixedSize; esxcli graphics host refresh'
            else:
                log.warning('please give correct platform, exit.........')
                exit()
            self.excute_cmd_on_server(cmd)
        except Exception as e:
            log.error(f"Enable heterogeneous support error: {str(e)}")
            return False

    def get_bus_list_ubuntu_kvm(self, pgpu=None):
        try:
            cmd = "nvidia-smi |grep 0000|awk -F '|' '{print $3}'|awk -F ' ' '{print $1}'"
            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
            bus_list1 = cmd_out.split('\n')[0:-1]
            cmd1 = 'ls /sys/bus/pci/devices'
            cmd1_out, cmd1_err = self.excute_cmd_on_server(cmd1)
            bus_list2 = cmd1_out.split('\n')[0:-1]
            bus_list = []
            for i in bus_list1:
                for j in bus_list2:
                    str1 = j.split(':')[0] + ':' + j.split(':')[1]
                    if str1 in i:
                        bus_list.append(j)
            if pgpu:
                bus_list = [i for i in bus_list if pgpu.split(':')[1] in i]
                return bus_list
            else:
                return bus_list
        except Exception as e:
            log.error(f"Get bus list error: {str(e)}")
            return False

    def get_available_bus_id_ubuntu_kvm(self, vgpu, pgpu=None):
        try:
            if pgpu:
                bus_id_list = self.get_bus_list_ubuntu_kvm(pgpu)
            else:
                bus_id_list = self.get_bus_list_ubuntu_kvm()
            available_bus_list = []
            for bus_id in bus_id_list:
                bus_id_split = bus_id.split(':')
                # bus = bus_id_split[0] +'\:' + bus_id_split[1] + '\:' + bus_id_split[2]
                bus = bus_id_split[0] + ':' + bus_id_split[1] + ':' + bus_id_split[2]
                cmd = 'cat /sys/bus/pci/devices/{}/nvidia/creatable_vgpu_types'.format(bus)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                for vgpu_check in cmd_out.split('\n'):
                    if vgpu.upper() in vgpu_check:
                        available_bus_list.append(bus)
            print(available_bus_list)
            if len(available_bus_list) != 0:
                return available_bus_list
            else:
                log.warning('there is no bus to create vgpu--{}, please check, exit...'.format(vgpu))
                exit()
        except Exception as e:
            log.error(f"Get available bus id error: {str(e)}")
            return False

    def get_exist_vgpu_ubuntu_kvm(self):
        try:
            bus_list = self.get_bus_list_ubuntu_kvm()
            bus_vgpu_dict = {}
            for i in bus_list:
                bus_id_number_list = i.split(':')
                j = bus_id_number_list[0][-4:] + '\:' + bus_id_number_list[1] + '\:' + bus_id_number_list[2]
                cmd = 'cat /sys/bus/pci/devices/{}/nvidia/current_vgpu_type'.format(j)
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                value = cmd_out.split('\n')[0]
                if value != '0' and value:
                    cmd1 = 'cat /usr/share/nvidia/vgpu/vgpuConfig.xml|grep "id=\\\"{}\\\""'.format(value)
                    cmd1_out, cmd1_err = self.excute_cmd_on_server(cmd1)
                    vgpu = cmd1_out.split('\n')[0].split('"')[3].split(' ')[-1]
                    bus_vgpu_dict[i] = vgpu.upper()
            return bus_vgpu_dict
        except Exception as e:
            log.error(f"Get exist vgpu ubuntu kvm error: {str(e)}")
            return False

    def create_vgpu_ubuntu_kvm(self, vgpu, pgpu=None):
        try:
            if vgpu.split('-')[0].lower() in ['t4', 'v100']:
                bus_list = self.get_bus_list()
                if pgpu:
                    self.create_vgpu_kvm(bus_list, vgpu, pgpu)
                else:
                    self.create_vgpu_kvm(bus_list, vgpu)
            else:
                log.info('create vgpu----{} on kvm host'.format(vgpu))
                if len(vgpu.split('-')) == 2:
                    if self.judge_mig_enable(vgpu, pgpu=server_args.bus_number) is True:
                        self.release_all_vgpu_on_kvm()
                        self.enable_mig(vgpu, action=0, gpu_bus=server_args.bus_number)
                if len(vgpu.split('-')) == 3:
                    self.create_ci_for_vgpu_kvm(vgpu)
                if pgpu:
                    available_bus_list = self.get_available_bus_id_ubuntu_kvm(vgpu, pgpu)
                else:
                    available_bus_list = self.get_available_bus_id_ubuntu_kvm(vgpu)
                vgputype_id = self.get_vgputype_id(vgpu)
                available_bus = random.choice(available_bus_list)
                cmd_vgpu = 'echo {} >/sys/bus/pci/devices/{}/nvidia/current_vgpu_type'.format(vgputype_id, available_bus)
                log.info('create vgpu by --- {}'.format(cmd_vgpu))
                cmd_out1, cmd_err = self.excute_cmd_on_server(cmd_vgpu)
                if cmd_err.split('\n')[0] != '':
                    log.info('generate vgpu failed')
                    return None
                else:
                    log.warning('generate vgpu successful')
                    if (self.is_1_1_vgpu(vgpu.lower()) and vgpu.split('-')[0].lower() not in ['t4', 'v100', 'a10']) or len(vgpu.split('-')) == 3:
                        cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1, enable_uvm=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(available_bus)
                    else:
                        cmd = 'echo "enable_debugging=0x1, enable_profiling=0x1" >/sys/bus/pci/devices/{}/nvidia/vgpu_params'.format(available_bus)
                    print(cmd)
                    self.excute_cmd_on_server(cmd)
                    if kvm_type == 'cg':
                        print('\nuse following command to launch vm\n')
                        print("qemu-system-aarch64 -object iommufd,id=iommufd0 -machine hmat=on -machine virt,accel=kvm,gic-version=3,iommu=nested-smmuv3,iommufd=iommufd0,ras=on -cpu host -smp cpus=8 -m size=8G,slots=2,maxmem=66G -monitor pty -display none  -k en-us -object memory-backend-ram,size=4G,id=m0 -object memory-backend-ram,size=4G,id=m1 -numa node,memdev=m0,cpus=0-3,nodeid=0 -numa node,memdev=m1,cpus=4-7,nodeid=1 -device vfio-pci,host={},rombar=0,id=dev0,iommufd=iommufd0  -uuid 88dbb03d-d40e-4dea-834a-7c42ee80f188 -object acpi-generic-initiator,id=gi0,pci-dev=dev0,host-nodes=2-9 -bios /usr/share/AAVMF/AAVMF_CODE.fd -device nvme,drive=nvme0,serial=deadbeaf1,bus=pcie.0 -drive file=/home/<USER>/Images/myvm_disk.qcow2,index=0,media=disk,format=qcow2,if=none,id=nvme0 -cdrom /home/<USER>/ISOs/BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso -device e1000,romfile=/home/<USER>/Images/efi-e1000.rom,netdev=net0 -netdev user,id=net0,hostfwd=tcp::5558-:22,hostfwd=tcp::5900-:5900 &".format(available_bus))
                        print('\n if configure public network, use following command\n')
                        print("qemu-system-aarch64 -object iommufd,id=iommufd0 -machine hmat=on -machine virt,accel=kvm,gic-version=3,iommu=nested-smmuv3,iommufd=iommufd0,ras=on -cpu host -smp cpus=36 -m size=256G,slots=4,maxmem=300G -monitor pty -display none -k en-us -object memory-backend-ram,size=128G,id=m0 -object memory-backend-ram,size=128G,id=m1 -device vfio-pci,host={},rombar=0,id=dev0,iommufd=iommufd0 -uuid 88dbb03d-d40e-4dea-834a-7c42ee80f188 -object acpi-generic-initiator,id=gi0,pci-dev=dev0,host-nodes=2-9 -bios /usr/share/AAVMF/AAVMF_CODE.fd -device nvme,drive=nvme0,serial=deadbeaf1,bus=pcie.0 -drive file=/home/<USER>/Images/br0_no_numa.qcow2,index=0,media=disk,format=qcow2,if=none,id=nvme0 -cdrom /home/<USER>/ISOs/BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso -device virtio-net-pci,netdev=net0,mac=fa:16:3e:db:13:a1 -netdev bridge,br=br0,id=net0 &".format(available_bus))
                        print(
                            """
                            a. qemu-img create -f qcow2 myvm_disk.qcow2 350G \n
                            b. download tot ISO file from https://urm.nvidia.com/artifactory/sw-dgx-platform-generic-local/ghvirt-iso/ \n
                            c. download rom file(efi-e1000.rom) from  https://github.com/qemu/qemu/blob/master/pc-bios/efi-e1000.rom \n
                            d. select you used base os version, such as BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso \n
                            e. On host machine, use — 'ssh -p 5558 user@127.0.0.1' to login guest
                            """
                        )
                    cmd = 'touch vgpu_info.txt'
                    os.popen(cmd)
                    with open('vgpu_info.txt', 'a+') as f:
                        f.write("On host {}, {}-- create {} on {} on ubuntu kvm".format(server_args.server, time.strftime("%Y%m%d--%H:%M", time.localtime()), vgpu, available_bus))
                        f.write("\n")
                    self.excute_cmd_on_server(cmd)
                    cmd1 = "echo 'On host {}, {}-- use {} create {} on ubuntu kvm' >> vgpu_info.txt".format(server_args.server, time.strftime("%Y%m%d--%H:%M", time.localtime()), available_bus, vgpu)
                    self.excute_cmd_on_server(cmd1)
                    return available_bus
        except Exception as e:
            log.error(f"Create vgpu ubuntu kvm error: {str(e)}")
            return False

    def get_used_vgpu_ubuntu_kvm(self):
        try:
            all_vm = self.get_all_vm()
            power_on_vm = []
            for vm in all_vm:
                vm_status = self.get_vm_status(vm)
                if vm_status == 'power_on':
                    power_on_vm.append(vm)
            exist_vgpu = self.get_exist_vgpu_ubuntu_kvm()
            if len(power_on_vm) == 0:
                log.info('there is no vgpu to be used by vm')
                return None, exist_vgpu
            else:
                bus_list = []
                used_vgpu, unused_vgpu = {}, {}
                if exist_vgpu:
                    for vm in power_on_vm:
                        cmd1 = "cat /etc/libvirt/qemu/{}.xml |grep 'address uuid'".format(vm)
                        cmd1_out, cmd1_err = self.excute_cmd_on_server(cmd1)
                        if 'address uuid' in cmd1_out.split('\n')[0]:
                            continue
                        else:
                            cmd = "cat /etc/libvirt/qemu/{}.xml |grep 'address domain'".format(vm)
                            cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                            cmd_out_list = cmd_out.split('\n')[0].split("'")
                            bus = cmd_out_list[1][2::] + ':' + cmd_out_list[3].strip('0x') + ':' + cmd_out_list[5].strip('0x') + '.' + cmd_out_list[7].strip('0x')
                            bus_list.append(bus)
                    if exist_vgpu:
                        for key, value in exist_vgpu.items():
                            for bus in bus_list:
                                if key == bus:
                                    used_vgpu[key] = value
                                else:
                                    unused_vgpu[key] = value
                    return used_vgpu, unused_vgpu
                else:
                    return None, None
        except Exception as e:
            log.error(f"Get used vgpu ubuntu kvm error: {str(e)}")
            return None, None

    def generate_vgpu_ubuntu_kvm(self, vgpu_list1, pgpu=None):
        """
        # generate uuid, will assign it to vgpu, only use it on kvm platform, if it exist and not be used , return it
        :param vgpu:
        :return: vgpu_uuid or False
        """
        try:
            if self.enable_vgpu_support_kvm() is True:
                vgpu_list2 = vgpu_list1.strip(',').split(',')
                vgpu_list = [i.lower().strip(' ') for i in vgpu_list2]
                used_vgpu_info, unused_vgpu_info = self.get_used_vgpu_ubuntu_kvm()
                if len(vgpu_list) > 1 and len(set(vgpu_list)) != 1 and len(vgpu_list[0].split('-')) == 2:
                    self.vgpu_heterogeneous_support(platform='kvm', operate='1')
                create_vgpu_bus = []
                for vgpu in vgpu_list:
                    if unused_vgpu_info:
                        if vgpu not in list(unused_vgpu_info.values()):
                            if pgpu:
                                create_vgpu_bus.append(self.create_vgpu_ubuntu_kvm(vgpu, pgpu))
                            else:
                                create_vgpu_bus.append(self.create_vgpu_ubuntu_kvm(vgpu))
                        else:
                            for key, value in unused_vgpu_info.items():
                                if vgpu.upper() == value:
                                    unused_vgpu_info.pop(key)
                                    if key not in create_vgpu_bus:
                                        create_vgpu_bus.append(key)
                                        break

                    else:
                        if pgpu:
                            create_vgpu_bus.append(self.create_vgpu_ubuntu_kvm(vgpu, pgpu))
                        else:
                            create_vgpu_bus.append(self.create_vgpu_ubuntu_kvm(vgpu))
                if len(create_vgpu_bus) == len(vgpu_list):
                    return create_vgpu_bus
                else:
                    log.error('create full vgpu fail, please check, exit.......')
                    exit(1)
            else:
                log.info('please enable the vgpu support on kvm platform')
                exit(1)
        except Exception as e:
            log.error(f"Generate vgpu ubuntu kvm error: {str(e)}")
            return False

    def add_vgpu_ubuntu_kvm_vm(self, vgpu_list1, vm_name, pgpu=None):
        """
        add param to new vgpu or existed vgpu, then assign vgpu to vm
        :param vgpu:
        :param vm_name:
        :param param:
        :return: True or False
        """
        try:
            if self.get_vm_status(vm_name) == 'power_on':
                self.vm_power_off(vm_name)
            # delete vgpu information in vm.xml file
            cmd = "sed -i '/hostdev/,/hostdev>/d' /etc/libvirt/qemu/{}.xml; virsh define /etc/libvirt/qemu/{}.xml".format(vm_name, vm_name)
            self.excute_cmd_on_server(cmd)
            # create vgpu
            if pgpu:
                bus_list1 = self.generate_vgpu_ubuntu_kvm(vgpu_list1, pgpu)
            else:
                bus_list1 = self.generate_vgpu_ubuntu_kvm(vgpu_list1)
            vgpu_list = vgpu_list1.split(',')
            if isinstance(vgpu_list, list) and len(vgpu_list) != 0:
                # add multi or single vgpu to vm
                for bus in bus_list1:
                    used_bus = bus.split(':')
                    insert_cmd_list = ["    </hostdev>",
                                       "    </source>",
                                       "      <address domain='0x0000' bus='0x{}' slot='0x{}' function='0x{}'/>".format(used_bus[1], used_bus[2].split('.')[0], used_bus[2].split('.')[1]),
                                       "    <source>",
                                       "    <hostdev mode='subsystem' type='pci' managed='no'>"]
                    for insert_cmd in insert_cmd_list:
                        add_cmd = 'sed -i "/<\/video>/a\{}" /etc/libvirt/qemu/{}.xml'.format(insert_cmd, vm_name)
                        self.excute_cmd_on_server(add_cmd)
            refine_cmd = "virsh define /etc/libvirt/qemu/{}.xml".format(vm_name)
            cmd_out, cmd_err = self.excute_cmd_on_server(refine_cmd)
        except Exception as e:
            log.error(f"Add vgpu ubuntu kvm vm error: {str(e)}")
            return False

    def release_all_vgpu_ubuntu_kvm(self):
        try:
            self.power_off_all_vm()
            vgpu_dict = self.get_exist_vgpu_ubuntu_kvm()
            if vgpu_dict:
                passed = 0
                for key, value in vgpu_dict.items():
                    key_list = key.split(':')
                    bus_id = key_list[0] + '\:' + key_list[1] + '\:' + key_list[2]
                    cmd = "echo '0'>/sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(bus_id)
                    self.excute_cmd_on_server(cmd)
                    cmd1 = "cat /sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(bus_id)
                    cmd1_out, cmd1_err = self.excute_cmd_on_server(cmd1)
                    if cmd1_out.split("\n")[0] == '0':
                        log.info('release vgpu --{} successful'.format(value))
                        passed += 1
                    else:
                        log.info('release vgpu --{} fail'.format(value))
                print(passed)
                if passed == len(list(vgpu_dict.keys())) and passed != 0:
                    if len(value.split('-')) == 3:
                        destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                        log.warning('we will destory mig CI and GI')
                        cmd_out, cmd_err = self.excute_cmd_on_server(destory_ci_gi)
                        print(cmd_out)
                bus_list = self.get_gpu_bus_id()
                for bus in bus_list:
                    if 'T4' in bus or 'V100' in bus:
                        self.release_all_vgpu_on_kvm()
                cmd = 'rm vgpu_info.txt'
                cmd_out, cmd_err = self.excute_cmd_on_server(cmd)
                print('vgpu_create_info as follows:')
                print(cmd_out)
            else:
                log.info('there is no vgpu to release')
        except Exception as e:
            log.error(f"Release all vgpu ubuntu kvm error: {str(e)}")
            return False

    def release_specify_vgpu_ubuntu_kvm(self, vgpu):
        try:
            self.power_off_all_vm()
            used, un_used = self.get_used_vgpu_ubuntu_kvm()
            for key, value in un_used.items():
                print(key)
                key_list = key.split(':')
                bus_id = key_list[0] + '\:' + key_list[1] + '\:' + key_list[2]
                cmd = "echo '0'>/sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(bus_id)
                self.excute_cmd_on_server(cmd)
                cmd1 = "cat /sys/bus/pci/devices/{}/nvidia/current_vgpu_type".format(bus_id)
                cmd1_out, cmd1_err = self.excute_cmd_on_server(cmd1)
                if cmd1_out.split("\n")[0] == '0':
                    log.info('release vgpu --{} successful'.format(value))
                    if len(value.split('-')) == 3:
                        destory_ci_gi = 'nvidia-smi mig -dci&&nvidia-smi mig -dgi'
                        log.warning('we will destory mig CI and GI')
                        cmd_out, cmd_err = self.excute_cmd_on_server(destory_ci_gi)
                        print(cmd_out)
                else:
                    log.info('release vgpu --{} fail'.format(value))
            bus_list = self.get_gpu_bus_id()
            for bus in bus_list:
                if 'T4' in bus or 'V100' in bus:
                    self.release_all_vgpu_on_kvm()
        except Exception as e:
            log.error(f"Release specify vgpu ubuntu kvm error: {str(e)}")
            return False


if __name__ == '__main__':
    vgpu_server = Vgpu_server()
    # vgpu_server.add_vgpu_ubuntu_kvm_vm('a100-20c,a100-20c', 'ubuntu24.04')
    # vgpu_server.generate_vgpu_ubuntu_kvm('a100-10c,a100-10c,a100-10c,a100-10c')
    # print(vgpu_server.get_gpu_bus_id())
    # get_vm_detail_info('*************', 'lab', 'labuser')
    vm_list1 = vgpu_server.get_all_vm()
    kvm_type = ''
    vm_list = random.sample(list(vm_list1.keys()), len(list(vm_list1.keys())))
    if platform.lower() == 'kvm':
        kvm_type = vgpu_server.check_kvm_type()
    if platform.lower() == 'esxi':
        esxi_version = vgpu_server.judge_esxi_version()
        print('esxi version is {} '.format(esxi_version))
        host_driver_url = '//builds/linuxbuilds/release/display/VMware/{}'.format(server_args.host_driver)
        if server_args.host_driver:
            # if server_ip != '*************':
            driver1 = server_args.host_driver.split('.')[0]
            if '8.0' in esxi_version:
                if driver1 >= '525':
                    host_driver = 'NVD_bootbank_NVD-AIE_ESXi_8.0.0_Driver_{}-1OEM.800.1.0.********.vib'.format(
                        server_args.host_driver)
                    host_driver_zip = 'NVD-AIE-x86_64-{}-1OEM.800.1.0.********-bundle.zip'.format(server_args.host_driver)
                else:
                    log.error('there is no driver for vmware 8.0.x if driver is less 525, please confirm it')
                    exit()
            elif '9.0' in esxi_version:
                if driver1 >= '565':
                    if driver1 == '570':
                        if server_args.host_driver <= '570.133.01':
                            number = '********'
                        else:
                            number = '********'
                    elif driver1 == '580':
                        number = '********'
                    else:
                        log.error('please check the driver version')
                        exit()
                    host_driver = 'NVD_bootbank_NVD-AIE_ESXi_9.0.0_Driver_{}-1OEM.900.0.{}.vib'.format(
                        server_args.host_driver, number)
                    host_driver_zip = 'NVD-AIE-x86_64-{}-1OEM.900.0.{}-bundle.zip'.format(server_args.host_driver, number)
                else:
                    log.error('there is no driver for vmware 8.0.x if driver is less 525, please confirm it')
                    exit()
            elif '7.0.2' in esxi_version:
                if driver1 >= '525':
                    host_driver = 'NVD_bootbank_NVD-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(
                        server_args.host_driver)
                    host_driver_zip = 'NVD-AIE-x86_64-{}-1OEM.702.0.0.********-bundle.zip'.format(
                        server_args.host_driver)
                else:
                    host_driver = 'NVIDIA_bootbank_NVIDIA-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(
                        server_args.host_driver)
                    if server_args.host_driver >= '470.187':
                        host_driver = 'NVD_bootbank_NVD-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(
                            server_args.host_driver)
            elif '7.0' in esxi_version:
                if driver1 >= '525':
                    host_driver = 'NVD_bootbank_NVD-AIE_ESXi_7.0.1_Driver_{}-1OEM.700.0.0.********.vib'.format(
                        server_args.host_driver)
                    host_driver_zip = 'NVD-AIE-x86_64-{}-1OEM.700.0.0.********-bundle.zip'.format(server_args.host_driver)
                else:
                    host_driver = 'NVIDIA_bootbank_NVIDIA-AIE_ESXi_7.0.1_Driver_{}-1OEM.700.0.0.********.vib'.format(
                        server_args.host_driver)
                    if server_args.host_driver >= '470.187':
                        host_driver = 'NVD_bootbank_NVD-AIE_ESXi_7.0.2_Driver_{}-1OEM.702.0.0.********.vib'.format(
                            server_args.host_driver)
            else:
                log.error('please check the vmware version, exit......')
                exit()
    elif platform.lower() == 'kvm' and kvm_type != 'cg':
        if server_args.host_driver:
            host_driver_url = '//builds/linuxbuilds/release/display/VGX-x86_64/{}'.format(server_args.host_driver)
            if server_args.host_driver.split('.')[0] <= '525':
                kvm_version = vgpu_server.get_kvm_version()
                host_driver = 'NVIDIA-vGPU-rhel-{}-{}.x86_64.rpm'.format(kvm_version, server_args.host_driver)
            elif server_args.host_driver < '535.36' and len(server_args.host_driver) <= 6:
                kvm_version = vgpu_server.get_kvm_version()
                host_driver = 'NVIDIA-vGPU-rhel-{}-{}.x86_64.rpm'.format(kvm_version, server_args.host_driver)
            else:
                host_driver = 'NVIDIA-Linux-x86_64-{}-vgpu-kvm-aie.run'.format(server_args.host_driver)
    elif kvm_type == 'cg' and platform.lower() == 'kvm':
        if server_args.host_driver:
            host_driver_url = '//builds/linuxbuilds/release/display/VGX-aarch64/{}'.format(server_args.host_driver)
            host_driver = 'NVIDIA-Linux-aarch64-{}-vgpu-kvm-aie.run'.format(server_args.host_driver)
    else:
        log.info('please give correct platform,exit.....')
        exit(6)
    if server_args.guest_driver:
        guest_driver_url = '//builds/linuxbuilds/release/display/VGX-x86_64/{}'.format(server_args.guest_driver)
        guest_driver = 'NVIDIA-Linux-x86_64-{}-grid.run'.format(server_args.guest_driver)
        if kvm_type == 'cg':
            guest_driver_url = '//builds/linuxbuilds/release/display/VGX-aarch64/{}'.format(server_args.guest_driver)
            guest_driver = 'NVIDIA-Linux-aarch64-{}-grid.run'.format(server_args.guest_driver)
    if server_args.server_operate:
        if server_args.server_operate.lower() == 'reboot' or server_args.server_operate.lower() == 'reset':
            vgpu_server.reboot_server()
            time.sleep(2)
            exit()
        if platform.lower() == 'esxi':
            driver_path = vgpu_server.get_vm_path((vm_list[0])) + '/..'
        elif platform.lower() == 'kvm':
            driver_path = '~'
        else:
            log.error('please give correct platform, exit.....')
            exit(1)
        if server_args.server_operate.lower() == 'install':
            if platform.lower() == 'esxi':
                host_driver = prepare_driver_esxi('host')
                scp_file(server_ip, server_user, server_password, '{}/{}'.format(download_path, host_driver), driver_path)
                vgpu_server.install_host_driver(driver_path, host_driver)
            else:
                host_driver = prepare_driver(server_args.host_user, server_args.host_password, server_args.server, 'host')
                driver_path = '~'
                vgpu_server.install_host_driver(driver_path, host_driver, kvm_type=kvm_type)
        elif server_args.server_operate.lower() == 'upgrade':
            if platform.lower() == 'esxi':
                host_driver = prepare_driver_esxi('host')
                scp_file(server_ip, server_user, server_password, '{}/{}'.format(download_path, host_driver), driver_path)
                vgpu_server.upgrade_host_driver(driver_path, host_driver)
            else:
                vgpu_server.uninstall_host_driver()
                vgpu_server.install_host_driver(driver_path, host_driver)
        elif server_args.server_operate.lower() == 'uninstall':
            vgpu_server.uninstall_host_driver()
        else:
            log.info('please give correct operation to operate driver of server, such as install/uninstall/upgrade')
    if server_args.vgpu_heterogeneous:
        vgpu_server.vgpu_heterogeneous_support(platform, operate=server_args.vgpu_heterogeneous)
    if platform.lower() == 'kvm' and server_args.release_vgpu:
        if server_args.release_vgpu.lower() != 'all':
            if server_args.vm_name:
                vgpu_server.release_vgpu_on_kvm(server_args.release_vgpu, vm=server_args.vm_name)
            else:
                if kvm_type == 'redhat':
                    vgpu_server.release_vgpu_on_kvm(server_args.release_vgpu)
                else:
                    vgpu_server.release_all_vgpu_ubuntu_kvm(server_args.release_vgpu)
        else:
            if kvm_type == 'redhat':
                vgpu_server.release_all_vgpu_on_kvm()
            else:
                vgpu_server.release_all_vgpu_ubuntu_kvm()
    if server_args.vm_operate and server_args.vm_name:
        if server_args.vm_operate.lower() == 'power_on':
            vgpu_server.vm_power_on(server_args.vm_name)
        elif server_args.vm_operate.lower() == 'power_off':
            vgpu_server.vm_power_off(server_args.vm_name)
        else:
            log.info('please give correct option to operate vm')
    if server_args.show_infor:
        if server_args.show_infor.lower() == 'vm':
            log.info('the host have the following vm')
            vm_dict = vgpu_server.get_all_vm()
            for key, value in vm_dict.items():
                print('the host has the VM: {}'.format(key))
                if vgpu_server.get_vm_status(key) == 'power_on':
                    ip_list = vgpu_server.get_vm_ip(key)
                    print('the power_on vm--{} ip is {}'.format(key, ip_list))
                else:
                    print('the vm---{} is power off, can not get the ip address now'.format(key))
        if server_args.show_infor.lower() == 'vgpu':
            if platform.lower() == 'kvm':
                cmd = 'cat vgpu_info.txt'
                cmd_out, cmd_err = vgpu_server.excute_cmd_on_server(cmd)
                print('host create vGPU info as follows')
                for i in cmd_out.split('\n'):
                    if i != '':
                        print(i)
                log.warning('we get all the exist vgpu on kvm host')
                if kvm_type == 'redhat':
                    print(vgpu_server.get_exist_vgpu_kvm())
                    log.warning('we get all the used vGPU on host')
                    vgpu_name, vgpu_used_info = vgpu_server.get_used_vgpu_info()
                    print(vgpu_used_info)
                elif kvm_type == 'ubuntu' or kvm_type == 'cg':
                    log.warning('On ubnutu kvm, for Ampere++, vgpu info as follows')
                    print('exist vgpu info is {}'.format(vgpu_server.get_exist_vgpu_ubuntu_kvm()))
                    used_vgpu, un_used_info = vgpu_server.get_used_vgpu_ubuntu_kvm()
                    print('the used vgpu is {}'.format(used_vgpu))
                    bus_list = vgpu_server.get_gpu_bus_id()
                    for bus in bus_list:
                        if 'T4' in bus or 'V100' in bus:
                            log.warning('On ubuntu24.04 KVM, for volta and turing, vgpu info as follows')
                            print('the exist vgpu info is {}'.format(vgpu_server.get_exist_vgpu_kvm()))
                            print('the used vgpu info is {}'.format(vgpu_server.get_used_vgpu_info()))
                else:
                    log.info('please give correct kvm platform, exit.....')
            log.warning('the host has the pgpu as follows')
            cmd_out, cmd_err = vgpu_server.excute_cmd_on_server('nvidia-smi')
            print(cmd_out)
    if server_args.enable_mig and server_args.vgpu:
        if platform == 'kvm':
            if server_args.vgpu.split('-')[0].lower() == 'h100' or server_args.vgpu.split('-')[0].lower() in 'gh200':
                vgpu_server.release_specify_vgpu_ubuntu_kvm(server_args.vgpu)
            else:
                vgpu_server.release_all_vgpu_on_kvm()
        vgpu_server.enable_mig(server_args.vgpu, action=int(server_args.enable_mig), gpu_bus=server_args.bus_number)
    if server_args.create_vgpu:
        vgpu_server.enable_vgpu_support_kvm()
        if platform.lower() == 'kvm':
            if kvm_type == 'redhat' or 't4' in server_args.create_vgpu.lower() or 'v100' in server_args.create_vgpu.lower() or 'dc' in server_args.create_vgpu.lower():
                if server_args.bus_number:
                    bus_list = vgpu_server.get_bus_list(pgpu_bus=server_args.bus_number)
                else:
                    bus_list = vgpu_server.get_bus_list()
                vgpu_server.generate_vgpu_kvm(server_args.create_vgpu, bus_list)
            elif kvm_type == 'ubuntu' or kvm_type == 'cg':
                if server_args.bus_number:
                    vgpu_server.generate_vgpu_ubuntu_kvm(server_args.create_vgpu, server_args.bus_number)
                else:
                    vgpu_server.generate_vgpu_ubuntu_kvm(server_args.create_vgpu)
            else:
                log.error('please give correct kvm platform to create vGPU')
    if server_args.vgpu and server_args.vm_name:
        if platform.lower() == 'kvm':
            if kvm_type == 'redhat' or 't4' in server_args.vgpu.lower() or 'v100' in server_args.vgpu.lower() or 'dc' in server_args.create_vgpu.lower():
                vgpu_server.enable_vgpu_support_kvm()
                if server_args.bus_number:
                    bus_list = vgpu_server.get_bus_list(pgpu_bus=server_args.bus_number)
                else:
                    bus_list = vgpu_server.get_bus_list()
                vgpu_server.add_vgpu_kvm_vm(server_args.vgpu, server_args.vm_name, bus_list)
            elif kvm_type == 'ubuntu' or kvm_type == 'cg':
                if server_args.bus_number:
                    vgpu_server.add_vgpu_ubuntu_kvm_vm(server_args.vgpu, server_args.vm_name, server_args.bus_number)
                else:
                    vgpu_server.add_vgpu_ubuntu_kvm_vm(server_args.vgpu, server_args.vm_name)
            else:
                log.error('please give correct kvm platform to create vGPU and assign to vm')
        else:
            vgpu_server.del_all_vgpu_vm_vmware(server_args.vm_name)
            vgpu_server.add_vgpu_to_vm_vmware(server_args.vgpu, server_args.vm_name)
        vgpu_server.vm_power_on(server_args.vm_name)
    if server_args.vgpu_list and server_args.vm_name:
        if platform.lower() == 'kvm':
            vgpu_server.enable_vgpu_support_kvm()
            if kvm_type == 'redhat' or len([i for i in server_args.vgpu_list.split(',') if 't4' in i.lower()]) != 0 or len([i for i in server_args.vgpu_list.split(',') if 'v100' in i.lower()]) != 0:
                if server_args.bus_number:
                    bus_list = vgpu_server.get_bus_list(pgpu_bus=server_args.bus_number)
                else:
                    bus_list = vgpu_server.get_bus_list()
                vgpu_server.add_vgpu_kvm_vm(server_args.vgpu_list, server_args.vm_name, bus_list)
            elif kvm_type == 'ubuntu' or kvm_type == 'cg':
                vgpu_server.add_vgpu_ubuntu_kvm_vm(server_args.vgpu_list, server_args.vm_name)
        else:
            vgpu_server.del_all_vgpu_vm_vmware(server_args.vm_name)
            vgpu_server.add_vgpu_to_vm_vmware(server_args.vgpu_list, server_args.vm_name)
        vgpu_server.vm_power_on(server_args.vm_name)
    if server_args.delete_params:
        log.info('the delete tools option is {}'.format(server_args.delete_params))
        if platform.lower() == 'esxi':
            if server_args.delete_params.lower() == 'all':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'debug', 'profile', 'uvm')
            elif server_args.delete_params == 'debug_profile' or server_args.add_params == 'profile_debug':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'debug', 'profile')
            elif server_args.delete_params == 'uvm_profile' or server_args.add_params == 'profile_uvm':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'uvm', 'profile')
            elif server_args.delete_params == 'debug_uvm' or server_args.add_params == 'uvm_debug':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'debug', 'uvm')
            elif server_args.delete_params.lower() == 'debug':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'debug')
            elif server_args.delete_params.lower() == 'profile':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'profile')
            elif server_args.delete_params.lower() == 'uvm':
                vgpu_server.delete_vgpu_params_vmware_from_vm(server_args.vm_name, 'uvm')
            else:
                log.info('please give correct tools option to delete')
                exit(2)
        elif platform.lower() == 'kvm':
            if server_args.delete_params.lower() == 'all':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'all')
            elif server_args.delete_params.lower() == 'debug_profile':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'debug_profile')
            elif server_args.delete_params.lower() == 'debug':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'debug')
            elif server_args.delete_params.lower() == 'profile':
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'profile')
            else:
                vgpu_server.delete_param_kvm_vm(server_args.vgpu, 'uvm')
        else:
            log.info('please give correct params to operate')
    if server_args.add_params and server_args.vgpu:
        if platform.lower() == 'esxi':
            if server_args.add_params.lower() == 'all':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'debug', 'profile', 'uvm')
            elif server_args.add_params.lower() == 'debug_profile' or server_args.add_params.lower() == 'profile_debug':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'debug', 'profile')
            elif server_args.add_params.lower() == 'debug_uvm' or server_args.add_params.lower() == 'debug_uvm':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'debug', 'uvm')
            elif server_args.add_params.lower() == 'uvm_profile' or server_args.add_params.lower() == 'profile_uvm':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'uvm', 'profile')
            elif server_args.add_params.lower() == 'debug':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'debug')
            elif server_args.add_params.lower() == 'profile':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'profile')
            elif server_args.add_params.lower() == 'uvm':
                vgpu_server.add_vgpu_params_vmware(server_args.vm_name, 'uvm')
            else:
                log.info('please give correct tools option to add, thanks...')
                exit(2)
        elif platform.lower() == 'kvm':
            vgpu_server.add_param_kvm_vgpu(server_args.vgpu, server_args.add_params.lower())
        else:
            log.info('please give correct params to operate')
    if server_args.guest_driver and server_args.vm_name and server_args.guest_user and server_args.guest_password:
        if vgpu_server.vm_power_on(server_args.vm_name):
            if server_args.guest_ip:
                log.info('the vm has ip address---{}'.format(server_args.guest_ip))
                guest_ip = server_args.guest_ip
            else:
                ip_list = vgpu_server.get_vm_ip(server_args.vm_name)
                guest_ip = ip_list[0]
                log.info('the vm has ip address---{}'.format(ip_list))
            exist_guest_driver = get_guest_driver(guest_ip, server_args.guest_user, server_args.guest_password)
            if exist_guest_driver is not None:
                guest_driver1 = guest_driver.split('-')[3]
                if re.search(guest_driver1, exist_guest_driver, re.IGNORECASE) is not None:
                    log.info('the driver version is {}, no need install'.format(exist_guest_driver))
                else:
                    log.info('need uninstall driver---{}, then install new driver'.format(exist_guest_driver))
                    prepare_driver(server_args.guest_user, server_args.guest_password, guest_ip, 'guest')
                    uninstall_driver(guest_ip, server_args.guest_user, server_args.guest_password)
                    install_driver(guest_ip, server_args.guest_user, server_args.guest_password, guest_driver, server_args.driver_openRM)
            else:
                prepare_driver(server_args.guest_user, server_args.guest_password, guest_ip, 'guest')
                log.info('there is no driver now, directly install the driver ---{}'.format(guest_driver))
                install_driver(guest_ip, server_args.guest_user, server_args.guest_password, guest_driver, server_args.driver_openRM)
    if server_args.cuda_version and server_args.cuda_type and server_args.vm_name and server_args.guest_user and server_args.guest_password:
        if vgpu_server.vm_power_on(server_args.vm_name):
            if server_args.guest_ip:
                log.info('the vm has ip address---{}'.format(server_args.guest_ip))
                guest_ip = server_args.guest_ip
            else:
                ip_list = vgpu_server.get_vm_ip(server_args.vm_name)
                guest_ip = ip_list[0]
                log.info('the vm has ip address---{}'.format(ip_list))
        if server_args.cuda_date:
            prepare_cuda(guest_ip, server_args.guest_user, server_args.guest_password, cuda_date=server_args.cuda_date)
        elif server_args.cuda_toolkit_download:  # only download toolkit
            if server_args.cuda_date:
                prepare_cuda(guest_ip, server_args.guest_user, server_args.guest_password, cuda_date=server_args.date, cuda_toolkit_download=server_args.cuda_toolkit_download)
            else:  # only download kitpicks or kitbundles latest toolkit
                prepare_cuda(guest_ip, server_args.guest_user, server_args.guest_password, cuda_toolkit_download=server_args.cuda_toolkit_download)
        else:  # downlad latest kitpick or kitbundles package and install
            prepare_cuda(guest_ip, server_args.guest_user, server_args.guest_password)
    if server_args.fwd_driver and server_args.vm_name and server_args.guest_user and server_args.guest_password:
        guest_driver_url = '//builds/linuxbuilds/release/display/VGX-x86_64/{}'.format(server_args.fwd_driver)
        if vgpu_server.vm_power_on(server_args.vm_name):
            if server_args.guest_ip:
                log.info('the vm has ip address---{}'.format(server_args.guest_ip))
                guest_ip = server_args.guest_ip
            else:
                ip_list = vgpu_server.get_vm_ip(server_args.vm_name)
                guest_ip = ip_list[0]
                log.info('the vm has ip address---{}'.format(ip_list))
        prepare_fwd_env(guest_ip, server_args.guest_user, server_args.guest_password, server_args.fwd_driver)
    vgpu_server.disconnect_server()


example_text = """
Example:
python config_server.py -s ************* -p kvm -hu user -hp password -hd 520.18 -so upgrade/install/uninstall     #  upgrade/install/uninstall host driver
python config_server.py -s ************* -p esxi -hu user -hp password -a debug_profile -vg A40-48c # add tools option--debug and profile to A40-48c
python config_server.py -s ************* -p esxi-hu user -hp password -vg v100-16c -vm ubuntu20.04-desktop-auto # modify vgpu of ubuntu20.04-desktop-auto to v100-16c
python config_server.py -s ************* -p kvm -hu user -hp password -vgl h100-20c -vm vm1 #  create a vgpu---h100-20c and assign it to vm1
python config_server.py -s ************* -p kvm -hu user -hp password -rg all  # release all the vgpu in in host 157
python config_server.py -s ************* -p esxi -hu user -hp password -vm ubuntu22.04-desktop-155 -gu lab -gp labuser -gr labuser -vgl t4-8c -gd 535.75   # assign t4-8c to vm, then install the driver
"""
version = '1.0'




