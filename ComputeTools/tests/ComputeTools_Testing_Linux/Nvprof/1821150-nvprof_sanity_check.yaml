Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1821150
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            gpu_family=`echo "${GPU_FAMILY}" | tr '[:upper:]' '[:lower:]'`
            if [[ "${gpu_family}" != "maxwell" && "${gpu_family}" != "pascal" && "${gpu_family}" != "volta" ]]; then
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [[ "$platform" = "dgx" || "$platform" = "power" || "$platform" = "x86" ]]; then
                echo "&&&& Supported Platform $PLATFORM"
            else
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      expected_regex_matches_many:
         - 'Available Events:'
         - 'warp'
      cmd: |
            /usr/local/cuda-${TK_BRANCH}/bin/nvprof --query-events
    - expected_returncode: 0
      expected_regex_matches_many:
         - 'Available Metrics:'
         - 'warp'
      cmd: |
            /usr/local/cuda-${TK_BRANCH}/bin/nvprof --query-metrics
    - expected_returncode: 0
      fail_if_not_regex_matches: 'inst_executed\s+\d+'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof -e inst_executed ./matrixMul
    - expected_returncode: 0
      fail_if_not_regex_matches: 'inst_executed\s+\d+'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof -e all ./asyncAPI
    - expected_returncode: 0
      expected_regex_matches_many:
         - 'gld_efficiency'
         - 'shared_efficiency'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof  -m gld_efficiency,shared_efficiency ./matrixMul
    - expected_returncode: 0
      expected_regex_matches_many:
         - 'Metric Name'
         - 'warp'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof -m all ./cdpAdvancedQuicksort
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof -m all ./UnifiedMemoryStreams
    - expected_returncode: 0
      expected_regex_matches_many:
         - 'Metric Name'
         - 'warp'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof -m all ./batchCUBLAS
    - expected_returncode: 0
      expected_regex_matches_many:
         - '&&&& PASS'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --source-level-analysis global_access,shared_access,branch,instruction_execution,pc_sampling -o ayncAPI.pdm ./asyncAPI
            if [ -f "ayncAPI.pdm" ]; then
               echo "&&&& PASS"
            else
               echo "&&&& FAILED"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
        - 'Error|ERROR'
        - 'FAIL|Fail'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof ./vectorAddMMAP
    - expected_returncode: 0
      fail_if_regex_matches_any:
        - 'Error|ERROR'
        - 'FAIL|Fail'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo $SUDO_PASSWORD | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --profile-child-processes ./memMapIPCDrv
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
