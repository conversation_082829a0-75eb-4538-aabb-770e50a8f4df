Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2188444
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            gpu_family=`echo "${GPU_FAMILY}" | tr '[:upper:]' '[:lower:]'`
            if [[ "${gpu_family}" != "maxwell" && "${gpu_family}" != "pascal" && "${gpu_family}" != "volta" ]]; then
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [[ "$platform" = "dgx" || "$platform" = "power" || "$platform" = "x86" ]]; then
                echo "&&&& Supported Platform $PLATFORM"
            else
                echo "&&&& NA"
            fi
    - expected_returncode: 1
      expected_regex_matches_many:
        - 'm<PERSON><PERSON> could not find anything to do'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            if [ "$PLATFORM" = "Power" ]; then
               export SMPIPATH=/opt/ibm/spectrum_mpi
               export CUDAPATH=/usr/local/cuda
               export PATH=$SMPIPATH/bin:$CUDAPATH/bin:"$PATH"
               export OMPI_CXX=g++
               export OMPI_CC=gcc
            else
               export LD_LIBRARY_PATH=/usr/local/openmpi/lib:$LD_LIBRARY_PATH
               export CUDAPATH=/usr/local/cuda
               export PATH=$CUDAPATH/bin:/usr/local/openmpi/bin:"$PATH"
            fi
            mpirun
    - expected_returncode: 0
      expected_regex_matches_many:
        - 'Range "MPI_Reduce"'
        - 'Range "MPI_Scatter"'
      cmd: |
            if [ "$PLATFORM" = "Power" ]; then
               export LD_LIBRARY_PATH=/usr/local/cuda-${TK_BRANCH}/lib64:$LD_LIBRARY_PATH
               export SMPIPATH=/opt/ibm/spectrum_mpi
               export CUDAPATH=/usr/local/cuda
               export PATH=$SMPIPATH/bin:$CUDAPATH/bin:"$PATH"
               export OMPI_CXX=g++
               export OMPI_CC=gcc
            else
               export LD_LIBRARY_PATH=/usr/local/openmpi/lib:$LD_LIBRARY_PATH
               export CUDAPATH=/usr/local/cuda
               export PATH=$CUDAPATH/bin:/usr/local/openmpi/bin:"$PATH"
            fi
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            mpirun -n 1 nvprof --annotate-mpi openmpi ./simpleMPI
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
