Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1821828
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      timeout: 180m
      fail_if_regex_matches_any:
        - '&&&& FAILED '
      fail_if_not_contains_many:
        - '&&&& PASS for Cupti L0'
        - '&&&& PASS for Cupti L1'
        - '&&&& PASS for Nvprof L0'
        - '&&&& PASS for Nvprof L1'
      cmd: |
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            Extract="tar xvf "
            URL_with_filename=`python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.get_dvs_package('cuda_app', '${platform}', '${TK_BRANCH}')"`
            DVS_package=`echo $URL_with_filename|awk -F"/" '{print $NF}' `
            DVSURL=`echo $URL_with_filename|sed "s/$DVS_package//"`
            echo "url:$DVSURL, package:$DVS_package"
            mkdir HWPM_context_switch
            cd HWPM_context_switch
            rm -rf *
            lftp -c "glob -- pget -n 80 $DVSURL/$DVS_package"
            tar xvf $DVS_package
            tar xvf CUDA-sanity-tests-package.tar.bz2
            cd ./tests/disabled/runtime/cnp_while1
            echo "Start cnp_while1 as background at `pwd`"
            sed -i "s#_CNP_WHILE_PATH_#`pwd`#g" ${SCRIPTS_FOLDER}/loop_run_cnp_while1.sh
            nohup ${SCRIPTS_FOLDER}/loop_run_cnp_while1.sh &
            cd -
            if [ "$platform" = "x86" ] || [ "$platform" = "power" ]; then
                Extract="tar xvf "
            fi
            if [ "$platform" = "arm" ]; then
                Extract="unzip -o "
            fi
            URL_with_filename=`python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.get_dvs_package('cupti', '${platform}', '${TK_BRANCH}')"`
            Cupti_package=`echo $URL_with_filename|awk -F"/" '{print $NF}' `
            CuptiURL=`echo $URL_with_filename|sed "s/$Cupti_package//"`
            echo "url:$CuptiURL, package:$Cupti_package"
            mkdir cupti
            cd cupti
            lftp -c "glob -- pget -n 80 $CuptiURL/$Cupti_package"
            $Extract $Cupti_package
            tar xvf CUDA-AgoraCupti-package.tar.bz2
            cd host/linux-*
            arch=""
            if (( $(echo "${TK_BRANCH} >= 11.6" |bc -l))); then
                  if [ "$platform" = "arm" ]; then
                      arch="--arch=aarch64sbsa"
                  fi
                  python CuptiSmoke.py $arch --testsuite L0 2>&1 | tee cupti-smoke-L0.log
            else
                  if [ "$platform" = "arm" ]; then
                      arch="--cpuarch=aarch64sbsa"
                  fi
                  if [ "$platform" = "power" ]; then
                      arch="--cpuarch=ppc64le"
                  fi
                  perl cupti_smoke.pl $arch --build release --test-list-file=test_list_L0 2>&1 | tee cupti-smoke-L0.log
            fi
            fail_in_l0=`tail -20 cupti-smoke-L0.log| grep 'FAILED' | awk '{print $1}'`
            tmp=0
            known_issues=(
                "&&&& FAILED concurrentKernel"
                "&&&& FAILED concurrentKernel --parallel-launch"
            )
            for i in "${known_issues[@]}"
            do
                    if [ `grep -Fx "$i" cupti-smoke-L0.log|wc -l` -gt "0" ]; then
                            tmp=$(($tmp+1))
                    fi
            done
            fail_in_l0=$(($fail_in_l0-$tmp))
            if [ "$fail_in_l0" = "0" ]; then
                    echo "&&&& PASS for Cupti L0"
            else
                    echo "&&&& FAIL for Cupti L0, Detail log: `pwd`/cupti-smoke-L0.log"
            fi
            echo "==Run Cupti smoke when cnp_while1 running in background"
            if (( $(echo "${TK_BRANCH} >= 11.6" |bc -l))); then
                  python CuptiSmoke.py $arch --testsuite L1 2>&1 | tee cupti-smoke-L1.log
            else
                  echo "replay_multiple -thread -swcounter --ccle=72" > filter_list_L1
                  echo "reg_access_optimization -mode 0 --ccle=72" >> filter_list_L1
                  perl cupti_smoke.pl $arch --build release --test-list-file=test_list_L1 --filter-list-file=filter_list_L1 2>&1 | tee cupti-smoke-L1.log
            fi
            fail_in_l1=`grep '0 FAILED' cupti-smoke-L1.log|wc -l`
            if [ "$fail_in_l1" = "1" ]; then
               echo "&&&& PASS for Cupti L1"
            else
               echo "&&&& FAIL for Cupti L1, Detail log: `pwd`/cupti-smoke-L1.log"
            fi
            cd ../../..
            if [[ "$platform" = "dgx" || "$platform" = "power" || "$platform" = "x86" ]]; then
                echo "&&&& Supported Platform $PLATFORM"
                Extract="tar xvf "
                URL_with_filename=`python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.get_dvs_package('cuda_app', '${platform}', '${TK_BRANCH}')"`
                Nvprof_package=`echo $URL_with_filename|awk -F"/" '{print $NF}' `
                NvprofURL=`echo $URL_with_filename|sed "s/$Nvprof_package//"`
                echo "url:$NvprofURL, package:$Nvprof_package"
                mkdir nvprof
                cd nvprof
                lftp -c "glob -- pget -n 80 $NvprofURL/$Nvprof_package"
                $Extract $Nvprof_package
                7z x CUDA-nvprof-package.tar.7z
                $Extract CUDA-nvprof-package.tar
                cd nvprof/test/
                arch=""
                #if [ "$platform" = "arm" ]; then
                #    arch="--cpuarch=aarch64"
                #fi
                if [ "$platform" = "power" ]; then
                    arch="--cpuarch=ppc64le"
                fi
                echo "==Run Nvprof smoke when cnp_while1 running in background"
                echo $SUDO_PASSWORD | sudo -E -S perl smoke.pl $arch --build release --test-list-file=test_list_L0 2>&1 | tee nvprof-smoke-L0.log
                fail_in_nvprof_l0=`grep 'FA!L3D 0' nvprof-smoke-L0.log|wc -l`
                if [ "$fail_in_nvprof_l0" = "1" ]; then
                   echo "&&&& PASS for Nvprof L0"
                else
                   echo "&&&& FAIL for Nvprof L0, Detail log: `pwd`/nvprof-smoke-L0.log"
                fi
                echo $SUDO_PASSWORD | sudo -E -S perl smoke.pl $arch --build release --test-list-file=test_list_L1 2>&1 | tee nvprof-smoke-L1.log
                fail_in_nvprof_l1=`grep 'FA!L3D 0' nvprof-smoke-L1.log|wc -l`
                if [ "$fail_in_nvprof_l1" = "1" ]; then
                   echo "&&&& PASS for Nvprof L1"
                else
                   echo "&&&& FAIL for Nvprof L1, Detail log: `pwd`/nvprof-smoke-L1.log"
                fi
            else
                echo "&&&& NA"
            fi
            echo $SUDO_PASSWORD | sudo -S ps -ef|grep -E 'cnp_while1|sleep 9999'|grep -v grep|awk '{print $2}'|xargs kill -9
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
