Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3094452
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            grep 'common_case_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            if [ "$?" = 0 ]; then
                sed -i '/common_case_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
                sed -i "/yaml_mgr = /icommon_case_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            else
                sed -i "/yaml_mgr = /icommon_case_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            fi
    - expected_returncode: 0
      na_if_contains: 'platform is arm'
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                echo "platform is arm"
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            vgpu=`echo "${VGPU}" | tr [:upper:] [:lower:]`
            major=$(echo $TK_VER|awk -F "." '{print $1}')
            minor=$(echo $TK_VER|awk -F "." '{print $2}')
            cuda_short_version=$major.$minor
            if [ "$vgpu" = "none" ]; then
                export hpc_folder=hpc_sdk
                export PGI=/opt/nvidia/$hpc_folder
                if [ "$cuda_short_version" -le "12.1" ]
                then
                  export pgi_version=22.11
                  export pgi_version1=2211
                else
                  export pgi_version=25.5
                  export pgi_version1=255
                fi
                platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
                if ! ls /opt/nvidia/$hpc_folder/*/$pgi_version 1> /dev/null 2>&1; then
                        echo "Install PGI.."
                        mkdir pgi
                        cd pgi
                        rm -rf *
                        if [ "$platform" = "x86" ]; then
                            wget --no-check-certificate http://$CQA_FS01_USERNAME:$<EMAIL>/Packages/PGI/${pgi_version}/x86_64/nvhpc_2025_${pgi_version1}_Linux_x86_64_cuda_multi.tar.gz
                        fi
                        if [ "$platform" = "arm" ]; then
                            wget --no-check-certificate http://$CQA_FS01_USERNAME:$<EMAIL>/Packages/PGI/${pgi_version}/aarch64/nvhpc_2025_${pgi_version1}_Linux_aarch64_cuda_multi.tar.gz
                        fi
                        tar xvf nvhpc_2025_${pgi_version1}_Linux_*.tar.gz
                        cd `ls|grep tar.gz| cut -d"." -f1`
                        sed -i "s/paassword/$SUDO_PASSWORD/g" ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py
                        python3 ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py -s "sudo ./install"
                fi
            fi
    - expected_returncode: 0
      na_if_contains: 'not support on ARM'
      cmd: |
            python3 ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py -e tools_support_pgi_3094452
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      fail_if_contains: '"passed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/common_case_device${CUDA_VISIBLE_DEVICES}/3094452_tools_support_pgi_result_$date1/tools_support_pgi.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
