Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3288716
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            grep 'common_case_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            if [ "$?" = 0 ]; then
                sed -i '/common_case_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
                sed -i "/yaml_mgr = /icommon_case_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            else
                sed -i "/yaml_mgr = /icommon_case_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml
            fi
    - expected_returncode: 0
      na_if_contains: "not support this case if cuda version less than 12.2"
      cmd: |
            python3 ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_common_case.py -e tools_support_split_arrive_wait_3288716
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      fail_if_contains: '"passed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/common_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/common_case_device${CUDA_VISIBLE_DEVICES}/3288716_tools_support_split_arrive_wait_result_$date1/tools_support_split_arrive_wait.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
