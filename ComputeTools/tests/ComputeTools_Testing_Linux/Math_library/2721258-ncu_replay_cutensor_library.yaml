Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2721258
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml
    - expected_returncode: 0
      cmd: |
        script_path=${SCRIPT_DIR}
        ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml
        grep 'math_lib_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cublas_case.py
        if [ "$?" = 0 ]; then
            sed -i '/math_lib_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cublas_case.py
            sed -i "/yaml_mgr = /imath_lib_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cublas_case.py
        else
            sed -i "/yaml_mgr = /imath_lib_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cublas_case.py
        fi
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml
            grep 'math_lib_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cutensor_case.py
            if [ "$?" = 0 ]; then
                sed -i '/math_lib_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cutensor_case.py
                sed -i "/yaml_mgr = /imath_lib_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cutensor_case.py
            else
                sed -i "/yaml_mgr = /imath_lib_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cutensor_case.py
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#x86_64#ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cutensor_case.py -t ncu -o replay
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/math_lib_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/math_library_device${CUDA_VISIBLE_DEVICES}/ncu_cutensor_result_$date1/ncu_cutensor_replay.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
