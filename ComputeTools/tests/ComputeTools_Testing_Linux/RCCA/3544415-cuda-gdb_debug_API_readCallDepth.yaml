Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3544415
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            mkdir ${TEST_WS}/3544415
            cd ${TEST_WS}/3544415
            rm -rf *
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [[ "$platform" = "arm" ]]; then
                wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Automation/CUDA_Linux/Sources/ComputeTools/3544415/tx_cuda_many_kernels_ARM
                chmod +x *
                python3 ${SCRIPTS_FOLDER}/cuda-gdb_debug_API_readCallDepth.py -s "cuda-gdb ./tx_cuda_many_kernels_ARM"
            elif [[ "$platform" = "x86" ]]; then
                wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Automation/CUDA_Linux/Sources/ComputeTools/3544415/tx_cuda_many_kernels_x64
                chmod +x *
                python3 ${SCRIPTS_FOLDER}/cuda-gdb_debug_API_readCallDepth.py -s "cuda-gdb ./tx_cuda_many_kernels_x64"
            else
                echo "Make sure Platform is arm or x86"
            fi          
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
