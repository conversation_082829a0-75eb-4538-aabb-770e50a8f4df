Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3564429
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            export hpc_folder=hpc_sdk
            export PGI=/opt/nvidia/$hpc_folder
            if [ "$TK_BRANCH" -le "12.9" ]
                then
                  export pgi_version=25.1
                  export pgi_version1=251
            else
                  export pgi_version=25.5
                  export pgi_version1=255
            fi
            distro=`echo ${DISTRO} | tr '[:upper:]' '[:lower:]'`
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [ "$VGPU" = "True" -a "$platform" = "x86" ]; then
                if [ $distro = 'ubuntu' ]; then
                    echo $SUDO_PASSWORD | sudo -S ${PKG_CMD} install -y nfs-kernel-server
                fi
                if [ $distro = 'rhel' ] || [ $distro = 'centos' ]; then
                    echo $SUDO_PASSWORD | sudo -S ${PKG_CMD} install -y nfs-utils
                fi
                echo "Mount network disk to /opt/nvidia"
                echo $SUDO_PASSWORD | sudo -S mkdir -p /opt/nvidia
                echo $SUDO_PASSWORD | sudo -S umount /opt/nvidia
                echo $SUDO_PASSWORD | sudo -S mount 10.19.173.11:/opt/nvidia /opt/nvidia
            else
                platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
                if ! ls /opt/nvidia/$hpc_folder/*/$pgi_version 1> /dev/null 2>&1; then
                        echo "Install PGI.."
                        mkdir pgi
                        cd pgi
                        rm -rf *
                        if [ "$platform" = "x86" ]; then
                            wget --no-check-certificate http://$CQA_FS01_USERNAME:$<EMAIL>/Packages/PGI/${pgi_version}/x86_64/nvhpc_2025_${pgi_version1}_Linux_x86_64_cuda_multi.tar.gz
                        fi
                        if [ "$platform" = "arm" ]; then
                            wget --no-check-certificate http://$CQA_FS01_USERNAME:$<EMAIL>/Packages/PGI/${pgi_version}/aarch64/nvhpc_2025_${pgi_version1}_Linux_aarch64_cuda_multi.tar.gz
                        fi
                        tar xvf nvhpc_2025_${pgi_version1}_Linux_*.tar.gz
                        cd `ls|grep tar.gz| cut -d"." -f1`
                        sed -i "s/paassword/$SUDO_PASSWORD/g" ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py
                        python3 ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py -s "sudo ./install"
                fi
            fi
            echo "Preparing env for PGI"
            if [ "$platform" = "x86" ]; then
                export PATH=/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/man/:"$MANPATH"
            fi
            if [ "$platform" = "arm" ]; then
                export PATH=/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/man/:"$MANPATH"
            fi
            if [ "$platform" = "power" ]; then
                export PATH=${CUDA_PATH}/bin:/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/man/:"$MANPATH"
            fi
            mkdir ${TEST_WS}/3564429
            cd ${TEST_WS}/3564429
            rm -rf *
            wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Compute_Tools/ComputeToolsTest/P1072_T3564429/repro.cuf
            nvfortran -g -cuda repro.cuf -o nv.out
            if [ -f "nv.out" ]; then
               echo "Compile successfully!"
            else
               echo "[Error]: Fail to compile! Please check!"
            fi
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_Debug_CUDA_Fortran_code_compiled_by_NVFortran_compiler.py -s "${CUDA_PATH}/bin/cuda-gdb ./nv.out"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
