Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3825550
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml
            grep 'cuda_gdb_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cuda_gdb_case.py
            if [ "$?" = 0 ]; then
                sed -i '/cuda_gdb_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cuda_gdb_case.py
                sed -i "/yaml_mgr = /icuda_gdb_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cuda_gdb_case.py
            else
                sed -i "/yaml_mgr = /icuda_gdb_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cuda_gdb_case.py
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml
            fi
    - expected_returncode: 0
      na_if_contains: 'we support this case since 12.7'
      cmd: |
            python3 ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cuda_gdb_case.py -e cuda_gdb_rcca_3825550
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cuda_gdb_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/cuda_gdb_device${CUDA_VISIBLE_DEVICES}/3825550_cuda_gdb_rcca_3825550_result_$date1/cuda_gdb_rcca_3825550.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
