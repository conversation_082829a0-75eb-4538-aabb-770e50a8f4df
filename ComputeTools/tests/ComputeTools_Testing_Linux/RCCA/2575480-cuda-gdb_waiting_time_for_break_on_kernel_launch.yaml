Settings:
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2575480
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAILED'
      cmd: |
            mkdir 2410009_perf_break
            cd 2410009_perf_break
            wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Automation/CUDA_Linux/Sources/ComputeTools/2410009_perf_break/perf_break.cu
            wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Automation/CUDA_Linux/Sources/ComputeTools/2410009_perf_break/script.gdb
            ${CUDA_PATH}/bin/nvcc -o perf_break  perf_break.cu
            if [ -f "perf_break" ]; then
                echo "&&&& PASS"
            else
                echo "&&&& FAILED"
            fi
    - expected_returncode: 0
      fail_if_contains: "&&&& FAIL"
      cmd: |
           cd 2410009_perf_break
           python3 ${SCRIPTS_FOLDER}/cuda-gdb_waiting_time_for_break_on_kernel_launch.py -s "cuda-gdb -batch-silent ./perf_break -x script.gdb"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
