Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2819146
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/debug/
            if [ -x "matrixMul" ]; then
                echo "&&&& PASS"
            else
                echo "&&&& FAIL"
            fi
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_cudbgprocess_exit_if_RPC_client_exit.py -s "cuda-gdb ./matrixMul"
            ps -ef|grep exception_err|grep -v grep|awk '{print $2}'|xargs kill -9
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
