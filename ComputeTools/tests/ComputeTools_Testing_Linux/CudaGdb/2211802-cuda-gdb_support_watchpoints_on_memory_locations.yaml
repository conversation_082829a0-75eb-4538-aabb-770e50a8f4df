Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2211802
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            mkdir 2420229
            cd 2420229
            wget --no-check-certificate http://$CQA_FS01_USERNAME:$CQA_FS01_PASSWORD@cqa-fs01/Compute_Tools/ComputeToolsTest/P1072_T2211802/test.cu
            ${CUDA_PATH}/bin/nvcc -g test.cu
            if [ -f "a.out" ]; then
                echo "&&&& PASS to build a.out"
            else
                echo "&&&& FAILED to build a.out"
            fi
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_support_watchpoints_on_memory_locations.py -s "cuda-gdb -q ./a.out"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
