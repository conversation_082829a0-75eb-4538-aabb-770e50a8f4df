Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3532274
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            grep 'sanitizer_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            if [ "$?" = 0 ]; then
                sed -i '/sanitizer_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
                sed -i "/yaml_mgr = /isanitizer_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            else
                sed -i "/yaml_mgr = /isanitizer_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#x86_64#aarch64#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            fi
    - expected_returncode: 0
      na_if_contains: "we only support this case since cuda 12.2"
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py -e sanitizer_support_multi_ctx_coredump_3532274
    - expected_returncode: 0
      timeout: 1200s
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/sanitizer_device${CUDA_VISIBLE_DEVICES}/3532274_sanitizer_support_multi_ctx_coredump_result_$date1/sanitizer_support_multi_ctx_coredump.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
