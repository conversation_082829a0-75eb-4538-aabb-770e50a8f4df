Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2819761
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            bash ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            grep 'sanitizer_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            if [ "$?" = 0 ]; then
                sed -i '/sanitizer_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
                sed -i "/yaml_mgr = /isanitizer_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            else
                sed -i "/yaml_mgr = /isanitizer_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
        toolkit=`echo "$TK_BRANCH"`
        echo "$toolkit"
        toolkit1='11.5'
        a=`echo "$toolkit<$toolkit1" | bc`
        if [ $a -eq 1 ]; then
            echo "&&&& NA"
        fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_sanitizer_case.py -e detail_session_option
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/sanitizer_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/sanitizer_device${CUDA_VISIBLE_DEVICES}/2819761_detail_session_option_result_$date1/detail_session_option.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
