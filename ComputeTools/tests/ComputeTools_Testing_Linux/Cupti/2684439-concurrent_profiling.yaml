Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2684439
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml
            grep 'cupti_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cupti_case.py
            if [ "$?" = 0 ]; then
                sed -i '/cupti_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cupti_case.py
                sed -i "/yaml_mgr = /icupti_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cupti_case.py
            else
                sed -i "/yaml_mgr = /icupti_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cupti_case.py
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
        toolkit=`echo "$TK_BRANCH"`
        echo "$toolkit"
        toolkit1='11.4'
        a=`echo "$toolkit<$toolkit1" | bc`
        if [ $a -eq 1 ]; then
            echo "&&&& NA"
        fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#bin/x86_64#bin/sbsa#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_cupti_case.py -e concurrent_profiling
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/cupti_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/cupti_device${CUDA_VISIBLE_DEVICES}/2684439_concurrent_profiling_result_$date1/concurrent_profiling.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
