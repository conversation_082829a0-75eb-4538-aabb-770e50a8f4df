Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2930791
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml
            sample=`grep SAMPLE: ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml`
            optix_sample=`grep SAMPLE_OPTIX: ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml`
            sed -i "s#${sample}#    SAMPLE: graphic#g" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml
            sed -i "s#${optix_sample}#    SAMPLE_OPTIX: 0#g" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml
            grep 'optix_coverage_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/graphic_testing.py
            if [ "$?" = 0 ]; then
                sed -i '/optix_coverage_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/graphic_testing.py
                sed -i "/yaml_mgr = /ioptix_coverage_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/graphic_testing.py
            else
                sed -i "/yaml_mgr = /ioptix_coverage_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/graphic_testing.py
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                echo '&&&& NA'
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                sed -i "s#x86_64#aarch64#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/graphic_testing.py -t ncu
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/optix_coverage.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/graphic_coverage_device${CUDA_VISIBLE_DEVICES}/ncu_graphic_result_$date1/ncu_graphic.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
