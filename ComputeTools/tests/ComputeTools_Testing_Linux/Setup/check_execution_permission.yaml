Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1821318
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/setup_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/setup_case.yaml
            grep 'setup_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/check_setup_case.py
            if [ "$?" = 0 ]; then
                sed -i '/setup_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/check_setup_case.py
                sed -i "/yaml_mgr = /isetup_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/setup_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/check_setup_case.py
            else
                sed -i "/yaml_mgr = /isetup_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/setup_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/check_setup_case.py
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/check_setup_case.py -e check_execution_permission
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/setup_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/setup/1821318_check_execution_permission_result_$date1/check_execution_permission.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |