Settings:
  env: ~
  enabled: True
  print_instance: True

Tests: !!seq
  - name: 2898531-ncu_gromacs_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898539-ncu_gromacs_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898540-ncu_gromacs_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898522-san_gromacs_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898530-san_gromacs_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898541-cupti_gromacs_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898542-cupti_gromacs_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898545-ncu_lammps_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898546-ncu_lammps_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898547-ncu_lammps_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898543-san_lammps_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898544-san_lammps_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898553-cupti_lammps_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898554-cupti_lammps_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898562-ncu_nemo_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898563-ncu_nemo_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898564-ncu_nemo_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898555-san_nemo_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898561-san_nemo_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898565-cupti_nemo_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898566-cupti_nemo_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898579-ncu_clara_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898580-ncu_clara_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898581-ncu_clara_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898577-san_clara_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898578-san_clara_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898582-cupti_clara_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898583-cupti_clara_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721444-ncu_tfv1_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721445-ncu_tfv1_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721446-ncu_tfv1_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721438-san_tfv1_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721439-san_tfv1_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721442-cupti_tfv1_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2721443-cupti_tfv1_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898586-ncu_tfv2_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898587-ncu_tfv2_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898588-ncu_tfv2_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898584-san_tfv2_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898585-san_tfv2_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898589-cupti_tfv2_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898590-cupti_tfv2_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898657-ncu_mxnet_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898658-ncu_mxnet_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898659-ncu_mxnet_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898655-san_mxnet_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898656-san_mxnet_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898660-cupti_mxnet_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898661-cupti_mxnet_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898664-ncu_tsrt_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898722-ncu_tsrt_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898723-ncu_tsrt_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898662-san_tsrt_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898663-san_tsrt_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898724-cupti_tsrt_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898725-cupti_tsrt_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898593-ncu_pytorch_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898614-ncu_pytorch_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898652-ncu_pytorch_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898591-san_pytorch_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898592-san_pytorch_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898653-cupti_pytorch_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 2898654-cupti_pytorch_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107538-cupti_tfv2_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107543-cupti_spark_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107535-cupti_nemo_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107536-cupti_lammps_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107532-cupti_gromacs_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107542-cupti_tsrt_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107540-cupti_pytorch_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107541-cupti_mxnet_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107537-cupti_tfv1_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107539-cupti_clara_pcsampling
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107565-ncu_spark_multipassapplicationreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107564-ncu_spark_singlepasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107561-cupti_spark_traceinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107563-ncu_spark_multipasskernelreplay
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107559-san_spark_memcheckleakcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107560-san_spark_initcheck
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3107562-cupti_spark_profileinjection
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3178913-cudagdb_gromacs_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3182256-cudagdb_gromacs_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3190729-cudagdb_lammps_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3190730-cudagdb_lammps_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191245-cudagdb_nemo_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191246-cudagdb_nemo_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191247-cudagdb_tfv1_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191248-cudagdb_tfv1_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191249-cudagdb_tfv2_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191250-cudagdb_tfv2_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191251-cudagdb_clara_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191252-cudagdb_clara_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191253-cudagdb_mxnet_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191254-cudagdb_mxnet_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191255-cudagdb_tsrt_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191286-cudagdb_tsrt_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
  - name: 3191256-cudagdb_pytorch_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191290-cudagdb_pytorch_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191257-cudagdb_spark_overhead
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3191258-cudagdb_spark_sanity
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3305264-san_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3310125-cupti_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3383937-cupti_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3638194-ncu_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3292084-cupti_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: 3683025-cupti_pytorch_rcca
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~

