Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2898664
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            scriptModify="/raid/devtools/dl_hpc_tools_testing_v3/util/hpc_dl_split.py"
            echo ${SUDO_PASSWORD} | sudo -E -S python3 ${scriptModify} -s tsrt -t ncu -o multipass
    - expected_returncode: 0
      cmd: |
            cd /raid/devtools/dl_hpc_tools_testing_v3/case
            echo ${SUDO_PASSWORD} | sudo -E -S nohup bash -x run_all.bash &
    - expected_returncode: 0
      expected_contains: "PASSED"
      cmd: |
            cd /raid/devtools/dl_hpc_tools_testing_v3/case
            res=`echo ${SUDO_PASSWORD} | sudo -E -S python3 -c "import for_portal;print(for_portal.main('/raid/devtools/dl_hpc_tools_testing_v3/log'))"`
            
            if ( echo $res |grep -qw "PASS" )
            then
                echo "PASSED"
            else
                echo "FAILED"
                echo "Please check /raid/devtools/dl_hpc_tools_testing_v3/log for details info"
                exit -1
            fi
  teardown: !!seq  
    - expected_returncode: 0
      cmd: |
