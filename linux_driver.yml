### Extra vars to pass in:
# driver: driver version
# tester_user: username to download driver
# tester_password: password of tester_user
# hqnvhwy: which nvhighway to use
# chip: gpu chip
# app: testing app name

- name: Install driver on x86 linux
  hosts: all
  become: yes
  gather_facts: yes
  vars:
    driver_version_file: '/etc/dev_driver_version'
    devDriver: false
    needReinstall: false

  tasks:
    - debug:
        var: ansible_architecture

    - name: Define arch for driver installation
      set_fact:
        arch: "{% if ansible_architecture == 'x86_64' %}x86_64{% elif ansible_architecture == 'aarch64' %}aarch64{% elif ansible_architecture == 'ppc64le' %}ppc64le{% endif %}"

    - name: Set install_cmd 
      set_fact:
        install_cmd: 'NVIDIA-Linux-{{ arch }}-{{ driver }}.run -s -c -X -n -b --no-check-for-alternate-installs --install-libglvnd' 

    - name: set devDriver flag for develop branch driver
      set_fact:
        devDriver: true
      when: "'chips_a' in '{{ driver }}' or 'cuda_a' in '{{ driver }}' or 'virtual' in '{{ driver | lower}}'"
    
    - name: get installed driver
      shell: nvidia-smi -q | grep "Driver Version" | cut -d ':' -f 2 | tr -d ' '
      register: installed_driver

    - name: check if need reinstall driver for develop driver
      block:
        - name: touch {{ driver_version_file }} if not exists
          file: 
            path: "{{ driver_version_file }}"
            state: touch
        
        - name: get installed dev driver from {{ driver_version_file }}
          shell: cat {{ driver_version_file }}
          register: driverinfo
        
        - name: set needReinstall when driver mismatch or no driver for dev driver branch
          set_fact:
            needReinstall: true
          when: driverinfo.stdout != driver or installed_driver.stdout |length == 0
      when: devDriver
    
    - name: check if need reinstall driver for release driver
      set_fact:
        needReinstall: true
      when: not devDriver and installed_driver.stdout != driver|string

    - debug:
        var: needReinstall

    - debug:
        var: driver

    - name: uninstall the driver and remove nvidia stuff
      block:
        - name: check if has nvidia-uninstall command
          shell: which nvidia-uninstall
          register: nvidia_uninstallmsg
          ignore_errors: true
        
        - name: uninstall driver with apt remove
          shell: apt-get remove --purge -y "*nvidia*" --allow-change-held-packages
          when: installed_driver.stdout |length > 0 and nvidia_uninstallmsg is failure and (ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian') 
        
        - name: Uninstall nvidia driver from nvidia-uninstall
          shell: nvidia-uninstall --silent
          when: installed_driver.stdout |length > 0 and nvidia_uninstallmsg.rc == 0
        
        - name: check {{ driver_version_file }} stat
          stat:
            path: "{{ driver_version_file }}"
          register: devdriver_file_stat

        - name: clean {{ driver_version_file }} after uninstall if exists  # driver switch from dev->release
          lineinfile:
            path: "{{ driver_version_file }}"
            state: absent
            regexp: '.*'
          when: devdriver_file_stat.stat.exists
        
        - name: Check Nouveau presence
          shell: lsmod | grep nouveau
          ignore_errors: yes
          register: nouveau
        
        - name: Exclude Nouveau and reboot after disable
          block:
            - name: add blacklist nouveau in conf file
              shell: >
                echo blacklist nouveau > /etc/modprobe.d/exclude-nvidia-nouveau.conf &&
                echo options nouveau modeset=0 >> /etc/modprobe.d/exclude-nvidia-nouveau.conf
            
            - name: update-initramfs for ubuntu
              shell: update-initramfs -u
              when: ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian'
        
            - name:  dracut update for redhat
              shell: dracut --force
              when: ansible_distribution == 'RedHat' or ansible_distribution == 'Rocky' or ansible_distribution == 'Fedora'
          when: nouveau.stdout|length > 0
        
        - name: reboot after driver uninstall
          reboot:
            reboot_timeout: 1800 
          when: installed_driver.stdout |length > 0

        - name: Cleanup nvidia driver stuff
          shell: >
            init 3 &&
            (pkill -9 nsys || echo "No nsys is running" ) &&
            (pkill -9 nvidia* || echo "No nvidia* processes are running")
        
        - name: rmmod nvidia_uvm nvidia_drm nvidia_modeset nvidia
          vars:
            modules: [nvidia_uvm, nvidia_drm, nvidia_modeset, nvidia]
          modprobe: name="{{ item }}" state=absent   
          with_items: "{{ modules }}"  
          ignore_errors: yes 
      when: needReinstall
    
    - name: Install test driver {{ driver }}
      block:
        - name: apt install lftp and driver prerequisites on ubuntu
          apt:
            name:
              - lftp
              - pkg-config
              - libglvnd-dev
              - "linux-headers-{{ ansible_kernel }}"
            state: present
            update_cache: true
          become: yes
          when: ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian'

        - name: dnf install lftp and driver prerequisites on Redhat / Rocky
          dnf:
            name:
              - lftp
              - libglvnd-devel
              - '@Development Tools'
          become: yes
          when: ansible_distribution == 'RedHat' or ansible_distribution == 'Rocky' or ansible_distribution == 'Fedora'
        
        - name: install with open-rm for hopper by default
          set_fact:
            install_cmd: 'NVIDIA-Linux-{{ arch }}-{{ driver }}.run -s -c -X -n -b --no-check-for-alternate-installs --install-libglvnd -m=kernel-open'
          when: '"GH100" in "{{ chip }}" and "cpurm" not in "{{ chip|lower }}" ' 
        
        - name: Download/Install release driver (x86 only)
          shell: >
            rm -rf NVIDIA-Linux-* &&
            lftp -u "{{ tester_user }},{{ tester_password }}" -e
            "set ftp:ssl-allow no;
            pget -n 10 /builds/linuxbuilds/release/display/{{ arch }}/{{ driver }}/NVIDIA-Linux-{{ arch }}-{{ driver }}.run;
            quit" {{ hqnvhwy }}.nvidia.com &&
            chmod +x NVIDIA-Linux-{{ arch }}-{{ driver }}.run && (bash NVIDIA-Linux-{{ arch }}-{{ driver }}.run -s -c -n -b --uninstall;
            bash {{ install_cmd }} )
          args:
            chdir: /tmp
          when: not devDriver
        
        #\\builds\unixbuilds\daily\display\x86_64\dev\gpu_drv\cuda_a\20220906_31776335\NVIDIA-Linux-x86_64-dev_gpu_drv_cuda_a-20220906_31776335.run
        - name: install dev branch driver cuda_a or chips_a
          block:
          - name: retrieve dev driver date
            debug:
              msg: "{{ driver | regex_replace('.*-([\\d_]+)$', '\\1') }}"
            register: driver_date
          
          - name: retrieve dev driver branch
            debug:
              msg: "{{ driver | regex_replace('^(.*)-([\\d_]+)$', '\\1') }}"
            register: dev_branch

          - name: download & install cuda_a or chips_a driver
            shell: >
              rm -rf NVIDIA-Linux-* &&
              lftp -u "{{ tester_user }},{{ tester_password }}" -e
              "set ftp:ssl-allow no;
              pget -n 10 /builds/linuxbuilds/daily/display/{{ arch }}/dev/gpu_drv/{{ dev_branch.msg }}/{{ driver_date.msg }}//NVIDIA-Linux-{{ arch }}-dev_gpu_drv_{{ driver }}.run;
              quit" {{ hqnvhwy }}.nvidia.com &&
              chmod +x NVIDIA-Linux-{{ arch }}-dev_gpu_drv_{{ driver }}.run && (bash NVIDIA-Linux-{{ arch }}-dev_gpu_drv_{{ driver }}.run -s -c -n -b --uninstall;
              bash NVIDIA-Linux-{{ arch }}-dev_gpu_drv_{{ driver }}.run  -s -c -X -n -b --no-check-for-alternate-installs --install-libglvnd)
            args:
              chdir: /tmp
          when: devDriver and 'virtual' not in driver|lower
        
        - name: download and install virtual build from qafarm-sh01
          shell: rm -rf {{ driver }}.run && wget http://*************:10080/perfworks/virtual_drivers/{{ driver }}.run && chmod a+x {{ driver }}.run && bash {{ driver }}.run -s -c -X -n -b --no-check-for-alternate-installs --install-libglvnd
          args:
            chdir: /tmp 
          when: "'virtual' in driver|lower"
            
        - name: get driver version after install
          shell: nvidia-smi -q | grep "Driver Version" | cut -d ':' -f 2 | tr -d ' '
          register: current_driver
        
        - name: save the dev driver to {{ driver_version_file }}
          lineinfile:
            path: "{{ driver_version_file }}"
            line: "{{ driver }}"
          when: current_driver.stdout |length > 0 and devDriver
        
        - name: reboot after driver reinstallation
          reboot:
            reboot_timeout: 1800
      when: needReinstall
    
    # use `nvidia-smi -pm 1 instead of nvidia-persistenced which is unstable on GH100 ` 
    - name: Disable MIG
      include_tasks: colossus_ansiblebooks/perfworks/config_tasks/disable_mig.yml
      when: '"GH100" in "{{ chip }}" or "GA100" in "{{ chip }}" ' 