---
- name: Ansible playbook for setting up environment for nsys
  hosts: all
  vars_prompt:
    - name: "branch"
      prompt: "Enter the branch (bringup or main)"
      private: no
      confirm: no
    - name: "build"
      prompt: "Enter the build number (YYYY.V.V.VV)"
      private: no
      confirm: no
  vars:
    - target_mac_address: "00:b0:2d:5d:26:4e"
    - bsp_type: "daily"
    - app_name: "Dev_QuadDBringup_drive"
  
  tasks:
    - name: Set variables (QNX)
      set_fact:
        board_user: "root"
        board_password: "root"
        samples: "qnx"
      when: os_name == "qnx"
    
    - name: Set variables (Linux)
      set_fact:
        board_user: "nvidia"
        board_password: "nvidia"
        samples: "l4t"
      when: os_name == "linux"

    - name: Set 777 mode to /dev/ttyACM1
      shell: chmod 777 /dev/ttyACM1
      become: yes 

    - name: Install docker
      apt:
        name: 
          - docker.io
          - sshpass
          - arp-scan
          - lftp
        state: present
      become: yes

    - name: Login to docker
      shell: docker login nvcr.io
      become: yes

    - name: Create docker image name (daily)
      set_fact:
        image_name: "nvcr.io/nv-drive-internal/driveos-pdk-dev/drive-agx-orin-{{ os_name }}-aarch64-pdk-build-x86-internal:{{ drive_bsp_branch }}-latest" 
      when: bsp_type == "daily"

    - name: Create docker image name (release)
      set_fact:
        image_name: "nvcr.io/nv-drive-internal/driveos-pdk-qa/drive-agx-orin-{{ os_name }}-aarch64-pdk-build-x86-internal:{{ drive_bsp_branch }}-latest" 
      when: bsp_type == "release"
    

    #- name: Pull the latest image
    #  shell: "docker pull {{ image_name }}" 
    #  become: yes

    - name: Aurixreset before flashing
      shell: "echo rsaurixreset > /dev/ttyACM1"
      become: yes 

    - name: Wait for aurix to boot
      pause:
        seconds: 60
    
    - name: Flash device (QNX)
      shell: docker run --rm --privileged --net host -v /dev/bus/usb:/dev/bus/usb -v /tmp:/drive_flashing {{ image_name }} -c 'cp -r /drive/drive-{{ os_name }}/samples /drive_flashing && cp -r /drive/drive-{{ os_name }}/lib-target /drive_flashing && ./flash.py /dev/ttyACM1 p3710'
      timeout: 14400
      become: yes
      when: os_name == "qnx"

    - name: Flash device (Linux + bypass OEM screen)
      shell: docker run --rm --privileged --net host -v /dev/bus/usb:/dev/bus/usb -v /tmp:/drive_flashing {{ image_name }} -c 'cp -r /drive/drive-{{ os_name }}/samples /drive_flashing && cp -r /drive/drive-{{ os_name }}/lib-target /drive_flashing && sudo mount "$NV_WORKSPACE/drive-linux/filesystem/targetfs.img" "$NV_WORKSPACE/drive-linux/filesystem/targetfs" && sudo touch "$NV_WORKSPACE/drive-linux/filesystem/targetfs/etc/nvidia/oem-config/oem_config_completed" "$NV_WORKSPACE/drive-linux/filesystem/targetfs/etc/nvidia/persistent_partitions_disabled" && sudo umount "$NV_WORKSPACE/drive-linux/filesystem/targetfs" && ./flash.py /dev/ttyACM1 p3710'
      timeout: 14400
      become: yes
      when: os_name == "linux"

    - name: Wait for board to boot
      pause:
        seconds: 120
    
    - name: Run arp command
      shell: arp-scan -l | grep {{ target_mac_address }} | cut -f1
      become: yes
      register: arp_output
    
    - name: Set board_ip
      set_fact: 
        board_ip: "{{ arp_output.stdout }}"

    - name: Display matched IP address
      debug:
        var: board_ip

    - name: Set LFTP branch name (6.0.9.x)
      set_fact:
        lftp_bsp: "6.0-safety"
      when: (drive_bsp_branch == "*******") or (drive_bsp_branch == "*******")

    - name: Set LFTP branch name (non-6.0.9.x)
      set_fact:
        lftp_bsp: "{{ drive_bsp_branch }}"
      when: (drive_bsp_branch != "*******") and (drive_bsp_branch != "*******")
        

    - name: Get list of firmwares
      shell: "lftp -u devtools_tester1,Dev@Tools%0606 hqnvhwy02.nvidia.com -e 'set ftp:ssl-allow no;ls /builds/linuxbuilds/{{ bsp_type }}/Embedded/BuildBrain/SDK/{{ os_name }}/embedded-{{ lftp_bsp }} > /tmp/ls_output;exit'"

    - name: Get latest version of branch
      shell: "cat /tmp/ls_output | sort | tail -n 1 | awk '{print $NF}'"
      register: bsp_version_output

    - name: Set BSP version
      set_fact:
        bsp_version: "{{ bsp_version_output.stdout }}"

    - name: Download nvplayfair
      shell: "cd /tmp && lftp -u devtools_tester1,Dev@Tools%0606 hqnvhwy02.nvidia.com -e 'set ssl:verify-certificate no;cd /builds/linuxbuilds/{{ bsp_type }}/Embedded/BuildBrain/SDK/{{ os_name }}/embedded-{{ lftp_bsp }}/{{ bsp_version }}/public;mget *playfair*;exit'"
      ignore_errors: yes

    - name: Unpack nvplayfair
      shell: "cd /tmp && ar -x /tmp/*playfair* && tar xvf data.tar.xz"
      ignore_errors: yes

    - name: Find nvplayfair library
      command: "find /tmp -name 'libnvplayfair.so'"
      register: playfair_search_result
      become: yes
      ignore_errors: yes

    - name: Set playfair path
      set_fact:
        playfair: "{{ playfair_search_result.stdout_lines[0] }}"
      ignore_errors: yes

    - name: Copy playfair library to lib-target
      shell: "cp {{ playfair }} /tmp/lib-target" 
      become: yes
      ignore_errors: yes

    - name: Prepare the QNX target
      shell: "sshpass -p {{ board_password }} ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {{ board_user }}@{{ board_ip }} \"mkqnx6fs -q /dev/vblk_ufs40 && mount /dev/vblk_ufs40 && mkdir -p /root /tmp /opt\""
      when: samples == "qnx"

    - name: Create target directory on QNX
      shell: "sshpass -p {{ board_password }} ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {{ board_user }}@{{ board_ip }} \"mkdir -p /root/target/qa /root/target/reports\""
      when: samples == "qnx"
    
    - name: Create target directory on Linux
      shell: "sshpass -p {{ board_password }} ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {{ board_user }}@{{ board_ip }} \"mkdir -p /home/<USER>/target/qa /home/<USER>/target/reports\""
      when: samples == "l4t"

    - name: Copy samples on host
      shell: "docker run --rm --privileged --net host -v /dev/bus/usb:/dev/bus/usb -v /tmp:/drive_flashing {{ image_name }} -c 'cp -r /drive/drive-{{ os_name }}/samples /drive_flashing/samples'"
      become: yes 

    - name: Copy samples from host to target
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/samples {{ board_user }}@{{ board_ip }}:"

    - name: Copy lib-target from host to target
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/lib-target {{ board_user }}@{{ board_ip }}:"

    - name: Remove samples 
      file:
        name: /tmp/samples 
        state: absent
      become: yes
      
    - name: Download samples pack
      get_url:
        url: http://devtools-qa-njtrack.nvidia.com:8080/fileserver/{{ samples }}.tar.gz
        dest: /tmp/samples.tar.gz

    - name: Create samples dir
      file:
        name: /tmp/nsys_samples
        state: directory
    
    - name: Unpack samples pack
      unarchive:
        src: /tmp/samples.tar.gz
        dest: /tmp/nsys_samples
        remote_src: yes
    
    - name: Copy samples pack on target
      shell: 'sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/nsys_samples/tegra-armv8 {{ board_user }}@{{ board_ip }}:target/qa'

    - name: Remove samples
      file:
        name: /tmp/nsys_samples
        state: absent

    - name: Checkout nsys code
      shell: cd nsys_cli && git checkout main && git pull

    - name: Set download link
      set_fact:
        download_link: "{{ 'http://nsys/dvs-nightlies/QuadDBringup/' + build if branch == 'Dev/QuadDBringup' else 'http://nsys/dvs-prerel/QuadD_Auto/' + build }}"

    - name: Remove any installed nsys 
      shell: "dpkg -r $(dpkg --get-selections | grep nsight-systems | awk '{ print $1 }')"
      become: yes

    - name: Download deb file (x86_64)
      get_url:
        url: "{{ download_link }}/NsightSystems-linux-nda-DVS.deb"
        dest: "/tmp/NsightSystems-linux-nda-DVS.deb"
        mode: '0644'
      when: ansible_distribution == 'Ubuntu' and ansible_architecture == 'x86_64'

    - name: Install deb package (x86_64)
      shell: "dpkg -i /tmp/NsightSystems-linux-nda-DVS.deb"
      when: ansible_distribution == 'Ubuntu' and ansible_architecture == 'x86_64'
      become: yes

    - name: Prepare python dependencies
      shell: 
        cmd: cd nsys_cli && source venv/bin/activate && pip install -r requirements.txt
        executable: /bin/bash

    - name: Download python bindings
      get_url:
        url: "{{ download_link }}/NsightSystems-linux-python_bindings-public-DVS.tar.gz"
        dest: "/tmp/bindings.tar.gz"

    - name: Install python bindings
      shell: 
        cmd: cd nsys_cli && source venv/bin/activate && cd /tmp && tar xvf /tmp/bindings.tar.gz && python3 -m pip install $(find . -name "*.whl") && rm -rf /tmp/bindings.tar.gz /tmp/Nsight*
        executable: /bin/bash
      
    - name: Get target directory path (QNX)
      command: "find /opt -type d -name 'target-qnx-armv8'"
      register: target_directory_result
      ignore_errors: yes
      become: yes
      when: samples == "qnx"
    
    - name: Print target directory path (debug)
      debug:
        var: target_directory_result
    

    - name: Print target directory path
      set_fact:
        target_dir: "{{ target_directory_result.stdout_lines[0] }}"
      when: target_directory_result.changed

    - name: Get target directory path (Linux)
      command: "find /opt -type d -name 'target-linux-tegra-armv8'"
      register: target_directory_result
      ignore_errors: yes
      become: yes
      when: samples == "l4t"

    - name: Print target directory path (debug)
      debug:
        var: target_directory_result

    - name: Set target_dir
      set_fact:
        target_dir: "{{ target_directory_result.stdout_lines[0] }}"
      when: target_directory_result.changed

    - name: Print target_dir
      debug:
        var: target_dir

    - name: Copy target dir
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r {{ target_dir }} {{ board_user }}@{{ board_ip }}:"

    - name: Create a symlink on a QNX
      shell: "sshpass -p {{ board_password }} ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {{ board_user }}@{{ board_ip }} \"mkdir /usr && mkdir /usr/sbin && ln -s /root/target-qnx-armv8/nsys /usr/sbin\""
      when: samples == "qnx"

    - name: Create a symlink on a Linux
      shell: "sshpass -p {{ board_password }} ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {{ board_user }}@{{ board_ip }} \"echo {{ board_password }} | sudo -S ln -s /home/<USER>/target-linux-tegra-armv8/nsys /usr/local/bin\""
      when: samples == "l4t"

    - name: Prepare VIC sample (Download file)
      shell: wget -O /tmp/ldc_basic.conf http://devtools-qa-njtrack.nvidia.com:8080/fileserver/ldc_basic_qnx.conf

    - name: Prepare VIC sample (Copy file on board)
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/ldc_basic.conf {{ board_user }}@{{ board_ip }}:"

    - name: Prepare DLA sample (Download file)
      shell: wget -O /tmp/mnist-hw.nvdla.******* http://devtools-qa-njtrack.nvidia.com:8080/fileserver/mnist-hw.nvdla.*******
     
    - name: Prepare DLA sample (Copy file on board)
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/mnist-hw.nvdla.******* {{ board_user }}@{{ board_ip }}:"
    
    - name: Prepare ENC sample (Download file)
      shell: wget -O /tmp/NTSC352x240.yv12 http://devtools-qa-njtrack.nvidia.com:8080/fileserver/NTSC352x240.yv12
     
    - name: Prepare ENC sample (Copy file on board)
      shell: "sshpass -p {{ board_password }} scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r /tmp/NTSC352x240.yv12 {{ board_user }}@{{ board_ip }}:samples/nvmedia_6x/iep"

    - name: Remove all the redundant files
      file:
        name: '{{ item }}'
        state: absent
      with_items:
        - /tmp/bindings.tar.gz
        - /tmp/NsightSystems*
        - /tmp/nsys_samples
        - /tmp/samples
        - /tmp/NTSC352x240.yv12
        - /tmp/mnist-hw.nvdla.*******
        - /tmp/ldc_basic.conf
        - /tmp/lib-target
        - /tmp/*playfair*
        - /tmp/ls_output
        - /home/<USER>/farm_results
        - /tmp/tegra-armv8
        - /tmp/samples.tar.gz
        - /tmp/opt
      become: yes

    - name: Create temporary directory for test run results
      shell: mkdir -p /tmp/test_results/reports

    - name: Set correct test group name (Linux)
      set_fact:
        mark: drive_linux
        target_env: "/home/<USER>/target"
      when: os_name == "linux"

    - name: Set correct test group name (Linux)
      set_fact:
        mark: qnx
        target_env: "/root/target"
      when: os_name == "qnx"

    - name: Remove known_hosts
      known_hosts:
        name: "{{ board_ip }}"
        state: absent 

    - name: Run nsys tests
      shell: 
        cmd: cd nsys_cli && source venv/bin/activate && pytest -m {{ mark }} --ip={{ board_ip }} --username={{ board_user }} --password={{ board_password }} --host_env=/tmp/test_results --target_env={{ target_env }} --log-cli-level=INFO --devtest --tegra test
        executable: /bin/bash 
      ignore_errors: yes

    - name: Copy test results to reports dir
      shell: 
        cmd: mv /tmp/test_results/reports/2*/* /tmp/test_results/reports
        executable: /bin/bash

    - name: Group tests
      shell: 
        cmd: cd nsys_cli && source venv/bin/activate && python3 ./ci/group_tests.py --dir /tmp/test_results/reports
        executable: /bin/bash 

    - name: Create farm_results directory
      file:
        name: /home/<USER>/farm_results
        state: directory

    - name: Copy results from tmp to farm_results
      shell: cp -r /tmp/test_results/reports/* farm_results/
    
    - name: Create directory at results server
      shell: "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null <EMAIL> \"mkdir -p /raid/farm_results/{{ app_name }}/{{ build }}_{Runtype=Nightly-Driver={{ drive_bsp_branch }}}/Nsys_CLI-{OS={{ os_name }}-GPU=GA10B}\""
    
    - name: Copy test results at server
      shell: "rsync -azvh --rsh='ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null' --include='/tmp/test_results/reports/*' /tmp/test_results/reports/. <EMAIL>:/raid/farm_results/{{ app_name }}/{{ build }}_{Runtype=Nightly-Driver={{ drive_bsp_branch }}}/Nsys_CLI-{OS={{ os_name }}-GPU=GA10B}"

    - name: Remove temporary directory for test run results
      file:
        name: /tmp/test_results
        state: absent

    - name: Mark task as finished 
      shell: "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null <EMAIL> \"echo '--app {{ app_name }} --build {{ build }}_{Runtype=Nightly-Driver={{ drive_bsp_branch }}} --task Nsys_CLI-{OS={{ os_name }}-GPU=GA10B}' >> /raid/farm_results/finishedTasks4NewDB/nsys_task.log\""            
