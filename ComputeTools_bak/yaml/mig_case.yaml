global:
  env:
    CU<PERSON>_MAJOR: 11
    CUDA_MINOR: 8
    CUDA_REVISION: 46
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: r520
    DRIVER_VERSION: 520.32
    DEVICES: 0
    GPU_ID: 0
    PLATFORM: x86
    DVS_BUILD: C
    INSTALLER: runfile
    HOST_HOME: /home/<USER>
    SCRIPT_HOME: /home/<USER>/yichi/tesla_automation_test
    TOOLS_HOME: ${HOST_HOME}/tesla_automation/mig_case
    DATE: 202206131209
    DATE1: 20220613
    MIG: 
    HOST_P4_PATH: ${HOST_HOME}/p4
    SAMPLE_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples
    SAMPLE_BIN_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/bin/x86_64/linux/debug
    BASE_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}
    HOST_PASSWORD: labuser
    INJECTION_LOG_PATH: ${TOOLS_HOME}/cupti_injection_result_${DATE}
    BINARY_LOG_PATH: ${TOOLS_HOME}/cupti_binary_result_${DATE}
    LD_PATH: "/usr/local/cuda-${CUDA_SHORT_VERSION}/extras/CUPTI/lib64:/usr/local/cuda-${CUDA_SHORT_VERSION}/lib64:$LD_LIBRARY_PATH"
    4_2_CUPTI_PATH: ${TOOLS_HOME}/4_2_cupti_result_${DATE}
    4_2_MEMCHECK_PATH: ${TOOLS_HOME}/4_2_memcheck_result_${DATE}
    4_2_SANITIZER_PATH: ${TOOLS_HOME}/4_2_sanitizer_result_${DATE}
    4_2_DEBUGGER_PATH: ${TOOLS_HOME}/4_2_debugger_result_${DATE}
    4_2_REBEL_PATH: ${TOOLS_HOME}/4_2_rebel_result_${DATE}
    3_1_SMOKE_PATH: ${TOOLS_HOME}/3_1_SMOKE_${DATE}
    4_2_SMOKE_PATH: ${TOOLS_HOME}/4_2_SMOKE_${DATE}
    7_1_SMOKE_PATH: ${TOOLS_HOME}/7_1_SMOKE_${DATE}
    7_1_CUPTI_PATH: ${TOOLS_HOME}/7_1_cupti_result_${DATE}
    7_1_MEMCHECK_PATH: ${TOOLS_HOME}/7_1_memcheck_result_${DATE}
    7_1_SANITIZER_PATH: ${TOOLS_HOME}/7_1_sanitizer_result_${DATE}
    7_1_DEBUGGER_PATH: ${TOOLS_HOME}/7_1_debugger_result_${DATE}
    7_1_REBEL_PATH: ${TOOLS_HOME}/7_1_rebel_result_${DATE}
    3_1_SANITY_PATH: ${TOOLS_HOME}/3_1_sanity_result_${DATE}
    3_1_TEST_PATH: ${TOOLS_HOME}/3_1_test_result_${DATE}
    4_2_TEST_PATH: ${TOOLS_HOME}/4_2_test_result_${DATE}
    7_1_TEST_PATH: ${TOOLS_HOME}/7_1_test_result_${DATE}
    # CLOCK_LOG_PATH: ${TOOLS_HOME}/mig_clock_result_${DATE}
    # SHARED_LOG_PATH: ${TOOLS_HOME}/mig_shared_result_${DATE}
    7_1_MIGCLOCK_PATH: ${TOOLS_HOME}/mig_clock_result_${DATE}
    4_2_ISOSHARED_PATH: ${TOOLS_HOME}/4_2_isoshared_result_${DATE}
    CUPTI_SAMPLE_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}/extras/CUPTI/samples
    SUDO_EXECUTE: echo ${HOST_PASSWORD}|sudo -S PATH=/usr/local/cuda-${CUDA_SHORT_VERSION}/bin:"$PATH"
    PREPARE: ../scripts/mig_config.sh %s ${GPU_ID} ${HOST_PASSWORD}
    ENDING: ../scripts/mig_config.sh d ${GPU_ID} ${HOST_PASSWORD}
    MIG_ENABLED: echo ${HOST_PASSWORD} | sudo -S nvidia-smi -mig 1 -i ${GPU_ID}
    MIG_DISABLED: echo ${HOST_PASSWORD} | sudo -S nvidia-smi -mig 0 -i ${GPU_ID}
    MIG_CHECK: nvidia-smi | grep -A 2 'Graphics Device\|A100\|NVIDIA Graphics' | awk -F '|' '{print $4}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'|tail -1
    PM_ENABLED: echo ${HOST_PASSWORD} | sudo -S nvidia-smi -pm 1 -i ${GPU_ID}
    PM_DISABLED: echo ${HOST_PASSWORD} | sudo -S nvidia-smi -pm 0 -i ${GPU_ID}
    PM_CHECK: echo ${HOST_PASSWORD} |sudo -S nvidia-smi -q| grep 'Persistence Mode' | awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g
    INSTANCE_LIST: nvidia-smi -L |grep MIG | awk -F ':' '{print $3}'|awk -F ')' '{print $1}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'
    INSTANCE_TYPE: nvidia-smi -L |grep MIG | awk '{print $2}' 
    LOCK_GPU_CLOCK: echo ${HOST_PASSWORD} |sudo -S nvidia-smi -lgc tdp,tdp
    SET_PTRACE_SCOPE: echo ${HOST_PASSWORD} | sudo -S sysctl kernel.yama.ptrace_scope=0   

# run sanitizer on 7_1 / 4_2
SANITIZER:
  STEP1:
    CMD_LIST:
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./matrixMul
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./mergeSort
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./vectorAdd
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./asyncAPI
  # run sanitizer sanity test
  STEP2:
    CMD_LIST:
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool memcheck --leak-check full ./matrixMul
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --save initcheck.log ./matrixMul
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck --log-file synccheck.log ./asyncAPI
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --log-file %%p.log ./mergeSort; ls *.log
  # run sanitizer smoke test
  # STEP3:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/sanitizer_smoke.py -a x86 -d sanitizer -r sanitizer

# run memcheck on 7_1 / 4_2
MEMCHECK:
  # run  sample
  STEP1:
    CMD_LIST:
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./matrixMul
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./mergeSort
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./vectorAdd
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./matrixMul
  # run memcheck sanity test
  STEP2:
    CMD_LIST:
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck --tool memcheck --leak-check full ./matrixMul
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck --tool initcheck --save initcheck.log ./matrixMul
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck --tool synccheck --log-file synccheck.log ./asyncAPI
     - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck --tool racecheck --log-file %%p.log ./mergeSort; ls *.log
  # run memcheck smoke test
  # STEP3:
  #     CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/memcheck_smoke.py -a x86 -d memcheck -r memcheck

CUPTI_BUILD_PREPARE:
  EXTENSIONS: cd ${CUPTI_SAMPLE_PATH}/extensions/src/profilerhost_util; ${SUDO_EXECUTE} make clean; ${SUDO_EXECUTE} make
  BUILD_CMD: cd ${CUPTI_SAMPLE_PATH}/%s; ${SUDO_EXECUTE} make clean; ${SUDO_EXECUTE} make
    
# run cupti on 7_1 / 4_2
CUPTI:
  STEP1:
    CMD_LIST:
      - cd ${CUPTI_SAMPLE_PATH}/sass_source_map; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./sass_source_map
      - cd ${CUPTI_SAMPLE_PATH}/activity_trace_async;export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./activity_trace_async
      - cd ${CUPTI_SAMPLE_PATH}/callback_timestamp; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./callback_timestamp
      - cd ${CUPTI_SAMPLE_PATH}/unified_memory; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./unified_memory
  # run cupti next-gen profile sample
  STEP2:
    CMD_LIST:
      - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/autorange_profiling; cd ${CUPTI_SAMPLE_PATH}/autorange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./auto_range_profiling
      - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/userrange_profiling; cd ${CUPTI_SAMPLE_PATH}/userrange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./user_range_profiling
  # STEP3:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/cupti_smoke.py -a x86 -d cupti -r cupti
  # # # run trace_checker test
  # STEP4:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/run_cupti_case.py -e cupti_trace

# run rebel on 7_1 / 4_2
REBEL:
  # run  sample by ncu
  STEP1:
    CMD_ROTATE:
      CMD_KEEP: cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; while true ;do ./matrixMul; done 
      CMD_RUN: cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.* ./asyncAPI
      CMD_RUN_7_1: cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.* ./asyncAPI
      CMD_RUN_4_2: cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.* ./asyncAPI
      CMD_END: for i in `ps -ef |grep matrixMul |awk '{print $2}'`; do kill -9 $i; done # CUDA Sample keep running need to be killed manually, pending more investigations.Z
  # run rebel sanity test
  STEP2:
    CMD_LIST:
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu ./asyncAPI
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section regex:Schedule.* ./asyncAPI
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.*  ./asyncAPI
  # run rebel smoke test
  # STEP3:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/rebel_smoke.py -a x86 -d rebel -r rebel

# run debugger on 7_1 / 4_2
DEBUGGER:
  STEP1:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; python ${SCRIPT_HOME}/ComputeTools/tests/ComputeTools_Testing_Linux/CommonTestSteps/cuda-gdb_sanity_check.py -v ${CUDA_SHORT_VERSION} -s 'cuda-gdb ${SAMPLE_BIN_PATH}/matrixMul'
  # STEP2:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/debugger_smoke.py -a x86 -d debugger -r debugger

# run sanity on 3_1
# Add this Sanity test but have som defects now: cupti make is not properly for multprocess.
SANITY:
  STEP1:
    CMD_LIST:
      - export CUDA_VISIBLE_DEVICES=%s; python ${SCRIPT_HOME}/ComputeTools/tests/ComputeTools_Testing_Linux/CommonTestSteps/cuda-gdb_sanity_check.py -v ${CUDA_SHORT_VERSION} -s 'cuda-gdb ${SAMPLE_BIN_PATH}/matrixMul'
  STEP2:  
    CMD_LIST:
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./matrixMul
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./mergeSort
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./vectorAdd
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./matrixMul
  STEP3:
    CMD_LIST:
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./matrixMul
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./mergeSort
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./vectorAdd
      - cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./asyncAPI
  STEP4:
    CMD_LIST:
      - cd ${CUPTI_SAMPLE_PATH}/sass_source_map; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./sass_source_map
      - cd ${CUPTI_SAMPLE_PATH}/activity_trace_async; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./activity_trace_async
      - cd ${CUPTI_SAMPLE_PATH}/callback_timestamp; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./callback_timestamp
      - cd ${CUPTI_SAMPLE_PATH}/unified_memory; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./unified_memory
  STEP5:
    CMD_LIST:
      - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/autorange_profiling; cd ${CUPTI_SAMPLE_PATH}/autorange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./auto_range_profiling
      - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/userrange_profiling; cd ${CUPTI_SAMPLE_PATH}/userrange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./user_range_profiling
      # - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/autorange_profiling; cd ${CUPTI_SAMPLE_PATH}/autorange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./autoRangeSample
      # - echo ${HOST_PASSWORD}| sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/userrange_profiling; cd ${CUPTI_SAMPLE_PATH}/userrange_profiling; export LD_LIBRARY_PATH=${LD_PATH};export CUDA_VISIBLE_DEVICES=%s; ./userRangeSample
  STEP6:
    CMD_LIST:
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu ./asyncAPI
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section regex:Schedule.* ./asyncAPI
      # - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section Schedule.* ./asyncAPI

      # - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.*  ./asyncAPI


# run mig 3_1 which is smoke test for all tools on 3_1
SMOKE:
  STEP1:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/memcheck_smoke.py -a x86 -d memcheck -r memcheck
    TIME_OUT: 7200
  STEP2:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/sanitizer_smoke.py -a x86 -d sanitizer -r sanitizer
    TIME_OUT: 7200
  STEP3:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/cupti_smoke.py -a x86 -d cupti -r cupti
    TIME_OUT: 7200
  STEP4:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/rebel_smoke.py -a x86 -d rebel -r rebel
    TIME_OUT: 7200
  STEP5:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/debugger_smoke.py -a x86 -d debugger -r debugger
    TIME_OUT: 7200


# run mig clock control
MIGCLOCK:
  # LOG_NAME: ${CLOCK_LOG_PATH}/mig_clock.log
  # get the compute instace id
  # CMD1: nvidia-smi -L |grep MIG | awk -F ':' '{print $3}'|awk -F ')' '{print $1}'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'
  STEP1:
    CMD_ONE: export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --clock-control none --section 'regex:Instruction.*' ./asyncAPI
    CHECK_POINT: "==WARNING== Note: Running with unmodified GPU clocks. If not controlled otherwise, profiling results may be inconsistent."
  STEP2:
    CMD_ONE: export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; echo ${HOST_PASSWORD} | sudo -S chmod -R 777 /tmp; echo ${HOST_PASSWORD} | sudo -S /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section 'regex:Launch.*' ./asyncAPI
  STEP3:
    CMD_ONE: export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section 'regex:Compute.*' ./asyncAPI
    # CHECK_POINT: "Error: Failed to lock GPU clock frequencies! Try locking the clocks externally (e.g. using 'nvidia-smi --lock-gpu-clocks=tdp,tdp') or profile without fixed frequencies (see '--clock-control')"
  STEP4:
    # CMD1: echo ${HOST_PASSWORD} |sudo -S nvidia-smi -lgc tdp,tdp
    CMD_ONE: echo ${HOST_PASSWORD} |sudo -S nvidia-smi -i ${GPU_ID} -lgc tdp,tdp; export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --section 'regex:Warp.*'  ./asyncAPI
  STEP5:
    CMD_ONE: echo ${HOST_PASSWORD} |sudo -S nvidia-smi -i ${GPU_ID} -lgc tdp,tdp; export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --clock-control none --section 'regex:Schedule.*' ./asyncAPI
 #   CHECK_POINT: "==WARNING== Note: Running with unmodified GPU clocks. If not controlled otherwise, profiling results may be inconsistent. "
  # STEP6:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/cupti_smoke.py -a x86 -d cupti -r cupti


ISOSHARED:
  STEP1:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu ./asyncAPI
  STEP2:
    CMD_LIST:
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --set full ./asyncAPI
  STEP3:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics dram__cycles_elapsed.avg.per_second ./asyncAPI
  STEP4:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --metrics regex:.* ./asyncAPI
    # TIME_OUT: 30
  STEP5:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/rebel_smoke.py -a x86 -d rebel
  STEP6:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; export LD_LIBRARY_PATH=${LD_PATH}; cd ${CUPTI_SAMPLE_PATH}/autorange_profiling; echo ${HOST_PASSWORD} | sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/autorange_profiling; ./auto_range_profiling --metrics dram__cycles_elapsed.avg.per_second
  STEP7:
    CMD_LIST: 
      - export CUDA_VISIBLE_DEVICES=%s; export LD_LIBRARY_PATH=${LD_PATH};cd ${CUPTI_SAMPLE_PATH}/userrange_profiling; echo ${HOST_PASSWORD} | sudo -S chmod -R 777 ${CUPTI_SAMPLE_PATH}/userrange_profiling; ./user_range_profiling --metrics dram__cycles_elapsed.avg.per_second
  # STEP8:
  #   CMD_SPECIFIED:
  #     TYPE_SPECIFIED: 2c.4g
  #     PARAMS_SPECIFIED: shared
  #     PARAMS_NORMAL: isolated
  #     CMD_PROTOTYPE: export CUDA_VISIBLE_DEVICES=%s; cd ~/rebel_${DATE1}/Rebel-Release-public-test/target/linux-desktop-glibc_2_11_3-x64/cuda/; python TestCmdlineProfiler.py --mig %s
  #   TIME_OUT: 120

TEST:
  # STEP1:
    # CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/memcheck_smoke.py -a x86 -d memcheck -r memcheck
  # STEP1:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/sanitizer_smoke.py -a x86 -d sanitizer -r sanitizer
  STEP1:
    CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/cupti_smoke.py -a x86 -d cupti -r cupti
  # STEP1:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/debugger_smoke.py -a x86 -d debugger -r debugger
  # STEP1:
  #   CMD_SMOKE: python ${SCRIPT_HOME}/ComputeTools/scripts/rebel_smoke.py -a x86 -d rebel -r rebel
