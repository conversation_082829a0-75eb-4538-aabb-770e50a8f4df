global:
  env:
    CU<PERSON>_MAJOR: 12
    CUDA_MINOR: 5
    CUDA_REVISION: 16
    CUDA_BRANCH: r${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_SHORT_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}
    CUDA_VERSION: ${CUDA_MAJOR}.${CUDA_MINOR}.${CUDA_REVISION}
    DRV_BRANCH: r555
    DRIVER_VERSION: 555.1
    DEVICES: 0
    DVS_BUILD: I
    VGPU: none
    INSTALLER: runfile
    HOST_HOME: /home/<USER>
    TOOLS_HOME: ${HOST_HOME}/tesla_automation/sanitizer_device${DEVICES}
    DATE: 20240422181809
    DATE1: 20240422
    HOST_P4_PATH: ${HOST_HOME}/p4
    PLATFORM: x86
    CQA_USER: Y3FhdXNlcg
    CQA_PASSWORD: Y3FhdGVzdA
    OPTIX_PATH: ${HOST_HOME}/optix
    OPTIX_BIN_PATH: ${OPTIX_PATH}/SDK/build/bin
    OPTIX_LIB_PATH: ${OPTIX_PATH}/SDK/build/lib
    OPTIX_FLAG: 0
    SAMPLE_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples
    SAMPLE_BIN_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/bin/x86_64/linux/debug
    SAMPLE_0_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/Samples/0_Introduction
    SAMPLE_0_PATH1: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/0_Simple
    SAMPLE_6_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/6_Performance
    SAMPLE_6_PATH1: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/6_Advanced
    SAMPLE_1_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/Samples/1_Utilities
    SAMPLE_1_PATH1: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/1_Utilities
    CUHOOK_PATH: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/Samples/2_Concepts_and_Techniques/cuHook
    CUHOOK_PATH1: ${HOST_HOME}/NVIDIA_CUDA-${CUDA_SHORT_VERSION}_Samples/7_CUDALibraries/cuHook
    BASE_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}
    HOST_PASSWORD: cuda
    OUTPUT_FLAG: 0
    SCPRIT_HOME: ${TOOLS_HOME}/scripts
    BASE_LOG_PATH3: ${TOOLS_HOME}/2179905_sanitizer_sanity_result_${DATE}
    BASE_LOG_PATH2: ${TOOLS_HOME}/2181021_sanitizer_check_option_result_${DATE}
    BASE_LOG_PATH4: ${TOOLS_HOME}/2181022_sanitizer_check_demangle_result_${DATE}
    BASE_LOG_PATH6: ${TOOLS_HOME}/2181023_sanitizer_check_filter_result_${DATE}
    BASE_LOG_PATH1: ${TOOLS_HOME}/2181029_sanitizer_sample_coverage_result_${DATE}
    BASE_LOG_PATH5: ${TOOLS_HOME}/2181031_sanitizer_no_root_result_${DATE}
    BASE_LOG_PATH7: ${TOOLS_HOME}/2181064_sanitizer_save_log_result_${DATE}
    BASE_LOG_PATH8: ${TOOLS_HOME}/2182162_sanitizer_launch_timeout_result_${DATE}
    TARGET_PROCESS_OPTION_PATH: ${TOOLS_HOME}/2493063_target_process_option_result_${DATE}
    CUDA_COREDUMP_DISABLE_PATH: ${TOOLS_HOME}/2524649_cuda_coredump_disable_result_${DATE}
    SANITIZER_TEST_CUBIN_PATH: ${TOOLS_HOME}/2572527_sanitizer_test_cubin_result_${DATE}
    SANITIZER_UNDER_MPS_PATH: ${TOOLS_HOME}/2623088_sanitizer_under_mps_result_${DATE}
    SANITIZER_NO_REPORT_ERROR_PATH: ${TOOLS_HOME}/2635440_sanitizer_no_report_error_result_${DATE}
    SANITIZER_NO_CRASH_PATH: ${TOOLS_HOME}/2635791_sanitizer_no_crash_result_${DATE}
    PADDING_OPTION_PATH: ${TOOLS_HOME}/2679219_padding_option_result_${DATE}
    NVTX_OPTION_PATH: ${TOOLS_HOME}/2679220_nvtx_option_result_${DATE}
    LAUNCH_COUNT_SKIP_PATH: ${TOOLS_HOME}/2680934_launch_count_skip_result_${DATE}
    SANITIZER_RACECHECK_NUM_WORKERS_PATH: ${TOOLS_HOME}/2680935_sanitizer_racecheck_num_workers_result_${DATE}
    DETAIL_SESSION_OPTION_PATH: ${TOOLS_HOME}/2819761_detail_session_option_result_${DATE}
    CUDA_LEAK_CHECK_PATH: ${TOOLS_HOME}/2714083_cuda_leak_check_result_${DATE}
    CORRECT_LINE_NUM_PATH: ${TOOLS_HOME}/2716405_correct_line_num_result_${DATE}
    INVALID_MEMORY_ACCESS_PATH: ${TOOLS_HOME}/2720172_invalid_memory_access_result_${DATE}
    STOP_USING_WARPFULLMASK_PATH: ${TOOLS_HOME}/2759132_stop_using_warpfullmask_result_${DATE}
    DEVICE_STACK_FRAME_PATH: ${TOOLS_HOME}/2764359_device_stack_frame_result_${DATE}
    RACECHECK_WITH_QUDA_PATH: ${TOOLS_HOME}/2771164_racecheck_with_quda_result_${DATE}
    MEMCHECK_WITH_FREE_PATH: ${TOOLS_HOME}/2788746_memcheck_with_free_result_${DATE}
    RELATIVE_RETURN_ADDRESS_PATH: ${TOOLS_HOME}/2800789_relative_return_address_result_${DATE}
    SANITIZER_XML_OPTION_PATH: ${TOOLS_HOME}/2806646_sanitizer_xml_option_result_${DATE}
    FILE_DESCRIPTOR_LIMIT_PATH: ${TOOLS_HOME}/2807199_file_descriptor_limit_result_${DATE}
    READ_ONLY_FLAG_PATH: ${TOOLS_HOME}/2818363_read_only_flag_result_${DATE}
    RACECHECK_DETECT_PRINT_OPTION_PATH: ${TOOLS_HOME}/2681761_racecheck_detect_print_option_result_${DATE}
    CUDA_BARRIERS_OPTION_PATH: ${TOOLS_HOME}/2819793_cuda_barriers_option_result_${DATE}
    LEAK_CHECK_ALLOC_PATH: ${TOOLS_HOME}/2833990_leak_check_alloc_result_${DATE}
    TARGET_FILTER_OPTION_PATH: ${TOOLS_HOME}/2834633_target_filter_option_result_${DATE}
    STACK_OVERFLOW_CHECK_PATH: ${TOOLS_HOME}/2897883_stack_overflow_check_result_${DATE}
    SANITIZER_MEMCHECK_DEMO_PATH: ${TOOLS_HOME}/2931394_sanitizer_memcheck_demo_result_${DATE}
    SANITIZER_RACECHECK_WARP_PATH: ${TOOLS_HOME}/2931395_sanitizer_racecheck_warp_result_${DATE}
    SANITIZER_RACECHECK_BLOCK_PATH: ${TOOLS_HOME}/2931396_sanitizer_racecheck_block_result_${DATE}
    SANITIZER_INITCHECK_ERROR_PATH: ${TOOLS_HOME}/2931397_sanitizer_initcheck_error_result_${DATE}
    SANITIZER_LEAKCHECK_MEMCHECK_DEMO_PATH: ${TOOLS_HOME}/2931398_sanitizer_leakcheck_memcheck_demo_result_${DATE}
    SANITIZER_SYNCCHECK_DIVERGENT_PATH: ${TOOLS_HOME}/2931399_sanitizer_synccheck_divergent_result_${DATE}
    SANITIZER_SYNCCHECK_SYNCWARP_PATH: ${TOOLS_HOME}/2931400_sanitizer_synccheck_syncwarp_result_${DATE}
    LEAKCHECK_DEVICES_ALLOCATIONS_PATH: ${TOOLS_HOME}/2949009_leakcheck_devices_allocations_result_${DATE}
    MEMCHECK_OPTION_TRACK_STREAM_PATH: ${TOOLS_HOME}/2949010_memcheck_option_track_stream_result_${DATE}
    SYNCCHECK_OPTION_MISS_BARRIER_PATH: ${TOOLS_HOME}/2949013_synccheck_option_miss_barrier_result_${DATE}
    CALL_HOST_DEVICE_PATH: ${TOOLS_HOME}/3021849_call_host_device_result_${DATE}
    MEMCHECK_ORDERED_RACE_DETECTION_PATH: ${TOOLS_HOME}/3051676_memcheck_ordered_race_detection_result_${DATE}
    DEPRECAT_CUDA_MEMCHECK_PATH: ${TOOLS_HOME}/2742928_deprecate_cuda_memcheck_result_${DATE}
    SUPPORT_ALIGNED_DEVICE_MALLOC_PATH: ${TOOLS_HOME}/3071342_support_aligned_device_malloc_result_${DATE}
    SANITIZER_SUPPORT_LDSM_PATH: ${TOOLS_HOME}/3079874_sanitizer_support_LDSM_result_${DATE}
    GRAPHIC_SAMPLE_COVERAGE_PATH: ${TOOLS_HOME}/3084216_graphic_sample_coverage_result_${DATE}
    SANITIZER_TRACK_UNUSED_MEMORY_PATH: ${TOOLS_HOME}/3090527_sanitizer_track_unused_memory_result_${DATE}
    SANITIZER_LAUNCH_END_CALLBACK_PATH: ${TOOLS_HOME}/3156328_sanitizer_launch_end_callback_result_${DATE}
    SANITIZER_STEAM_CAPTURE_PATH: ${TOOLS_HOME}/3156328_sanitizer_steam_capture_result_${DATE}
    SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH: ${TOOLS_HOME}/3158301_sanitizer_racecheck_memcpy_async_result_${DATE}
    SANITIZER_CHECK_CACHE_CONTROL_PATH: ${TOOLS_HOME}/3161772_sanitizer_check_cache_control_result_${DATE}
    SANITIZER_CHECK_READ_ACCESS_PATH: ${TOOLS_HOME}/3178884_sanitizer_check_read_access_result_${DATE}
    SANITIZER_MEMCHECK_PTXAS_MISCOMPILER_PATH: ${TOOLS_HOME}/3186829_sanitizer_memcheck_ptxas_miscompiler_result_${DATE}
    SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH: ${TOOLS_HOME}/3192559_sanitizer_memcheck_vector_atomics_result_${DATE}
    SANITIZER_SUPPORT_UNICODE_FILE_PATH: ${TOOLS_HOME}/3183138_sanitizer_support_unicode_file_result_${DATE}
    SANITIZER_MEMCHECK_CNP_SUPPORT_PATH: ${TOOLS_HOME}/3192566_sanitizer_memcheck_cnp_support_result_${DATE}
    SANITIZER_QUITE_CUDA_INIT_PATH: ${TOOLS_HOME}/3207945_sanitizer_quite_cuda_init_result_${DATE}
    SANITIZER_LAUNCH_ATTACH_PATH: ${TOOLS_HOME}/3207946_sanitizer_launch_attach_result_${DATE}
    SANITIZER_RCCA_3219363_PATH: ${TOOLS_HOME}/3219363_sanitizer_rcca_result_${DATE}
    SANITIZER_RCCA_3222551_PATH: ${TOOLS_HOME}/3222551_sanitizer_rcca_result_${DATE}
    SANITIZER_COREDUMP_GDB_PATH: ${TOOLS_HOME}/3251454_sanitizer_coredump_gdb_result_${DATE}
    SANITIZER_RCCA_3251459_PATH: ${TOOLS_HOME}/3251459_sanitizer_rcca_result_${DATE}
    SANITIZER_TEST_NVSCIBUFFER_PATH: ${TOOLS_HOME}/3302257_sanitizer_test_nvscibuffer_result_${DATE}
    RACECHECK_WARP_EXIT_PATH: ${TOOLS_HOME}/3368656_racecheck_warp_exit_result_${DATE}
    MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH: ${TOOLS_HOME}/3504892_detect_missing_moudle_unload_result_${DATE}
    SANITIZER_SUPPORT_MULTI_CTX_COREDUMP_PATH: ${TOOLS_HOME}/3532274_sanitizer_support_multi_ctx_coredump_result_${DATE}
    SANITIZER_PRELOAD_LIBRARY_PATH: ${TOOLS_HOME}/3532738_sanitizer_preload_library_result_${DATE}
    SANITIZER_INCORRECT_HOST_BACKTRACE_PATH: ${TOOLS_HOME}/3555855_sanitizer_incorrect_host_backtrace_result_${DATE}
    SANITIZER_SUPPRESSIONS_OPTION_PATH: ${TOOLS_HOME}/3568568_sanitizer_suppressions_option_result_${DATE}
    SANITIZER_COMPILER_HINT_OPTION_PATH: ${TOOLS_HOME}/3568768_sanitizer_compiler_hint_option_result_${DATE}
    SANITIZER_3700598_RCCA_PATH: ${TOOLS_HOME}/3700598_sanitizer_3700598_rcca_result_${DATE}
    SANITIZER_CASE_LOG_PATH: ${BASE_LOG_PATH}
    SANITIZER_RESULT_PATH: ${BASE_LOG_PATH}
    T744_PATH: ${TOOLS_HOME}/../t744
    LD_LIBRARY: "/usr/local/cuda-${CUDA_SHORT_VERSION}/lib64:$LD_LIBRARY_PATH"
    ENV_PATH: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin:"$PATH"
    QUDA_ENV: export QUDA_RESOURCE_PATH=.; export META="--unit-gauge 1 --dim 16 16 16 16 --Lsdim 1 --solve-type direct-pc --dslash-type wilson --mass -.1 --anisotropy 1 --inv-multigrid true --mg-verbosity 0 verbose --mg-verbosity 1 verbose";
          export SOLVER_PARAM="--prec single --prec-sloppy single --prec-precondition single --prec-null single --tol 1e-6 --niter 1";
          export MG_PARAM="--mg-levels 2 --mg-generate-nullspace true --mg-use-mma true";
          export LEVEL0_PARAM="--mg-nu-pre 0 0 --mg-nu-post 0 8 --mg-block-size 0 4 4 4 4 --mg-nvec 0 32 --mg-setup-inv 0 cg --mg-smoother 0 ca-gcr --mg-setup-maxiter 0 1";


TOOLS_X86:
    SANITIZER_PATH: ${HOST_HOME}/sanitizer_x86_{}
    DEBUG_PATH: ${HOST_HOME}/debugger_x86_{}
    REBEL_PATH: ${HOST_HOME}/rebel_x86_{}
    COMPILER_PATH: ${HOST_HOME}/compiler_x86_{}
    SANITIZER_RUN_PATH: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test
    SANITIZER_PATH1: ${SANITIZER_PATH}_1
    SANITIZER_RUN_PATH1: ${SANITIZER_PATH1}/nvidia-compute-sanitizer-test
    SANITIZER_PATH2: ${SANITIZER_PATH}_2
    SANITIZER_RUN_PATH2: ${SANITIZER_PATH2}/nvidia-compute-sanitizer-test
    SANITIZER_PATH3: ${SANITIZER_PATH}_3
    SANITIZER_RUN_PATH3: ${SANITIZER_PATH3}/nvidia-compute-sanitizer-test
    SANITIZER_URL: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA${CUDA_SHORT_VERSION}_Release_Linux_Sanitizer_Public/
    SANITIZER_11_URL: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Linux_Sanitizer_Public/
    SANITIZER_11_NEW_URL: "//builds/nightly/devtools/Sanitizer/Rel/DTC_${DVS_BUILD}"

TOOLS_PPC64LE:
    SANITIZER_PATH: ${HOST_HOME}/sanitizer_ppc64le_{}
    REBEL_PATH: ${HOST_HOME}/rebel_ppc64le_{}
    SANITIZER_RUN_PATH: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test
    SANITIZER_URL: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA${CUDA_SHORT_VERSION}_Release_Linux_Sanitizer_Public/
    SANITIZER_11_URL: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Linux_Sanitizer_Public/

TOOLS_ARM:
    SANITIZER_PATH: ${HOST_HOME}/sanitizer_arm_{}
    DEBUG_PATH: ${HOST_HOME}/debugger_arm_{}
    REBEL_PATH: ${HOST_HOME}/rebel_arm_{}
    COMPILER_PATH: ${HOST_HOME}/compiler_arm_{}
    SANITIZER_RUN_PATH: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test
    SANITIZER_RUN_PATH1: ${SANITIZER_PATH}/nvidia-compute-sanitizer-test/target
    SANITIZER_URL: http://dvstransfer.nvidia.com/dvsshare/dvs-binaries/Agora_Rel_CUDA${CUDA_SHORT_VERSION}_Release_Linux_Sanitizer_Public/
    SANITIZER_11_URL: http://dvstransfer.nvidia.com/dvs-binaries/Agora_Rel_DTC_${DVS_BUILD}_Release_Linux_Sanitizer_Public/


# get the SM value of GPU and name of GPU
PREPARE:
    CMD1: "cd %s/deviceQuery; make clean; make"
    CMD2: "cd %s/deviceQuery; make clean; make; ./deviceQuery | grep 'Device 0'"
    CMD3: "cd %s/deviceQuery;./deviceQuery |grep 'CUDA Capability Major/Minor version number'|awk -F ':' '{print $2}'"
    CMD4: "cd %s/deviceQuery; make clean; make;./deviceQuery | grep 'Device 0'|awk -F ':' '{print $2}'|sed -e 's/^[ ]*//g' | sed -e 's/[ ]*$//g'"


SANITIZER_SAMPLE_COVERAGE:
  LOG_NAME: ${BASE_LOG_PATH1}/sanitizer_sample_coverage.txt
  RUN_CASE: true
  SAMPLE_LIST: "cdpAdvancedQuicksort,reductionMultiBlockCG,simpleCudaGraphs,batchCUBLAS,simpleMultiGPU,cudaOpenMP,p2pBandwidthLatencyTest,cuHook"
  SAMPLE_LIST1: "cdpAdvancedQuicksort,UnifiedMemoryStreams,reductionMultiBlockCG,simpleCudaGraphs,batchCUBLAS,simpleMultiGPU,cudaOpenMP,p2pBandwidthLatencyTest,vectorAddMMAP,cuHook"
  # vectorAddMMAP,cuHook not support on arm
  SAMPLE_LIST2: "cdpAdvancedQuicksort,UnifiedMemoryStreams,reductionMultiBlockCG,simpleCudaGraphs,batchCUBLAS,simpleMultiGPU,cudaOpenMP,p2pBandwidthLatencyTest"
  STEP1:
    CMD:  cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=$LD_LIBRARY; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool memcheck --leak-check full ./%s
    CMD1: cd %s;  LD_PRELOAD=./libcuhook.so.1 /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --target-processes all --leak-check full ./%s
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
    CHECK_POINT1: 'LEAK SUMMARY: 0 bytes leaked in 0 allocations'
  STEP2:
    CMD:  cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=$LD_LIBRARY;  /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck ./%s
    CMD1: cd %s;  LD_PRELOAD=./libcuhook.so.1 /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --target-processes all ./%s
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  STEP3:
    CMD:  cd ${SAMPLE_BIN_PATH}; export LD_LIBRARY_PATH=$LD_LIBRARY;  /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck ./%s
    CMD1: cd %s;  LD_PRELOAD=./libcuhook.so.1 /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck --target-processes all ./%s
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  STEP4:
    CMD: cd ${SAMPLE_BIN_PATH};  export LD_LIBRARY_PATH=$LD_LIBRARY; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./%s
    CMD1: cd %s;  LD_PRELOAD=./libcuhook.so.1 /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --target-processes all ./%s
    CHECK_POINT: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT: 'Warning: CUDA Dynamic Parallelism is not supported by the selected tool'


SANITIZER_SANITY:
  LOG_NAME: ${BASE_LOG_PATH3}/sanitizer_sanity.txt
  RUN_CASE: true
  STEP1:
    CHECK_OPTION: leak-check,initcheck,synccheck,racecheck
    CMD:
     -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool memcheck --leak-check full --save leak.log ./matrixMul; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --read leak.log
     -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --save initcheck.log ./matrixMul
     -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck --log-file synccheck.log ./asyncAPI
     -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --log-file %p.log -c 10 ./mergeSort
    CHECK_POINT: '0 errors'
    CHECK_POINT1: '========= COMPUTE-SANITIZER'
    CHECK_POINT2: 'ERROR SUMMARY: 0 errors'
    CHECK_POINT3: 'LEAK SUMMARY: 0 bytes leaked in 0 allocations'
  STEP2:
    CMD: cd ${SAMPLE_BIN_PATH}; ls *.log

SANITIZER_SAVE_LOG:
  LOG_NAME: ${BASE_LOG_PATH7}/sanitizer_save_log.txt
  RUN_CASE: true
  PREPARE:
    VECTORADD_ERR: '%s/vectorAdd_err'
    CMD: 'rm -r %s/vectorAdd_err;cp -r %s/vectorAdd %s/vectorAdd_err; cd %s/vectorAdd_err; sed -i "s/threadsPerBlock = 256/threadsPerBlock = 2048/" vectorAdd.cu
          ;sed -i "s#err = cudaFree(d_C)#//err = cudaFree(d_C)#" vectorAdd.cu;make clean;make dbg=1'
    CMD1: cd %s/vectorAdd_err; make clean; make dbg=1
  STEP1:
    RUN_STEP1: true
    CMD: cd %s/vectorAdd_err&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save err.log ./vectorAdd
    CMD1: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --log-file err1.log ./vectorAdd
    CMD2: cd %s/vectorAdd_err; ls
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"
    CHECK_POINT: "err.log"
    CHECK_POINT1: "err1.log"
  STEP2:
    RUN_STEP2: true
    CMD: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --read err.log
    CMD1: cd %s/vectorAdd_err; cat err1.log
    CHECK_POINT: "ERROR SUMMARY: 2 errors"
  STEP3:
    RUN_STEP3: true
    CMD: cd ${SAMPLE_BIN_PATH}; mv *.log ${BASE_LOG_PATH7}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save %p.log ./vectorAdd; ls *.log
    CMD1: cd ${SAMPLE_BIN_PATH}; mv *.log ${BASE_LOG_PATH7}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --log-file %p.log ./vectorAdd; ls *.log
    CMD2: cd ${SAMPLE_BIN_PATH}; ls *.log
  STEP4:
    CMD: cd ${SAMPLE_BIN_PATH};  export FOO=testing; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save %q{FOO}.log ./vectorAdd; ls testing.log
    CMD1: cd ${SAMPLE_BIN_PATH};  export FOO1=testing1; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --log-file %q{FOO1}.log ./vectorAdd; ls testing1.log
    CMD2: cd ${SAMPLE_BIN_PATH}; ls *.log
    CHECK_POINT: "testing.log"
    CHECK_POINT1: "testing1.log"
  STEP5:
    CMD: cd ${SAMPLE_BIN_PATH}; mv *.log ${BASE_LOG_PATH7}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save %%.log ./vectorAdd; ls %.log
    CMD1: cd ${SAMPLE_BIN_PATH}; mv *.log ${BASE_LOG_PATH7}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --log-file %%.log ./vectorAdd; ls %.log
    CMD2: cd ${SAMPLE_BIN_PATH}; ls *.log
    CHECK_POINT: "ERROR SUMMARY: 2 errors"
  STEP6:
    CMD: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save %x.log ./vectorAdd
    CMD1: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --log-file %x.log ./vectorAdd
    CHECK_POINT: "Unknown macro '%x'"
  STEP7:
    CMD: cd %s/vectorAdd; make -j16 -k; make dbg=1 -B -j16 -k

SANITIZER_LAUNCH_TIMEOUT:
  LOG_NAME: ${BASE_LOG_PATH8}/sanitizer_launch_timeout.txt
  RUN_CASE: true
  PREPARE:
    VECTORADD_WHILE: '%s/vectorAdd_while'
    CMD: 'rm -r %s/vectorAdd_while;cp -r %s/vectorAdd %s/vectorAdd_while; cd %s/vectorAdd_while;
          sed -i "/Error code to check return values for CUDA calls/i\    while(true){;};" vectorAdd.cu; make clean;make dbg=1'
    CMD1: cd %s/vectorAdd_while; make clean; make dbg=1
  STEP1:
    CMD: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --kill {} ./vectorAdd
    CHECK_POINT: "Error: No attachable process found"
  STEP2:
    CMD: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --launch-timeout 30 --kill {} ./vectorAdd
    CHECK_POINT: "Error: No attachable process found"
  STEP3:
    CMD: cd %s/vectorAdd; make -j16 -k; make dbg=1 -B -j16 -k

SANITIZER_NO_ROOT:
  LOG_NAME: ${BASE_LOG_PATH5}/sanitizer_no_root.txt
  STEP1:
    CMD: cd /usr/local/cuda-${CUDA_SHORT_VERSION}/samples/0_Simple/matrixMul; echo ${HOST_PASSWORD}|sudo -S make clean; echo ${HOST_PASSWORD}|sudo -S make dbg=1
    CMD_1: cd {}/matrixMul; make clean; make dbg=1
  STEP2:
    CMD: cd /usr/local/cuda-${CUDA_SHORT_VERSION}/samples/0_Simple/matrixMul; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./matrixMul
    CMD_1: cd {}/matrixMul; echo ${HOST_PASSWORD}|sudo -S cp matrixMul /usr/local/cuda-${CUDA_SHORT_VERSION}; cd /usr/local/cuda-${CUDA_SHORT_VERSION}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./matrixMul
    CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  STEP3:
    CMD: cd {}/matrixMul; make -j16 -k; make dbg=1 -B -j16 -k

SANITIZER_OPTION_CHECK:
  LOG_NAME: ${BASE_LOG_PATH2}/sanitizer_check_option.txt
  RUN_CASE: true
  SAMPLE_LIST: vectorAdd, asyncAPI
  PREPARE:
    VECTORADD_ERR: '%s/vectorAdd_err'
    CMD: 'rm -r %s/vectorAdd_err;cp -r %s/vectorAdd %s/vectorAdd_err; cd %s/vectorAdd_err; sed -i "s/threadsPerBlock = 256/threadsPerBlock = 2048/" vectorAdd.cu
          ;sed -i "s#err = cudaFree(d_C)#//err = cudaFree(d_C)#" vectorAdd.cu;make clean;make dbg=1'
    CMD1: export PATH=${ENV_PATH}; cd %s/vectorAdd_err; make clean; make dbg=1
  STEP1:
    RUN_STEP1: true
    CMD: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./vectorAdd
    CHECK_POINT: "ERROR SUMMARY: 2 errors"
    CHECK_POINT_1: "ERROR SUMMARY: 5 errors"
    CHECK_POINT2: "LEAK SUMMARY: 600000 bytes leaked in 3 allocations"
  STEP2:
    RUN_STEP2: true
    CMD1: cd %s/asyncAPI; make clean, make dbg=1; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./asyncAPI
    CHECK_POINT1: "ERROR SUMMARY: 0 errors"
    CMD2: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./vectorAdd
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"
    CMD3: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full --prefix "******" --error-exitcode 6 --print-limit 0 ./vectorAdd
    CHECK_POINT3: "Program hit invalid configuration argument (error 9) on CUDA API call to cudaLaunchKernel"
    CHECK_POINT3_1: Program hit cudaErrorInvalidConfiguration (error 9)
  STEP3:
    RUN_STEP3: true
    CMD1: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./vectorAdd
    CMD2: cd %s/vectorAdd_err; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --check-exit-code no ./vectorAdd
    CHECK_POINT: "Target application returned an error"
  STEP4:
    CMD: cd %s/vectorAdd; make -j16 -k; make dbg=1 -B -j16 -k

CHECK_DEMANGLE:
  LOG_NAME: ${BASE_LOG_PATH4}/check_demangle.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r t744; mkdir t744; cd t744; wget --user %s --password %s %s/%s/%s
  STEP1:
    CMD: export PATH=${ENV_PATH}; cd ${T744_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o t744 t744.cu; ls t744
    CHECK_POINT: t744
  STEP2:
    CMD1: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck ./t744
    CMD2: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --racecheck-report hazard ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT1_1: "void tkernel<int>(T1 *)"
    CHECK_POINT1_2: "void tkernel<int>(int*)"
    CHECK_POINT2: 'RACECHECK SUMMARY: 160 hazards displayed (0 errors, 160 warnings)'
    CHECK_POINT2_2: "void tkernel<int>(T1 *)"
    CHECK_POINT2_1: "void tkernel<int>(int*)"
  STEP3:
    CMD: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --demangle simple ./t744
    CHECK_POINT: tkernel
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  STEP4:
    CMD: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --demangle no ./t744
    CHECK_POINT: _Z7tkernelIiEvPT_
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"


CHECK_FILTER:
  LOG_NAME: ${BASE_LOG_PATH6}/check_filter.txt
  STEP1:
    CMD: export PATH=${ENV_PATH}; cd ${T744_PATH}; rm t744; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o t744 t744.cu
  STEP2:
    CMD: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck ./t744
    CHECK_POINT: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  STEP3:
    CMD1: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kernel_name=aaa ./t744
    CMD2: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kne=_Z7tkernelIiEvPT_ ./t744
    CMD3: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kne=tkernel  ./t744
    CHECK_POINT: "void tkernel<int>"
    CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
    CHECK_POINT2: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT2_1: "void tkernel<int>"
    CHECK_POINT3: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  STEP4:
    CMD1: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kns=tkernel  ./t744
    CMD2: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kernel_substring=aaaa  ./t744
    CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
    CHECK_POINT1_1: "void tkernel<int>"
    CHECK_POINT2: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  STEP5:
    CMD: cd ${T744_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --{} kne=_Z7tkernelIiEvPT_ ./t744
    CHECK_POINT1: "void tkernel<int>"

NVTX_OPTION:
  LOG_NAME: ${NVTX_OPTION_PATH}/nvtx_option.txt
  STEP1:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --nvtx no ./MemoryPool
    CHECK_POINT: "ERROR SUMMARY: 0 errors"
  STEP2:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no --nvtx yes ./MemoryPool
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no ./MemoryPool
    CHECK_POINT: "ERROR SUMMARY: 3 errors"
  STEP3:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no --nvtx yes --leak-check full ./Naming
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no --leak-check full ./Naming
    CHECK_POINT: "LEAK SUMMARY: 1 bytes leaked in 1 allocations"
    CHECK_POINT1: "ERROR SUMMARY: 1 error"
    CHECK_POINT2: "My allocation"

PADDING_OPTION:
  LOG_NAME: ${PADDING_OPTION_PATH}/padding_option.txt
  STEP1:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no --padding 16 ./Padding
    CHECK_POINT: "ERROR SUMMARY: 1 error"

LAUNCH_COUNT_SKIP:
  LOG_NAME: ${LAUNCH_COUNT_SKIP_PATH}/launch_count_skip.txt
  STEP1:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 6 hazards displayed (6 errors, 0 warnings)"
  STEP2:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck -s 2 -c 2 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 2 hazards displayed (2 errors, 0 warnings)"
  STEP3:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck -c 4 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 4 hazards displayed (4 errors, 0 warnings)"
  STEP4:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck -s 2 ./raceSkipCount
    CHECK_POINT: "RACECHECK SUMMARY: 4 hazards displayed (4 errors, 0 warnings)"

SANITIZER_NO_CRASH:
  LOG_NAME: ${SANITIZER_NO_CRASH_PATH}/sanitizer_not_crash.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r alloca; mkdir alloca; cd alloca; wget --user %s --password %s %s/%s/%s
    CMD1: cd ${TOOLS_HOME}/../alloca; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o alloca Alloca1.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../alloca; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool memcheck ./alloca
  STEP2:
    CMD: cd ${TOOLS_HOME}/../alloca; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck ./alloca
  STEP3:
    CMD: cd ${TOOLS_HOME}/../alloca; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck ./alloca
  STEP4:
    CMD: cd ${TOOLS_HOME}/../alloca; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./alloca
  CHECK_POINT: "ERROR SUMMARY: 0 errors"
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

SANITIZER_NO_REPORT_ERROR:
  LOG_NAME: ${SANITIZER_NO_REPORT_ERROR_PATH}/sanitizer_no_report_error.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r Bug3188503; mkdir Bug3188503; cd Bug3188503
    CMD1: cd ${TOOLS_HOME}/../Bug3188503; wget --user %s --password %s %s/%s/%s
    CMD2: cd ${TOOLS_HOME}/../Bug3188503; cuda_root=`grep 'CUDA_ROOT =' Makefile`;sed -i "s#$cuda_root#CUDA_ROOT = /usr/local/cuda-${CUDA_SHORT_VERSION}#" Makefile; make clean all
  STEP1:
    CMD: cd ${TOOLS_HOME}/../Bug3188503; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./a.out
  CHECK_POINT: "ERROR SUMMARY: 0 errors"

TARGET_PROCESS_OPTION:
  LOG_NAME: ${TARGET_PROCESS_OPTION_PATH}/target_process_option.txt
  PREPARE:
    CMD1: cd %s/matrixMul; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD2: cd %s/asyncAPI; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD3: cd %s/mergeSort; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD1_1: cd %s/matrixMul; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD2_1: cd %s/asyncAPI; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD3_1: cd %s/mergeSort; make clean; make -j16 -k; make dbg=1 -B -j16 -k
    CMD4: cd ${TOOLS_HOME}/../; rm run.sh; touch run.sh; chmod u+x run.sh
    CMD5: cd ${TOOLS_HOME}/../; rm test.py; wget --user %s --password %s %s/P1072_T2493063/test.py; chmod u+x test.py
    CMD6: cd ${TOOLS_HOME}/../; rm test.pl; wget --user %s --password %s %s/P1072_T2493063/test.pl; chmod u+x test.pl
    CMD7: cd ${TOOLS_HOME}/../; sed -i 's#/home/<USER>/NVIDIA_CUDA-11.8_Samples/Samples/0_Introduction#{}#g' test.py; sed -i 's#/home/<USER>/NVIDIA_CUDA-11.8_Samples/Samples/0_Introduction#{}#g' test.pl
  STEP1:
    CMD: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool memcheck -c 16 {} ./run.sh
    CMD1: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool memcheck -c 16 {} python3 ./test.py
    CMD2: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool memcheck -c 16 {} perl ./test.pl
  STEP2:
    CMD_1: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool initcheck -c 16 {} ./run.sh
    CMD_2: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool synccheck -c 16 {} ./run.sh
    CMD_3: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool racecheck -c 16 {} ./run.sh
  CHECK_POINT: "ERROR SUMMARY: 0 errors"
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

RACECHECK_DETECT_PRINT_OPTION:
  LOG_NAME: ${RACECHECK_DETECT_PRINT_OPTION_PATH}/racecheck_detect_print_option.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2681761; mkdir 2681761; cd 2681761; wget --user %s --password %s %s/%s/%s
    CMD1: cd ${TOOLS_HOME}/../2681761; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o race race.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2681761; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --racecheck-report hazard --racecheck-detect-level info --print-level warn --save out.nvm ./race
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2681761; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool racecheck --print-level info --read out.nvm
  STEP3:
    CMD: cd ${TOOLS_HOME}/../2681761; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool racecheck --racecheck-report hazard --racecheck-detect-level warn ./race
  STEP4:
    CMD: cd ${TOOLS_HOME}/../2681761; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool racecheck --racecheck-report hazard --racecheck-detect-level error ./race
  CHECK_POINT: "RACECHECK SUMMARY: 3 hazards displayed (1 error, 2 warnings)"
  CHECK_POINT1: "RACECHECK SUMMARY: 12 hazards displayed (1 error, 2 warnings)"
  CHECK_POINT1_1: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"


CUDA_COREDUMP_DISABLE:
  LOG_NAME: ${CUDA_COREDUMP_DISABLE_PATH}/cuda_coredump_disable.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2524649; mkdir 2524649; cd 2524649; wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD1: cd ${TOOLS_HOME}/../2524649; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -o memcheck_demo memcheck_demo.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2524649; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel  ./memcheck_demo
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2524649; export CUDA_COREDUMP_FILE=corefile; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel  ./memcheck_demo
  STEP3:
    CMD: cd ${TOOLS_HOME}/../2524649; export CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel ./memcheck_demo
  CHECK_POINT: "ERROR SUMMARY: 2 errors"

CUDA_LEAK_CHECK:
  LOG_NAME: ${CUDA_LEAK_CHECK_PATH}/cuda_leak_check.txt
  PREPARE:
    CMD: cd %s/simpleCubemapTexture; cp simpleCubemapTexture.cu simpleCubemapTexture.cu_bak; sed -i "/checkCudaErrors(cudaFreeArray(cu_3darray));/a\    cudaDeviceReset();" simpleCubemapTexture.cu;  sed -i '251d' simpleCubemapTexture.cu; make clean; make dbg=1
    CMD1: cd %s/simpleCubemapTexture; cp simpleCubemapTexture.cu simpleCubemapTexture.cu_bak; sed -i '/checkCudaErrors(cudaFreeArray(cu_3darray));/d' simpleCubemapTexture.cu; make clean; make dbg=1
    CMD2: cd %s/simpleCubemapTexture; cp simpleCubemapTexture.cu simpleCubemapTexture.cu_bak; sed -i '/checkCudaErrors(cudaFreeArray(cu_3darray));/d' simpleCubemapTexture.cu; make clean; make dbg=1
  STEP1:
    CMD: cd %s/simpleCubemapTexture; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./simpleCubemapTexture
  RESTORE:
    CMD: cd %s/simpleCubemapTexture; rm simpleCubemapTexture.cu; cp simpleCubemapTexture.cu_bak simpleCubemapTexture.cu; make clean; make dbg=1
  CHECK_POINT: "LEAK SUMMARY: 24576 bytes leaked in 1 allocations"
  CHECK_POINT1: "ERROR SUMMARY: 1 error"

DEVICE_STACK_FRAME:
  LOG_NAME: ${DEVICE_STACK_FRAME_PATH}/device_stack_frame.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2764359; mkdir 2764359; cd 2764359; wget --user %s --password %s %s/2764359/test_double_free.cu; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o test test_double_free.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2764359; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel  ./test
  CHECK_POINT: "ERROR SUMMARY: 1 error"
  CHECK_POINT1: "Malloc/Free Error encountered : Double free"
  CHECK_POINT2: "test_double_free.cu:4:f()"
  CHECK_POINT3: "kernel()"
  CHECK_POINT2_1: "test_double_free.cu"
  CHECK_POINT4: "f()"

CORRECT_LINE_NUM:
  LOG_NAME: ${CORRECT_LINE_NUM_PATH}/correct_line_num.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2716405; mkdir 2716405; cd 2716405; wget --user %s --password %s %s/2716405/testbt2-noinline.cu
    CMD_1: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -o test-lineinfo testbt2-noinline.cu
    CMD_2: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -rdc=true -o test-lineinfo-rdc testbt2-noinline.cu
    CMD1: cd ${TOOLS_HOME}/../2716405; wget --user %s --password %s %s/2716405/testbt2-forceinline.cu
    CMD1_1: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -o test-lineinfo-forceinline testbt2-forceinline.cu
    CMD1_2: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -rdc=true -o test-lineinfo-forceinline-rdc testbt2-forceinline.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --show-backtrace device  ./test-lineinfo
    CMD1: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --show-backtrace device ./test-lineinfo-rdc
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --show-backtrace device  ./test-lineinfo-forceinline
    CMD1: cd ${TOOLS_HOME}/../2716405; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --show-backtrace device ./test-lineinfo-forceinline-rdc
  CHECK_POINT_1: "testbt2-noinline.cu:14:local_func"
  CHECK_POINT_2: "testbt2-noinline.cu:23:local_access"
  CHECK_POINT_3: "testbt2-noinline.cu:32:module_test"
  CHECK_POINT1: "testbt2-forceinline.cu:14:local_func"
  CHECK_POINT1_1: "testbt2-forceinline.cu:23:local_access"
  CHECK_POINT1_2: "testbt2-forceinline.cu:32:module_test"
  CHECK_POINT1_3: module_test.*local_func\(long\)\+0x.*testbt2-noinline.cu:14
  CHECK_POINT1_4: module_test.*local_access\(\)\+0x.*testbt2-noinline.cu:23
  CHECK_POINT1_5: module_test\(\)\+0x.*testbt2-noinline.cu:32
  CHECK_POINT2_1: local_func\(long\)\+0x.*testbt2-noinline.cu:14
  CHECK_POINT2_2: local_access\(\)\+0x.*testbt2-noinline.cu:23
  CHECK_POINT2_3: module_test\(\)\+0x.*testbt2-noinline.cu:32
  CHECK_POINT3_1: local_func\(long\)\+0x.*testbt2-forceinline.cu:14
  CHECK_POINT3_2: local_access\(\)\+0x.*testbt2-forceinline.cu:23
  CHECK_POINT3_3: module_test\(\)\+0x.*testbt2-forceinline.cu:32


MEMCHECK_WITH_FREE:
  LOG_NAME: ${MEMCHECK_WITH_FREE_PATH}/memcheck_with_free.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2788746; mkdir 2788746; cd 2788746; wget --user %s --password %s %s/2788746/test.cu
    CMD1: cd ${TOOLS_HOME}/../2788746; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -o test test.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2788746; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --show-backtrace=no --destroy-on-device-error=kernel ./test
    CHECK_POINT: "Malloc/Free Warning encountered : Empty malloc"
    CHECK_POINT1: "Malloc/Free Error encountered : Double free"
    CHECK_POINT2: "ERROR SUMMARY: 2 errors"

RELATIVE_RETURN_ADDRESS:
  LOG_NAME: ${RELATIVE_RETURN_ADDRESS_PATH}/relative_return_address.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/; rm -r 2800789; mkdir 2800789; cd 2800789; wget --user %s --password %s %s/P1072_T2800789/RelativeReturnAddress.cu
    CMD1: cd ${TOOLS_HOME}/2800789; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lineinfo -lcudart -o RelativeReturnAddress RelativeReturnAddress.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/2800789; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace device ./RelativeReturnAddress
    CHECK_POINT: "Invalid __global__ read of size 4 bytes"
    CHECK_POINT_1: "Invalid __global__ read of size 4 bytes"
    CHECK_POINT1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:21:device_kernel0(int *)"
    CHECK_POINT2: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:28:device_kernel0(int *)"
    CHECK_POINT3: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:31:device_kernel0(int *)"
    CHECK_POINT4: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:41:device_kernel0(int *)"
    CHECK_POINT5: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:53:device_kernel0(int *)"
    CHECK_POINT6: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:61:device_kernel0(int *)"
    CHECK_POINT7: "Device Frame:${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:70:device_kernel0(int *)"
    CHECK_POINT1_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:21"
    CHECK_POINT2_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:28"
    CHECK_POINT3_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:31"
    CHECK_POINT4_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:41"
    CHECK_POINT5_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:53"
    CHECK_POINT6_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:61"
    CHECK_POINT7_1: "${TOOLS_HOME}/2800789/RelativeReturnAddress.cu:70"
    CHECK_POINT8: "Address 0x3 is misaligned"
    CHECK_POINT9: "ERROR SUMMARY: 3 errors"
    CHECK_POINT10: "Device Frame:[clone device_kernel0(int *)]"

FILE_DESCRIPTOR_LIMIT:
  LOG_NAME: ${FILE_DESCRIPTOR_LIMIT_PATH}/file_descriptor_limit.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2807199; mkdir 2807199; cd 2807199; wget --user %s --password %s %s/P1072_T2807199/test.c ;gcc -o test test.c
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2807199; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --target-processes all ./test
  STEP2:
    CMD: cd ${TOOLS_HOME}/../2807199; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/ncu --target-processes all ./test

READ_ONLY_FLAG:
  LOG_NAME: ${READ_ONLY_FLAG_PATH}/read_only_flag.txt
  STEP1:
    CMD: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --show-backtrace no memmapBasic read-only
  CHECK_POINT: 'ERROR SUMMARY: 1 error'

DETAIL_SESSION_OPTION:
  LOG_NAME: ${DETAIL_SESSION_OPTION_PATH}/detail_session_option.txt
  STEP1:
    CMD: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-session-details {} ./asyncAPI
  STEP2:
    CMD: cd ${SAMPLE_BIN_PATH}; rm out.nvm; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-session-details {} --save-session-details {} --save out.nvm ./asyncAPI
  STEP3:
    CMD: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --read out.nvm --print-session-details {}
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT1: 'Process ID'
  CHECK_POINT2: 'Created'
  CHECK_POINT3: 'System OS'
  CHECK_POINT4: 'OS build'
  CHECK_POINT5: 'System CPU'
  CHECK_POINT6: 'CPU architecture'
  CHECK_POINT7: 'Computer Name'
  CHECK_POINT8: 'Target application'
  CHECK_POINT9: 'CUDA version'
  CHECK_POINT10: 'Display Driver version'

STOP_USING_WARPFULLMASK:
  LOG_NAME: ${STOP_USING_WARPFULLMASK_PATH}/stop_using_warpfullmask.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r Bug200746055; mkdir Bug200746055; cd Bug200746055 ; wget --user %s --password %s %s/Bug200746055/repro.cu ;
    CMD1: cd ${TOOLS_HOME}/../Bug200746055; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lineinfo -lcuda -o repro repro.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../Bug200746055; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --target-processes all ./repro
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'

LEAK_CHECK_ALLOC:
  LOG_NAME: ${LEAK_CHECK_ALLOC_PATH}/leak_check_alloc.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2833990; mkdir 2833990; cd 2833990 ; wget --user %s --password %s %s/2833990/nvcc_sanitizers.cu
    CMD1: cd ${TOOLS_HOME}/../2833990; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -o nvcc_sanitizers nvcc_sanitizers.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2833990; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./nvcc_sanitizers
  CHECK_POINT: 'ERROR SUMMARY: 3 errors'
  CHECK_POINT1: 'LEAK SUMMARY: 196608 bytes leaked in 3 allocations'

STACK_OVERFLOW_CHECK:
  LOG_NAME: ${STACK_OVERFLOW_CHECK_PATH}/stack_overflow_check.txt
  PREPARE:
    CMD: cd ${TOOLS_HOME}/../; rm -r 2897883; mkdir 2897883; cd 2897883 ; wget --user %s --password %s %s/P1072_T2897883/test.cu
    CMD1: cd ${TOOLS_HOME}/../2897883; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lineinfo -arch=sm_%s -o test test.cu
  STEP1:
    CMD: cd ${TOOLS_HOME}/../2897883; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel ./test
  CHECK_POINT: 'Stack overflow'
  CHECK_POINT1: 'test.cu:3:test(int)'
  CHECK_POINT2: 'ERROR SUMMARY: 1 error'
  CHECK_POINT1_1: 'test.cu:3'

CUDA_BARRIERS_OPTION:
  LOG_NAME: ${CUDA_BARRIERS_OPTION_PATH}/cuda_barriers_option.txt
  PREPARE:
    CMD1: cd ${SAMPLE_0_PATH}/simpleAWBarrier; mv simpleAWBarrier.cu simpleAWBarrier.cu_bak; make clean; wget --user %s --password %s %s/P1072_T2819793/simpleAWBarrier-mod.cu
    CMD2: cd ${SAMPLE_0_PATH}/simpleAWBarrier; mv simpleAWBarrier-mod.cu simpleAWBarrier.cu; make
  STEP1:
    CMD: cd ${SAMPLE_0_PATH}/simpleAWBarrier; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool {} ./simpleAWBarrier
  STEP2:
    CMD: cd ${SAMPLE_0_PATH}/simpleAWBarrier; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool {} --num-cuda-barriers 0 ./simpleAWBarrier
  STEP3:
    CMD: cd ${SAMPLE_0_PATH}/simpleAWBarrier; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool {} --num-cuda-barriers 2 ./simpleAWBarrier
  STEP4:
    CMD: cd ${SAMPLE_0_PATH}/simpleAWBarrier; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool {} --num-cuda-barriers 1 ./simpleAWBarrier
  RESTORE:
    CMD: cd ${SAMPLE_0_PATH}/simpleAWBarrier; rm simpleAWBarrier.cu; mv simpleAWBarrier.cu_bak simpleAWBarrier.cu; make clean; make
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'
  CHECK_POINT1: 'ERROR SUMMARY: 1 error'
  CHECK_POINT2: 'Warning: Detected overflow of tracked cuda::barrier structures. Results might be incorrect. Try using --num-cuda-barriers to fix the issue'
  CHECK_POINT3: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'
  CHECK_POINT4: 'RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)'

TARGET_FILTER_OPTION:
  LOG_NAME: ${TARGET_FILTER_OPTION_PATH}/target_filter_option.txt
  STEP1:
    CMD1: rm -r %s/vectorAdd_err; cp -r %s/vectorAdd %s/vectorAdd_err; cd %s/vectorAdd_err; sed -i 's#err = cudaFree(d_C)#//err = cudaFree(d_C)#' vectorAdd.cu; make clean; make dbg=1
    CMD2: cd ${TOOLS_HOME}/../; rm -r test.sh; touch test.sh; chmod u+x test.sh
  STEP2:
    CMD1: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full {} ./test.sh
    CMD2: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full {} --target-processes-filter mergeSort ./test.sh
    CMD3: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full {} --target-processes-filter regex:.*merge ./test.sh
    CMD4: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full {} --target-processes-filter regex:.*vector ./test.sh
  STEP3:
    CMD1: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer {} --target-processes-filter matrixMul ./test.sh
    CMD2: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer {} --target-processes-filter Matrix ./test.sh
    CMD3: cd ${TOOLS_HOME}/../; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer {} --target-processes-filter regex:*Mul ./test.sh
  RESTORE:
    CMD1: cd %s/vectorAdd; make -j16 -k; make dbg=1 -B -j16 -k
  CHECK_POINT1: "LEAK SUMMARY: 200000 bytes leaked in 1 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 1 error"
  CHECK_POINT3: "LEAK SUMMARY: 0 bytes leaked in 0 allocations"
  CHECK_POINT4: "ERROR SUMMARY: 0 errors"
  CHECK_POINT5: "Error: Target application terminated before first instrumented API call"
  CHECK_POINT6: 'Invalid regex provided to --target-processes-filter option: The repeat operator "*" cannot start a regular expression.'
  CHECK_POINT6_1: 'Invalid regex provided to --target-processes-filter option. Invalid regex. The repeat operator "*" cannot start a regular expression'
  CHECK_POINT7: "The error occurred while parsing the regular expression: '>>>HERE>>>*Mul'"

SANITIZER_XML_OPTION:
  LOG_NAME: ${SANITIZER_XML_OPTION_PATH}/sanitizer_xml_option.txt
  STEP1:
    CMD1: cd ${TOOLS_HOME}/; rm -r 2800789; mkdir 2800789; cd 2800789; wget --user %s --password %s %s/P1072_T2800789/RelativeReturnAddress.cu
    CMD2: cd ${TOOLS_HOME}/2800789; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -lcudart -gencode arch=compute_%s,code=sm_%s -o RelativeReturnAddress RelativeReturnAddress.cu
  STEP2:
    CMD1: cd ${TOOLS_HOME}/2800789; rm out.xml; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error=kernel --xml {} --save out.xml ./RelativeReturnAddress
    CMD2: cd ${TOOLS_HOME}/2800789; rm out.xml; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --destroy-on-device-error=kernel --launch-timeout 600 --xml {} --save out.xml ./RelativeReturnAddress

INVALID_MEMORY_ACCESS:
  LOG_NAME: ${INVALID_MEMORY_ACCESS_PATH}/invalid_memory_access.txt
  STEP1:
    CMD1: cd ${TOOLS_HOME}/; rm -r Julia; mkdir Julia; cd Julia; wget https://julialang-s3.julialang.org/bin/linux/x64/1.6/julia-1.6.0-rc3-linux-x86_64.tar.gz;tar -xvf julia-1.6.0-rc3-linux-x86_64.tar.gz
    CMD2: cd ${TOOLS_HOME}/Julia; git clone https://github.com/JuliaGPU/CUDA.jl.git; cd CUDA.jl; git checkout f3b5ea5059808137ba0037d30aeca640fa506977
    CMD3: cd ${TOOLS_HOME}/Julia; lftp -c 'glob -- pget -n 80  http://cuda-internal.nvidia.com/release-candidates/kitpicks/cuda-r11-2/11.2.2/005/local_installers/cuda_11.2.2_460.32.03_linux.run'; echo ${HOST_PASSWORD} | sudo -S bash cuda_11.2.2_460.32.03_linux.run --silent --toolkit
    CMD4: cd ${TOOLS_HOME}/Julia/CUDA.jl; echo ${HOST_PASSWORD} | sudo -S rm -r ~/.julia; PATH=${TOOLS_HOME}/Julia/julia-1.6.0-rc3/bin:"$PATH" julia --project -e 'using Pkg; Pkg.instantiate()'
    CMD5: cd ${TOOLS_HOME}/Julia/CUDA.jl; rm reproducer.jl; touch reproducer.jl
  STEP2:
    CMD1: cd ${TOOLS_HOME}/Julia/CUDA.jl; export PATH=${TOOLS_HOME}/Julia/julia-1.6.0-rc3/bin:"$PATH"; export JULIA_CUDA_USE_BINARYBUILDER=false; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --show-backtrace=no --report-api-errors=no --launch-timeout=0 julia --project reproducer.jl
  RESTORE:
    CMD1: cd /usr/local; echo ${HOST_PASSWORD} | sudo -S rm -r cuda; echo ${HOST_PASSWORD} | sudo -S ln -s /usr/local/cuda-${CUDA_SHORT_VERSION} cuda
  CHECK_POINT: "ERROR SUMMARY: 0 errors"

SANITIZER_UNDER_MPS:
  LOG_NAME: ${SANITIZER_UNDER_MPS_PATH}/invalid_memory_access.txt
  PREPARE:
    CMD1: ulimit -n 16000;export CUDA_MPS_LOG_DIRECTORY=/tmp; export CUDA_MPS_PIPE_DIRECTORY=/tmp; nvidia-cuda-mps-control -d; cd ${SAMPLE_BIN_PATH}; ./asyncAPI
    CMD2: ps -ef| grep nvidia
    CHECK_POINT: nvidia-cuda-mps-server
  STEP1:
    CHECK_OPTION: leak-check,initcheck,synccheck,racecheck
    CMD:
      -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool memcheck --leak-check full --save leak.log ./matrixMul; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --read leak.log
      -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --save initcheck.log ./matrixMul
      -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck --log-file synccheck.log ./asyncAPI
      -   cd ${SAMPLE_BIN_PATH}&&rm *.log; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --log-file %p.log ./mergeSort
    CMD1: cd ${SAMPLE_BIN_PATH}; ls *.log
    CHECK_POINT: '0 errors'
    CHECK_POINT1: '========= COMPUTE-SANITIZER'
    CHECK_POINT2: 'ERROR SUMMARY: 0 errors'
    CHECK_POINT3: 'LEAK SUMMARY: 0 bytes leaked in 0 allocations'
  STEP2:
    CMD1: cd %s; python3 ComputeSanitizerTest.py --mps  2>&1| tee %s/sanitizer_test.txt
  STEP3:
    CMD1: ps -ef |grep nvidia-cuda-mps | awk '{print $2}'
    CMD2: kill -9 %s

SANITIZER_TEST_CUBIN:
  LOG_NAME: ${SANITIZER_TEST_CUBIN_PATH}/sanitizer_test_cubin.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r large-cubin-generator; mkdir large-cubin-generator
    CMD2: cd ${TOOLS_HOME}/large-cubin-generator; wget --user %s --password %s %s/large-cubin-generator/%s
  STEP1:
    CMD1: cd ${TOOLS_HOME}/large-cubin-generator; bash generate_large_cubin.sh output.cubin sm_%s
    CMD2: cd ${TOOLS_HOME}/large-cubin-generator; ls -l output.cubin | awk '{print $5}'
    CMD3: cd ${TOOLS_HOME}/large-cubin-generator; make; ./load_module output.cubin
  STEP2:
    CMD1: cd ${TOOLS_HOME}/large-cubin-generator;/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./load_module output.cubin
  CHECK_POINT: 'ERROR SUMMARY: 0 errors'

RACECHECK_WITH_QUDA:
  LOG_NAME: ${RACECHECK_WITH_QUDA_PATH}/racecheck_with_quda.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r quda; mkdir quda;cd quda; wget --user %s --password %s %s/P1072_T2771164/quda.tar.gz; tar zxvf quda.tar.gz
    CMD2: cd ${TOOLS_HOME}/quda/quda; git checkout feature/generic_kernel; mkdir build; cd build; PATH=/opt/cmake/bin:"$PATH" cmake .. -DQUDA_DIRAC_DEFAULT_OFF=ON -DQUDA_DIRAC_WILSON=ON -DQUDA_MULTIGRID=ON -DQUDA_PRECISION=4 -DQUDA_RECONSTRUCT=4 -DQUDA_FAST_COMPILE_REDUCE=ON  -DQUDA_FAST_COMPILE_DSLASH=ON -DCMAKE_BUILD_TYPE=RELEASE
    CMD3: cd ${TOOLS_HOME}/quda/quda/build; PATH=/opt/cmake/bin:"$PATH" cmake .; make -j24
  STEP1:
    CMD1: cd ${TOOLS_HOME}/quda/quda/build; ${QUDA_ENV} tests/invert_test $META $SOLVER_PARAM $MG_PARAM $LEVEL0_PARAM $LEVEL1_PARAM $LEVEL2_PARAM
    CMD2: cd ${TOOLS_HOME}/quda/quda/build; ${QUDA_ENV} /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --launch-timeout 600 --tool racecheck tests/invert_test $META $SOLVER_PARAM $MG_PARAM $LEVEL0_PARAM
  CHECK_POINT: 'RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)'

SANITIZER_MEMCHECK_DEMO:
  LOG_NAME: ${SANITIZER_MEMCHECK_DEMO_PATH}/sanitizer_memcheck_demo.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r memcheck; mkdir memcheck;cd memcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/memcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD3: cd ${TOOLS_HOME}/memcheck; make clean; make
    CMD4: cd ${TOOLS_HOME}/memcheck; make clean; make dbg=1
  STEP1:
    CMD1: cd ${TOOLS_HOME}/memcheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error=kernel ./memcheck_demo
  STEP2:
    CMD1: cd ${TOOLS_HOME}/memcheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error=kernel ./memcheck_demo
  CHECK_POINT1: "Invalid __global__ write of size 4 bytes"
  CHECK_POINT2: "unaligned_kernel()"
  CHECK_POINT3: "misaligned"
  CHECK_POINT4: "out_of_bounds_kernel()"
  CHECK_POINT5: "is out of bounds"
  CHECK_POINT6: "ERROR SUMMARY: 2 errors"
  CHECK_POINT7: "memcheck_demo.cu:34:unaligned_kernel()"
  CHECK_POINT8: "memcheck_demo.cu:39:out_of_bounds_function()"
  CHECK_POINT9: "memcheck_demo.cu:34"
  CHECK_POINT10: "memcheck_demo.cu:39"

SANITIZER_RACECHECK_WARP:
  LOG_NAME: ${SANITIZER_RACECHECK_WARP_PATH}/sanitizer_racecheck_warp.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r racecheck; mkdir racecheck;cd racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/Makefile
    CMD2: cd ${TOOLS_HOME}/racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/warp_error.cu
    CMD3: cd ${TOOLS_HOME}/racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/block_error.cu
    CMD4: cd ${TOOLS_HOME}/racecheck; make clean; make
  STEP1:
    CMD1: cd ${TOOLS_HOME}/racecheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --print-limit 0 --tool racecheck --racecheck-report hazard ./warp_error
  CHECK_POINT1: "Warning: (Warp Level Programming) Potential RAW hazard detected"
  CHECK_POINT2: "Write Thread"
  CHECK_POINT3: "warp_error.cu:44:sumKernel"
  CHECK_POINT4: "Read Thread"
  CHECK_POINT5: "warp_error.cu:56:sumKernel"
  CHECK_POINT6: "RACECHECK SUMMARY: 248 hazards displayed (0 errors, 248 warnings)"
  CHECK_POINT7: "warp_error.cu:44"
  CHECK_POINT8: "warp_error.cu:56"

SANITIZER_RACECHECK_BLOCK:
  LOG_NAME: ${SANITIZER_RACECHECK_BLOCK_PATH}/sanitizer_racecheck_block.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r racecheck; mkdir racecheck;cd racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/Makefile
    CMD2: cd ${TOOLS_HOME}/racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/warp_error.cu
    CMD3: cd ${TOOLS_HOME}/racecheck; wget --user %s --password %s %s/compute-sanitizer-samples/Racecheck/block_error.cu
    CMD4: cd ${TOOLS_HOME}/racecheck; make clean; make
  STEP1:
    CMD1: cd ${TOOLS_HOME}/racecheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --racecheck-report analysis ./block_error
  CHECK_POINT1: 'Error: Race reported between Write access at sumKernel.*block_error.cu:41'
  CHECK_POINT2: 'Read access at sumKernel.*block_error.cu:51'
  CHECK_POINT1_1: 'Error: Race reported between Write access at .*block_error.cu:41:sumKernel'
  CHECK_POINT2_1: 'Read access at .*block_error.cu:51:sumKernel'
  CHECK_POINT3: 'RACECHECK SUMMARY: 1 hazard displayed \(1 error, 0 warnings\)'

SANITIZER_INITCHECK_ERROR:
  LOG_NAME: ${SANITIZER_INITCHECK_ERROR_PATH}/sanitizer_initcheck_error.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r initcheck; mkdir initcheck;cd initcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Initcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/initcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Initcheck/memset_error.cu
    CMD3: cd ${TOOLS_HOME}/initcheck; make clean; make
  STEP1:
    CMD1: cd ${TOOLS_HOME}/initcheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck  ./memset_error
  CHECK_POINT1: "Uninitialized __global__ memory read of size 4 bytes"
  CHECK_POINT2: "memset_error.cu:41:vectorAdd"
  CHECK_POINT3: " ERROR SUMMARY: 48 errors"
  CHECK_POINT2_1: "vectorAdd.*memset_error.cu:41"

SANITIZER_LEAKCHECK_MEMCHECK_DEMO:
  LOG_NAME: ${SANITIZER_LEAKCHECK_MEMCHECK_DEMO_PATH}/sanitizer_leakcheck_memcheck_demo.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r memcheck; mkdir memcheck;cd memcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/Makefile
    CMD2: cd ${TOOLS_HOME}/memcheck; wget --user %s --password %s %s/compute-sanitizer-samples/Memcheck/memcheck_demo.cu
    CMD3: cd ${TOOLS_HOME}/memcheck; make clean; make dbg=1
  STEP1:
    CMD1: cd ${TOOLS_HOME}/memcheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error=kernel --leak-check=full ./memcheck_demo
  CHECK_POINT1: "Invalid __global__ write of size 4 bytes"
  CHECK_POINT2: "LEAK SUMMARY: 1024 bytes leaked in 1 allocations"
  CHECK_POINT3: "ERROR SUMMARY: 3 errors"
  CHECK_POINT4: 'memcheck_demo.cu:65'

SANITIZER_SYNCCHECK_DIVERGENT:
  LOG_NAME: ${SANITIZER_SYNCCHECK_DIVERGENT_PATH}/sanitizer_synccheck_divergent.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r synccheck; mkdir synccheck;cd synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/Makefile
    CMD2: cd ${TOOLS_HOME}/synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/divergent_threads.cu
    CMD3: cd ${TOOLS_HOME}/synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/illegal_syncwarp.cu
    CMD4: cd ${TOOLS_HOME}/synccheck; make clean; make
  STEP1:
    CMD1: cd ${TOOLS_HOME}/synccheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool synccheck ./divergent_threads
  CHECK_POINT1: "Barrier error detected. Divergent thread(s) in warp"
  CHECK_POINT2: "ERROR SUMMARY: 16 errors"
  CHECK_POINT3: "ERROR SUMMARY: 0 error"

SANITIZER_SYNCCHECK_SYNCWARP:
  LOG_NAME: ${SANITIZER_SYNCCHECK_SYNCWARP_PATH}/sanitizer_synccheck_syncwarp.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r synccheck; mkdir synccheck;cd synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/Makefile
    CMD2: cd ${TOOLS_HOME}/synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/divergent_threads.cu
    CMD3: cd ${TOOLS_HOME}/synccheck; wget --user %s --password %s %s/compute-sanitizer-samples/Synccheck/illegal_syncwarp.cu
    CMD4: cd ${TOOLS_HOME}/synccheck; make clean; make; mv illegal_syncwarp illegal_syncwarp-release
    CMD5: cd ${TOOLS_HOME}/synccheck; make clean; make dbg=1; mv illegal_syncwarp illegal_syncwarp-debug
  STEP1:
    CMD1: cd ${TOOLS_HOME}/synccheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool synccheck ./illegal_syncwarp-release
    CMD2: cd ${TOOLS_HOME}/synccheck; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool synccheck ./illegal_syncwarp-debug
  CHECK_POINT1: "Barrier error detected. Invalid arguments"
  CHECK_POINT2: "ERROR SUMMARY: 17 errors"

LEAKCHECK_DEVICES_ALLOCATIONS:
  LOG_NAME: ${LEAKCHECK_DEVICES_ALLOCATIONS_PATH}/leakcheck_devices_allocations.txt
  STEP1:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --leak-check full ./MallocAsync
  CHECK_POINT1: "LEAK SUMMARY: 4 bytes leaked in 1 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 3 errors"

MEMCHECK_OPTION_TRACK_STREAM:
  LOG_NAME: ${MEMCHECK_OPTION_TRACK_STREAM_PATH}/memcheck_option_track_stream.txt
  STEP1:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace kernel
  STEP2:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --track-stream-ordered-races use-before-alloc --show-backtrace no ./MallocAsyncRace kernel
  STEP3:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --track-stream-ordered-races use-after-free --show-backtrace no ./MallocAsyncRace kernel
  STEP4:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --track-stream-ordered-races no --show-backtrace no ./MallocAsyncRace kernel
  CHECK_POINT1: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2: "Free-before-alloc"
  CHECK_POINT3: "Use-after-free"
  CHECK_POINT4: "ERROR SUMMARY: 15 errors"
  CHECK_POINT5: "ERROR SUMMARY: 24 errors"
  CHECK_POINT6: "ERROR SUMMARY: 39 errors"
  CHECK_POINT7: "ERROR SUMMARY: 0 errors"

SYNCCHECK_OPTION_MISS_BARRIER:
  LOG_NAME: ${SYNCCHECK_OPTION_MISS_BARRIER_PATH}/synccheck_option_miss_barrier.txt
  STEP1:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --tool synccheck  --missing-barrier-init-is-fatal yes ./SyncCudaBarrier 6
  CHECK_POINT1: "Barrier error detected. Missing init"
  CHECK_POINT2: "ERROR SUMMARY: 32 errors"

CALL_HOST_DEVICE:
  LOG_NAME: ${CALL_HOST_DEVICE_PATH}/call_host_device.txt
  STEP1:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel ./localRecursive
  STEP2:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --num-callers-device 0 --num-callers-host 0 ./localRecursive
  STEP3:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --num-callers-device 1 --num-callers-host 1 ./localRecursive
  STEP4:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --num-callers-device 2 --num-callers-host 2 ./localRecursive
  STEP5:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-error kernel --num-callers-device 2 --num-callers-host 2 --show-backtrace no ./localRecursive
  CHECK_POINT1: "ERROR SUMMARY: 1 error"

DEPRECAT_CUDA_MEMCHECK:
  LOG_NAME: ${DEPRECAT_CUDA_MEMCHECK_PATH}/deprecat_cuda_memcheck.txt
  STEP1:
    CMD1: cd %s/asyncAPI; make clean; make; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-memcheck ./asyncAPI
  CHECK_POINT1: "This tool is deprecated and will be removed in a future release of the CUDA toolkit"
  CHECK_POINT2: "Please use the compute-sanitizer tool as a drop-in replacement"

MEMCHECK_ORDERED_RACE_DETECTION:
  LOG_NAME: ${MEMCHECK_ORDERED_RACE_DETECTION_PATH}/memcheck_ordered_race_detection.txt
  STEP1:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace kernel
  STEP2:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memset
  STEP3:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memcpy-host-to-device
  STEP4:
    CMD1: cd %s; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --destroy-on-device-err kernel --track-stream-ordered-races all --show-backtrace no ./MallocAsyncRace memcpy-device-to-device
  CHECK_POINT1: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT2: "Free-before-alloc"
  CHECK_POINT3: "Use-after-free"
  CHECK_POINT4: "ERROR SUMMARY: 39 errors"
  CHECK_POINT5: "Use-before-alloc"
  CHECK_POINT6: "ERROR SUMMARY: 39 errors"

# run All checkers - add support __nv_aligned_device_malloc()   3071342
SUPPORT_ALIGNED_DEVICE_MALLOC:
  LOG_NAME: ${SUPPORT_ALIGNED_DEVICE_MALLOC_PATH}/support_aligned_device_malloc.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r 3071342; mkdir 3071342;cd 3071342; wget --user %s --password %s %s/P1072_T3071342/test.cu
    CMD2: cd ${TOOLS_HOME}/3071342; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -arch=sm_%s -o test test.cu
  STEP1:
    CMD1: cd ${TOOLS_HOME}/3071342; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  ./test
  STEP2:
    CMD1: cd ${TOOLS_HOME}/3071342; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck ./test
  STEP3:
    CMD1: cd ${TOOLS_HOME}/3071342; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool synccheck ./test
  STEP4:
    CMD1: cd ${TOOLS_HOME}/3071342; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./test
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"
  CHECK_POINT2: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

# Add sanitizer support for LDSM   3079874
SANITIZER_SUPPORT_LDSM:
  LOG_NAME: ${SANITIZER_SUPPORT_LDSM_PATH}/sanitizer_support_LDSM.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r 3079874; mkdir 3079874;cd 3079874; wget --user %s --password %s %s/P1072_T3079874/CuLdSm.cu
    CMD2: cd ${TOOLS_HOME}/3079874; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -arch sm_%s -lineinfo -o CuLdSm CuLdSm.cu
  STEP1:
    CMD1: cd ${TOOLS_HOME}/3079874; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./CuLdSm
  STEP2:
    CMD1: cd ${TOOLS_HOME}/3079874; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./CuLdSm
  CHECK_POINT1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2: "LEAK SUMMARY: 2052 bytes leaked in 2 allocations"
  CHECK_POINT3: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"
  CHECK_POINT4: "ERROR SUMMARY: 0 errors"


# Initcheck -option -track-unused-memory
SANITIZER_TRACK_UNUSED_MEMORY:
  LOG_NAME: ${SANITIZER_TRACK_UNUSED_MEMORY_PATH}/sanitizer_track_unused_memory.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r Unused; mkdir Unused;cd Unused; wget --user %s --password %s %s/Unused/unused.cu
    CMD2: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lcuda -o unused unused.cu
  STEP1:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} ./unused 80
  STEP2:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --unused-memory-threshold 75 ./unused 20
  STEP3:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --unused-memory-threshold 80 ./unused 20
  STEP4:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --unused-memory-threshold 81 ./unused 20
  STEP5:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --unused-memory-threshold 120 ./unused
  STEP6:
    CMD1: cd ${TOOLS_HOME}/Unused; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --track-unused-memory {} --unused-memory-threshold 75 --xml {} --save out.xml ./unused 20
  CHECK_POINT1: "ERROR SUMMARY: 1 error"
  CHECK_POINT2: "20% of allocation were unused"
  CHECK_POINT3: "F"
  CHECK_POINT4: "ERROR SUMMARY: 0 error"
  CHECK_POINT5: "the argument for option 'unused-memory-threshold' is invalid"

# [Sanitizer][RCCA]Calling sanitizerMemcpyDeviceToHost in the LAUNCH_END callback
SANITIZER_LAUNCH_END_CALLBACK:
  LOG_NAME: ${SANITIZER_LAUNCH_END_CALLBACK_PATH}/sanitizer_launch_end_callback.txt
  PREPARE:
    CMD1: cd ${TOOLS_HOME}/; rm -r sanitizer-bug-reproducer*; wget --user {} --password {} {}/sanitizer-bug-reproducer/sanitizer-bug-reproducer.tar.gz
    CMD2: cd ${TOOLS_HOME}/; tar zxvf sanitizer-bug-reproducer.tar.gz; cd sanitizer-bug-reproducer; chmod u+x run_hanging.sh
  STEP1:
    CMD1: cd ${TOOLS_HOME}/sanitizer-bug-reproducer;./run_hanging.sh
  CHECK_POINT1: "Sanitizer error"
  CHECK_POINT2: "kernel launch begin"
  CHECK_POINT3: "kernel launch end"

# [Sanitizer][RCCA]Initcheck for steam capture 3156524
SANITIZER_STEAM_CAPTURE:
  LOG_NAME: ${SANITIZER_STEAM_CAPTURE_PATH}/sanitizer_steam_capture.txt
  PREPARE:
    CMD1: cd ${SANITIZER_STEAM_CAPTURE_PATH}/; rm -r 3156524; mkdir 3156524; cd 3156524; wget --user {} --password {} {}/3156524/capture_memset.cu
    CMD2: cd ${SANITIZER_STEAM_CAPTURE_PATH}/3156524; nvcc -arch=compute_{} -Xcompiler -g -lineinfo capture_memset.cu
  STEP1:
    CMD1: cd ${SANITIZER_STEAM_CAPTURE_PATH}/3156524; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck a.out
  CHECK_POINT1: "ERROR SUMMARY: 0 error"

# Racechek option --racecheck-memcpy-async 3158301
SANITIZER_RACECHECK_MEMCPY_ASYNC:
  LOG_NAME: ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}/sanitizer_racecheck_memcpy_async.txt
  PREPARE:
    CMD1: cp {}/raceMemcpyAsync ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}
  STEP1:
    CMD1: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool=racecheck raceMemcpyAsync 1
    CMD2: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool=racecheck raceMemcpyAsync 2
  STEP2:
    CMD1: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool=racecheck --racecheck-memcpy-async no raceMemcpyAsync 1
    CMD2: cd ${SANITIZER_RACECHECK_MEMCPY_ASYNC_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool=racecheck --racecheck-memcpy-async no raceMemcpyAsync 2
  CHECK_POINT1: "RACECHECK SUMMARY: 1 hazard displayed (0 errors, 1 warning)"
  CHECK_POINT2: "RACECHECK SUMMARY: 1 hazard displayed (1 error, 0 warnings)"
  CHECK_POINT3: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

# Memcheck option --check-cache-control 3161772
SANITIZER_CHECK_CACHE_CONTROL:
  LOG_NAME: ${SANITIZER_CHECK_CACHE_CONTROL_PATH}/sanitizer_check_cache_control.txt
  PREPARE:
    CMD1: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}; wget --user {} --password {} {}/cache_control/cask_sdk_x86_64_linux_cuda11.4_internal_mini_release_72d188a63cb2154821a09aacc451103dd2a6eb2d.tar.gz
    CMD2: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}; tar zxvf *.tar.gz
  STEP1:
    CMD1: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}/*/bin; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --check-cache-control no ./cask-sdk-cask-tester run sm86_xmma_fprop_implicit_gemm_interleaved_i8i8_i8i32_f32_nchw_vect_c_32kcrs_vect_c_32_nchw_vect_c_32_tilesize32x32x64_stage5_warpsize2x1x1_g1_tensor16x8x32_simple_t1r1s1_epifadd --x.batches 1 --x.channels 64 --y.channels 64 --x.height 256 --x.width 512 --w.height 1 --w.width 1 --groups 1 --dilation_height 1 --dilation_width 1 --padding_top 0 --padding_bottom 0 --padding_left 0 --padding_right 0 --stride_height 1 --stride_width 1 --with_bias --alpha 1.0 --beta 1.0 --is_xcorrelation --apply_relu --fill skip --cta_swizzle --cask_managed_w --dynamic_resize --warm_ups 2 --iterations 1 --per_channel_scaling --cask_managed_w --cask_managed_bias --relu_upper_bound 127
    CMD2: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}/*/bin; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --check-cache-control yes ./cask-sdk-cask-tester run sm86_xmma_fprop_implicit_gemm_interleaved_i8i8_i8i32_f32_nchw_vect_c_32kcrs_vect_c_32_nchw_vect_c_32_tilesize32x32x64_stage5_warpsize2x1x1_g1_tensor16x8x32_simple_t1r1s1_epifadd --x.batches 1 --x.channels 64 --y.channels 64 --x.height 256 --x.width 512 --w.height 1 --w.width 1 --groups 1 --dilation_height 1 --dilation_width 1 --padding_top 0 --padding_bottom 0 --padding_left 0 --padding_right 0 --stride_height 1 --stride_width 1 --with_bias --alpha 1.0 --beta 1.0 --is_xcorrelation --apply_relu --fill skip --cta_swizzle --cask_managed_w --dynamic_resize --warm_ups 2 --iterations 1 --per_channel_scaling --cask_managed_w --cask_managed_bias --relu_upper_bound 127
  STEP1_1:
    CMD1: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}/*/bin; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./cask-sdk-cask-tester run sm86_xmma_fprop_implicit_gemm_interleaved_i8i8_i8i32_f32_nchw_vect_c_32kcrs_vect_c_32_nchw_vect_c_32_tilesize32x32x64_stage5_warpsize2x1x1_g1_tensor16x8x32_simple_t1r1s1_epifadd --x.batches 1 --x.channels 64 --y.channels 64 --x.height 256 --x.width 512 --w.height 1 --w.width 1 --groups 1 --dilation_height 1 --dilation_width 1 --padding_top 0 --padding_bottom 0 --padding_left 0 --padding_right 0 --stride_height 1 --stride_width 1 --with_bias --alpha 1.0 --beta 1.0 --is_xcorrelation --apply_relu --fill skip --cta_swizzle --cask_managed_w --dynamic_resize --warm_ups 2 --iterations 1 --per_channel_scaling --cask_managed_w --cask_managed_bias --relu_upper_bound 127
    CMD2: cd ${SANITIZER_CHECK_CACHE_CONTROL_PATH}/*/bin; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --check-cache-control ./cask-sdk-cask-tester run sm86_xmma_fprop_implicit_gemm_interleaved_i8i8_i8i32_f32_nchw_vect_c_32kcrs_vect_c_32_nchw_vect_c_32_tilesize32x32x64_stage5_warpsize2x1x1_g1_tensor16x8x32_simple_t1r1s1_epifadd --x.batches 1 --x.channels 64 --y.channels 64 --x.height 256 --x.width 512 --w.height 1 --w.width 1 --groups 1 --dilation_height 1 --dilation_width 1 --padding_top 0 --padding_bottom 0 --padding_left 0 --padding_right 0 --stride_height 1 --stride_width 1 --with_bias --alpha 1.0 --beta 1.0 --is_xcorrelation --apply_relu --fill skip --cta_swizzle --cask_managed_w --dynamic_resize --warm_ups 2 --iterations 1 --per_channel_scaling --cask_managed_w --cask_managed_bias --relu_upper_bound 127

  CHECK_POINT1: "ERROR SUMMARY: 0 error"


# Memcheck- stream ordered race check for read access 3178884
SANITIZER_CHECK_READ_ACCESS:
  LOG_NAME: ${SANITIZER_CHECK_READ_ACCESS_PATH}/sanitizer_check_read_access.txt
  PREPARE:
    CMD1: cd ${SANITIZER_CHECK_READ_ACCESS_PATH}; wget --user {} --password {} {}/ReadAccess/read_access.cu; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc  -o read_access read_access.cu
  STEP1:
    CMD1: cd ${SANITIZER_CHECK_READ_ACCESS_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --track-stream-ordered-races=all --show-backtrace=no read_access
  CHECK_POINT1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2: "Use-before-alloc"
  CHECK_POINT3: "Use-after-free"

# [Sanitizer][RCCA]memcheck for ptxas miscompile 3186829
SANITIZER_MEMCHECK_PTXAS_MISCOMPILER:
  LOG_NAME: ${SANITIZER_MEMCHECK_PTXAS_MISCOMPILER_PATH}/sanitizer_memcheck_ptxas_miscompiler.txt
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_PTXAS_MISCOMPILER_PATH}; wget --user {} --password {} {}/ptxjit/test.tgz;tar -zxvf test.tgz; cd test; make
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_PTXAS_MISCOMPILER_PATH}/test; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./ptxjit
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# Memcheck - vector atomics 3192559
SANITIZER_MEMCHECK_VECTOR_ATOMICS:
  LOG_NAME: ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}/sanitizer_memcheck_vector_atomics.txt
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}; wget --user {} --password {} {}/P1072_3192559/vector_atomics_globals-4sanitizer.cu
    CMD2: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -lineinfo -arch sm_{} -o vector_atomics-nullF vector_atomics_globals-4sanitizer.cu
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_VECTOR_ATOMICS_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./vector_atomics-nullF
  CHECK_POINT1: "ERROR SUMMARY: 2 errors"
  CHECK_POINT2: "Invalid __global__ write of size 16 bytes"
  CHECK_POINT3: "vector_atomics_globals-4sanitizer.cu:85:call_atomic_v4_system"
  CHECK_POINT3_1: "call_atomic_v4_system.*vector_atomics_globals-4sanitizer.cu:85"


# SSANITIZER_SUPPORT_UNICODE_FILEanitizer supports Unicode files and paths 3183138
SANITIZER_SUPPORT_UNICODE_FILE:
  LOG_NAME: ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/sanitizer_support_unicode_file.txt
  PREPARE:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}; mkdir {}
    CMD2: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; wget --user {} --password {} {}/P1072_3183138/coredump.cu; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -g -G -o coredump coredump.cu
    CMD3: cp -r /usr/local/cuda-${CUDA_SHORT_VERSION}/compute-sanitizer/* ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}
  STEP1:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --report-api-error no ./coredump
  STEP2:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --report-api-error no --save {}-save.log ./coredump
    CMD2: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --read {}-save.log
  STEP3:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --report-api-error no --log-file {}-logfile.log ./coredump; cat {}-logfile.log
  STEP4:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --report-api-error no --xml {} --save {}-save.xml ./coredump; cat {}-save.xml
  STEP5:
    CMD1: cd ${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --report-api-error no --injection-path=${SANITIZER_SUPPORT_UNICODE_FILE_PATH}/{} ./coredump
  CHECK_POINT1: "ERROR SUMMARY: 16 errors"
  CHECK_POINT2: "Invalid __global__ read of size 4 bytes"

# Memcheck - CNP support for cluster kernel attributes 3192566
SANITIZER_MEMCHECK_CNP_SUPPORT:
  LOG_NAME: ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}/sanitizer_memcheck_CNP_support.txt
  PREPARE:
    CMD1: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}; wget --user {} --password {} {}/P1072_3192566/cnp_v2_static_cluster-4sanitizer.cu
    CMD2: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lineinfo -arch=sm_{} -rdc=true -o cnp_v2_static_cluster-4sanitizer cnp_v2_static_cluster-4sanitizer.cu
  STEP1:
    CMD1: cd ${SANITIZER_MEMCHECK_CNP_SUPPORT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./cnp_v2_static_cluster-4sanitizer
  CHECK_POINT1: "ERROR SUMMARY: 4 errors"
  CHECK_POINT2: "Invalid __global__ write of size 1 bytes"
  CHECK_POINT3: "cnp_v2_static_cluster-4sanitizer.cu:40:clusterKernel"
  CHECK_POINT3_1: "clusterKernel.*cnp_v2_static_cluster-4sanitizer.cu:40"

# Option - quite/require-cuda-init 3207945
SANITIZER_QUITE_CUDA_INIT:
  LOG_NAME: ${SANITIZER_QUITE_CUDA_INIT_PATH}/sanitizer_quite_cuda_init.txt
  RUN_CASE: true
  PREPARE:
    CMD1: 'rm -r {}/vectorAdd_err;cp -r {}/vectorAdd {}/vectorAdd_err; cd {}/vectorAdd_err; sed -i "s/threadsPerBlock = 256/threadsPerBlock = 2048/" vectorAdd.cu
          ;sed -i "s#err = cudaFree(d_C)#//err = cudaFree(d_C)#" vectorAdd.cu;make clean;make dbg=1'
    CMD2: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}; wget --user {} --password {} {}/helloworld/helloworld.c; gcc helloworld.c -o helloworld
  STEP1:
    CMD1: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./vectorAdd
  STEP2:
    CMD1: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full -q ./vectorAdd
  STEP3:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --mode attach 1234
  STEP4:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer -q --mode attach 1234
  STEP5:
    CMD1: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --require-cuda-init yes ./helloworld
    CMD2: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --require-cuda-init no ./helloworld
    CMD3: cd ${SANITIZER_QUITE_CUDA_INIT_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --require-cuda-init no -q ./helloworld
  # restore the sample
  STEP6:
    CMD1: cd {}/vectorAdd; make clean; make -j16 -k; make dbg=1 -B -j16 -k
  CHECK_POINT1: "LEAK SUMMARY: 600000 bytes leaked in 3 allocations"
  CHECK_POINT2: "ERROR SUMMARY: 5 error"
  CHECK_POINT3: "Finding attachable process on host 127.0.0.1"
  CHECK_POINT4: "No processes found on 127.0.0.1"
  CHECK_POINT5: "Target application returned an error"
  CHECK_POINT6: "Error: Target application terminated before first instrumented API call"
  CHECK_POINT7: "Target application terminated before first instrumented API call"
  CHECK_POINT8: "========= COMPUTE-SANITIZER"

# Option - launch and attach 3207946
SANITIZER_LAUNCH_ATTACH:
  LOG_NAME: ${SANITIZER_LAUNCH_ATTACH_PATH}/sanitizer_launch_attach.txt
  STEP1:
    CMD1: cd ${SAMPLE_BIN_PATH}&&/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --mode launch ./vectorAdd
  STEP2:
    CMD1: sleep 2s&&ps -ef|grep vectorAdd|head -2|tail -1|awk '{print $2}'|xargs /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --mode attach
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# [Sanitizer][RCCA] No false positive when test with whole graph update with allocation nodes 3219363
SANITIZER_RCCA_3219363:
  LOG_NAME: ${SANITIZER_RCCA_3219363_PATH}/sanitizer_memcheck_CNP_support.txt
  PREPARE:
    CMD1: cd ${SANITIZER_RCCA_3219363_PATH}; wget --user {} --password {} {}/3754674/main.cu&&/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -arch=sm_{} main.cu -o main
  STEP1:
    CMD1: cd ${SANITIZER_RCCA_3219363_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./main
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

#[Sanitizer]Racecheck barrier sync detection  3222551
SANITIZER_RCCA_3222551:
  LOG_NAME: ${SANITIZER_RCCA_3222551_PATH}/sanitizer_memcheck_CNP_support.txt
  PREPARE:
    CMD1: cd ${SANITIZER_RCCA_3222551_PATH}; wget --user {} --password {} {}/test-cuda-barrier/test-cuda-barrier.cu&&/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lcuda -arch sm_{} -o test-cuda-barrier test-cuda-barrier.cu
  STEP1:
    CMD1: cd ${SANITIZER_RCCA_3222551_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./test-cuda-barrier
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# [Sanitizer][RCCA]Test sanitizer-generated coredump open in cuda-gdb 3251454
SANITIZER_COREDUMP_GDB:
  LOG_NAME: ${SANITIZER_COREDUMP_GDB_PATH}/sanitizer_coredump_gdb.txt
  PREPARE:
    CMD1: cd ${SANITIZER_COREDUMP_GDB_PATH}; wget --user {} --password {} {}/3963239/coredump_repro.cu&&/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc coredump_repro.cu
  STEP1:
    CMD1: cd ${SANITIZER_COREDUMP_GDB_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --padding=1 --generate-coredump {} --coredump-name a.nvcudmp ./a.out
    CMD2: cd ${SANITIZER_COREDUMP_GDB_PATH}/; ls a.nvcudmp
  STEP2:
    CMD1: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdb -ex 'target cudacore a.nvcudmp'

# [Sanitizer][RCCA]Test sanitizer-generated coredump file 3251459
SANITIZER_RCCA_3251459:
  LOG_NAME: ${SANITIZER_RCCA_3251459_PATH}/sanitizer_rcca_3251459.txt
  PREPARE:
    CMD1: cd ${SANITIZER_RCCA_3251459_PATH}; wget --user {} --password {} {}/3964958/coredump_gen.cu&&/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc coredump_gen.cu
  STEP1:
    CMD1: cd ${SANITIZER_RCCA_3251459_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --padding=1 --generate-coredump {} --coredump-name a.nvcudmp ./a.out
    CMD2: cd ${SANITIZER_RCCA_3251459_PATH}/; ls a.nvcudmp

# Sanitizer test with nvscibuffer 3302257
SANITIZER_TEST_NVSCIBUFFER:
  LOG_NAME: ${SANITIZER_TEST_NVSCIBUFFER_PATH}/sanitizer_test_nvscibuffer.txt
  PREPARE:
    CMD: cd ${SANITIZER_TEST_NVSCIBUFFER_PATH}; wget --user {} --password {} {}/P1072_3302257/test.cu
    CMD1: cd ${SANITIZER_TEST_NVSCIBUFFER_PATH}; wget {}&&tar vxzf *.tgz && echo {}|sudo -S dpkg -i nvsci_pkg.deb
    CMD2: cd ${SANITIZER_TEST_NVSCIBUFFER_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -lcuda -lnvscibuf -o test test.cu && ./test 10&& ./test 0
  STEP1:
    CMD1: cd ${SANITIZER_TEST_NVSCIBUFFER_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./test -offset 0
  STEP2:
    CMD1: cd ${SANITIZER_TEST_NVSCIBUFFER_PATH}/; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer ./test -offset 10
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# Racecheck - cluster synchronization after warp fully exits 3368656
RACECHECK_WARP_EXIT:
  LOG_NAME: ${RACECHECK_WARP_EXIT_PATH}/racecheck_warp_exit.txt
  PREPARE:
    CMD1: cd ${RACECHECK_WARP_EXIT_PATH}; wget --user {} --password {} {}/cga_exit/broadcast-bench.tar.gz&&tar zxvf broadcast-bench.tar.gz
    CMD2: cd ${RACECHECK_WARP_EXIT_PATH}/broadcast-bench; export CUDA_HOME=/usr/local/cuda-${CUDA_SHORT_VERSION}&&make TYPE=int64_t FUNC=BCAST_RWLR BENCH=broadcast-lat
  STEP1:
    CMD1: cd ${RACECHECK_WARP_EXIT_PATH}/broadcast-bench; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck ./bin/broadcast-lat-int64_t-BCAST_RWLR
  CHECK_POINT1: "RACECHECK SUMMARY: 0 hazards displayed (0 errors, 0 warnings)"

# Memcheck - detect-missing-module-unload 3504892
MEMCHECK_DETECT_MISSING_MODULE_UNLOAD:
  LOG_NAME: ${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/detect_missing_module_unload.txt
  PREPARE:
    CMD1: cd {}; rm -r vectorAddDrv_3504892; cp -r vectorAddDrv vectorAddDrv_3504892; cd vectorAddDrv_3504892; rm vectorAddDrv.cpp; wget --user {} --password {} {}/P1072_T3504892/Linux/vectorAddDrv.cpp; make clean; make
    CMD2: cp -r {}/vectorAddDrv_3504892 ${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}
  STEP1:
    CMD1: cd ${MEMCHECK_DETECT_MISSING_MODULE_UNLOAD_PATH}/vectorAddDrv_3504892; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --detect-missing-module-unload --leak-check full ./vectorAddDrv
  CHECK_POINT1: "Leaked .*bytes from CUDA module.*"
  CHECK_POINT2: "ERROR SUMMARY: 1000 errors"

# sanitizer support multi-ctx app for coredump generation 3532274
SANITIZER_SUPPORT_MULTI_CTX_COREDUMP:
  LOG_NAME: ${SANITIZER_SUPPORT_MULTI_CTX_COREDUMP_PATH}/sanitizer_support_muliti_ctx_coredump.txt
  PREPARE:
   CMD1: cp {}/cudacore-dump* ${SANITIZER_SUPPORT_MULTI_CTX_COREDUMP_PATH}
   CMD2: cp {}/multictx_coredump ${SANITIZER_SUPPORT_MULTI_CTX_COREDUMP_PATH}
  STEP1:
    CMD1: cd ${SANITIZER_SUPPORT_MULTI_CTX_COREDUMP_PATH};/usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --launch-timeout 0 --target-processes all --generate-coredump --coredump-name=multi-ctx-dump ./{}
  STEP2:
    CMD1: /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/cuda-gdb -ex 'target cudacore multi-ctx-dump'


# Option - preload-library 3532738
SANITIZER_PRELOAD_LIBRARY:
  LOG_NAME: ${SANITIZER_PRELOAD_LIBRARY_PATH}/sanitizer_preload_library.txt
  PREPARE:
   CMD1: cd {}; cp TestLibraryPreload libTestLibraryPre*.so ${SANITIZER_PRELOAD_LIBRARY_PATH}
  STEP1:
    CMD1: cd ${SANITIZER_PRELOAD_LIBRARY_PATH}; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer {} --preload-library libTestLibraryPreloadOverrideLib.so ./TestLibraryPreload
  CHECK_POINT1: "Symbol was overriden: 0"
  CHECK_POINT2: "Error: Target application terminated before first instrumented API call"

# Racecheck - Option - racecheck-num-workers 2680935
SANITIZER_RACECHECK_NUM_WORKERS:
  LOG_NAME: ${SANITIZER_RACECHECK_NUM_WORKERS_PATH}/sanitizer_racecheck_num_workers.txt
  STEP1:
    CMD1: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --racecheck-num-workers=2 -c 6 ./matrixMul
    CMD2: cd ${SAMPLE_BIN_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool racecheck --racecheck-num-workers=4 -c 6 ./matrixMul
    CMD3: sleep 16; ps -ef|grep matrixMul|awk -F ' ' '{print $2}'|tail -2|head -1|xargs ps -o nlwp|tail -1
    CMD4: sleep 18; ps -ef|grep matrixMul|awk -F ' ' '{print $2}'|tail -2|head -1|xargs ps -o nlwp|tail -1

# [Sanitizer][RCCA]Incorrect host backtrace in memcheck_demo sample 3555855
SANITIZER_INCORRECT_HOST_BACKTRACE:
  LOG_NAME: ${SANITIZER_INCORRECT_HOST_BACKTRACE_PATH}/sanitizer_incorrect_host_backtrace.txt
  PREPARE:
    CMD1: cd ${SANITIZER_INCORRECT_HOST_BACKTRACE_PATH}; wget --user {} --password {} {}/P1072_T3555855/{}
    CMD2: cd ${SANITIZER_INCORRECT_HOST_BACKTRACE_PATH}; make dbg=1
  STEP1:
    CMD1: cd ${SANITIZER_INCORRECT_HOST_BACKTRACE_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --leak-check full ./memcheck_demo
  CHECK_POINT1: "Running out_of_bounds_kernel"
  CHECK_POINT2: "Leaked 1,024 bytes"
  CHECK_POINT3: "Host Frame.*memcheck_demo.cu:65"
  CHECK_POINT4: "LEAK SUMMARY: 1024 bytes leaked in 1 allocations"
  CHECK_POINT5: "ERROR SUMMARY: 5 errors"


# Option - suppressions 3568568
SANITIZER_SUPPRESSIONS_OPTION:
  LOG_NAME: ${SANITIZER_SUPPRESSIONS_OPTION_PATH}/sanitizer_suppressions_option.txt
  PREPARE:
    CMD1: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; wget --user {} --password {} {}/compute-sanitizer-samples/Suppressions/{}
    CMD2: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; make clean; make
  STEP1:
    CMD1: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --save supp.xml --xml suppressions_demo
    CMD2: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; cat supp.xml
    CMD3: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --suppressions supp.xml suppressions_demo
  STEP2:
    CMD1: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer  --tool initcheck --save supp1.xml --xml suppressions_initcheck_demo
    CMD2: cd ${SANITIZER_SUPPRESSIONS_OPTION_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer --tool initcheck --suppressions supp1.xml suppressions_initcheck_demo
  CHECK_POINT1: 'Program hit cudaErrorMemoryAllocation (error 2) due to "out of memory" on CUDA API call to cudaMalloc'
  CHECK_POINT2: "ERROR SUMMARY: 0 errors"
  CHECK_POINT3: "Uninitialized __global__ memory read of size 4 bytes"
  CHECK_POINT4: 'Program hit cudaErrorMemoryAllocation.*error 2.*due to.*out of memory.*on CUDA API call to cudaMalloc'

# Racecheck - support 'Compiler fix for WARPSYNC bug for sync instructions and opt-in perf hint option to get back perf' 3568768
SANITIZER_COMPILER_HINT_OPTION:
  LOG_NAME: ${SANITIZER_COMPILER_HINT_OPTION_PATH}/sanitizer_compiler_hint_option.txt
  DIR_TYPE: 'redux,bar,shfl,vote,match'
  STEP1:
    CMD1: cd {}; perl ptxtest/runPTXtest.pl -dvs -swTree={} -temp={}/temp/ -arch=sm_{} -config=release -branch=${DRV_BRANCH}_00 -noparallel -sc -testdir={} -withDB
  STEP2:
    CMD1: cd {}/; compute-sanitizer --support-32bit perl ptxtest/runPTXtest.pl -dvs -swTree={} -temp={}/temp/ -arch=sm_{} -config=release -branch=${DRV_BRANCH}_00 -noparallel -sc -testdir={} -withDB
  CHECK_POINT1: "ERROR SUMMARY: 0 errors"

# [Sanitizer][RCCA]No false positive when vector loads are not 32-bits per vector element 3700598
SANITIZER_3700598_RCCA:
  LOG_NAME: ${SANITIZER_3700598_RCCA_PATH}/sanitizer_suppressions_option.txt
  PREPARE:
    CMD1: cd ${SANITIZER_3700598_RCCA_PATH}; wget --user {} --password {} {}/3700598/cs16-fail.cu
    CMD2: cd ${SANITIZER_3700598_RCCA_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/nvcc -std=c++17 cs16-fail.cu -o cs16-fail
  STEP1:
    CMD1: cd ${SANITIZER_3700598_RCCA_PATH}; /usr/local/cuda-${CUDA_SHORT_VERSION}/bin/compute-sanitizer cs16-fail
