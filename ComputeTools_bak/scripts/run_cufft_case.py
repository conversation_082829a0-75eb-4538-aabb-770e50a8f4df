# -*- encoding: utf-8 -*-
import os
import sys
import yaml
# -*- encoding: utf-8 -*-
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
import argparse
from common_utils import *
import threading
import logging
from run_cublas_case import gpu_dict, prepare_gpu
import time
from yaml_mgr import YamlManger

math_lib_yaml = '{}/../yaml/math_lib_case.yaml'.format(os.getcwd())
yaml_mgr = YamlManger(math_lib_yaml)
case_config = yaml_mgr.load()

tools_home = case_config['global']['env']['TOOLS_HOME']
platform = case_config['global']['env']['PLATFORM'].lower()
build_download_url = case_config['global']['env']['CUFFT_BASE_URL']
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
date1 = case_config['global']['env']['DATE1']
host_home = case_config['global']['env']['HOST_HOME']
cufft_skip_list = case_config['CUFFT_SKIP_CASE']['CASE_LIST']
download_to_path = case_config['global']['env']['CUFFT_PATH']
cupti_path = case_config['global']['env']['CUPTI_DOWNLOAD_PATH']
if platform == 'x86':
    cupti_run_path = '%s/target/linux-desktop-GNU_glibc_2_11_3-x64' % cupti_path
    cufft_bin = '%s' % download_to_path + '/bin/x86_64_Linux_release'
    cupti_host_path = '%s/host/linux-desktop-GNU_glibc_2_11_3-x64' % cupti_path
elif platform == 'arm':
    if cuda_short_version < '11.5':
        cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        cupti_host_path = '%s/host/linux-desktop-glibc_2_19_0-arm' % cupti_path
    else:
        cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
        cupti_host_path = '%s/host/linux-desktop-t210-a64' % cupti_path
    cufft_bin = '%s' % download_to_path + '/bin/aarch64_Linux_release'
elif platform == 'power':
    if cuda_short_version < '11.5':
        cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-x86_64" % cupti_path
        cupti_host_path = '%s/host/linux-desktop-glibc_2_19_0-x86_64' % cupti_path
    else:
        cupti_run_path = "%s/target/linux-desktop-gcc_4_8_3_glibc_2_19_0-ppc64le" % cupti_path
        cupti_host_path = '%s/host/linux-desktop-gcc_4_8_3_glibc_2_19_0-ppc64le' % cupti_path
else:
    print('will use the new dvs package')
    exit(10)

logger = logging.getLogger()
logger.setLevel(level=logging.INFO)
# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/cufft_case.log' % tools_home)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger = logging.getLogger(__name__)


def get_execute_time(func):
    def wrapper(*args, **kw):
        local_time = time.time()
        func(*args, **kw)
        logger.info('current Function [%s] with %s option, it run time is %.2f' % (func.__name__, args, time.time() - local_time))
        with open('%s/cufft_execute_time.txt' % tools_home, 'a+') as f:
            f.write('%s---current Function [%s] with %s option, it run time is %.2f' % (time.strftime('%Y/%m/%d %H:%M:%S', time.localtime(time.time())), func.__name__, args, time.time() - local_time))
            f.write('\n')
    return wrapper


def prepare_cufft():
    download_path = get_dvs_package('cufft', platform, cuda_short_version)
    cufft_package = download_path.split('/')[-1]
    logger.info('we will download the package from %s' % download_path)
    logger.info('we will download the package to %s ' % download_to_path)
    mkdir(download_to_path)
    create_file(download_to_path, 'cufft_build.txt')
    os.chdir(download_to_path)
    password = case_config['global']['env']['HOST_PASSWORD']
    if check_file(download_to_path, 'cufft_build.txt') is True:
        logger.info('we had download the package, no need download again')
    else:
        cmd2 = "cd %s; lftp -c 'glob -- pget -n 80 %s'" % (download_to_path, download_path)
        logger.info('we will use the command to download package, the command is %s ' % cmd2)
        out2 = run_loc_cmd(cmd2)
        if out2.succeeded:
            if platform == 'x86':
                cmd3 = 'echo %s| sudo -S apt install p7zip-full; cd %s; tar xzvf %s; 7za x CUDA-cufft-package.tar.7z; tar -xvf CUDA-cufft-package.tar' % (password, download_to_path, cufft_package)
            elif platform == 'power':
                cmd3 = 'cd %s; tar xzvf %s; tar -xjvf CUDA-cufft-package.tar.bz2' % (download_to_path, cufft_package)
            else:
                # cmd3 = 'cd %s; tar xzvf %s; tar -xjvf CUDA-cufft-package.tar.bz2' % (download_to_path, cufft_package)
                cmd3 = 'cd %s; tar xzvf %s' % (download_to_path, cufft_package)
            logger.info('we will use the command to extract package, the command is %s ' % cmd3)
            out3 = run_loc_cmd(cmd3)
            if out3.succeeded:
                logger.info('extract the cufft package successful')
                with open('%s/cufft_build.txt' % download_to_path, 'a+') as f:
                    f.write(cufft_package)
                    f.write('\n')
            else:
                logger.info('extract the cufft package failed')
        else:
            logger.info('Failed to download cufft package')
    return download_to_path


def get_sm():
    # prepare env
    sm_cmd = case_config['PREPARE']['CMD']
    sm_out = run_loc_cmd(sm_cmd)
    if sm_out.succeeded:
        sm = sm_out['output'].split('\n')[-1].strip(' ')
        return sm
    else:
        logger.info('we get the sm failed')
        exit()


def get_cmd(cmd_file):
    cmd_list = []
    with open(cmd_file, 'r') as f:
        for line in f.readlines():
            cmd_list.append(line)


def get_cufft_list():
    prepare_test_file()
    L0_cmd_list, call_back_list, fp_L0_list, bf_L0_list = [], [], [], []
    with open('%s/cufft/l0.txt' % host_home, 'r') as f1:
        for line in f1.readlines():
            if '-ngpus=2' not in line:
                L0_cmd_list.append('cufftbench %s' % line.split('\n')[0])
    with open('%s/cufft/l0_callback.txt' % host_home, 'r') as f2:
        for line in f2.readlines():
            if '-ngpus=2' not in line:
                call_back_list.append('cufftbench_static %s' % line.split('\n')[0])
    with open('%s/cufft/l0_fp16.txt' % host_home, 'r') as f3:
        for line in f3.readlines():
            if '-ngpus=2' not in line:
                fp_L0_list.append('cufftbench %s' % line.split('\n')[0])
    with open('%s/cufft/l0_bf16.txt' % host_home, 'r') as f4:
        for line in f4.readlines():
            if '-ngpus=2' not in line:
                bf_L0_list.append('cufftbench %s' % line.split('\n')[0])
    sm = get_sm()
    if sm >= '8.0':
        return L0_cmd_list, call_back_list, fp_L0_list
    else:
        return L0_cmd_list, call_back_list, bf_L0_list


@get_execute_time
def sanitizer_cufft_case(tools_option):
    result = {}
    passed, failed = 0, 0
    log_name = case_config['SANITIZER_CUFFT']['LOG_NAME'] % (tools_option, tools_option)
    log_path = case_config['global']['env']['SANITIZER_CUFFT_LOG_PATH'] % tools_option
    mkdir(log_path)
    prepare_tools_package('cufft', download_to_path, platform, cuda_short_version)
    if tools_option == 'racecheck':
        check_point = case_config['SANITIZER_CUBLAS']['CHECK_POINT1']
    else:
        check_point = case_config['SANITIZER_CUBLAS']['CHECK_POINT']
    check_point2 = case_config['SANITIZER_CUBLAS']['CHECK_POINT2']
    L0_list, call_back_list, fb_list = get_cufft_list()
    L0_list_1 = L0_list + call_back_list + fb_list
    run_list = []
    print('===================')
    print(cufft_bin)
    print('------------------------------------------')
    for i in L0_list_1:
        if tools_option == 'memcheck':
            run_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s --leak-check full --report-api-errors no ./%s' % (cufft_bin, cuda_short_version, tools_option, i))
        elif tools_option == 'initcheck':
            run_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s ./%s' % (cufft_bin, cuda_short_version, tools_option, i))
        elif tools_option == 'racecheck':
            run_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s -c 10 ./%s' % (cufft_bin, cuda_short_version, tools_option, i))
        else:
            run_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s ./%s' % (cufft_bin, cuda_short_version, tools_option, i))
    for cmd in run_list:
        if tools_option == 'memcheck':
            check_result(cmd, 'run_sanitizer_cufft------%s' % cmd, log_name, result, check_point, check_point2)
        else:
            check_result(cmd, 'run_sanitizer_cufft------%s' % cmd, log_name, result, check_point)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/sanitizer_%s_cufft.json' % (log_path, tools_option))
    return result, passed, failed


def run_ncu_cmd(log_name, result, cmd_list):
    for cmd in cmd_list:
        out = run_loc_cmd(cmd, timeout=3600)
        save_log(log_name, cmd, 'ncu-cufft-cmd_%s' % cmd, out['output'])
        if out.succeeded and 'Error' not in out['output'] and 'n/a' not in out['output'].split('\n')[3::] and 'ERROR' not in out['output']:
            # if out.succeeded and check_failure(out['output']) == 0:
            result['run_ncu_cmd------%s' % cmd] = 'passed'
            logger.info('we run ncu_cufft------%s passed' % cmd)
        else:
            result['run_ncu_cmd------%s' % cmd] = 'failed'
            logger.info('we run ncu_cufft------%s failed' % cmd)


def prepare_test_file():
    file_list = ['l0_bf16.txt', 'l0_callback.txt', 'l0_fp16.txt', 'l0.txt']
    mkdir('%s/cufft' % host_home)
    for file in file_list:
        os.chdir('%s/cufft' % host_home)
        if os.path.exists(file):
            logger.info('we had download the L0 file, no need download again')
        else:
            cqa_user = b64_strip_decode(case_config['global']['env']['CQA_USER'])
            cqa_password = b64_strip_decode(case_config['global']['env']['CQA_PASSWORD'])
            cmd = case_config['SANITIZER_CUFFT']['PREPARE']['CMD'] % (cqa_user, cqa_password, file)
            out = run_loc_cmd(cmd)
            if out.succeeded:
                logger.info('we download %s successful' % file)
            else:
                logger.info('we download %s fail' % file)
                exit(1)


@get_execute_time
def ncu_cufft_case():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NCU_CUFFT']['LOG_NAME']
    log_path = case_config['global']['env']['NCU_CUFFT_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cufft', download_to_path, platform, cuda_short_version)
    sm = get_sm()
    print(sm)
    L0_list, call_back_list, fb_list = get_cufft_list()
    L0_list_1 = L0_list + call_back_list + fb_list
    ncu_list = []
    for cmd in L0_list_1:
        ncu_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/ncu  -c 10 ./%s' % (cufft_bin, cuda_short_version, cmd.split('\n')[0]))
    for cmd1 in ncu_list:
        check_result(cmd1, 'run_ncu-command-----%s' % cmd1, log_name, result, 'nan', 'n/a', 'Error', 'ERROR', flag=3)
    # run_ncu_cmd(log_name, result, ncu_list)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/ncu_cufft.json' % log_path)
    return result, passed, failed


@get_execute_time
def ncu_option_cufft_case(ncu_option):
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NCU_CUFFT']['LOG_NAME1'] % (ncu_option, ncu_option)
    log_path = case_config['global']['env']['NCU_OPTION_CUFFT_LOG_PATH'] % ncu_option
    mkdir(log_path)
    prepare_tools_package('cufft', download_to_path, platform, cuda_short_version)
    sm = get_sm()
    print(sm)
    L0_list, call_back_list, fb_list = get_cufft_list()
    L0_list_1 = L0_list + call_back_list + fb_list
    ncu_list = []
    if ncu_option == 'single':
        L0_list_1 = L0_list + call_back_list + fb_list
        options = case_config['NCU_OPTION']['OPTIONS1']
    elif ncu_option == 'replay':
        L0_list_1_1 = L0_list + call_back_list + fb_list
        L0_list_1 = [i for i in L0_list_1_1 if i not in cufft_skip_list]
        options = case_config['NCU_OPTION']['OPTIONS2']
    else:
        logger.info('please give the correctly ncu options')
        exit(2)
    for cmd in L0_list_1:
        ncu_list.append('cd %s; export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/ncu -c 10 %s ./%s' % (cufft_bin, cuda_short_version, options, cmd))
    if platform == 'arm':
        run_ncu_cmd(log_name, result, ncu_list)
    else:
        for cmd1 in ncu_list:
            check_result(cmd1, 'run_ncu-command-----%s' % cmd1, log_name, result, 'nan', 'n/a', 'Error', 'ERROR', flag=3)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/ncu_cufft_%s.json' % (log_path, ncu_option))
    return result, passed, failed


@get_execute_time
def cupti_profile_injection_cufft():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['LOG_NAME2']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_CUFFT_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cufft', download_to_path, platform, cuda_short_version)
    L0_list, call_back_list, fb_list = get_cufft_list()
    prepare_tools_package('cupti', cupti_path, platform, cuda_short_version)
    gpu_type = prepare_gpu()
    cupti_injection_list = []
    L0_list_1 = L0_list + call_back_list + fb_list
    print(L0_list_1)
    for i in range(0, len(L0_list_1)):
        if cuda_short_version < '11.2':
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1'] % (cupti_run_path, cufft_bin, gpu_dict['%s' % gpu_type], i, L0_list_1[i])
        elif '11.2' <= cuda_short_version < '11.7':
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1_1'] % (cupti_run_path, cufft_bin, i, L0_list_1[i])
        else:
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1_2'] % (cupti_run_path, cufft_bin, i, L0_list_1[i])
        cupti_injection_list.append(step1_cmd1)
    # prepare cufft bin
    os.chdir(cupti_run_path)
    if os.path.exists('cufftbench'):
        logger.info('we had prepared the bin file, no need prepare again')
    else:
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['PREPARE']['CMD'] % (cufft_bin, cupti_run_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
    # run cupti inject cufft case
    check_point = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT']
    check_point1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT1']
    check_point2 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT2']
    for cmd1 in cupti_injection_list:
        out = run_loc_cmd(cmd1, timeout=7200)
        save_log(log_name, cmd1, 'run_sanitizer_cufft------%s' % cmd1, out['output'])
        print(out['output'])
        if out.succeeded and check_point not in out['output'] and check_point1 not in out['output'] and check_point2 not in out['output']:
            result['run_sanitizer_cufft------%s' % cmd1] = 'passed'
            logger.info('we run the case--%s successful' % cmd1)
        elif 'test WAIVED' in out['output']:
            logger.info('we do not record the waived case--%s' % cmd1)
        else:
            result['run_sanitizer_cufft------%s' % cmd1] = 'failed'
            logger.info('we run the case--%s failed' % cmd1)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/cupti_profile_cufft.json' % log_path)
    return result, passed, failed


@get_execute_time
def cupti_trace_injection_cufft():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['LOG_NAME2']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_CUFFT_LOG_PATH']
    mkdir(log_path)
    prepare_tools_package('cufft', download_to_path, platform, cuda_short_version)
    prepare_tools_package('cupti', cupti_path, platform, cuda_short_version)
    # prepare cufft bin & json
    os.chdir(cupti_run_path)
    if os.path.exists('cufftbench'):
        logger.info('we had prepared the bin file, no need prepare again')
    else:
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['PREPARE']['CMD'] % (cufft_bin, cupti_run_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
    prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['PREPARE']['CMD1'] % (cupti_host_path, 'cufft', 'cufft')
    # run_cmd(prepare_cmd1, 'run_prepare_cmd1', 'passed', 'failed', log_name, result)
    check_result(prepare_cmd1, 'run_prepare_cmd1', log_name, result)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    L0_list, call_back_list, fb_list = get_cufft_list()
    L0_list_1 = L0_list + call_back_list + fb_list
    print(L0_list_1)
    for index, cmd in enumerate(L0_list_1):
        # dict1["tests"].append({"name": "%s" % cmd, "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "verify": {}})
        args_list = [x.strip() for x in cmd.split(" ")[1::] if x.strip() != '']
        if cuda_short_version <= '11.4':
            dict1["tests"].append({"name": "%s_%s" % (cmd.split(" ")[0].split("/")[-1], index), "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "exe_args": args_list})
        else:
            dict1["tests"].append({"name": "%s_%s" % (cmd.split(" ")[0].split("/")[-1], index), "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "exe-args": args_list})
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    dict_to_json(dict2, '%s' % (cupti_host_path + '/' + 'test_cufft.json'))
    # run cufft case coverage
    if cuda_short_version <= '11.4':
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1']['CMD'] % (cupti_host_path, 'cufft', 'cufft')
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_1']['CMD'] % (cupti_host_path, 'cufft', 'cufft')
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_2']['CMD'] % (cupti_host_path, 'cufft', 'cufft')
    else:
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1']['CMD1'] % (cupti_host_path, 'cufft', 'cufft')
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_1']['CMD1'] % (cupti_host_path, 'cufft', 'cufft')
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_2']['CMD1'] % (cupti_host_path, 'cufft', 'cufft')
    # run_cmd(step1_cmd, 'run_trace_injection', 'passed', 'failed', log_name, result)
    print(step1_cmd)
    check_result(step1_cmd, 'run_trace_injection', log_name, result)
    # caluate the result
    if cuda_short_version <= '11.4':
        cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD1'] % (cupti_host_path, 'cufft', log_path, 'cufft')
        cmd2 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD2'] % (cupti_host_path, 'cufft', log_path, 'cufft')
    else:
        cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD1_1'] % (cupti_host_path, 'cufft', log_path, 'cufft')
        cmd2 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD2_1'] % (cupti_host_path, 'cufft', log_path, 'cufft')
    out1 = run_loc_cmd(cmd1)
    if out1.succeeded:
        result['failed'] = int(out1['output'])
    else:
        result['failed'] = 0
    out2 = run_loc_cmd(cmd2)
    if out2.succeeded:
        result['passed'] = int(out2['output'])
    print(result)
    dict_to_json(result, '%s/cupti_trace_cufft_coverage.json' % log_path)
    return result, passed, failed


example_text = """
We use compute-sanitizer/nsight compute/cupti to run cufft case

Example:
python run_cufft_case.py -t all # run all the tools-sanitizer/ncu/cupti
python run_cufft_case.py -t sanitizer -o racecheck
python run_cufft_case.py -t ncu -o option  single or python run_cufft_case.py -t ncu
python run_cufft_case.py -t cupti -o trace/profile
sanitizer has four options: racecheck, initcheck, memcheck, synccheck
cupti: trace; profile
ncu: option single/replay or default run ncu
"""
reversion = '1.0'


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-t", "--tools", required=False,
        help='Specify tools to run cufft case')
    parser.add_argument(
        "-o", "--option", required=False,
        help='Specify the option of the tools will used')
    args = parser.parse_args()
    if args.tools == 'sanitizer':
        if args.option:
            sanitizer_cufft_case(args.option)
        else:
            logger.info('please give correct option when use sanitizer to run cufft case')
    elif args.tools == 'ncu':
        if args.option == 'single':
            ncu_option_cufft_case('single')
        elif args.option == 'replay':
            ncu_option_cufft_case('replay')
        else:
            ncu_cufft_case()
    elif args.tools == 'all':
        for option in ['memcheck', 'synccheck', 'racecheck', 'initcheck']:
            sanitizer_cufft_case(option)
        ncu_cufft_case()
        ncu_option_cufft_case('single')
        ncu_option_cufft_case('replay')
        cupti_profile_injection_cufft()
        cupti_trace_injection_cufft()
        # cupti_case
    elif args.tools == 'cupti':
        if args.option == 'profile':
            cupti_profile_injection_cufft()
        elif args.option == 'trace':
            cupti_trace_injection_cufft()
        else:
            logger.info('please give me correct cupti injection option')

    else:
        logger.info('please give correct tools to run cufft')
