[    0.000000] Linux version 4.18.0-513.9.1.el8_9.x86_64 (<EMAIL>) (gcc version 8.5.0 20210514 (Red Hat 8.5.0-20) (GCC)) #1 SMP Thu Nov 16 10:29:04 EST 2023
[    0.000000] Command line: BOOT_IMAGE=(hd0,gpt2)/boot/vmlinuz-4.18.0-513.9.1.el8_9.x86_64 root=UUID=634743d0-c083-44a3-aae6-b54e4ddd6180 ro crashkernel=auto rhgb quiet nouveau.modeset=0
[    0.000000] x86/fpu: Supporting XSAVE feature 0x001: 'x87 floating point registers'
[    0.000000] x86/fpu: Supporting XSAVE feature 0x002: 'SSE registers'
[    0.000000] x86/fpu: Supporting XSAVE feature 0x004: 'AVX registers'
[    0.000000] x86/fpu: Supporting XSAVE feature 0x200: 'Protection Keys User registers'
[    0.000000] x86/fpu: xstate_offset[2]:  576, xstate_sizes[2]:  256
[    0.000000] x86/fpu: xstate_offset[9]:  832, xstate_sizes[9]:    8
[    0.000000] x86/fpu: Enabled xstate features 0x207, context size is 840 bytes, using 'compacted' format.
[    0.000000] signal: max sigframe size: 3376
[    0.000000] BIOS-provided physical RAM map:
[    0.000000] BIOS-e820: [mem 0x0000000000000000-0x000000000009ffff] usable
[    0.000000] BIOS-e820: [mem 0x00000000000a0000-0x00000000000fffff] reserved
[    0.000000] BIOS-e820: [mem 0x0000000000100000-0x000000002fffffff] usable
[    0.000000] BIOS-e820: [mem 0x0000000030000000-0x000000003000afff] ACPI NVS
[    0.000000] BIOS-e820: [mem 0x000000003000b000-0x0000000075cfffff] usable
[    0.000000] BIOS-e820: [mem 0x0000000075d00000-0x0000000075ffffff] reserved
[    0.000000] BIOS-e820: [mem 0x0000000076000000-0x00000000a70c2fff] usable
[    0.000000] BIOS-e820: [mem 0x00000000a70c3000-0x00000000a8f67fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000a8f68000-0x00000000a9052fff] ACPI data
[    0.000000] BIOS-e820: [mem 0x00000000a9053000-0x00000000a94d3fff] ACPI NVS
[    0.000000] BIOS-e820: [mem 0x00000000a94d4000-0x00000000aa3fefff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000aa3ff000-0x00000000abffffff] usable
[    0.000000] BIOS-e820: [mem 0x00000000ac000000-0x00000000afffffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000b8180000-0x00000000b8180fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000b9180000-0x00000000b9180fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000ce100000-0x00000000ce1fffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000ce300000-0x00000000ce400fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000ce500000-0x00000000ce5fffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fc180000-0x00000000fc180fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fea00000-0x00000000feafffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fec00000-0x00000000fec00fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fec10000-0x00000000fec10fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fed00000-0x00000000fed00fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fed40000-0x00000000fed44fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fed80000-0x00000000fed8ffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fedc0000-0x00000000fedc0fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fedc2000-0x00000000fedc8fff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000fee00000-0x00000000feefffff] reserved
[    0.000000] BIOS-e820: [mem 0x00000000ff000000-0x00000000ffffffff] reserved
[    0.000000] BIOS-e820: [mem 0x0000000100000000-0x000000204f0fffff] usable
[    0.000000] BIOS-e820: [mem 0x000000204f100000-0x000000204fffffff] reserved
[    0.000000] BIOS-e820: [mem 0x000007fc00000000-0x000007fc03ffffff] reserved
[    0.000000] NX (Execute Disable) protection: active
[    0.000000] e820: update [mem 0xa054e018-0xa05b9257] usable ==> usable
[    0.000000] e820: update [mem 0xa054e018-0xa05b9257] usable ==> usable
[    0.000000] e820: update [mem 0xa04e2018-0xa054d257] usable ==> usable
[    0.000000] e820: update [mem 0xa04e2018-0xa054d257] usable ==> usable
[    0.000000] e820: update [mem 0x9ffb9018-0x9ffc8c57] usable ==> usable
[    0.000000] e820: update [mem 0x9ffb9018-0x9ffc8c57] usable ==> usable
[    0.000000] e820: update [mem 0xa04d9018-0xa04e1057] usable ==> usable
[    0.000000] e820: update [mem 0xa04d9018-0xa04e1057] usable ==> usable
[    0.000000] extended physical RAM map:
[    0.000000] reserve setup_data: [mem 0x0000000000000000-0x000000000009ffff] usable
[    0.000000] reserve setup_data: [mem 0x00000000000a0000-0x00000000000fffff] reserved
[    0.000000] reserve setup_data: [mem 0x0000000000100000-0x000000002fffffff] usable
[    0.000000] reserve setup_data: [mem 0x0000000030000000-0x000000003000afff] ACPI NVS
[    0.000000] reserve setup_data: [mem 0x000000003000b000-0x0000000075cfffff] usable
[    0.000000] reserve setup_data: [mem 0x0000000075d00000-0x0000000075ffffff] reserved
[    0.000000] reserve setup_data: [mem 0x0000000076000000-0x000000009ffb9017] usable
[    0.000000] reserve setup_data: [mem 0x000000009ffb9018-0x000000009ffc8c57] usable
[    0.000000] reserve setup_data: [mem 0x000000009ffc8c58-0x00000000a04d9017] usable
[    0.000000] reserve setup_data: [mem 0x00000000a04d9018-0x00000000a04e1057] usable
[    0.000000] reserve setup_data: [mem 0x00000000a04e1058-0x00000000a04e2017] usable
[    0.000000] reserve setup_data: [mem 0x00000000a04e2018-0x00000000a054d257] usable
[    0.000000] reserve setup_data: [mem 0x00000000a054d258-0x00000000a054e017] usable
[    0.000000] reserve setup_data: [mem 0x00000000a054e018-0x00000000a05b9257] usable
[    0.000000] reserve setup_data: [mem 0x00000000a05b9258-0x00000000a70c2fff] usable
[    0.000000] reserve setup_data: [mem 0x00000000a70c3000-0x00000000a8f67fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000a8f68000-0x00000000a9052fff] ACPI data
[    0.000000] reserve setup_data: [mem 0x00000000a9053000-0x00000000a94d3fff] ACPI NVS
[    0.000000] reserve setup_data: [mem 0x00000000a94d4000-0x00000000aa3fefff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000aa3ff000-0x00000000abffffff] usable
[    0.000000] reserve setup_data: [mem 0x00000000ac000000-0x00000000afffffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000b8180000-0x00000000b8180fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000b9180000-0x00000000b9180fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000ce100000-0x00000000ce1fffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000ce300000-0x00000000ce400fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000ce500000-0x00000000ce5fffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fc180000-0x00000000fc180fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fea00000-0x00000000feafffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fec00000-0x00000000fec00fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fec10000-0x00000000fec10fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fed00000-0x00000000fed00fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fed40000-0x00000000fed44fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fed80000-0x00000000fed8ffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fedc0000-0x00000000fedc0fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fedc2000-0x00000000fedc8fff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000fee00000-0x00000000feefffff] reserved
[    0.000000] reserve setup_data: [mem 0x00000000ff000000-0x00000000ffffffff] reserved
[    0.000000] reserve setup_data: [mem 0x0000000100000000-0x000000204f0fffff] usable
[    0.000000] reserve setup_data: [mem 0x000000204f100000-0x000000204fffffff] reserved
[    0.000000] reserve setup_data: [mem 0x000007fc00000000-0x000007fc03ffffff] reserved
[    0.000000] efi: EFI v2.80 by American Megatrends
[    0.000000] efi:  TPMFinalLog=0xa9487000  ACPI=0xa9486000  ACPI 2.0=0xa9486014  SMBIOS=0xaa0ef000  SMBIOS 3.0=0xaa0ee000  MEMATTR=0xa06b5018  ESRT=0xa4316318  MOKvar=0xaa17b000  TPMEventLog=0xa05ba018 
[    0.000000] secureboot: Secure boot disabled
[    0.000000] SMBIOS 3.3.0 present.
[    0.000000] DMI: ASRockRack 1U1G-MILAN/N/ROMED8-NL, BIOS L3.12E 09/06/2022
[    0.000000] tsc: Fast TSC calibration using PIT
[    0.000000] tsc: Detected 3000.081 MHz processor
[    0.000000] e820: update [mem 0x00000000-0x00000fff] usable ==> reserved
[    0.000000] e820: remove [mem 0x000a0000-0x000fffff] usable
[    0.000000] last_pfn = 0x204f100 max_arch_pfn = 0x400000000
[    0.000000] MTRR default type: uncachable
[    0.000000] MTRR fixed ranges enabled:
[    0.000000]   00000-9FFFF write-back
[    0.000000]   A0000-BFFFF write-through
[    0.000000]   C0000-FFFFF uncachable
[    0.000000] MTRR variable ranges enabled:
[    0.000000]   0 base 000000000000 mask FFFF80000000 write-back
[    0.000000]   1 base 000080000000 mask FFFFE0000000 write-back
[    0.000000]   2 base 0000A0000000 mask FFFFF0000000 write-back
[    0.000000]   3 base 0000A9C30000 mask FFFFFFFF0000 uncachable
[    0.000000]   4 disabled
[    0.000000]   5 disabled
[    0.000000]   6 disabled
[    0.000000]   7 disabled
[    0.000000] TOM2: 0000002050000000 aka 132352M
[    0.000000] x86/PAT: Configuration [0-7]: WB  WC  UC- UC  WB  WP  UC- WT  
[    0.000000] total RAM covered: 2815M
[    0.000000] Found optimal setting for mtrr clean up
[    0.000000]  gran_size: 64K 	chunk_size: 128M 	num_reg: 4  	lose cover RAM: 0G
[    0.000000] e820: update [mem 0xa9c30000-0xa9c3ffff] usable ==> reserved
[    0.000000] e820: update [mem 0xb0000000-0xffffffff] usable ==> reserved
[    0.000000] last_pfn = 0xac000 max_arch_pfn = 0x400000000
[    0.000000] esrt: Reserving ESRT space from 0x00000000a4316318 to 0x00000000a4316378.
[    0.000000] e820: update [mem 0xa4316000-0xa4316fff] usable ==> reserved
[    0.000000] Using GB pages for direct mapping
[    0.000000] BRK [0x1261a01000, 0x1261a01fff] PGTABLE
[    0.000000] BRK [0x1261a02000, 0x1261a02fff] PGTABLE
[    0.000000] BRK [0x1261a03000, 0x1261a03fff] PGTABLE
[    0.000000] BRK [0x1261a04000, 0x1261a04fff] PGTABLE
[    0.000000] BRK [0x1261a05000, 0x1261a05fff] PGTABLE
[    0.000000] BRK [0x1261a06000, 0x1261a06fff] PGTABLE
[    0.000000] BRK [0x1261a07000, 0x1261a07fff] PGTABLE
[    0.000000] BRK [0x1261a08000, 0x1261a08fff] PGTABLE
[    0.000000] BRK [0x1261a09000, 0x1261a09fff] PGTABLE
[    0.000000] BRK [0x1261a0a000, 0x1261a0afff] PGTABLE
[    0.000000] BRK [0x1261a0b000, 0x1261a0bfff] PGTABLE
[    0.000000] BRK [0x1261a0c000, 0x1261a0cfff] PGTABLE
[    0.000000] RAMDISK: [mem 0x4f866000-0x55e22fff]
[    0.000000] ACPI: Early table checksum verification disabled
[    0.000000] ACPI: RSDP 0x00000000A9486014 000024 (v02 AMD   )
[    0.000000] ACPI: XSDT 0x00000000A9485728 0000EC (v01 AMD    AmdTable 03242016 AMI  01000013)
[    0.000000] ACPI: FACP 0x00000000A904B000 000114 (v06 AMD    AmdTable 03242016 AMI  00010013)
[    0.000000] ACPI: DSDT 0x00000000A9039000 01187C (v02 AMD    AmdTable 03242016 INTL 20190509)
[    0.000000] ACPI: FACS 0x00000000A9480000 000040
[    0.000000] ACPI: SSDT 0x00000000A9052000 00092A (v02 AMD    AmdTable 00000002 MSFT 04000000)
[    0.000000] ACPI: SPMI 0x00000000A9051000 000041 (v05 AMD    AmdTable 00000000 AMI. 00000000)
[    0.000000] ACPI: SPMI 0x00000000A9050000 000041 (v05 AMD    AmdTable 00000000 AMI. 00000000)
[    0.000000] ACPI: SSDT 0x00000000A904C000 0033A1 (v02 AMD    AMD AFC  00000001 INTL 20190509)
[    0.000000] ACPI: FIDT 0x00000000A9038000 00009C (v01 AMD    AmdTable 03242016 AMI  00010013)
[    0.000000] ACPI: MCFG 0x00000000A9037000 00003C (v01 AMD    AmdTable 03242016 MSFT 00010013)
[    0.000000] ACPI: SSDT 0x00000000A9036000 0003CC (v02 AMD    CPUSSDT  03242016 AMI  03242016)
[    0.000000] ACPI: SSDT 0x00000000A9035000 000342 (v02 AMD    CPMRAS   00000001 INTL 20190509)
[    0.000000] ACPI: BERT 0x00000000A9034000 000030 (v01 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: AAFT 0x00000000A9032000 000073 (v01 AMD    OEMAAFT  03242016 MSFT 00000097)
[    0.000000] ACPI: HPET 0x00000000A9031000 000038 (v01 AMD    AmdTable 03242016 AMI  00000005)
[    0.000000] ACPI: BGRT 0x00000000A9030000 000038 (v01 AMD    AmdTable 03242016 AMI  00010013)
[    0.000000] ACPI: IVRS 0x00000000A902F000 0001F0 (v02 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: TPM2 0x00000000A902E000 00004C (v04 AMD    AmdTable 00000001 AMI  00000000)
[    0.000000] ACPI: PCCT 0x00000000A902D000 00006E (v02 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: SSDT 0x00000000A9025000 007E89 (v02 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: CRAT 0x00000000A9023000 001DA8 (v01 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: CDIT 0x00000000A9022000 000029 (v01 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: SSDT 0x00000000A9020000 00182E (v02 AMD    CPMCMN   00000001 INTL 20190509)
[    0.000000] ACPI: WSMT 0x00000000A901F000 000028 (v01 AMD    AmdTable 03242016 AMI  00010013)
[    0.000000] ACPI: APIC 0x00000000A901E000 000882 (v04 AMD    AmdTable 03242016 AMI  00010013)
[    0.000000] ACPI: ERST 0x00000000A901D000 000230 (v01 AMIER  AMI.ERST 00000000 AMI. 00000000)
[    0.000000] ACPI: HEST 0x00000000A9000000 01CA74 (v01 AMD    AmdTable 00000001 AMD  00000001)
[    0.000000] ACPI: FPDT 0x00000000A9033000 000034 (v01 AMD    A M I    01072009 AMI  01000013)
[    0.000000] ACPI: Reserving FACP table memory at [mem 0xa904b000-0xa904b113]
[    0.000000] ACPI: Reserving DSDT table memory at [mem 0xa9039000-0xa904a87b]
[    0.000000] ACPI: Reserving FACS table memory at [mem 0xa9480000-0xa948003f]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa9052000-0xa9052929]
[    0.000000] ACPI: Reserving SPMI table memory at [mem 0xa9051000-0xa9051040]
[    0.000000] ACPI: Reserving SPMI table memory at [mem 0xa9050000-0xa9050040]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa904c000-0xa904f3a0]
[    0.000000] ACPI: Reserving FIDT table memory at [mem 0xa9038000-0xa903809b]
[    0.000000] ACPI: Reserving MCFG table memory at [mem 0xa9037000-0xa903703b]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa9036000-0xa90363cb]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa9035000-0xa9035341]
[    0.000000] ACPI: Reserving BERT table memory at [mem 0xa9034000-0xa903402f]
[    0.000000] ACPI: Reserving AAFT table memory at [mem 0xa9032000-0xa9032072]
[    0.000000] ACPI: Reserving HPET table memory at [mem 0xa9031000-0xa9031037]
[    0.000000] ACPI: Reserving BGRT table memory at [mem 0xa9030000-0xa9030037]
[    0.000000] ACPI: Reserving IVRS table memory at [mem 0xa902f000-0xa902f1ef]
[    0.000000] ACPI: Reserving TPM2 table memory at [mem 0xa902e000-0xa902e04b]
[    0.000000] ACPI: Reserving PCCT table memory at [mem 0xa902d000-0xa902d06d]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa9025000-0xa902ce88]
[    0.000000] ACPI: Reserving CRAT table memory at [mem 0xa9023000-0xa9024da7]
[    0.000000] ACPI: Reserving CDIT table memory at [mem 0xa9022000-0xa9022028]
[    0.000000] ACPI: Reserving SSDT table memory at [mem 0xa9020000-0xa902182d]
[    0.000000] ACPI: Reserving WSMT table memory at [mem 0xa901f000-0xa901f027]
[    0.000000] ACPI: Reserving APIC table memory at [mem 0xa901e000-0xa901e881]
[    0.000000] ACPI: Reserving ERST table memory at [mem 0xa901d000-0xa901d22f]
[    0.000000] ACPI: Reserving HEST table memory at [mem 0xa9000000-0xa901ca73]
[    0.000000] ACPI: Reserving FPDT table memory at [mem 0xa9033000-0xa9033033]
[    0.000000] ACPI: Local APIC address 0xfee00000
[    0.000000] No NUMA configuration found
[    0.000000] Faking a node at [mem 0x0000000000000000-0x000000204f0fffff]
[    0.000000] NODE_DATA(0) allocated [mem 0x204f0d5000-0x204f0fffff]
[    0.000000] Using crashkernel=auto, the size chosen is a best effort estimation.
[    0.000000] Reserving 512MB of memory at 2032MB for crashkernel (System RAM: 130938MB)
[    0.000000] Zone ranges:
[    0.000000]   DMA      [mem 0x0000000000001000-0x0000000000ffffff]
[    0.000000]   DMA32    [mem 0x0000000001000000-0x00000000ffffffff]
[    0.000000]   Normal   [mem 0x0000000100000000-0x000000204f0fffff]
[    0.000000]   Device   empty
[    0.000000] Movable zone start for each node
[    0.000000] Early memory node ranges
[    0.000000]   node   0: [mem 0x0000000000001000-0x000000000009ffff]
[    0.000000]   node   0: [mem 0x0000000000100000-0x000000002fffffff]
[    0.000000]   node   0: [mem 0x000000003000b000-0x0000000075cfffff]
[    0.000000]   node   0: [mem 0x0000000076000000-0x00000000a70c2fff]
[    0.000000]   node   0: [mem 0x00000000aa3ff000-0x00000000abffffff]
[    0.000000]   node   0: [mem 0x0000000100000000-0x000000204f0fffff]
[    0.000000] Zeroed struct page in unavailable ranges: 34216 pages
[    0.000000] Initmem setup node 0 [mem 0x0000000000001000-0x000000204f0fffff]
[    0.000000] On node 0 totalpages: 33520216
[    0.000000]   DMA zone: 64 pages used for memmap
[    0.000000]   DMA zone: 159 pages reserved
[    0.000000]   DMA zone: 3999 pages, LIFO batch:0
[    0.000000]   DMA32 zone: 10727 pages used for memmap
[    0.000000]   DMA32 zone: 686521 pages, LIFO batch:63
[    0.000000]   Normal zone: 512964 pages used for memmap
[    0.000000]   Normal zone: 32829696 pages, LIFO batch:63
[    0.000000] ACPI: PM-Timer IO Port: 0x808
[    0.000000] ACPI: Local APIC address 0xfee00000
[    0.000000] ACPI: LAPIC_NMI (acpi_id[0xff] high edge lint[0x1])
[    0.000000] IOAPIC[0]: apic_id 240, version 33, address 0xfec00000, GSI 0-23
[    0.000000] IOAPIC[1]: apic_id 241, version 33, address 0xce400000, GSI 24-55
[    0.000000] IOAPIC[2]: apic_id 242, version 33, address 0xb9180000, GSI 56-87
[    0.000000] IOAPIC[3]: apic_id 243, version 33, address 0xb8180000, GSI 88-119
[    0.000000] IOAPIC[4]: apic_id 244, version 33, address 0xfc180000, GSI 120-151
[    0.000000] ACPI: INT_SRC_OVR (bus 0 bus_irq 0 global_irq 2 dfl dfl)
[    0.000000] ACPI: INT_SRC_OVR (bus 0 bus_irq 9 global_irq 9 low level)
[    0.000000] ACPI: IRQ0 used by override.
[    0.000000] ACPI: IRQ9 used by override.
[    0.000000] Using ACPI (MADT) for SMP configuration information
[    0.000000] ACPI: HPET id: 0x10228201 base: 0xfed00000
[    0.000000] e820: update [mem 0xa165f000-0xa17befff] usable ==> reserved
[    0.000000] smpboot: Allowing 32 CPUs, 0 hotplug CPUs
[    0.000000] PM: Registered nosave memory: [mem 0x00000000-0x00000fff]
[    0.000000] PM: Registered nosave memory: [mem 0x000a0000-0x000fffff]
[    0.000000] PM: Registered nosave memory: [mem 0x30000000-0x3000afff]
[    0.000000] PM: Registered nosave memory: [mem 0x75d00000-0x75ffffff]
[    0.000000] PM: Registered nosave memory: [mem 0x9ffb9000-0x9ffb9fff]
[    0.000000] PM: Registered nosave memory: [mem 0x9ffc8000-0x9ffc8fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa04d9000-0xa04d9fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa04e1000-0xa04e1fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa04e2000-0xa04e2fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa054d000-0xa054dfff]
[    0.000000] PM: Registered nosave memory: [mem 0xa054e000-0xa054efff]
[    0.000000] PM: Registered nosave memory: [mem 0xa05b9000-0xa05b9fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa165f000-0xa17befff]
[    0.000000] PM: Registered nosave memory: [mem 0xa4316000-0xa4316fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa70c3000-0xa8f67fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa8f68000-0xa9052fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa9053000-0xa94d3fff]
[    0.000000] PM: Registered nosave memory: [mem 0xa94d4000-0xaa3fefff]
[    0.000000] PM: Registered nosave memory: [mem 0xac000000-0xafffffff]
[    0.000000] PM: Registered nosave memory: [mem 0xb0000000-0xb817ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xb8180000-0xb8180fff]
[    0.000000] PM: Registered nosave memory: [mem 0xb8181000-0xb917ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xb9180000-0xb9180fff]
[    0.000000] PM: Registered nosave memory: [mem 0xb9181000-0xce0fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xce100000-0xce1fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xce200000-0xce2fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xce300000-0xce400fff]
[    0.000000] PM: Registered nosave memory: [mem 0xce401000-0xce4fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xce500000-0xce5fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xce600000-0xfc17ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfc180000-0xfc180fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfc181000-0xfe9fffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfea00000-0xfeafffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfeb00000-0xfebfffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfec00000-0xfec00fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfec01000-0xfec0ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfec10000-0xfec10fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfec11000-0xfecfffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed00000-0xfed00fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed01000-0xfed3ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed40000-0xfed44fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed45000-0xfed7ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed80000-0xfed8ffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfed90000-0xfedbffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfedc0000-0xfedc0fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfedc1000-0xfedc1fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfedc2000-0xfedc8fff]
[    0.000000] PM: Registered nosave memory: [mem 0xfedc9000-0xfedfffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfee00000-0xfeefffff]
[    0.000000] PM: Registered nosave memory: [mem 0xfef00000-0xfeffffff]
[    0.000000] PM: Registered nosave memory: [mem 0xff000000-0xffffffff]
[    0.000000] [mem 0xce600000-0xfc17ffff] available for PCI devices
[    0.000000] Booting paravirtualized kernel on bare hardware
[    0.000000] clocksource: refined-jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 1910969940391419 ns
[    0.000000] setup_percpu: NR_CPUS:8192 nr_cpumask_bits:32 nr_cpu_ids:32 nr_node_ids:1
[    0.000000] percpu: Embedded 63 pages/cpu s221184 r8192 d28672 u262144
[    0.000000] pcpu-alloc: s221184 r8192 d28672 u262144 alloc=1*2097152
[    0.000000] pcpu-alloc: [0] 00 01 02 03 04 05 06 07 [0] 08 09 10 11 12 13 14 15 
[    0.000000] pcpu-alloc: [0] 16 17 18 19 20 21 22 23 [0] 24 25 26 27 28 29 30 31 
[    0.000000] Built 1 zonelists, mobility grouping on.  Total pages: 32996302
[    0.000000] Policy zone: Normal
[    0.000000] Kernel command line: BOOT_IMAGE=(hd0,gpt2)/boot/vmlinuz-4.18.0-513.9.1.el8_9.x86_64 root=UUID=634743d0-c083-44a3-aae6-b54e4ddd6180 ro crashkernel=auto rhgb quiet nouveau.modeset=0
[    0.000000] Specific versions of hardware are certified with Red Hat Enterprise Linux 8. Please see the list of hardware certified with Red Hat Enterprise Linux 8 at https://catalog.redhat.com.
[    0.000000] software IO TLB: area num 32.
[    0.000000] Memory: 2066000K/134080864K available (14341K kernel code, 5952K rwdata, 8520K rodata, 2608K init, 26336K bss, 2999308K reserved, 0K cma-reserved)
[    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=32, Nodes=1
[    0.000000] ftrace: allocating 40939 entries in 160 pages
[    0.000000] ftrace: allocated 160 pages with 2 groups
[    0.000000] rcu: Hierarchical RCU implementation.
[    0.000000] rcu: 	RCU restricting CPUs from NR_CPUS=8192 to nr_cpu_ids=32.
[    0.000000] 	Rude variant of Tasks RCU enabled.
[    0.000000] 	Tracing variant of Tasks RCU enabled.
[    0.000000] rcu: RCU calculated value of scheduler-enlistment delay is 100 jiffies.
[    0.000000] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=32
[    0.000000] NR_IRQS: 524544, nr_irqs: 2856, preallocated irqs: 16
[    0.000000] random: crng done (trusting CPU's manufacturer)
[    0.001000] Console: colour dummy device 80x25
[    0.001000] printk: console [tty0] enabled
[    0.001000] ACPI: Core revision ********
[    0.001000] clocksource: hpet: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: ************ ns
[    0.001000] hpet clockevent registered
[    0.001000] APIC: Switch to symmetric I/O mode setup
[    0.001330] Switched APIC routing to physical flat.
[    0.002603] ..TIMER: vector=0x30 apic1=0 pin1=2 apic2=-1 pin2=-1
[    0.007001] clocksource: tsc-early: mask: 0xffffffffffffffff max_cycles: 0x2b3e924f436, max_idle_ns: ************ ns
[    0.007004] Calibrating delay loop (skipped), value calculated using timer frequency.. 6000.16 BogoMIPS (lpj=3000081)
[    0.007006] pid_max: default: 32768 minimum: 301
[    0.009021] LSM: Security Framework initializing
[    0.009029] Yama: becoming mindful.
[    0.009033] SELinux:  Initializing.
[    0.009049] LSM support for eBPF active
[    0.013064] Dentry cache hash table entries: 8388608 (order: 14, ******** bytes, vmalloc)
[    0.015009] Inode-cache hash table entries: 4194304 (order: 13, ******** bytes, vmalloc)
[    0.015076] Mount-cache hash table entries: 131072 (order: 8, 1048576 bytes, vmalloc)
[    0.015139] Mountpoint-cache hash table entries: 131072 (order: 8, 1048576 bytes, vmalloc)
[    0.015327] x86/cpu: User Mode Instruction Prevention (UMIP) activated
[    0.015428] LVT offset 2 assigned for vector 0xf4
[    0.015460] process: using mwait in idle threads
[    0.015460] Last level iTLB entries: 4KB 512, 2MB 512, 4MB 256
[    0.015461] Last level dTLB entries: 4KB 2048, 2MB 2048, 4MB 1024, 1GB 0
[    0.015464] Spectre V1 : Mitigation: usercopy/swapgs barriers and __user pointer sanitization
[    0.015465] Spectre V2 : Mitigation: Retpolines
[    0.015465] Spectre V2 : Spectre v2 / SpectreRSB mitigation: Filling RSB on context switch
[    0.015466] Spectre V2 : Spectre v2 / SpectreRSB : Filling RSB on VMEXIT
[    0.015466] Spectre V2 : Enabling Restricted Speculation for firmware calls
[    0.015467] Spectre V2 : mitigation: Enabling conditional Indirect Branch Prediction Barrier
[    0.015468] Spectre V2 : User space: Mitigation: STIBP always-on protection
[    0.015469] Speculative Store Bypass: Mitigation: Speculative Store Bypass disabled via prctl
[    0.017176] Freeing SMP alternatives memory: 36K
[    0.119408] smpboot: CPU0: AMD EPYC 7313P 16-Core Processor (family: 0x19, model: 0x1, stepping: 0x1)
[    0.119518] Performance Events: Fam17h+ 16-deep BRS, core perfctr, AMD PMU driver.
[    0.119522] ... version:                0
[    0.119523] ... bit width:              48
[    0.119523] ... generic registers:      6
[    0.119523] ... value mask:             0000ffffffffffff
[    0.119524] ... max period:             00007fffffffffff
[    0.119525] ... fixed-purpose events:   0
[    0.119525] ... event mask:             000000000000003f
[    0.119548] rcu: Hierarchical SRCU implementation.
[    0.120000] NMI watchdog: Enabled. Permanently consumes one hw-PMU counter.
[    0.120000] smp: Bringing up secondary CPUs ...
[    0.120000] x86: Booting SMP configuration:
[    0.120000] .... node  #0, CPUs:        #1  #2  #3  #4  #5  #6  #7  #8  #9 #10 #11 #12 #13 #14 #15 #16
[    0.140072] Spectre V2 : Update user space SMT mitigation: STIBP always-on
[    0.140094]  #17 #18 #19 #20 #21 #22 #23 #24 #25 #26 #27 #28 #29 #30 #31
[    0.159019] smp: Brought up 1 node, 32 CPUs
[    0.159019] smpboot: Max logical packages: 1
[    0.159019] smpboot: Total of 32 processors activated (192005.18 BogoMIPS)
[    0.215013] node 0 deferred pages initialised in 55ms
[    0.217027] devtmpfs: initialized
[    0.217041] x86/mm: Memory block size: 2048MB
[    0.217489] ACPI: PM: Registering ACPI NVS region [mem 0x30000000-0x3000afff] (45056 bytes)
[    0.217489] ACPI: PM: Registering ACPI NVS region [mem 0xa9053000-0xa94d3fff] (4722688 bytes)
[    0.217489] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 1911260446275000 ns
[    0.217489] futex hash table entries: 8192 (order: 7, 524288 bytes, vmalloc)
[    0.217489] pinctrl core: initialized pinctrl subsystem
[    0.218007] NET: Registered protocol family 16
[    0.218087] DMA: preallocated 4096 KiB GFP_KERNEL pool for atomic allocations
[    0.218094] DMA: preallocated 4096 KiB GFP_KERNEL|GFP_DMA pool for atomic allocations
[    0.218100] DMA: preallocated 4096 KiB GFP_KERNEL|GFP_DMA32 pool for atomic allocations
[    0.218108] audit: initializing netlink subsys (disabled)
[    0.218112] audit: type=2000 audit(1708306262.218:1): state=initialized audit_enabled=0 res=1
[    0.218112] cpuidle: using governor menu
[    0.218112] Detected 1 PCC Subspaces
[    0.218112] Registering PCC driver as Mailbox controller
[    0.218112] acpiphp: ACPI Hot Plug PCI Controller Driver version: 0.5
[    0.218112] PCI: MMCONFIG for domain 0000 [bus 00-ff] at [mem 0xe0000000-0xefffffff] (base 0xe0000000)
[    0.218112] PCI: not using MMCONFIG
[    0.218115] PCI: Using configuration type 1 for base access
[    0.218115] PCI: Using configuration type 1 for extended access
[    0.220024] HugeTLB registered 1.00 GiB page size, pre-allocated 0 pages
[    0.220024] HugeTLB registered 2.00 MiB page size, pre-allocated 0 pages
[    0.220042] cryptd: max_cpu_qlen set to 1000
[    0.220055] ACPI: Added _OSI(Module Device)
[    0.220056] ACPI: Added _OSI(Processor Device)
[    0.220057] ACPI: Added _OSI(3.0 _SCP Extensions)
[    0.220057] ACPI: Added _OSI(Processor Aggregator Device)
[    0.220058] ACPI: Added _OSI(Linux-Dell-Video)
[    0.220059] ACPI: Added _OSI(Linux-Lenovo-NV-HDMI-Audio)
[    0.220060] ACPI: Added _OSI(Linux-HPI-Hybrid-Graphics)
[    0.228944] ACPI: 7 ACPI AML tables successfully acquired and loaded
[    0.232263] ACPI: Interpreter enabled
[    0.232272] ACPI: PM: (supports S0 S5)
[    0.232273] ACPI: Using IOAPIC for interrupt routing
[    0.232376] PCI: MMCONFIG for domain 0000 [bus 00-ff] at [mem 0xe0000000-0xefffffff] (base 0xe0000000)
[    0.232451] PCI: MMCONFIG at [mem 0xe0000000-0xefffffff] reserved in ACPI motherboard resources
[    0.245791] HEST: Table parsing has been initialized.
[    0.245794] PCI: Using host bridge windows from ACPI; if necessary, use "pci=nocrs" and report a bug
[    0.246317] ACPI: Enabled 3 GPEs in block 00 to 1F
[    0.247035] ACPI: Power Resource [P0SA]
[    0.247049] ACPI: Power Resource [P3SA]
[    0.247258] ACPI: Power Resource [P0SA]
[    0.247271] ACPI: Power Resource [P3SA]
[    0.247980] ACPI: Power Resource [P0SA]
[    0.247994] ACPI: Power Resource [P3SA]
[    0.248201] ACPI: Power Resource [P0SA]
[    0.248214] ACPI: Power Resource [P3SA]
[    0.248979] ACPI: Power Resource [P0SA]
[    0.248993] ACPI: Power Resource [P3SA]
[    0.249343] ACPI: Power Resource [P0SA]
[    0.249355] ACPI: Power Resource [P3SA]
[    0.250302] ACPI: Power Resource [P0SA]
[    0.250315] ACPI: Power Resource [P3SA]
[    0.250515] ACPI: Power Resource [P0SA]
[    0.250527] ACPI: Power Resource [P3SA]
[    0.252559] ACPI: Power Resource [P0SA]
[    0.252574] ACPI: Power Resource [P3SA]
[    0.252789] ACPI: Power Resource [P0SA]
[    0.252803] ACPI: Power Resource [P3SA]
[    0.253504] ACPI: Power Resource [P0SA]
[    0.253517] ACPI: Power Resource [P3SA]
[    0.253736] ACPI: Power Resource [P0SA]
[    0.253750] ACPI: Power Resource [P3SA]
[    0.254445] ACPI: Power Resource [P0SA]
[    0.254458] ACPI: Power Resource [P3SA]
[    0.254675] ACPI: Power Resource [P0SA]
[    0.254690] ACPI: Power Resource [P3SA]
[    0.255386] ACPI: Power Resource [P0SA]
[    0.255398] ACPI: Power Resource [P3SA]
[    0.255602] ACPI: Power Resource [P0SA]
[    0.255616] ACPI: Power Resource [P3SA]
[    0.257264] ACPI: PCI Root Bridge [S0D0] (domain 0000 [bus c0-ff])
[    0.257269] acpi PNP0A08:00: _OSC: OS supports [ExtendedConfig ASPM ClockPM Segments MSI EDR HPX-Type3]
[    0.257323] acpi PNP0A08:00: _OSC: platform does not support [AER LTR DPC]
[    0.257414] acpi PNP0A08:00: _OSC: OS now controls [PCIeHotplug SHPCHotplug PME PCIeCapability]
[    0.257577] PCI host bridge to bus 0000:c0
[    0.257578] pci_bus 0000:c0: root bus resource [io  0xc000-0xffff window]
[    0.257579] pci_bus 0000:c0: root bus resource [mem 0xc2000000-0xc5bfffff window]
[    0.257580] pci_bus 0000:c0: root bus resource [mem 0x10021000000-0x18020ffffff window]
[    0.257582] pci_bus 0000:c0: root bus resource [bus c0-ff]
[    0.257594] pci 0000:c0:00.0: [1022:1480] type 00 class 0x060000
[    0.257661] pci 0000:c0:00.2: [1022:164f] type 00 class 0x080600
[    0.257743] pci 0000:c0:01.0: [1022:1482] type 00 class 0x060000
[    0.257791] pci 0000:c0:02.0: [1022:1482] type 00 class 0x060000
[    0.257838] pci 0000:c0:03.0: [1022:1482] type 00 class 0x060000
[    0.257893] pci 0000:c0:03.1: [1022:1483] type 01 class 0x060400
[    0.257967] pci 0000:c0:03.1: PME# supported from D0 D3hot D3cold
[    0.258049] pci 0000:c0:04.0: [1022:1482] type 00 class 0x060000
[    0.258098] pci 0000:c0:05.0: [1022:1482] type 00 class 0x060000
[    0.258147] pci 0000:c0:07.0: [1022:1482] type 00 class 0x060000
[    0.258198] pci 0000:c0:07.1: [1022:1484] type 01 class 0x060400
[    0.258223] pci 0000:c0:07.1: enabling Extended Tags
[    0.258261] pci 0000:c0:07.1: PME# supported from D0 D3hot D3cold
[    0.258334] pci 0000:c0:08.0: [1022:1482] type 00 class 0x060000
[    0.258386] pci 0000:c0:08.1: [1022:1484] type 01 class 0x060400
[    0.258411] pci 0000:c0:08.1: enabling Extended Tags
[    0.258451] pci 0000:c0:08.1: PME# supported from D0 D3hot D3cold
[    0.258572] pci 0000:c1:00.0: [10de:26b5] type 00 class 0x030200
[    0.258581] pci 0000:c1:00.0: reg 0x10: [mem 0xc4000000-0xc4ffffff]
[    0.258588] pci 0000:c1:00.0: reg 0x14: [mem 0x15000000000-0x15fffffffff 64bit pref]
[    0.258596] pci 0000:c1:00.0: reg 0x1c: [mem 0x17040000000-0x17041ffffff 64bit pref]
[    0.258615] pci 0000:c1:00.0: Enabling HDA controller
[    0.258663] pci 0000:c1:00.0: PME# supported from D0 D3hot
[    0.258689] pci 0000:c1:00.0: reg 0xbf0: [mem 0xc5000000-0xc503ffff]
[    0.258690] pci 0000:c1:00.0: VF(n) BAR0 space: [mem 0xc5000000-0xc57fffff] (contains BAR0 for 32 VFs)
[    0.258696] pci 0000:c1:00.0: reg 0xbf4: [mem 0x16000000000-0x1607fffffff 64bit pref]
[    0.258697] pci 0000:c1:00.0: VF(n) BAR1 space: [mem 0x16000000000-0x16fffffffff 64bit pref] (contains BAR1 for 32 VFs)
[    0.258703] pci 0000:c1:00.0: reg 0xbfc: [mem 0x17000000000-0x17001ffffff 64bit pref]
[    0.258704] pci 0000:c1:00.0: VF(n) BAR3 space: [mem 0x17000000000-0x1703fffffff 64bit pref] (contains BAR3 for 32 VFs)
[    0.258845] pci 0000:c0:03.1: PCI bridge to [bus c1]
[    0.258850] pci 0000:c0:03.1:   bridge window [mem 0xc4000000-0xc57fffff]
[    0.258853] pci 0000:c0:03.1:   bridge window [mem 0x15000000000-0x17041ffffff 64bit pref]
[    0.258899] pci 0000:c2:00.0: [1022:148a] type 00 class 0x130000
[    0.258928] pci 0000:c2:00.0: enabling Extended Tags
[    0.259042] pci 0000:c2:00.2: [1022:1498] type 00 class 0x108000
[    0.259056] pci 0000:c2:00.2: reg 0x18: [mem 0xc5a00000-0xc5a7ffff]
[    0.259067] pci 0000:c2:00.2: reg 0x24: [mem 0xc5a80000-0xc5a81fff]
[    0.259074] pci 0000:c2:00.2: enabling Extended Tags
[    0.259183] pci 0000:c0:07.1: PCI bridge to [bus c2]
[    0.259187] pci 0000:c0:07.1:   bridge window [mem 0xc5a00000-0xc5afffff]
[    0.259241] pci 0000:c3:00.0: [1022:1485] type 00 class 0x130000
[    0.259273] pci 0000:c3:00.0: enabling Extended Tags
[    0.259392] pci 0000:c3:00.2: [1022:1498] type 00 class 0x108000
[    0.259407] pci 0000:c3:00.2: reg 0x18: [mem 0xc5900000-0xc597ffff]
[    0.259418] pci 0000:c3:00.2: reg 0x24: [mem 0xc5980000-0xc5981fff]
[    0.259426] pci 0000:c3:00.2: enabling Extended Tags
[    0.259540] pci 0000:c0:08.1: PCI bridge to [bus c3]
[    0.259544] pci 0000:c0:08.1:   bridge window [mem 0xc5900000-0xc59fffff]
[    0.259803] ACPI: PCI Root Bridge [S0D1] (domain 0000 [bus 80-bf])
[    0.259806] acpi PNP0A08:01: _OSC: OS supports [ExtendedConfig ASPM ClockPM Segments MSI EDR HPX-Type3]
[    0.259863] acpi PNP0A08:01: _OSC: platform does not support [AER LTR DPC]
[    0.259957] acpi PNP0A08:01: _OSC: OS now controls [PCIeHotplug SHPCHotplug PME PCIeCapability]
[    0.260114] PCI host bridge to bus 0000:80
[    0.260115] pci_bus 0000:80: root bus resource [io  0x8000-0xbfff window]
[    0.260116] pci_bus 0000:80: root bus resource [mem 0xba000000-0xbc3fffff window]
[    0.260117] pci_bus 0000:80: root bus resource [mem 0x18021000000-0x20020ffffff window]
[    0.260118] pci_bus 0000:80: root bus resource [bus 80-bf]
[    0.260127] pci 0000:80:00.0: [1022:1480] type 00 class 0x060000
[    0.260188] pci 0000:80:00.2: [1022:164f] type 00 class 0x080600
[    0.260268] pci 0000:80:01.0: [1022:1482] type 00 class 0x060000
[    0.260318] pci 0000:80:02.0: [1022:1482] type 00 class 0x060000
[    0.260366] pci 0000:80:03.0: [1022:1482] type 00 class 0x060000
[    0.260416] pci 0000:80:04.0: [1022:1482] type 00 class 0x060000
[    0.260464] pci 0000:80:05.0: [1022:1482] type 00 class 0x060000
[    0.260513] pci 0000:80:07.0: [1022:1482] type 00 class 0x060000
[    0.260565] pci 0000:80:07.1: [1022:1484] type 01 class 0x060400
[    0.260591] pci 0000:80:07.1: enabling Extended Tags
[    0.260632] pci 0000:80:07.1: PME# supported from D0 D3hot D3cold
[    0.260704] pci 0000:80:08.0: [1022:1482] type 00 class 0x060000
[    0.260757] pci 0000:80:08.1: [1022:1484] type 01 class 0x060400
[    0.260783] pci 0000:80:08.1: enabling Extended Tags
[    0.260825] pci 0000:80:08.1: PME# supported from D0 D3hot D3cold
[    0.260902] pci 0000:80:08.2: [1022:1484] type 01 class 0x060400
[    0.260928] pci 0000:80:08.2: enabling Extended Tags
[    0.260970] pci 0000:80:08.2: PME# supported from D0 D3hot D3cold
[    0.261046] pci 0000:80:08.3: [1022:1484] type 01 class 0x060400
[    0.261073] pci 0000:80:08.3: enabling Extended Tags
[    0.261115] pci 0000:80:08.3: PME# supported from D0 D3hot D3cold
[    0.261230] pci 0000:81:00.0: [1022:148a] type 00 class 0x130000
[    0.261261] pci 0000:81:00.0: enabling Extended Tags
[    0.261376] pci 0000:81:00.2: [1022:1498] type 00 class 0x108000
[    0.261390] pci 0000:81:00.2: reg 0x18: [mem 0xbc300000-0xbc37ffff]
[    0.261401] pci 0000:81:00.2: reg 0x24: [mem 0xbc380000-0xbc381fff]
[    0.261409] pci 0000:81:00.2: enabling Extended Tags
[    0.261517] pci 0000:80:07.1: PCI bridge to [bus 81]
[    0.261522] pci 0000:80:07.1:   bridge window [mem 0xbc300000-0xbc3fffff]
[    0.261575] pci 0000:82:00.0: [1022:1485] type 00 class 0x130000
[    0.261609] pci 0000:82:00.0: enabling Extended Tags
[    0.261731] pci 0000:82:00.2: [1022:1498] type 00 class 0x108000
[    0.261747] pci 0000:82:00.2: reg 0x18: [mem 0xbc200000-0xbc27ffff]
[    0.261758] pci 0000:82:00.2: reg 0x24: [mem 0xbc280000-0xbc281fff]
[    0.261766] pci 0000:82:00.2: enabling Extended Tags
[    0.261883] pci 0000:80:08.1: PCI bridge to [bus 82]
[    0.261887] pci 0000:80:08.1:   bridge window [mem 0xbc200000-0xbc2fffff]
[    0.261934] pci 0000:83:00.0: [1022:7901] type 00 class 0x010601
[    0.261969] pci 0000:83:00.0: reg 0x24: [mem 0xbc100000-0xbc1007ff]
[    0.261979] pci 0000:83:00.0: enabling Extended Tags
[    0.262035] pci 0000:83:00.0: PME# supported from D3hot D3cold
[    0.262125] pci 0000:80:08.2: PCI bridge to [bus 83]
[    0.262130] pci 0000:80:08.2:   bridge window [mem 0xbc100000-0xbc1fffff]
[    0.262176] pci 0000:84:00.0: [1022:7901] type 00 class 0x010601
[    0.262211] pci 0000:84:00.0: reg 0x24: [mem 0xbc000000-0xbc0007ff]
[    0.262221] pci 0000:84:00.0: enabling Extended Tags
[    0.262276] pci 0000:84:00.0: PME# supported from D3hot D3cold
[    0.262365] pci 0000:80:08.3: PCI bridge to [bus 84]
[    0.262369] pci 0000:80:08.3:   bridge window [mem 0xbc000000-0xbc0fffff]
[    0.262637] ACPI: PCI Root Bridge [S0D2] (domain 0000 [bus 40-7f])
[    0.262640] acpi PNP0A08:02: _OSC: OS supports [ExtendedConfig ASPM ClockPM Segments MSI EDR HPX-Type3]
[    0.262695] acpi PNP0A08:02: _OSC: platform does not support [AER LTR DPC]
[    0.262788] acpi PNP0A08:02: _OSC: OS now controls [PCIeHotplug SHPCHotplug PME PCIeCapability]
[    0.262959] PCI host bridge to bus 0000:40
[    0.262960] pci_bus 0000:40: root bus resource [io  0x03b0-0x03df window]
[    0.262961] pci_bus 0000:40: root bus resource [io  0x4000-0x7fff window]
[    0.262962] pci_bus 0000:40: root bus resource [mem 0x000a0000-0x000bffff window]
[    0.262963] pci_bus 0000:40: root bus resource [mem 0xb0000000-0xb3afffff window]
[    0.262964] pci_bus 0000:40: root bus resource [mem 0x20081000000-0x28080ffffff window]
[    0.262965] pci_bus 0000:40: root bus resource [bus 40-7f]
[    0.262973] pci 0000:40:00.0: [1022:1480] type 00 class 0x060000
[    0.263033] pci 0000:40:00.2: [1022:164f] type 00 class 0x080600
[    0.263114] pci 0000:40:01.0: [1022:1482] type 00 class 0x060000
[    0.263170] pci 0000:40:01.1: [1022:1483] type 01 class 0x060400
[    0.263244] pci 0000:40:01.1: PME# supported from D0 D3hot D3cold
[    0.263328] pci 0000:40:01.5: [1022:1483] type 01 class 0x060400
[    0.263401] pci 0000:40:01.5: PME# supported from D0 D3hot D3cold
[    0.263475] pci 0000:40:02.0: [1022:1482] type 00 class 0x060000
[    0.263523] pci 0000:40:03.0: [1022:1482] type 00 class 0x060000
[    0.263571] pci 0000:40:04.0: [1022:1482] type 00 class 0x060000
[    0.263618] pci 0000:40:05.0: [1022:1482] type 00 class 0x060000
[    0.263669] pci 0000:40:07.0: [1022:1482] type 00 class 0x060000
[    0.263720] pci 0000:40:07.1: [1022:1484] type 01 class 0x060400
[    0.263745] pci 0000:40:07.1: enabling Extended Tags
[    0.263784] pci 0000:40:07.1: PME# supported from D0 D3hot D3cold
[    0.263856] pci 0000:40:08.0: [1022:1482] type 00 class 0x060000
[    0.263908] pci 0000:40:08.1: [1022:1484] type 01 class 0x060400
[    0.263933] pci 0000:40:08.1: enabling Extended Tags
[    0.263974] pci 0000:40:08.1: PME# supported from D0 D3hot D3cold
[    0.264054] pci 0000:40:08.2: [1022:1484] type 01 class 0x060400
[    0.264080] pci 0000:40:08.2: enabling Extended Tags
[    0.264120] pci 0000:40:08.2: PME# supported from D0 D3hot D3cold
[    0.264192] pci 0000:40:08.3: [1022:1484] type 01 class 0x060400
[    0.264218] pci 0000:40:08.3: enabling Extended Tags
[    0.264258] pci 0000:40:08.3: PME# supported from D0 D3hot D3cold
[    0.264373] pci 0000:41:00.0: [1344:51c0] type 00 class 0x010802
[    0.264390] pci 0000:41:00.0: reg 0x10: [mem 0xb3880000-0xb38bffff 64bit]
[    0.264413] pci 0000:41:00.0: reg 0x20: [mem 0xb3840000-0xb387ffff 64bit]
[    0.264421] pci 0000:41:00.0: reg 0x30: [mem 0xb3800000-0xb383ffff pref]
[    0.264487] pci 0000:41:00.0: PME# supported from D0 D1 D3hot
[    0.264541] pci 0000:41:00.0: 31.504 Gb/s available PCIe bandwidth, limited by 8.0 GT/s PCIe x4 link at 0000:40:01.1 (capable of 63.012 Gb/s with 16.0 GT/s PCIe x4 link)
[    0.264579] pci 0000:40:01.1: PCI bridge to [bus 41]
[    0.264583] pci 0000:40:01.1:   bridge window [mem 0xb3800000-0xb38fffff]
[    0.264628] pci 0000:42:00.0: [1a03:1150] type 01 class 0x060400
[    0.264720] pci 0000:42:00.0: supports D1 D2
[    0.264721] pci 0000:42:00.0: PME# supported from D0 D1 D2 D3hot D3cold
[    0.268013] pci 0000:40:01.5: PCI bridge to [bus 42-43]
[    0.268018] pci 0000:40:01.5:   bridge window [io  0x7000-0x7fff]
[    0.268020] pci 0000:40:01.5:   bridge window [mem 0xb2000000-0xb30fffff]
[    0.268055] pci_bus 0000:43: extended config space not accessible
[    0.268082] pci 0000:43:00.0: [1a03:2000] type 00 class 0x030000
[    0.268101] pci 0000:43:00.0: reg 0x10: [mem 0xb2000000-0xb2ffffff]
[    0.268111] pci 0000:43:00.0: reg 0x14: [mem 0xb3000000-0xb301ffff]
[    0.268122] pci 0000:43:00.0: reg 0x18: [io  0x7000-0x707f]
[    0.268168] pci 0000:43:00.0: BAR 0: assigned to efifb
[    0.268202] pci 0000:43:00.0: supports D1 D2
[    0.268204] pci 0000:43:00.0: PME# supported from D0 D1 D2 D3hot D3cold
[    0.268303] pci 0000:42:00.0: PCI bridge to [bus 43]
[    0.268308] pci 0000:42:00.0:   bridge window [io  0x7000-0x7fff]
[    0.268311] pci 0000:42:00.0:   bridge window [mem 0xb2000000-0xb30fffff]
[    0.268379] pci 0000:44:00.0: [1022:148a] type 00 class 0x130000
[    0.268413] pci 0000:44:00.0: enabling Extended Tags
[    0.268548] pci 0000:44:00.2: [1022:1498] type 00 class 0x108000
[    0.268565] pci 0000:44:00.2: reg 0x18: [mem 0xb3700000-0xb377ffff]
[    0.268579] pci 0000:44:00.2: reg 0x24: [mem 0xb3780000-0xb3781fff]
[    0.268588] pci 0000:44:00.2: enabling Extended Tags
[    0.268734] pci 0000:40:07.1: PCI bridge to [bus 44]
[    0.268739] pci 0000:40:07.1:   bridge window [mem 0xb3700000-0xb37fffff]
[    0.268819] pci 0000:45:00.0: [1022:1485] type 00 class 0x130000
[    0.268858] pci 0000:45:00.0: enabling Extended Tags
[    0.268993] pci 0000:45:00.1: [1022:1486] type 00 class 0x108000
[    0.269012] pci 0000:45:00.1: reg 0x18: [mem 0xb3300000-0xb33fffff]
[    0.269023] pci 0000:45:00.1: reg 0x24: [mem 0xb3482000-0xb3483fff]
[    0.269031] pci 0000:45:00.1: enabling Extended Tags
[    0.269140] pci 0000:45:00.2: [1022:1498] type 00 class 0x108000
[    0.269156] pci 0000:45:00.2: reg 0x18: [mem 0xb3400000-0xb347ffff]
[    0.269167] pci 0000:45:00.2: reg 0x24: [mem 0xb3480000-0xb3481fff]
[    0.269176] pci 0000:45:00.2: enabling Extended Tags
[    0.269284] pci 0000:45:00.3: [1022:148c] type 00 class 0x0c0330
[    0.269296] pci 0000:45:00.3: reg 0x10: [mem 0xb3200000-0xb32fffff 64bit]
[    0.269326] pci 0000:45:00.3: enabling Extended Tags
[    0.269369] pci 0000:45:00.3: PME# supported from D0 D3hot D3cold
[    0.269450] pci 0000:40:08.1: PCI bridge to [bus 45]
[    0.269454] pci 0000:40:08.1:   bridge window [mem 0xb3200000-0xb34fffff]
[    0.269501] pci 0000:46:00.0: [1022:7901] type 00 class 0x010601
[    0.269535] pci 0000:46:00.0: reg 0x24: [mem 0xb3600000-0xb36007ff]
[    0.269545] pci 0000:46:00.0: enabling Extended Tags
[    0.269601] pci 0000:46:00.0: PME# supported from D3hot D3cold
[    0.269698] pci 0000:40:08.2: PCI bridge to [bus 46]
[    0.269702] pci 0000:40:08.2:   bridge window [mem 0xb3600000-0xb36fffff]
[    0.269751] pci 0000:47:00.0: [1022:7901] type 00 class 0x010601
[    0.269786] pci 0000:47:00.0: reg 0x24: [mem 0xb3500000-0xb35007ff]
[    0.269795] pci 0000:47:00.0: enabling Extended Tags
[    0.269849] pci 0000:47:00.0: PME# supported from D3hot D3cold
[    0.269937] pci 0000:40:08.3: PCI bridge to [bus 47]
[    0.269942] pci 0000:40:08.3:   bridge window [mem 0xb3500000-0xb35fffff]
[    0.270517] ACPI: PCI Root Bridge [PCI0] (domain 0000 [bus 00-3f])
[    0.270520] acpi PNP0A08:03: _OSC: OS supports [ExtendedConfig ASPM ClockPM Segments MSI EDR HPX-Type3]
[    0.270577] acpi PNP0A08:03: _OSC: platform does not support [AER LTR DPC]
[    0.270674] acpi PNP0A08:03: _OSC: OS now controls [PCIeHotplug SHPCHotplug PME PCIeCapability]
[    0.270893] PCI host bridge to bus 0000:00
[    0.270894] pci_bus 0000:00: root bus resource [io  0x0000-0x02ff window]
[    0.270895] pci_bus 0000:00: root bus resource [io  0x0300-0x03af window]
[    0.270896] pci_bus 0000:00: root bus resource [io  0x03e0-0x0cf7 window]
[    0.270897] pci_bus 0000:00: root bus resource [io  0x1000-0x3fff window]
[    0.270898] pci_bus 0000:00: root bus resource [mem 0x000c0000-0x000dffff window]
[    0.270898] pci_bus 0000:00: root bus resource [mem 0xf0000000-0xf24fffff window]
[    0.270899] pci_bus 0000:00: root bus resource [mem 0x28081000000-0x38080ffffff window]
[    0.270900] pci_bus 0000:00: root bus resource [bus 00-3f]
[    0.270910] pci 0000:00:00.0: [1022:1480] type 00 class 0x060000
[    0.270971] pci 0000:00:00.2: [1022:164f] type 00 class 0x080600
[    0.271060] pci 0000:00:01.0: [1022:1482] type 00 class 0x060000
[    0.271116] pci 0000:00:02.0: [1022:1482] type 00 class 0x060000
[    0.271172] pci 0000:00:03.0: [1022:1482] type 00 class 0x060000
[    0.271231] pci 0000:00:03.3: [1022:1483] type 01 class 0x060400
[    0.271308] pci 0000:00:03.3: PME# supported from D0 D3hot D3cold
[    0.271396] pci 0000:00:03.5: [1022:1483] type 01 class 0x060400
[    0.271474] pci 0000:00:03.5: PME# supported from D0 D3hot D3cold
[    0.271549] pci 0000:00:04.0: [1022:1482] type 00 class 0x060000
[    0.271605] pci 0000:00:05.0: [1022:1482] type 00 class 0x060000
[    0.271661] pci 0000:00:07.0: [1022:1482] type 00 class 0x060000
[    0.271714] pci 0000:00:07.1: [1022:1484] type 01 class 0x060400
[    0.271740] pci 0000:00:07.1: enabling Extended Tags
[    0.271781] pci 0000:00:07.1: PME# supported from D0 D3hot D3cold
[    0.271860] pci 0000:00:08.0: [1022:1482] type 00 class 0x060000
[    0.271914] pci 0000:00:08.1: [1022:1484] type 01 class 0x060400
[    0.271941] pci 0000:00:08.1: enabling Extended Tags
[    0.271983] pci 0000:00:08.1: PME# supported from D0 D3hot D3cold
[    0.272081] pci 0000:00:14.0: [1022:790b] type 00 class 0x0c0500
[    0.272189] pci 0000:00:14.3: [1022:790e] type 00 class 0x060100
[    0.272305] pci 0000:00:18.0: [1022:1650] type 00 class 0x060000
[    0.272337] pci 0000:00:18.1: [1022:1651] type 00 class 0x060000
[    0.272368] pci 0000:00:18.2: [1022:1652] type 00 class 0x060000
[    0.272398] pci 0000:00:18.3: [1022:1653] type 00 class 0x060000
[    0.272429] pci 0000:00:18.4: [1022:1654] type 00 class 0x060000
[    0.272459] pci 0000:00:18.5: [1022:1655] type 00 class 0x060000
[    0.272489] pci 0000:00:18.6: [1022:1656] type 00 class 0x060000
[    0.272520] pci 0000:00:18.7: [1022:1657] type 00 class 0x060000
[    0.272733] pci 0000:01:00.0: [15b3:1015] type 00 class 0x020000
[    0.272874] pci 0000:01:00.0: reg 0x10: [mem 0x3807e000000-0x3807fffffff 64bit pref]
[    0.273151] pci 0000:01:00.0: reg 0x30: [mem 0xf2300000-0xf23fffff pref]
[    0.273782] pci 0000:01:00.0: PME# supported from D3cold
[    0.274044] pci 0000:01:00.0: reg 0x1a4: [mem 0x38080800000-0x380808fffff 64bit pref]
[    0.274045] pci 0000:01:00.0: VF(n) BAR0 space: [mem 0x38080800000-0x38080ffffff 64bit pref] (contains BAR0 for 8 VFs)
[    0.275232] pci 0000:01:00.1: [15b3:1015] type 00 class 0x020000
[    0.275371] pci 0000:01:00.1: reg 0x10: [mem 0x3807c000000-0x3807dffffff 64bit pref]
[    0.275656] pci 0000:01:00.1: reg 0x30: [mem 0xf2200000-0xf22fffff pref]
[    0.276240] pci 0000:01:00.1: PME# supported from D3cold
[    0.276496] pci 0000:01:00.1: reg 0x1a4: [mem 0x38080000000-0x380800fffff 64bit pref]
[    0.276497] pci 0000:01:00.1: VF(n) BAR0 space: [mem 0x38080000000-0x380807fffff 64bit pref] (contains BAR0 for 8 VFs)
[    0.277657] pci 0000:00:03.3: PCI bridge to [bus 01]
[    0.277661] pci 0000:00:03.3:   bridge window [mem 0xf2200000-0xf23fffff]
[    0.277664] pci 0000:00:03.3:   bridge window [mem 0x3807c000000-0x38080ffffff 64bit pref]
[    0.277693] pci 0000:00:03.5: PCI bridge to [bus 02]
[    0.277744] pci 0000:03:00.0: [1022:148a] type 00 class 0x130000
[    0.277774] pci 0000:03:00.0: enabling Extended Tags
[    0.277885] pci 0000:03:00.2: [1022:1498] type 00 class 0x108000
[    0.277899] pci 0000:03:00.2: reg 0x18: [mem 0xf2400000-0xf247ffff]
[    0.277909] pci 0000:03:00.2: reg 0x24: [mem 0xf2480000-0xf2481fff]
[    0.277917] pci 0000:03:00.2: enabling Extended Tags
[    0.278037] pci 0000:00:07.1: PCI bridge to [bus 03]
[    0.278042] pci 0000:00:07.1:   bridge window [mem 0xf2400000-0xf24fffff]
[    0.278095] pci 0000:04:00.0: [1022:1485] type 00 class 0x130000
[    0.278127] pci 0000:04:00.0: enabling Extended Tags
[    0.278245] pci 0000:04:00.2: [1022:1498] type 00 class 0x108000
[    0.278260] pci 0000:04:00.2: reg 0x18: [mem 0xf2100000-0xf217ffff]
[    0.278271] pci 0000:04:00.2: reg 0x24: [mem 0xf2180000-0xf2181fff]
[    0.278279] pci 0000:04:00.2: enabling Extended Tags
[    0.278386] pci 0000:04:00.3: [1022:148c] type 00 class 0x0c0330
[    0.278398] pci 0000:04:00.3: reg 0x10: [mem 0xf2000000-0xf20fffff 64bit]
[    0.278427] pci 0000:04:00.3: enabling Extended Tags
[    0.278470] pci 0000:04:00.3: PME# supported from D0 D3hot D3cold
[    0.278547] pci 0000:00:08.1: PCI bridge to [bus 04]
[    0.278551] pci 0000:00:08.1:   bridge window [mem 0xf2000000-0xf21fffff]
[    0.279240] ACPI: PCI: Interrupt link LNKA configured for IRQ 0
[    0.279273] ACPI: PCI: Interrupt link LNKB configured for IRQ 0
[    0.279299] ACPI: PCI: Interrupt link LNKC configured for IRQ 0
[    0.279333] ACPI: PCI: Interrupt link LNKD configured for IRQ 0
[    0.279363] ACPI: PCI: Interrupt link LNKE configured for IRQ 0
[    0.279387] ACPI: PCI: Interrupt link LNKF configured for IRQ 0
[    0.279412] ACPI: PCI: Interrupt link LNKG configured for IRQ 0
[    0.279436] ACPI: PCI: Interrupt link LNKH configured for IRQ 0
[    0.280016] iommu: Default domain type: Passthrough 
[    0.280058] SCSI subsystem initialized
[    0.280071] ACPI: bus type USB registered
[    0.280079] usbcore: registered new interface driver usbfs
[    0.280084] usbcore: registered new interface driver hub
[    0.280091] usbcore: registered new device driver usb
[    0.280107] pps_core: LinuxPPS API ver. 1 registered
[    0.280108] pps_core: Software ver. 5.3.6 - Copyright 2005-2007 Rodolfo Giometti <<EMAIL>>
[    0.280110] PTP clock support registered
[    0.280121] EDAC MC: Ver: 3.0.0
[    0.280126] Registered efivars operations
[    0.281047] PCI: Using ACPI for IRQ routing
[    0.284801] PCI: pci_cache_line_size set to 64 bytes
[    0.284948] e820: reserve RAM buffer [mem 0x75d00000-0x77ffffff]
[    0.284949] e820: reserve RAM buffer [mem 0x9ffb9018-0x9fffffff]
[    0.284950] e820: reserve RAM buffer [mem 0xa04d9018-0xa3ffffff]
[    0.284951] e820: reserve RAM buffer [mem 0xa04e2018-0xa3ffffff]
[    0.284951] e820: reserve RAM buffer [mem 0xa054e018-0xa3ffffff]
[    0.284952] e820: reserve RAM buffer [mem 0xa165f000-0xa3ffffff]
[    0.284952] e820: reserve RAM buffer [mem 0xa4316000-0xa7ffffff]
[    0.284953] e820: reserve RAM buffer [mem 0xa70c3000-0xa7ffffff]
[    0.284953] e820: reserve RAM buffer [mem 0x204f100000-0x204fffffff]
[    0.285050] NetLabel: Initializing
[    0.285051] NetLabel:  domain hash size = 128
[    0.285051] NetLabel:  protocols = UNLABELED CIPSOv4 CALIPSO
[    0.285061] NetLabel:  unlabeled traffic allowed by default
[    0.285069] pci 0000:43:00.0: vgaarb: setting as boot VGA device
[    0.285069] pci 0000:43:00.0: vgaarb: VGA device added: decodes=io+mem,owns=io+mem,locks=none
[    0.285069] pci 0000:43:00.0: vgaarb: bridge control possible
[    0.285069] vgaarb: loaded
[    0.285069] hpet0: at MMIO 0xfed00000, IRQs 2, 8, 0
[    0.285069] hpet0: 3 comparators, 32-bit 14.318180 MHz counter
[    0.287063] clocksource: Switched to clocksource tsc-early
[    0.295754] VFS: Disk quotas dquot_6.6.0
[    0.295768] VFS: Dquot-cache hash table entries: 512 (order 0, 4096 bytes)
[    0.295818] pnp: PnP ACPI init
[    0.296581] system 00:00: [mem 0xe0000000-0xefffffff] has been reserved
[    0.296586] system 00:00: Plug and Play ACPI device, IDs PNP0c01 (active)
[    0.296746] pnp 00:01: Plug and Play ACPI device, IDs PNP0b00 (active)
[    0.296894] system 00:02: [io  0x0a00-0x0a0f] has been reserved
[    0.296896] system 00:02: [io  0x0a10-0x0a1f] has been reserved
[    0.296897] system 00:02: [io  0x0a20-0x0a2f] has been reserved
[    0.296898] system 00:02: [io  0x0a30-0x0a3f] has been reserved
[    0.296899] system 00:02: [io  0x0a40-0x0a4f] has been reserved
[    0.296901] system 00:02: Plug and Play ACPI device, IDs PNP0c02 (active)
[    0.297042] pnp 00:03: [dma 0 disabled]
[    0.297063] pnp 00:03: Plug and Play ACPI device, IDs PNP0501 (active)
[    0.297192] pnp 00:04: [dma 0 disabled]
[    0.297212] pnp 00:04: Plug and Play ACPI device, IDs PNP0501 (active)
[    0.297350] system 00:05: [io  0x04d0-0x04d1] has been reserved
[    0.297351] system 00:05: [io  0x040b] has been reserved
[    0.297352] system 00:05: [io  0x04d6] has been reserved
[    0.297353] system 00:05: [io  0x0c00-0x0c01] has been reserved
[    0.297354] system 00:05: [io  0x0c14] has been reserved
[    0.297355] system 00:05: [io  0x0c50-0x0c51] has been reserved
[    0.297356] system 00:05: [io  0x0c52] has been reserved
[    0.297356] system 00:05: [io  0x0c6c] has been reserved
[    0.297357] system 00:05: [io  0x0c6f] has been reserved
[    0.297358] system 00:05: [io  0x0cd0-0x0cd1] has been reserved
[    0.297359] system 00:05: [io  0x0cd2-0x0cd3] has been reserved
[    0.297359] system 00:05: [io  0x0cd4-0x0cd5] has been reserved
[    0.297360] system 00:05: [io  0x0cd6-0x0cd7] has been reserved
[    0.297361] system 00:05: [io  0x0cd8-0x0cdf] has been reserved
[    0.297362] system 00:05: [io  0x0800-0x089f] has been reserved
[    0.297362] system 00:05: [io  0x0b00-0x0b0f] has been reserved
[    0.297363] system 00:05: [io  0x0b20-0x0b3f] has been reserved
[    0.297364] system 00:05: [io  0x0900-0x090f] has been reserved
[    0.297365] system 00:05: [io  0x0910-0x091f] has been reserved
[    0.297366] system 00:05: [io  0xfe00-0xfefe] has been reserved
[    0.297368] system 00:05: [mem 0xfedc0000-0xfedc0fff] has been reserved
[    0.297369] system 00:05: [mem 0xfee00000-0xfee00fff] has been reserved
[    0.297369] system 00:05: [mem 0xfed80000-0xfed814ff] has been reserved
[    0.297370] system 00:05: [mem 0xfed81900-0xfed8ffff] has been reserved
[    0.297371] system 00:05: [mem 0xfec10000-0xfec10fff] has been reserved
[    0.297372] system 00:05: [mem 0xff000000-0xffffffff] has been reserved
[    0.297375] system 00:05: Plug and Play ACPI device, IDs PNP0c02 (active)
[    0.297647] pnp: PnP ACPI: found 6 devices
[    0.302909] clocksource: acpi_pm: mask: 0xffffff max_cycles: 0xffffff, max_idle_ns: 2085701024 ns
[    0.302932] pci 0000:c0:03.1: PCI bridge to [bus c1]
[    0.302936] pci 0000:c0:03.1:   bridge window [mem 0xc4000000-0xc57fffff]
[    0.302938] pci 0000:c0:03.1:   bridge window [mem 0x15000000000-0x17041ffffff 64bit pref]
[    0.302941] pci 0000:c0:07.1: PCI bridge to [bus c2]
[    0.302944] pci 0000:c0:07.1:   bridge window [mem 0xc5a00000-0xc5afffff]
[    0.302948] pci 0000:c0:08.1: PCI bridge to [bus c3]
[    0.302950] pci 0000:c0:08.1:   bridge window [mem 0xc5900000-0xc59fffff]
[    0.302955] pci_bus 0000:c0: resource 4 [io  0xc000-0xffff window]
[    0.302956] pci_bus 0000:c0: resource 5 [mem 0xc2000000-0xc5bfffff window]
[    0.302956] pci_bus 0000:c0: resource 6 [mem 0x10021000000-0x18020ffffff window]
[    0.302957] pci_bus 0000:c1: resource 1 [mem 0xc4000000-0xc57fffff]
[    0.302958] pci_bus 0000:c1: resource 2 [mem 0x15000000000-0x17041ffffff 64bit pref]
[    0.302959] pci_bus 0000:c2: resource 1 [mem 0xc5a00000-0xc5afffff]
[    0.302960] pci_bus 0000:c3: resource 1 [mem 0xc5900000-0xc59fffff]
[    0.302975] pci 0000:80:07.1: PCI bridge to [bus 81]
[    0.302978] pci 0000:80:07.1:   bridge window [mem 0xbc300000-0xbc3fffff]
[    0.302982] pci 0000:80:08.1: PCI bridge to [bus 82]
[    0.302984] pci 0000:80:08.1:   bridge window [mem 0xbc200000-0xbc2fffff]
[    0.302989] pci 0000:80:08.2: PCI bridge to [bus 83]
[    0.302991] pci 0000:80:08.2:   bridge window [mem 0xbc100000-0xbc1fffff]
[    0.302995] pci 0000:80:08.3: PCI bridge to [bus 84]
[    0.302998] pci 0000:80:08.3:   bridge window [mem 0xbc000000-0xbc0fffff]
[    0.303002] pci_bus 0000:80: resource 4 [io  0x8000-0xbfff window]
[    0.303003] pci_bus 0000:80: resource 5 [mem 0xba000000-0xbc3fffff window]
[    0.303003] pci_bus 0000:80: resource 6 [mem 0x18021000000-0x20020ffffff window]
[    0.303004] pci_bus 0000:81: resource 1 [mem 0xbc300000-0xbc3fffff]
[    0.303005] pci_bus 0000:82: resource 1 [mem 0xbc200000-0xbc2fffff]
[    0.303006] pci_bus 0000:83: resource 1 [mem 0xbc100000-0xbc1fffff]
[    0.303006] pci_bus 0000:84: resource 1 [mem 0xbc000000-0xbc0fffff]
[    0.303019] pci 0000:40:01.1: bridge window [io  0x1000-0x0fff] to [bus 41] add_size 1000
[    0.303020] pci 0000:40:01.1: bridge window [mem 0x00100000-0x000fffff 64bit pref] to [bus 41] add_size 200000 add_align 100000
[    0.303025] pci 0000:40:01.1: BAR 15: assigned [mem 0x20081000000-0x200811fffff 64bit pref]
[    0.303026] pci 0000:40:01.1: BAR 13: assigned [io  0x4000-0x4fff]
[    0.303028] pci 0000:40:01.1: PCI bridge to [bus 41]
[    0.303029] pci 0000:40:01.1:   bridge window [io  0x4000-0x4fff]
[    0.303031] pci 0000:40:01.1:   bridge window [mem 0xb3800000-0xb38fffff]
[    0.303033] pci 0000:40:01.1:   bridge window [mem 0x20081000000-0x200811fffff 64bit pref]
[    0.303037] pci 0000:42:00.0: PCI bridge to [bus 43]
[    0.303038] pci 0000:42:00.0:   bridge window [io  0x7000-0x7fff]
[    0.303042] pci 0000:42:00.0:   bridge window [mem 0xb2000000-0xb30fffff]
[    0.303048] pci 0000:40:01.5: PCI bridge to [bus 42-43]
[    0.303049] pci 0000:40:01.5:   bridge window [io  0x7000-0x7fff]
[    0.303052] pci 0000:40:01.5:   bridge window [mem 0xb2000000-0xb30fffff]
[    0.303056] pci 0000:40:07.1: PCI bridge to [bus 44]
[    0.303059] pci 0000:40:07.1:   bridge window [mem 0xb3700000-0xb37fffff]
[    0.303063] pci 0000:40:08.1: PCI bridge to [bus 45]
[    0.303065] pci 0000:40:08.1:   bridge window [mem 0xb3200000-0xb34fffff]
[    0.303069] pci 0000:40:08.2: PCI bridge to [bus 46]
[    0.303072] pci 0000:40:08.2:   bridge window [mem 0xb3600000-0xb36fffff]
[    0.303076] pci 0000:40:08.3: PCI bridge to [bus 47]
[    0.303078] pci 0000:40:08.3:   bridge window [mem 0xb3500000-0xb35fffff]
[    0.303082] pci_bus 0000:40: resource 4 [io  0x03b0-0x03df window]
[    0.303083] pci_bus 0000:40: resource 5 [io  0x4000-0x7fff window]
[    0.303084] pci_bus 0000:40: resource 6 [mem 0x000a0000-0x000bffff window]
[    0.303084] pci_bus 0000:40: resource 7 [mem 0xb0000000-0xb3afffff window]
[    0.303085] pci_bus 0000:40: resource 8 [mem 0x20081000000-0x28080ffffff window]
[    0.303086] pci_bus 0000:41: resource 0 [io  0x4000-0x4fff]
[    0.303087] pci_bus 0000:41: resource 1 [mem 0xb3800000-0xb38fffff]
[    0.303087] pci_bus 0000:41: resource 2 [mem 0x20081000000-0x200811fffff 64bit pref]
[    0.303088] pci_bus 0000:42: resource 0 [io  0x7000-0x7fff]
[    0.303089] pci_bus 0000:42: resource 1 [mem 0xb2000000-0xb30fffff]
[    0.303090] pci_bus 0000:43: resource 0 [io  0x7000-0x7fff]
[    0.303090] pci_bus 0000:43: resource 1 [mem 0xb2000000-0xb30fffff]
[    0.303091] pci_bus 0000:44: resource 1 [mem 0xb3700000-0xb37fffff]
[    0.303092] pci_bus 0000:45: resource 1 [mem 0xb3200000-0xb34fffff]
[    0.303092] pci_bus 0000:46: resource 1 [mem 0xb3600000-0xb36fffff]
[    0.303093] pci_bus 0000:47: resource 1 [mem 0xb3500000-0xb35fffff]
[    0.303107] pci 0000:00:03.5: bridge window [io  0x1000-0x0fff] to [bus 02] add_size 1000
[    0.303108] pci 0000:00:03.5: bridge window [mem 0x00100000-0x000fffff 64bit pref] to [bus 02] add_size 200000 add_align 100000
[    0.303109] pci 0000:00:03.5: bridge window [mem 0x00100000-0x000fffff] to [bus 02] add_size 200000 add_align 100000
[    0.303113] pci 0000:00:03.5: BAR 14: assigned [mem 0xf0000000-0xf01fffff]
[    0.303114] pci 0000:00:03.5: BAR 15: assigned [mem 0x28081000000-0x280811fffff 64bit pref]
[    0.303117] pci 0000:00:03.5: BAR 13: assigned [io  0x1000-0x1fff]
[    0.303118] pci 0000:00:03.3: PCI bridge to [bus 01]
[    0.303121] pci 0000:00:03.3:   bridge window [mem 0xf2200000-0xf23fffff]
[    0.303123] pci 0000:00:03.3:   bridge window [mem 0x3807c000000-0x38080ffffff 64bit pref]
[    0.303126] pci 0000:00:03.5: PCI bridge to [bus 02]
[    0.303127] pci 0000:00:03.5:   bridge window [io  0x1000-0x1fff]
[    0.303130] pci 0000:00:03.5:   bridge window [mem 0xf0000000-0xf01fffff]
[    0.303132] pci 0000:00:03.5:   bridge window [mem 0x28081000000-0x280811fffff 64bit pref]
[    0.303135] pci 0000:00:07.1: PCI bridge to [bus 03]
[    0.303137] pci 0000:00:07.1:   bridge window [mem 0xf2400000-0xf24fffff]
[    0.303142] pci 0000:00:08.1: PCI bridge to [bus 04]
[    0.303144] pci 0000:00:08.1:   bridge window [mem 0xf2000000-0xf21fffff]
[    0.303148] pci_bus 0000:00: resource 4 [io  0x0000-0x02ff window]
[    0.303149] pci_bus 0000:00: resource 5 [io  0x0300-0x03af window]
[    0.303150] pci_bus 0000:00: resource 6 [io  0x03e0-0x0cf7 window]
[    0.303151] pci_bus 0000:00: resource 7 [io  0x1000-0x3fff window]
[    0.303152] pci_bus 0000:00: resource 8 [mem 0x000c0000-0x000dffff window]
[    0.303152] pci_bus 0000:00: resource 9 [mem 0xf0000000-0xf24fffff window]
[    0.303153] pci_bus 0000:00: resource 10 [mem 0x28081000000-0x38080ffffff window]
[    0.303154] pci_bus 0000:01: resource 1 [mem 0xf2200000-0xf23fffff]
[    0.303155] pci_bus 0000:01: resource 2 [mem 0x3807c000000-0x38080ffffff 64bit pref]
[    0.303156] pci_bus 0000:02: resource 0 [io  0x1000-0x1fff]
[    0.303156] pci_bus 0000:02: resource 1 [mem 0xf0000000-0xf01fffff]
[    0.303157] pci_bus 0000:02: resource 2 [mem 0x28081000000-0x280811fffff 64bit pref]
[    0.303158] pci_bus 0000:03: resource 1 [mem 0xf2400000-0xf24fffff]
[    0.303159] pci_bus 0000:04: resource 1 [mem 0xf2000000-0xf21fffff]
[    0.303195] NET: Registered protocol family 2
[    0.303346] IP idents hash table entries: 262144 (order: 9, 2097152 bytes, vmalloc)
[    0.304218] tcp_listen_portaddr_hash hash table entries: 65536 (order: 8, 1048576 bytes, vmalloc)
[    0.304380] TCP established hash table entries: 524288 (order: 10, 4194304 bytes, vmalloc)
[    0.304702] TCP bind hash table entries: 65536 (order: 8, 1048576 bytes, vmalloc)
[    0.304761] TCP: Hash tables configured (established 524288 bind 65536)
[    0.304921] MPTCP token hash table entries: 65536 (order: 8, 1572864 bytes, vmalloc)
[    0.305006] UDP hash table entries: 65536 (order: 9, 2097152 bytes, vmalloc)
[    0.305199] UDP-Lite hash table entries: 65536 (order: 9, 2097152 bytes, vmalloc)
[    0.305401] NET: Registered protocol family 1
[    0.305404] NET: Registered protocol family 44
[    0.305469] pci 0000:43:00.0: Video device with shadowed ROM at [mem 0x000c0000-0x000dffff]
[    0.305883] PCI: CLS 64 bytes, default 64
[    0.305907] Unpacking initramfs...
[    0.906077] Freeing initrd memory: 104180K
[    0.906101] pci 0000:c0:00.2: AMD-Vi: IOMMU performance counters supported
[    0.906139] pci 0000:80:00.2: AMD-Vi: IOMMU performance counters supported
[    0.906158] pci 0000:40:00.2: AMD-Vi: IOMMU performance counters supported
[    0.906178] pci 0000:00:00.2: AMD-Vi: IOMMU performance counters supported
[    0.906215] pci 0000:c0:01.0: Adding to iommu group 0
[    0.906231] pci 0000:c0:02.0: Adding to iommu group 1
[    0.906249] pci 0000:c0:03.0: Adding to iommu group 2
[    0.906260] pci 0000:c0:03.1: Adding to iommu group 3
[    0.906274] pci 0000:c0:04.0: Adding to iommu group 4
[    0.906289] pci 0000:c0:05.0: Adding to iommu group 5
[    0.906306] pci 0000:c0:07.0: Adding to iommu group 6
[    0.906316] pci 0000:c0:07.1: Adding to iommu group 7
[    0.906331] pci 0000:c0:08.0: Adding to iommu group 8
[    0.906341] pci 0000:c0:08.1: Adding to iommu group 9
[    0.906355] pci 0000:c1:00.0: Adding to iommu group 10
[    0.906367] pci 0000:c2:00.0: Adding to iommu group 11
[    0.906379] pci 0000:c2:00.2: Adding to iommu group 12
[    0.906391] pci 0000:c3:00.0: Adding to iommu group 13
[    0.906403] pci 0000:c3:00.2: Adding to iommu group 14
[    0.906418] pci 0000:80:01.0: Adding to iommu group 15
[    0.906433] pci 0000:80:02.0: Adding to iommu group 16
[    0.906447] pci 0000:80:03.0: Adding to iommu group 17
[    0.906463] pci 0000:80:04.0: Adding to iommu group 18
[    0.906477] pci 0000:80:05.0: Adding to iommu group 19
[    0.906494] pci 0000:80:07.0: Adding to iommu group 20
[    0.906504] pci 0000:80:07.1: Adding to iommu group 21
[    0.906522] pci 0000:80:08.0: Adding to iommu group 22
[    0.906533] pci 0000:80:08.1: Adding to iommu group 23
[    0.906543] pci 0000:80:08.2: Adding to iommu group 24
[    0.906554] pci 0000:80:08.3: Adding to iommu group 25
[    0.906565] pci 0000:81:00.0: Adding to iommu group 26
[    0.906577] pci 0000:81:00.2: Adding to iommu group 27
[    0.906588] pci 0000:82:00.0: Adding to iommu group 28
[    0.906600] pci 0000:82:00.2: Adding to iommu group 29
[    0.906610] pci 0000:83:00.0: Adding to iommu group 30
[    0.906621] pci 0000:84:00.0: Adding to iommu group 31
[    0.906638] pci 0000:40:01.0: Adding to iommu group 32
[    0.906649] pci 0000:40:01.1: Adding to iommu group 33
[    0.906660] pci 0000:40:01.5: Adding to iommu group 34
[    0.906675] pci 0000:40:02.0: Adding to iommu group 35
[    0.906689] pci 0000:40:03.0: Adding to iommu group 36
[    0.906703] pci 0000:40:04.0: Adding to iommu group 37
[    0.906718] pci 0000:40:05.0: Adding to iommu group 38
[    0.906734] pci 0000:40:07.0: Adding to iommu group 39
[    0.906745] pci 0000:40:07.1: Adding to iommu group 40
[    0.906763] pci 0000:40:08.0: Adding to iommu group 41
[    0.906774] pci 0000:40:08.1: Adding to iommu group 42
[    0.906785] pci 0000:40:08.2: Adding to iommu group 43
[    0.906796] pci 0000:40:08.3: Adding to iommu group 44
[    0.906807] pci 0000:41:00.0: Adding to iommu group 45
[    0.906823] pci 0000:42:00.0: Adding to iommu group 46
[    0.906827] pci 0000:43:00.0: Adding to iommu group 46
[    0.906838] pci 0000:44:00.0: Adding to iommu group 47
[    0.906850] pci 0000:44:00.2: Adding to iommu group 48
[    0.906861] pci 0000:45:00.0: Adding to iommu group 49
[    0.906874] pci 0000:45:00.1: Adding to iommu group 50
[    0.906885] pci 0000:45:00.2: Adding to iommu group 51
[    0.906896] pci 0000:45:00.3: Adding to iommu group 52
[    0.906906] pci 0000:46:00.0: Adding to iommu group 53
[    0.906918] pci 0000:47:00.0: Adding to iommu group 54
[    0.906932] pci 0000:00:01.0: Adding to iommu group 55
[    0.906947] pci 0000:00:02.0: Adding to iommu group 56
[    0.906963] pci 0000:00:03.0: Adding to iommu group 57
[    0.906975] pci 0000:00:03.3: Adding to iommu group 58
[    0.906985] pci 0000:00:03.5: Adding to iommu group 59
[    0.907000] pci 0000:00:04.0: Adding to iommu group 60
[    0.907015] pci 0000:00:05.0: Adding to iommu group 61
[    0.907032] pci 0000:00:07.0: Adding to iommu group 62
[    0.907043] pci 0000:00:07.1: Adding to iommu group 63
[    0.907059] pci 0000:00:08.0: Adding to iommu group 64
[    0.907069] pci 0000:00:08.1: Adding to iommu group 65
[    0.907090] pci 0000:00:14.0: Adding to iommu group 66
[    0.907103] pci 0000:00:14.3: Adding to iommu group 66
[    0.907154] pci 0000:00:18.0: Adding to iommu group 67
[    0.907166] pci 0000:00:18.1: Adding to iommu group 67
[    0.907179] pci 0000:00:18.2: Adding to iommu group 67
[    0.907192] pci 0000:00:18.3: Adding to iommu group 67
[    0.907205] pci 0000:00:18.4: Adding to iommu group 67
[    0.907218] pci 0000:00:18.5: Adding to iommu group 67
[    0.907230] pci 0000:00:18.6: Adding to iommu group 67
[    0.907243] pci 0000:00:18.7: Adding to iommu group 67
[    0.907275] pci 0000:01:00.0: Adding to iommu group 68
[    0.907305] pci 0000:01:00.1: Adding to iommu group 69
[    0.907317] pci 0000:03:00.0: Adding to iommu group 70
[    0.907329] pci 0000:03:00.2: Adding to iommu group 71
[    0.907340] pci 0000:04:00.0: Adding to iommu group 72
[    0.907352] pci 0000:04:00.2: Adding to iommu group 73
[    0.907365] pci 0000:04:00.3: Adding to iommu group 74
[    0.908457] pci 0000:c0:00.2: AMD-Vi: Found IOMMU cap 0x40
[    0.908458] AMD-Vi: Extended features (0x59f77efa2094ade): PPR X2APIC NX GT IA GA PC
[    0.908462] pci 0000:80:00.2: AMD-Vi: Found IOMMU cap 0x40
[    0.908462] AMD-Vi: Extended features (0x59f77efa2094ade): PPR X2APIC NX GT IA GA PC
[    0.908465] pci 0000:40:00.2: AMD-Vi: Found IOMMU cap 0x40
[    0.908465] AMD-Vi: Extended features (0x59f77efa2094ade): PPR X2APIC NX GT IA GA PC
[    0.908468] pci 0000:00:00.2: AMD-Vi: Found IOMMU cap 0x40
[    0.908468] AMD-Vi: Extended features (0x59f77efa2094ade): PPR X2APIC NX GT IA GA PC
[    0.908470] AMD-Vi: Interrupt remapping enabled
[    0.908471] AMD-Vi: X2APIC enabled
[    0.908671] PCI-DMA: Using software bounce buffering for IO (SWIOTLB)
[    0.908673] software IO TLB: mapped [mem 0x000000007b000000-0x000000007f000000] (64MB)
[    0.908679] ACPI: bus type thunderbolt registered
[    0.908737] LVT offset 0 assigned for vector 0x400
[    0.908928] perf: AMD IBS detected (0x000003ff)
[    0.908932] amd_uncore: 4 amd_df counters detected
[    0.908936] amd_uncore: 6 amd_l3 counters detected
[    0.909273] perf/amd_iommu: Detected AMD IOMMU #0 (2 banks, 4 counters/bank).
[    0.909277] perf/amd_iommu: Detected AMD IOMMU #1 (2 banks, 4 counters/bank).
[    0.909285] perf/amd_iommu: Detected AMD IOMMU #2 (2 banks, 4 counters/bank).
[    0.909288] perf/amd_iommu: Detected AMD IOMMU #3 (2 banks, 4 counters/bank).
[    0.912449] Initialise system trusted keyrings
[    0.912455] Key type blacklist registered
[    0.912478] workingset: timestamp_bits=36 max_order=25 bucket_order=0
[    0.913506] zbud: loaded
[    0.913722] pstore: using deflate compression
[    0.913985] Platform Keyring initialized
[    0.939440] NET: Registered protocol family 38
[    0.939443] Key type asymmetric registered
[    0.939444] Asymmetric key parser 'x509' registered
[    0.939444] Running certificate verification selftests
[    0.957175] Loaded X.509 cert 'Certificate verification self-testing key: f58703bb33ce1b73ee02eccdee5b8817518fe3db'
[    0.957602] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 247)
[    0.957637] io scheduler mq-deadline registered
[    0.957638] io scheduler kyber registered
[    0.957664] io scheduler bfq registered
[    0.957854] atomic64_test: passed for x86-64 platform with CX8 and with SSE
[    0.958574] pcieport 0000:c0:03.1: PME: Signaling with IRQ 30
[    0.958740] pcieport 0000:c0:07.1: PME: Signaling with IRQ 32
[    0.958861] pcieport 0000:c0:08.1: PME: Signaling with IRQ 34
[    0.958992] pcieport 0000:80:07.1: PME: Signaling with IRQ 36
[    0.959109] pcieport 0000:80:08.1: PME: Signaling with IRQ 38
[    0.959225] pcieport 0000:80:08.2: PME: Signaling with IRQ 39
[    0.959330] pcieport 0000:80:08.3: PME: Signaling with IRQ 40
[    0.959423] pcieport 0000:40:01.1: PME: Signaling with IRQ 41
[    0.959436] pcieport 0000:40:01.1: pciehp: Slot #34 AttnBtn- PwrCtrl- MRL- AttnInd- PwrInd- HotPlug+ Surprise- Interlock- NoCompl+ IbPresDis- LLActRep+
[    0.959571] pcieport 0000:40:01.5: PME: Signaling with IRQ 42
[    0.959698] pcieport 0000:40:07.1: PME: Signaling with IRQ 44
[    0.959795] pcieport 0000:40:08.1: PME: Signaling with IRQ 45
[    0.959915] pcieport 0000:40:08.2: PME: Signaling with IRQ 46
[    0.960022] pcieport 0000:40:08.3: PME: Signaling with IRQ 47
[    0.960112] pcieport 0000:00:03.3: PME: Signaling with IRQ 48
[    0.960209] pcieport 0000:00:03.5: PME: Signaling with IRQ 49
[    0.960223] pcieport 0000:00:03.5: pciehp: Slot #0 AttnBtn- PwrCtrl- MRL- AttnInd- PwrInd- HotPlug+ Surprise- Interlock- NoCompl+ IbPresDis- LLActRep+
[    0.960400] pcieport 0000:00:07.1: PME: Signaling with IRQ 51
[    0.960498] pcieport 0000:00:08.1: PME: Signaling with IRQ 52
[    0.960607] shpchp: Standard Hot Plug PCI Controller Driver version: 0.4
[    0.960764] efifb: probing for efifb
[    0.960775] efifb: framebuffer at 0xb2000000, using 1876k, total 1875k
[    0.960776] efifb: mode is 800x600x32, linelength=3200, pages=1
[    0.960777] efifb: scrolling: redraw
[    0.960778] efifb: Truecolor: size=8:8:8:8, shift=24:16:8:0
[    0.960802] fbcon: Deferring console take-over
[    0.960803] fb0: EFI VGA frame buffer device
[    0.961015] input: Power Button as /devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0C:00/input/input0
[    0.961027] ACPI: Power Button [PWRB]
[    0.961048] input: Power Button as /devices/LNXSYSTM:00/LNXPWRBN:00/input/input1
[    0.961070] ACPI: Power Button [PWRF]
[    0.962860] smpboot: Estimated ratio of average max frequency by base frequency (times 1024): 1148
[    0.962870] Monitor-Mwait will be used to enter C-1 state
[    0.962874] ACPI: \_SB_.C000: Found 2 idle states
[    0.962962] ACPI: \_SB_.C002: Found 2 idle states
[    0.963014] ACPI: \_SB_.C004: Found 2 idle states
[    0.963064] ACPI: \_SB_.C006: Found 2 idle states
[    0.963118] ACPI: \_SB_.C008: Found 2 idle states
[    0.963189] ACPI: \_SB_.C00A: Found 2 idle states
[    0.963263] ACPI: \_SB_.C00C: Found 2 idle states
[    0.963336] ACPI: \_SB_.C00E: Found 2 idle states
[    0.963410] ACPI: \_SB_.C010: Found 2 idle states
[    0.963482] ACPI: \_SB_.C012: Found 2 idle states
[    0.963551] ACPI: \_SB_.C014: Found 2 idle states
[    0.963622] ACPI: \_SB_.C016: Found 2 idle states
[    0.963696] ACPI: \_SB_.C018: Found 2 idle states
[    0.963769] ACPI: \_SB_.C01A: Found 2 idle states
[    0.963842] ACPI: \_SB_.C01C: Found 2 idle states
[    0.963921] ACPI: \_SB_.C01E: Found 2 idle states
[    0.963994] ACPI: \_SB_.C001: Found 2 idle states
[    0.964064] ACPI: \_SB_.C003: Found 2 idle states
[    0.964115] ACPI: \_SB_.C005: Found 2 idle states
[    0.964199] ACPI: \_SB_.C007: Found 2 idle states
[    0.964284] ACPI: \_SB_.C009: Found 2 idle states
[    0.964372] ACPI: \_SB_.C00B: Found 2 idle states
[    0.964463] ACPI: \_SB_.C00D: Found 2 idle states
[    0.964531] ACPI: \_SB_.C00F: Found 2 idle states
[    0.964624] ACPI: \_SB_.C011: Found 2 idle states
[    0.964696] ACPI: \_SB_.C013: Found 2 idle states
[    0.964764] ACPI: \_SB_.C015: Found 2 idle states
[    0.964836] ACPI: \_SB_.C017: Found 2 idle states
[    0.964907] ACPI: \_SB_.C019: Found 2 idle states
[    0.964979] ACPI: \_SB_.C01B: Found 2 idle states
[    0.965051] ACPI: \_SB_.C01D: Found 2 idle states
[    0.965104] ACPI: \_SB_.C01F: Found 2 idle states
[    0.965274] ERST: Error Record Serialization Table (ERST) support is initialized.
[    0.965305] pstore: Registered erst as persistent store backend
[    0.971327] GHES: APEI firmware first mode is enabled by APEI bit.
[    0.971589] Serial: 8250/16550 driver, 4 ports, IRQ sharing enabled
[    0.992318] 00:03: ttyS0 at I/O 0x3f8 (irq = 4, base_baud = 115200) is a 16550A
[    1.013066] 00:04: ttyS1 at I/O 0x2f8 (irq = 3, base_baud = 115200) is a 16550A
[    1.013512] Non-volatile memory driver v1.3
[    1.017645] tpm_tis MSFT0101:00: 2.0 TPM (device-id 0x1A, rev-id 16)
[    1.043702] rdac: device handler registered
[    1.043757] hp_sw: device handler registered
[    1.043757] emc: device handler registered
[    1.043787] alua: device handler registered
[    1.043838] libphy: Fixed MDIO Bus: probed
[    1.044035] xhci_hcd 0000:45:00.3: xHCI Host Controller
[    1.044056] xhci_hcd 0000:45:00.3: new USB bus registered, assigned bus number 1
[    1.044136] xhci_hcd 0000:45:00.3: hcc params 0x0260ffe5 hci version 0x110 quirks 0x0000000000000410
[    1.044410] xhci_hcd 0000:45:00.3: xHCI Host Controller
[    1.044427] xhci_hcd 0000:45:00.3: new USB bus registered, assigned bus number 2
[    1.044429] xhci_hcd 0000:45:00.3: Host supports USB 3.1 Enhanced SuperSpeed
[    1.044451] usb usb1: New USB device found, idVendor=1d6b, idProduct=0002, bcdDevice= 4.18
[    1.044453] usb usb1: New USB device strings: Mfr=3, Product=2, SerialNumber=1
[    1.044454] usb usb1: Product: xHCI Host Controller
[    1.044455] usb usb1: Manufacturer: Linux 4.18.0-513.9.1.el8_9.x86_64 xhci-hcd
[    1.044456] usb usb1: SerialNumber: 0000:45:00.3
[    1.044507] hub 1-0:1.0: USB hub found
[    1.044511] hub 1-0:1.0: 2 ports detected
[    1.044559] usb usb2: We don't know the algorithms for LPM for this host, disabling LPM.
[    1.044568] usb usb2: New USB device found, idVendor=1d6b, idProduct=0003, bcdDevice= 4.18
[    1.044570] usb usb2: New USB device strings: Mfr=3, Product=2, SerialNumber=1
[    1.044571] usb usb2: Product: xHCI Host Controller
[    1.044571] usb usb2: Manufacturer: Linux 4.18.0-513.9.1.el8_9.x86_64 xhci-hcd
[    1.044572] usb usb2: SerialNumber: 0000:45:00.3
[    1.044612] hub 2-0:1.0: USB hub found
[    1.044615] hub 2-0:1.0: 2 ports detected
[    1.044697] xhci_hcd 0000:04:00.3: xHCI Host Controller
[    1.044716] xhci_hcd 0000:04:00.3: new USB bus registered, assigned bus number 3
[    1.044796] xhci_hcd 0000:04:00.3: hcc params 0x0260ffe5 hci version 0x110 quirks 0x0000000000000410
[    1.045070] xhci_hcd 0000:04:00.3: xHCI Host Controller
[    1.045085] xhci_hcd 0000:04:00.3: new USB bus registered, assigned bus number 4
[    1.045086] xhci_hcd 0000:04:00.3: Host supports USB 3.1 Enhanced SuperSpeed
[    1.045103] usb usb3: New USB device found, idVendor=1d6b, idProduct=0002, bcdDevice= 4.18
[    1.045104] usb usb3: New USB device strings: Mfr=3, Product=2, SerialNumber=1
[    1.045105] usb usb3: Product: xHCI Host Controller
[    1.045106] usb usb3: Manufacturer: Linux 4.18.0-513.9.1.el8_9.x86_64 xhci-hcd
[    1.045106] usb usb3: SerialNumber: 0000:04:00.3
[    1.045144] hub 3-0:1.0: USB hub found
[    1.045148] hub 3-0:1.0: 2 ports detected
[    1.045255] usb usb4: We don't know the algorithms for LPM for this host, disabling LPM.
[    1.045264] usb usb4: New USB device found, idVendor=1d6b, idProduct=0003, bcdDevice= 4.18
[    1.045265] usb usb4: New USB device strings: Mfr=3, Product=2, SerialNumber=1
[    1.045266] usb usb4: Product: xHCI Host Controller
[    1.045266] usb usb4: Manufacturer: Linux 4.18.0-513.9.1.el8_9.x86_64 xhci-hcd
[    1.045267] usb usb4: SerialNumber: 0000:04:00.3
[    1.045305] hub 4-0:1.0: USB hub found
[    1.045309] hub 4-0:1.0: 2 ports detected
[    1.045434] usbcore: registered new interface driver usbserial_generic
[    1.045437] usbserial: USB Serial support registered for generic
[    1.045593] i8042: PNP: No PS/2 controller found.
[    1.045609] mousedev: PS/2 mouse device common for all mice
[    1.045790] rtc_cmos 00:01: registered as rtc0
[    1.045798] rtc_cmos 00:01: alarms up to one month, y3k, 114 bytes nvram, hpet irqs
[    1.045979] EFI Variables Facility v0.08 2004-May-17
[    1.047293] hid: raw HID events driver (C) Jiri Kosina
[    1.047316] usbcore: registered new interface driver usbhid
[    1.047316] usbhid: USB HID core driver
[    1.047357] drop_monitor: Initializing network drop monitor service
[    1.047398] Initializing XFRM netlink socket
[    1.047460] NET: Registered protocol family 10
[    1.047712] Segment Routing with IPv6
[    1.047724] NET: Registered protocol family 17
[    1.047795] mpls_gso: MPLS GSO support
[    1.049365] microcode: CPU1: patch_level=0x0a001173
[    1.049365] microcode: CPU0: patch_level=0x0a001173
[    1.049365] microcode: CPU3: patch_level=0x0a001173
[    1.049370] microcode: CPU9: patch_level=0x0a001173
[    1.049374] microcode: CPU17: patch_level=0x0a001173
[    1.049374] microcode: CPU16: patch_level=0x0a001173
[    1.049375] microcode: CPU18: patch_level=0x0a001173
[    1.049376] microcode: CPU19: patch_level=0x0a001173
[    1.049379] microcode: CPU20: patch_level=0x0a001173
[    1.049379] microcode: CPU4: patch_level=0x0a001173
[    1.049380] microcode: CPU25: patch_level=0x0a001173
[    1.049382] microcode: CPU5: patch_level=0x0a001173
[    1.049382] microcode: CPU21: patch_level=0x0a001173
[    1.049383] microcode: CPU7: patch_level=0x0a001173
[    1.049383] microcode: CPU23: patch_level=0x0a001173
[    1.049383] microcode: CPU24: patch_level=0x0a001173
[    1.049383] microcode: CPU2: patch_level=0x0a001173
[    1.049384] microcode: CPU6: patch_level=0x0a001173
[    1.049384] microcode: CPU27: patch_level=0x0a001173
[    1.049384] microcode: CPU11: patch_level=0x0a001173
[    1.049385] microcode: CPU26: patch_level=0x0a001173
[    1.049384] microcode: CPU22: patch_level=0x0a001173
[    1.049385] microcode: CPU10: patch_level=0x0a001173
[    1.049387] microcode: CPU12: patch_level=0x0a001173
[    1.049387] microcode: CPU28: patch_level=0x0a001173
[    1.049383] microcode: CPU8: patch_level=0x0a001173
[    1.049387] microcode: CPU13: patch_level=0x0a001173
[    1.049389] microcode: CPU30: patch_level=0x0a001173
[    1.049389] microcode: CPU14: patch_level=0x0a001173
[    1.049388] microcode: CPU31: patch_level=0x0a001173
[    1.049387] microcode: CPU29: patch_level=0x0a001173
[    1.049388] microcode: CPU15: patch_level=0x0a001173
[    1.049441] microcode: Microcode Update Driver: v2.2.
[    1.049816] resctrl: L3 allocation detected
[    1.049817] resctrl: MB allocation detected
[    1.049817] resctrl: L3 monitoring detected
[    1.049823] AVX2 version of gcm_enc/dec engaged.
[    1.049824] AES CTR mode by8 optimization enabled
[    1.057604] sched_clock: Marking stable (1057601286, 0)->(1347581596, -289980310)
[    1.057694] registered taskstats version 1
[    1.058185] Loading compiled-in X.509 certificates
[    1.058388] Loaded X.509 cert 'Red Hat Enterprise Linux kernel signing key: 3b1bc9a9bf3bdfe93ac380c73845d894639c5bcf'
[    1.058583] Loaded X.509 cert 'Red Hat Enterprise Linux Driver Update Program (key 3): bf57f3e87362bc7229d9f465321773dfd1f77a80'
[    1.058774] Loaded X.509 cert 'Red Hat Enterprise Linux kpatch signing key: 4d38fd864ebe18c5f0b72e3852e2014c3a676fc8'
[    1.058809] zswap: loaded using pool lzo/zbud
[    1.058894] page_owner is disabled
[    1.061513] Key type big_key registered
[    1.062879] Key type trusted registered
[    1.064213] Key type encrypted registered
[    1.064327] integrity: Loading X.509 certificate: UEFI:MokListRT (MOKvar table)
[    1.064447] integrity: Loaded X.509 cert 'Red Hat Secure Boot CA 5: cc6fa5e72868ba494e939bbd680b9144769a9f8f'
[    1.064454] ima: Allocated hash algorithm: sha256
[    1.105061] ima: No architecture policies found
[    1.105069] evm: Initialising EVM extended attributes:
[    1.105070] evm: security.selinux
[    1.105071] evm: security.ima
[    1.105072] evm: security.capability
[    1.105072] evm: HMAC attrs: 0x1
[    1.105357] rtc_cmos 00:01: setting system clock to 2024-02-19 01:31:04 UTC (1708306264)
[    1.106968] Freeing unused decrypted memory: 2036K
[    1.107249] Freeing unused kernel image (initmem) memory: 2608K
[    1.115006] Write protecting the kernel read-only data: 26624k
[    1.115668] Freeing unused kernel image (text/rodata gap) memory: 2012K
[    1.115861] Freeing unused kernel image (rodata/data gap) memory: 1720K
[    1.125594] systemd[1]: systemd 239 (239-78.el8) running in system mode. (+PAM +AUDIT +SELINUX +IMA -APPARMOR +SMACK +SYSVINIT +UTMP +LIBCRYPTSETUP +GCRYPT +GNUTLS +ACL +XZ +LZ4 +SECCOMP +BLKID +ELFUTILS +KMOD +IDN2 -IDN +PCRE2 default-hierarchy=legacy)
[    1.137046] systemd[1]: Detected architecture x86-64.
[    1.137048] systemd[1]: Running in initial RAM disk.
[    1.158028] systemd[1]: No hostname configured.
[    1.158031] systemd[1]: Set hostname to <localhost>.
[    1.158046] systemd[1]: Initializing machine ID from random generator.
[    1.193415] systemd[1]: Reached target Timers.
[    1.193440] systemd[1]: Reached target Slices.
[    1.193470] systemd[1]: Listening on Open-iSCSI iscsiuio Socket.
[    1.193491] systemd[1]: Listening on Open-iSCSI iscsid Socket.
[    1.203238] fuse: init (API version 7.34)
[    1.203681] Loading iSCSI transport class v2.0-870.
[    1.283007] usb 1-1: new high-speed USB device number 2 using xhci_hcd
[    1.293374] iscsi: registered transport (tcp)
[    1.303974] iscsi: registered transport (qla4xxx)
[    1.303999] QLogic iSCSI HBA Driver
[    1.307312] libcxgbi:libcxgbi_init_module: Chelsio iSCSI driver library libcxgbi v0.9.1-ko (Apr. 2015)
[    1.327537] Chelsio T4-T6 iSCSI Driver cxgb4i v0.9.5-ko (Apr. 2015)
[    1.327545] iscsi: registered transport (cxgb4i)
[    1.330864] cnic: QLogic cnicDriver v2.5.22 (July 20, 2015)
[    1.333087] QLogic NetXtreme II iSCSI Driver bnx2i v2.7.10.1 (Jul 16, 2014)
[    1.333096] iscsi: registered transport (bnx2i)
[    1.338328] iscsi: registered transport (be2iscsi)
[    1.338329] In beiscsi_module_init, tt=000000008a9af99f
[    1.383649] device-mapper: uevent: version 1.0.3
[    1.383702] device-mapper: ioctl: 4.46.0-ioctl (2022-02-22) initialised: <EMAIL>
[    1.412160] RPC: Registered named UNIX socket transport module.
[    1.412162] RPC: Registered udp transport module.
[    1.412163] RPC: Registered tcp transport module.
[    1.412163] RPC: Registered tcp NFSv4.1 backchannel transport module.
[    1.413097] usb 1-1: New USB device found, idVendor=05e3, idProduct=0608, bcdDevice=88.32
[    1.413100] usb 1-1: New USB device strings: Mfr=0, Product=1, SerialNumber=0
[    1.413101] usb 1-1: Product: USB2.0 Hub
[    1.460832] hub 1-1:1.0: USB hub found
[    1.461435] hub 1-1:1.0: 4 ports detected
[    1.579006] usb 1-2: new high-speed USB device number 3 using xhci_hcd
[    1.659988] ccp 0000:45:00.1: enabling device (0000 -> 0002)
[    1.660189] ccp 0000:45:00.1: no command queues available
[    1.660211] ccp 0000:45:00.1: SEV: memory encryption not enabled by BIOS
[    1.660213] ccp 0000:45:00.1: psp enabled
[    1.661442] nvme nvme0: pci function 0000:41:00.0
[    1.669534] nvme nvme0: 32/0/0 default/read/poll queues
[    1.671303] nvme0n1: detected capacity change from 0 to 1920383410176
[    1.672054]  nvme0n1: p1 p2 p3
[    1.680477] libata version 3.00 loaded.
[    1.684050] ACPI: bus type drm_connector registered
[    1.685268] ahci 0000:83:00.0: version 3.0
[    1.695707] ahci 0000:83:00.0: AHCI 0001.0301 32 slots 8 ports 6 Gbps 0xff impl SATA mode
[    1.695711] ahci 0000:83:00.0: flags: 64bit ncq sntf ilck pm led clo only pmp fbs pio slum part ems sxs 
[    1.696417] scsi host0: ahci
[    1.696602] scsi host1: ahci
[    1.696645] checking generic (b2000000 1d5000) vs hw (b2000000 1000000)
[    1.696648] fb0: switching to ast from EFI VGA
[    1.696832] scsi host2: ahci
[    1.696959] scsi host3: ahci
[    1.696960] ast 0000:43:00.0: [drm] Using P2A bridge for configuration
[    1.696964] ast 0000:43:00.0: [drm] AST 2500 detected
[    1.696970] ast 0000:43:00.0: [drm] Using analog VGA
[    1.696975] ast 0000:43:00.0: [drm] dram MCLK=800 Mhz type=7 bus_width=16
[    1.697076] scsi host4: ahci
[    1.697250] scsi host5: ahci
[    1.697351] [drm] Initialized ast 0.1.0 20120228 for 0000:43:00.0 on minor 0
[    1.697413] scsi host6: ahci
[    1.697529] scsi host7: ahci
[    1.697580] ata1: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100100 irq 109
[    1.697583] ata2: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100180 irq 110
[    1.697585] ata3: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100200 irq 111
[    1.697586] ata4: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100280 irq 112
[    1.697588] ata5: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100300 irq 113
[    1.697590] ata6: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100380 irq 114
[    1.697591] ata7: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100400 irq 115
[    1.697593] ata8: SATA max UDMA/133 abar m2048@0xbc100000 port 0xbc100480 irq 116
[    1.700513] fbcon: astdrmfb (fb0) is primary device
[    1.700515] fbcon: Deferring console take-over
[    1.700517] ast 0000:43:00.0: [drm] fb0: astdrmfb frame buffer device
[    1.708019] ahci 0000:84:00.0: AHCI 0001.0301 32 slots 8 ports 6 Gbps 0xff impl SATA mode
[    1.708024] ahci 0000:84:00.0: flags: 64bit ncq sntf ilck pm led clo only pmp fbs pio slum part ems 
[    1.708726] scsi host8: ahci
[    1.708825] scsi host9: ahci
[    1.708925] scsi host10: ahci
[    1.709019] scsi host11: ahci
[    1.709125] scsi host12: ahci
[    1.709221] scsi host13: ahci
[    1.709307] scsi host14: ahci
[    1.709410] scsi host15: ahci
[    1.709459] ata9: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000100 irq 127
[    1.709461] ata10: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000180 irq 128
[    1.709462] ata11: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000200 irq 129
[    1.709464] ata12: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000280 irq 130
[    1.709465] ata13: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000300 irq 131
[    1.709467] ata14: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000380 irq 132
[    1.709469] ata15: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000400 irq 133
[    1.709470] ata16: SATA max UDMA/133 abar m2048@0xbc000000 port 0xbc000480 irq 134
[    1.709554] usb 1-2: New USB device found, idVendor=046b, idProduct=ff01, bcdDevice= 1.00
[    1.709557] usb 1-2: New USB device strings: Mfr=1, Product=2, SerialNumber=3
[    1.709558] usb 1-2: Product: Virtual Hub
[    1.709559] usb 1-2: Manufacturer: American Megatrends Inc.
[    1.709560] usb 1-2: SerialNumber: serial
[    1.717288] mlx5_core 0000:01:00.0: firmware version: 14.31.1014
[    1.717321] mlx5_core 0000:01:00.0: 63.008 Gb/s available PCIe bandwidth (8.0 GT/s PCIe x8 link)
[    1.720011] ahci 0000:46:00.0: AHCI 0001.0301 32 slots 8 ports 6 Gbps 0xff impl SATA mode
[    1.720015] ahci 0000:46:00.0: flags: 64bit ncq sntf ilck pm led clo only pmp fbs pio slum part ems sxs 
[    1.720640] scsi host16: ahci
[    1.720712] scsi host17: ahci
[    1.720793] scsi host18: ahci
[    1.720858] scsi host19: ahci
[    1.720925] scsi host20: ahci
[    1.720991] scsi host21: ahci
[    1.721079] scsi host22: ahci
[    1.721151] scsi host23: ahci
[    1.721195] ata17: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600100 irq 144
[    1.721196] ata18: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600180 irq 145
[    1.721198] ata19: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600200 irq 146
[    1.721200] ata20: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600280 irq 147
[    1.721201] ata21: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600300 irq 148
[    1.721203] ata22: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600380 irq 149
[    1.721205] ata23: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600400 irq 150
[    1.721206] ata24: SATA max UDMA/133 abar m2048@0xb3600000 port 0xb3600480 irq 151
[    1.731577] ahci 0000:47:00.0: AHCI 0001.0301 32 slots 8 ports 6 Gbps 0xff impl SATA mode
[    1.731581] ahci 0000:47:00.0: flags: 64bit ncq sntf ilck pm led clo only pmp fbs pio slum part ems 
[    1.732568] scsi host24: ahci
[    1.732691] scsi host25: ahci
[    1.732783] scsi host26: ahci
[    1.732905] scsi host27: ahci
[    1.732991] scsi host28: ahci
[    1.733138] scsi host29: ahci
[    1.733274] scsi host30: ahci
[    1.733408] scsi host31: ahci
[    1.733483] ata25: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500100 irq 162
[    1.733485] ata26: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500180 irq 163
[    1.733486] ata27: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500200 irq 164
[    1.733488] ata28: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500280 irq 165
[    1.733490] ata29: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500300 irq 166
[    1.733492] ata30: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500380 irq 167
[    1.733494] ata31: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500400 irq 168
[    1.733495] ata32: SATA max UDMA/133 abar m2048@0xb3500000 port 0xb3500480 irq 169
[    1.749315] hub 1-2:1.0: USB hub found
[    1.749676] hub 1-2:1.0: 5 ports detected
[    1.952021] tsc: Refined TSC clocksource calibration: 2999.999 MHz
[    1.952041] clocksource: tsc: mask: 0xffffffffffffffff max_cycles: 0x2b3e44b2357, max_idle_ns: 440795324996 ns
[    1.952084] clocksource: Switched to clocksource tsc
[    1.997713] mlx5_core 0000:01:00.0: E-Switch: Total vports 10, per vport: max uc(128) max mc(2048)
[    2.001395] mlx5_core 0000:01:00.0: Port module event: module 0, Cable plugged
[    2.005165] ata7: SATA link down (SStatus 0 SControl 300)
[    2.005191] ata5: SATA link down (SStatus 0 SControl 300)
[    2.005216] ata6: SATA link down (SStatus 0 SControl 300)
[    2.005843] ata4: SATA link down (SStatus 0 SControl 300)
[    2.006025] ata8: SATA link down (SStatus 0 SControl 300)
[    2.006145] ata2: SATA link down (SStatus 0 SControl 300)
[    2.006209] ata1: SATA link down (SStatus 0 SControl 300)
[    2.006519] ata3: SATA link down (SStatus 0 SControl 300)
[    2.013106] ata15: SATA link down (SStatus 0 SControl 300)
[    2.013151] ata11: SATA link down (SStatus 0 SControl 300)
[    2.013221] ata10: SATA link down (SStatus 0 SControl 300)
[    2.013247] ata16: SATA link down (SStatus 0 SControl 300)
[    2.013482] ata14: SATA link down (SStatus 0 SControl 300)
[    2.013575] ata12: SATA link down (SStatus 0 SControl 300)
[    2.013601] ata13: SATA link down (SStatus 0 SControl 300)
[    2.014082] ata9: SATA link down (SStatus 0 SControl 300)
[    2.029090] ata24: SATA link down (SStatus 0 SControl 300)
[    2.029112] ata19: SATA link down (SStatus 0 SControl 300)
[    2.029135] ata17: SATA link down (SStatus 0 SControl 300)
[    2.029182] ata22: SATA link down (SStatus 0 SControl 300)
[    2.029204] ata23: SATA link down (SStatus 0 SControl 300)
[    2.029232] ata18: SATA link down (SStatus 0 SControl 300)
[    2.029255] ata21: SATA link down (SStatus 0 SControl 300)
[    2.029274] ata20: SATA link down (SStatus 0 SControl 300)
[    2.034006] usb 1-2.3: new high-speed USB device number 4 using xhci_hcd
[    2.037071] ata25: SATA link down (SStatus 0 SControl 300)
[    2.037110] ata28: SATA link down (SStatus 0 SControl 300)
[    2.037131] ata27: SATA link down (SStatus 0 SControl 300)
[    2.037173] ata30: SATA link down (SStatus 0 SControl 300)
[    2.037195] ata26: SATA link down (SStatus 0 SControl 300)
[    2.037386] ata29: SATA link down (SStatus 0 SControl 300)
[    2.037479] ata32: SATA link down (SStatus 0 SControl 300)
[    2.037503] ata31: SATA link down (SStatus 0 SControl 300)
[    2.125297] usb 1-2.3: New USB device found, idVendor=046b, idProduct=ffb0, bcdDevice= 1.00
[    2.125300] usb 1-2.3: New USB device strings: Mfr=1, Product=2, SerialNumber=3
[    2.125302] usb 1-2.3: Product: Virtual Ethernet
[    2.125303] usb 1-2.3: Manufacturer: American Megatrends Inc.
[    2.125304] usb 1-2.3: SerialNumber: 1234567890
[    2.192895] cdc_ether 1-2.3:2.0 usb0: register 'cdc_ether' at usb-0000:45:00.3-2.3, CDC Ethernet Device, be:13:e4:94:50:24
[    2.192910] usbcore: registered new interface driver cdc_ether
[    2.194043] cdc_ether 1-2.3:2.0 enp69s0f3u2u3c2: renamed from usb0
[    2.243005] usb 1-2.4: new low-speed USB device number 5 using xhci_hcd
[    2.250136] mlx5_core 0000:01:00.0: Supported tc offload range - chains: 4294967294, prios: 4294967295
[    2.258987] mlx5_core 0000:01:00.0: MLX5E: StrdRq(0) RqSz(1024) StrdSz(256) RxCqeCmprss(0 basic)
[    2.260389] mlx5_core 0000:01:00.1: firmware version: 14.31.1014
[    2.260436] mlx5_core 0000:01:00.1: 63.008 Gb/s available PCIe bandwidth (8.0 GT/s PCIe x8 link)
[    2.346549] usb 1-2.4: New USB device found, idVendor=046b, idProduct=ff10, bcdDevice= 1.00
[    2.346551] usb 1-2.4: New USB device strings: Mfr=1, Product=2, SerialNumber=0
[    2.346552] usb 1-2.4: Product: Virtual Keyboard and Mouse
[    2.346553] usb 1-2.4: Manufacturer: American Megatrends Inc.
[    2.429697] input: American Megatrends Inc. Virtual Keyboard and Mouse as /devices/pci0000:40/0000:40:08.1/0000:45:00.3/usb1/1-2/1-2.4/1-2.4:1.0/0003:046B:FF10.0001/input/input2
[    2.429801] hid-generic 0003:046B:FF10.0001: input,hidraw0: USB HID v1.10 Keyboard [American Megatrends Inc. Virtual Keyboard and Mouse] on usb-0000:45:00.3-2.4/input0
[    2.440630] input: American Megatrends Inc. Virtual Keyboard and Mouse as /devices/pci0000:40/0000:40:08.1/0000:45:00.3/usb1/1-2/1-2.4/1-2.4:1.1/0003:046B:FF10.0002/input/input3
[    2.440689] hid-generic 0003:046B:FF10.0002: input,hidraw1: USB HID v1.10 Mouse [American Megatrends Inc. Virtual Keyboard and Mouse] on usb-0000:45:00.3-2.4/input1
[    2.568478] mlx5_core 0000:01:00.1: E-Switch: Total vports 10, per vport: max uc(128) max mc(2048)
[    2.573347] mlx5_core 0000:01:00.1: Port module event: module 1, Cable unplugged
[    2.843784] mlx5_core 0000:01:00.1: Supported tc offload range - chains: 4294967294, prios: 4294967295
[    2.854185] mlx5_core 0000:01:00.1: MLX5E: StrdRq(0) RqSz(1024) StrdSz(256) RxCqeCmprss(0 basic)
[    2.856931] mlx5_core 0000:01:00.1 enp1s0f1: renamed from eth1
[    2.873075] mlx5_core 0000:01:00.0 enp1s0f0: renamed from eth0
[    3.131548] iscsi: registered transport (iser)
[    3.143389] Rounding down aligned max_sectors from 4294967295 to 4294967288
[    3.143424] db_root: cannot open: /etc/target
[    3.143430] fbcon: Taking over console
[    3.155479] Console: switching to colour frame buffer device 128x48
[    3.221373] SGI XFS with ACLs, security attributes, quota, no debug enabled
[    3.223029] XFS (nvme0n1p2): Mounting V5 Filesystem
[    3.225843] RPC: Registered rdma transport module.
[    3.225845] RPC: Registered rdma backchannel transport module.
[    3.416789] XFS (nvme0n1p2): Ending clean mount
[    3.767973] printk: systemd: 26 output lines suppressed due to ratelimiting
[    3.814705] SELinux:  Disabled at runtime.
[    3.850016] audit: type=1404 audit(1708306267.243:2): enforcing=0 old_enforcing=0 auid=4294967295 ses=4294967295 enabled=0 old-enabled=1 lsm=selinux res=1
[    3.857931] systemd[1]: RTC configured in localtime, applying delta of 0 minutes to system time.
[    3.862211] systemd[1]: systemd 239 (239-78.el8) running in system mode. (+PAM +AUDIT +SELINUX +IMA -APPARMOR +SMACK +SYSVINIT +UTMP +LIBCRYPTSETUP +GCRYPT +GNUTLS +ACL +XZ +LZ4 +SECCOMP +BLKID +ELFUTILS +KMOD +IDN2 -IDN +PCRE2 default-hierarchy=legacy)
[    3.874054] systemd[1]: Detected architecture x86-64.
[    3.874667] systemd[1]: Set hostname to <ipp2-2312.nvidia.com>.
[    3.990256] systemd[1]: File /usr/lib/systemd/system/systemd-logind.service.d/nss_nis.conf:2 configures an IP firewall (IPAddressAllow=any), but the local system does not support BPF/cgroup based firewalling.
[    3.990259] systemd[1]: Proceeding WITHOUT firewalling in effect! (This warning is only shown for the first loaded unit using IP firewalling.)
[    4.029918] systemd[1]: initrd-switch-root.service: Succeeded.
[    4.030065] systemd[1]: Stopped Switch Root.
[    4.030256] systemd[1]: systemd-journald.service: Service has no hold-off time (RestartSec=0), scheduling restart.
[    4.030299] systemd[1]: systemd-journald.service: Scheduled restart job, restart counter is at 1.
[    4.030329] systemd[1]: Stopped Journal Service.
[    4.409110] IPMI message handler: version 39.2
[    4.412097] ipmi device interface
[    4.416044] ipmi_si: IPMI System Interface driver
[    4.416055] ipmi_si dmi-ipmi-si.0: ipmi_platform: probing via SMBIOS
[    4.416058] ipmi_platform: ipmi_si: SMBIOS: io 0xca2 regsize 1 spacing 1 irq 0
[    4.416060] ipmi_si: Adding SMBIOS-specified kcs state machine
[    4.416209] ipmi_si IPI0001:00: ipmi_platform: probing via ACPI
[    4.416227] ipmi_si IPI0001:00: ipmi_platform: [io  0x0ca2] regsize 1 spacing 1 irq 0
[    4.419971] piix4_smbus 0000:00:14.0: SMBus Host Controller at 0xb00, revision 0
[    4.419976] piix4_smbus 0000:00:14.0: Using register 0x02 for SMBus port selection
[    4.420152] piix4_smbus 0000:00:14.0: Auxiliary SMBus Host Controller at 0xb20
[    4.421431] ipmi_si dmi-ipmi-si.0: Removing SMBIOS-specified kcs state machine in favor of ACPI
[    4.421434] ipmi_si: Adding ACPI-specified kcs state machine
[    4.421697] ptdma 0000:c2:00.2: enabling device (0000 -> 0002)
[    4.422163] ipmi_si: Trying ACPI-specified kcs state machine at i/o address 0xca2, slave address 0x20, irq 0
[    4.422640] sp5100_tco: SP5100/SB800 TCO WatchDog Timer Driver
[    4.424323] sp5100-tco sp5100-tco: Using 0xfeb00000 for watchdog MMIO address
[    4.424381] ptdma 0000:c3:00.2: enabling device (0000 -> 0002)
[    4.425149] sp5100-tco sp5100-tco: initialized. heartbeat=60 sec (nowayout=0)
[    4.427108] ptdma 0000:81:00.2: enabling device (0000 -> 0002)
[    4.431309] ptdma 0000:82:00.2: enabling device (0000 -> 0002)
[    4.432280] ptdma 0000:44:00.2: enabling device (0000 -> 0002)
[    4.432419] ptdma 0000:45:00.2: enabling device (0000 -> 0002)
[    4.436763] ptdma 0000:03:00.2: enabling device (0000 -> 0002)
[    4.438750] ptdma 0000:04:00.2: enabling device (0000 -> 0002)
[    4.487626] VFIO - User Level meta-driver version: 0.3
[    4.501774] input: PC Speaker as /devices/platform/pcspkr/input/input4
[    4.505353] RAPL PMU: API unit is 2^-32 Joules, 1 fixed counters, 163840 ms ovfl timer
[    4.505355] RAPL PMU: hw unit of domain package 2^-16 Joules
[    4.515753] SVM: TSC scaling supported
[    4.515756] kvm: Nested Virtualization enabled
[    4.515757] SVM: kvm: Nested Paging enabled
[    4.515787] SVM: Virtual VMLOAD VMSAVE supported
[    4.515787] SVM: Virtual GIF supported
[    4.515788] SVM: LBR virtualization supported
[    4.521105] MCE: In-kernel MCE decoding enabled.
[    4.528237] nvidia: loading out-of-tree module taints kernel.
[    4.528245] nvidia: module license 'NVIDIA' taints kernel.
[    4.528246] Disabling lock debugging due to kernel taint
[    4.528837] EDAC amd64: F19h detected (node 0).
[    4.529037] EDAC amd64: Node 0: DRAM ECC enabled.
[    4.529039] EDAC amd64: MCT channel count: 4
[    4.529403] EDAC MC0: Giving out device to module amd64_edac controller F19h: DEV 0000:00:18.3 (INTERRUPT)
[    4.529408] EDAC MC: UMC0 chip selects:
[    4.529409] EDAC amd64: MC: 0:     0MB 1:     0MB
[    4.529411] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529414] EDAC MC: UMC1 chip selects:
[    4.529415] EDAC amd64: MC: 0:     0MB 1:     0MB
[    4.529416] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529419] EDAC MC: UMC2 chip selects:
[    4.529419] EDAC amd64: MC: 0: 16384MB 1: 16384MB
[    4.529421] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529424] EDAC MC: UMC3 chip selects:
[    4.529424] EDAC amd64: MC: 0: 16384MB 1: 16384MB
[    4.529425] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529428] EDAC MC: UMC4 chip selects:
[    4.529429] EDAC amd64: MC: 0: 16384MB 1: 16384MB
[    4.529430] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529433] EDAC MC: UMC5 chip selects:
[    4.529433] EDAC amd64: MC: 0: 16384MB 1: 16384MB
[    4.529434] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529437] EDAC MC: UMC6 chip selects:
[    4.529438] EDAC amd64: MC: 0:     0MB 1:     0MB
[    4.529439] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529442] EDAC MC: UMC7 chip selects:
[    4.529442] EDAC amd64: MC: 0:     0MB 1:     0MB
[    4.529443] EDAC amd64: MC: 2:     0MB 3:     0MB
[    4.529444] EDAC amd64: using x16 syndromes.
[    4.529491] EDAC PCI0: Giving out device to module amd64_edac controller EDAC PCI controller: DEV 0000:00:18.0 (POLLED)
[    4.529493] AMD64 EDAC driver v3.5.0
[    4.534304] intel_rapl_common: Found RAPL domain package
[    4.534306] intel_rapl_common: Found RAPL domain core
[    4.538720] nvidia: module verification failed: signature and/or required key missing - tainting kernel
[    4.547626] nvidia-nvlink: Nvlink Core is being initialized, major device number 235

[    4.548766] nvidia 0000:c1:00.0: enabling device (0000 -> 0002)
[    4.599036] NVRM: loading NVIDIA UNIX x86_64 Kernel Module  535.161.05  Thu Jan 25 17:36:41 UTC 2024
[    4.610468] ipmi_si IPI0001:00: IPMI message handler: Found new BMC (man_id: 0x00c1d6, prod_id: 0x104e, dev_id: 0x20)
[    4.840794] ipmi_si IPI0001:00: IPMI kcs interface initialized
[    4.843330] ipmi_ssif: IPMI SSIF Interface driver
[    5.005220] NVRM: GPU at 0000:c1:00.0 has software scheduler ENABLED with policy BEST_EFFORT.
[    6.277909] IPv6: ADDRCONF(NETDEV_UP): enp1s0f0: link is not ready
[    6.580644] mlx5_core 0000:01:00.0 enp1s0f0: Link up
[    6.582884] IPv6: ADDRCONF(NETDEV_UP): enp1s0f0: link is not ready
[    6.583090] IPv6: ADDRCONF(NETDEV_CHANGE): enp1s0f0: link becomes ready
[    6.584930] IPv6: ADDRCONF(NETDEV_UP): enp1s0f1: link is not ready
[    6.918898] mlx5_core 0000:01:00.1 enp1s0f1: Link down
[    6.922385] IPv6: ADDRCONF(NETDEV_UP): enp1s0f1: link is not ready
[    6.923711] IPv6: ADDRCONF(NETDEV_UP): enp69s0f3u2u3c2: link is not ready
[    6.930082] IPv6: ADDRCONF(NETDEV_UP): enp1s0f1: link is not ready
[   13.440327] bridge: filtering via arp/ip/ip6tables is no longer available by default. Update your scripts to load br_netfilter if you need this.
[   13.708899] block nvme0n1: No UUID available providing old NGUID
[   13.715092] block nvme0n1: No UUID available providing old NGUID
[  532.823081] NVRM: GPU 0000:c1:00.0: UnbindLock acquired
[  533.021082] pci-pf-stub 0000:c1:00.0: claimed by pci-pf-stub
[  533.626720] pci 0000:c1:00.4: [10de:26b5] type 00 class 0x030200
[  533.626735] pci 0000:c1:00.4: enabling Extended Tags
[  533.626754] pci 0000:c1:00.4: Enabling HDA controller
[  533.626924] pci 0000:c1:00.4: Adding to iommu group 75
[  533.627035] NVRM: Aborting probe for VF 0000:c1:00.4 since PF is not bound to nvidia driver.
[  533.627039] nvidia: probe of 0000:c1:00.4 failed with error -1
[  533.627049] pci-pf-stub 0000:c1:00.4: claimed by pci-pf-stub
[  533.627078] pci 0000:c1:00.5: [10de:26b5] type 00 class 0x030200
[  533.627090] pci 0000:c1:00.5: enabling Extended Tags
[  533.627104] pci 0000:c1:00.5: Enabling HDA controller
[  533.627217] pci 0000:c1:00.5: Adding to iommu group 76
[  533.627263] NVRM: Aborting probe for VF 0000:c1:00.5 since PF is not bound to nvidia driver.
[  533.627266] nvidia: probe of 0000:c1:00.5 failed with error -1
[  533.627271] pci-pf-stub 0000:c1:00.5: claimed by pci-pf-stub
[  533.627290] pci 0000:c1:00.6: [10de:26b5] type 00 class 0x030200
[  533.627301] pci 0000:c1:00.6: enabling Extended Tags
[  533.627313] pci 0000:c1:00.6: Enabling HDA controller
[  533.627417] pci 0000:c1:00.6: Adding to iommu group 77
[  533.627461] NVRM: Aborting probe for VF 0000:c1:00.6 since PF is not bound to nvidia driver.
[  533.627462] nvidia: probe of 0000:c1:00.6 failed with error -1
[  533.627468] pci-pf-stub 0000:c1:00.6: claimed by pci-pf-stub
[  533.627489] pci 0000:c1:00.7: [10de:26b5] type 00 class 0x030200
[  533.627499] pci 0000:c1:00.7: enabling Extended Tags
[  533.627511] pci 0000:c1:00.7: Enabling HDA controller
[  533.627631] pci 0000:c1:00.7: Adding to iommu group 78
[  533.627688] NVRM: Aborting probe for VF 0000:c1:00.7 since PF is not bound to nvidia driver.
[  533.627690] nvidia: probe of 0000:c1:00.7 failed with error -1
[  533.627695] pci-pf-stub 0000:c1:00.7: claimed by pci-pf-stub
[  533.627714] pci 0000:c1:01.0: [10de:26b5] type 00 class 0x030200
[  533.627724] pci 0000:c1:01.0: enabling Extended Tags
[  533.627736] pci 0000:c1:01.0: Enabling HDA controller
[  533.627849] pci 0000:c1:01.0: Adding to iommu group 79
[  533.627909] NVRM: Aborting probe for VF 0000:c1:01.0 since PF is not bound to nvidia driver.
[  533.627911] nvidia: probe of 0000:c1:01.0 failed with error -1
[  533.627917] pci-pf-stub 0000:c1:01.0: claimed by pci-pf-stub
[  533.627938] pci 0000:c1:01.1: [10de:26b5] type 00 class 0x030200
[  533.627949] pci 0000:c1:01.1: enabling Extended Tags
[  533.627963] pci 0000:c1:01.1: Enabling HDA controller
[  533.628107] pci 0000:c1:01.1: Adding to iommu group 80
[  533.628157] NVRM: Aborting probe for VF 0000:c1:01.1 since PF is not bound to nvidia driver.
[  533.628160] nvidia: probe of 0000:c1:01.1 failed with error -1
[  533.628166] pci-pf-stub 0000:c1:01.1: claimed by pci-pf-stub
[  533.628186] pci 0000:c1:01.2: [10de:26b5] type 00 class 0x030200
[  533.628197] pci 0000:c1:01.2: enabling Extended Tags
[  533.628210] pci 0000:c1:01.2: Enabling HDA controller
[  533.628344] pci 0000:c1:01.2: Adding to iommu group 81
[  533.628382] NVRM: Aborting probe for VF 0000:c1:01.2 since PF is not bound to nvidia driver.
[  533.628383] nvidia: probe of 0000:c1:01.2 failed with error -1
[  533.628386] pci-pf-stub 0000:c1:01.2: claimed by pci-pf-stub
[  533.628399] pci 0000:c1:01.3: [10de:26b5] type 00 class 0x030200
[  533.628408] pci 0000:c1:01.3: enabling Extended Tags
[  533.628417] pci 0000:c1:01.3: Enabling HDA controller
[  533.628499] pci 0000:c1:01.3: Adding to iommu group 82
[  533.628533] NVRM: Aborting probe for VF 0000:c1:01.3 since PF is not bound to nvidia driver.
[  533.628534] nvidia: probe of 0000:c1:01.3 failed with error -1
[  533.628536] pci-pf-stub 0000:c1:01.3: claimed by pci-pf-stub
[  533.628548] pci 0000:c1:01.4: [10de:26b5] type 00 class 0x030200
[  533.628556] pci 0000:c1:01.4: enabling Extended Tags
[  533.628565] pci 0000:c1:01.4: Enabling HDA controller
[  533.628652] pci 0000:c1:01.4: Adding to iommu group 83
[  533.628684] NVRM: Aborting probe for VF 0000:c1:01.4 since PF is not bound to nvidia driver.
[  533.628685] nvidia: probe of 0000:c1:01.4 failed with error -1
[  533.628688] pci-pf-stub 0000:c1:01.4: claimed by pci-pf-stub
[  533.628701] pci 0000:c1:01.5: [10de:26b5] type 00 class 0x030200
[  533.628708] pci 0000:c1:01.5: enabling Extended Tags
[  533.628717] pci 0000:c1:01.5: Enabling HDA controller
[  533.628823] pci 0000:c1:01.5: Adding to iommu group 84
[  533.628881] NVRM: Aborting probe for VF 0000:c1:01.5 since PF is not bound to nvidia driver.
[  533.628882] nvidia: probe of 0000:c1:01.5 failed with error -1
[  533.628897] pci-pf-stub 0000:c1:01.5: claimed by pci-pf-stub
[  533.628909] pci 0000:c1:01.6: [10de:26b5] type 00 class 0x030200
[  533.628917] pci 0000:c1:01.6: enabling Extended Tags
[  533.628926] pci 0000:c1:01.6: Enabling HDA controller
[  533.629082] pci 0000:c1:01.6: Adding to iommu group 85
[  533.629119] NVRM: Aborting probe for VF 0000:c1:01.6 since PF is not bound to nvidia driver.
[  533.629121] nvidia: probe of 0000:c1:01.6 failed with error -1
[  533.629131] pci-pf-stub 0000:c1:01.6: claimed by pci-pf-stub
[  533.629144] pci 0000:c1:01.7: [10de:26b5] type 00 class 0x030200
[  533.629152] pci 0000:c1:01.7: enabling Extended Tags
[  533.629162] pci 0000:c1:01.7: Enabling HDA controller
[  533.629305] pci 0000:c1:01.7: Adding to iommu group 86
[  533.629340] NVRM: Aborting probe for VF 0000:c1:01.7 since PF is not bound to nvidia driver.
[  533.629341] nvidia: probe of 0000:c1:01.7 failed with error -1
[  533.629348] pci-pf-stub 0000:c1:01.7: claimed by pci-pf-stub
[  533.629361] pci 0000:c1:02.0: [10de:26b5] type 00 class 0x030200
[  533.629369] pci 0000:c1:02.0: enabling Extended Tags
[  533.629379] pci 0000:c1:02.0: Enabling HDA controller
[  533.629566] pci 0000:c1:02.0: Adding to iommu group 87
[  533.629602] NVRM: Aborting probe for VF 0000:c1:02.0 since PF is not bound to nvidia driver.
[  533.629604] nvidia: probe of 0000:c1:02.0 failed with error -1
[  533.629607] pci-pf-stub 0000:c1:02.0: claimed by pci-pf-stub
[  533.629622] pci 0000:c1:02.1: [10de:26b5] type 00 class 0x030200
[  533.629630] pci 0000:c1:02.1: enabling Extended Tags
[  533.629639] pci 0000:c1:02.1: Enabling HDA controller
[  533.629764] pci 0000:c1:02.1: Adding to iommu group 88
[  533.629803] NVRM: Aborting probe for VF 0000:c1:02.1 since PF is not bound to nvidia driver.
[  533.629804] nvidia: probe of 0000:c1:02.1 failed with error -1
[  533.629823] pci-pf-stub 0000:c1:02.1: claimed by pci-pf-stub
[  533.629844] pci 0000:c1:02.2: [10de:26b5] type 00 class 0x030200
[  533.629856] pci 0000:c1:02.2: enabling Extended Tags
[  533.629867] pci 0000:c1:02.2: Enabling HDA controller
[  533.629961] pci 0000:c1:02.2: Adding to iommu group 89
[  533.630028] NVRM: Aborting probe for VF 0000:c1:02.2 since PF is not bound to nvidia driver.
[  533.630029] nvidia: probe of 0000:c1:02.2 failed with error -1
[  533.630055] pci-pf-stub 0000:c1:02.2: claimed by pci-pf-stub
[  533.630074] pci 0000:c1:02.3: [10de:26b5] type 00 class 0x030200
[  533.630083] pci 0000:c1:02.3: enabling Extended Tags
[  533.630092] pci 0000:c1:02.3: Enabling HDA controller
[  533.630286] pci 0000:c1:02.3: Adding to iommu group 90
[  533.630354] NVRM: Aborting probe for VF 0000:c1:02.3 since PF is not bound to nvidia driver.
[  533.630355] nvidia: probe of 0000:c1:02.3 failed with error -1
[  533.630358] pci-pf-stub 0000:c1:02.3: claimed by pci-pf-stub
[  533.630374] pci 0000:c1:02.4: [10de:26b5] type 00 class 0x030200
[  533.630383] pci 0000:c1:02.4: enabling Extended Tags
[  533.630392] pci 0000:c1:02.4: Enabling HDA controller
[  533.630491] pci 0000:c1:02.4: Adding to iommu group 91
[  533.630523] NVRM: Aborting probe for VF 0000:c1:02.4 since PF is not bound to nvidia driver.
[  533.630524] nvidia: probe of 0000:c1:02.4 failed with error -1
[  533.630537] pci-pf-stub 0000:c1:02.4: claimed by pci-pf-stub
[  533.630552] pci 0000:c1:02.5: [10de:26b5] type 00 class 0x030200
[  533.630560] pci 0000:c1:02.5: enabling Extended Tags
[  533.630569] pci 0000:c1:02.5: Enabling HDA controller
[  533.630818] pci 0000:c1:02.5: Adding to iommu group 92
[  533.630925] NVRM: Aborting probe for VF 0000:c1:02.5 since PF is not bound to nvidia driver.
[  533.630926] nvidia: probe of 0000:c1:02.5 failed with error -1
[  533.630974] pci-pf-stub 0000:c1:02.5: claimed by pci-pf-stub
[  533.630990] pci 0000:c1:02.6: [10de:26b5] type 00 class 0x030200
[  533.630998] pci 0000:c1:02.6: enabling Extended Tags
[  533.631008] pci 0000:c1:02.6: Enabling HDA controller
[  533.631266] pci 0000:c1:02.6: Adding to iommu group 93
[  533.631308] NVRM: Aborting probe for VF 0000:c1:02.6 since PF is not bound to nvidia driver.
[  533.631309] nvidia: probe of 0000:c1:02.6 failed with error -1
[  533.631312] pci-pf-stub 0000:c1:02.6: claimed by pci-pf-stub
[  533.631334] pci 0000:c1:02.7: [10de:26b5] type 00 class 0x030200
[  533.631343] pci 0000:c1:02.7: enabling Extended Tags
[  533.631358] pci 0000:c1:02.7: Enabling HDA controller
[  533.631447] pci 0000:c1:02.7: Adding to iommu group 94
[  533.631491] NVRM: Aborting probe for VF 0000:c1:02.7 since PF is not bound to nvidia driver.
[  533.631492] nvidia: probe of 0000:c1:02.7 failed with error -1
[  533.631506] pci-pf-stub 0000:c1:02.7: claimed by pci-pf-stub
[  533.631521] pci 0000:c1:03.0: [10de:26b5] type 00 class 0x030200
[  533.631529] pci 0000:c1:03.0: enabling Extended Tags
[  533.631538] pci 0000:c1:03.0: Enabling HDA controller
[  533.631729] pci 0000:c1:03.0: Adding to iommu group 95
[  533.631786] NVRM: Aborting probe for VF 0000:c1:03.0 since PF is not bound to nvidia driver.
[  533.631787] nvidia: probe of 0000:c1:03.0 failed with error -1
[  533.631810] pci-pf-stub 0000:c1:03.0: claimed by pci-pf-stub
[  533.631826] pci 0000:c1:03.1: [10de:26b5] type 00 class 0x030200
[  533.631834] pci 0000:c1:03.1: enabling Extended Tags
[  533.631843] pci 0000:c1:03.1: Enabling HDA controller
[  533.631966] pci 0000:c1:03.1: Adding to iommu group 96
[  533.632016] NVRM: Aborting probe for VF 0000:c1:03.1 since PF is not bound to nvidia driver.
[  533.632017] nvidia: probe of 0000:c1:03.1 failed with error -1
[  533.632039] pci-pf-stub 0000:c1:03.1: claimed by pci-pf-stub
[  533.632054] pci 0000:c1:03.2: [10de:26b5] type 00 class 0x030200
[  533.632063] pci 0000:c1:03.2: enabling Extended Tags
[  533.632072] pci 0000:c1:03.2: Enabling HDA controller
[  533.632264] pci 0000:c1:03.2: Adding to iommu group 97
[  533.632310] NVRM: Aborting probe for VF 0000:c1:03.2 since PF is not bound to nvidia driver.
[  533.632311] nvidia: probe of 0000:c1:03.2 failed with error -1
[  533.632318] pci-pf-stub 0000:c1:03.2: claimed by pci-pf-stub
[  533.632334] pci 0000:c1:03.3: [10de:26b5] type 00 class 0x030200
[  533.632342] pci 0000:c1:03.3: enabling Extended Tags
[  533.632351] pci 0000:c1:03.3: Enabling HDA controller
[  533.632464] pci 0000:c1:03.3: Adding to iommu group 98
[  533.632500] NVRM: Aborting probe for VF 0000:c1:03.3 since PF is not bound to nvidia driver.
[  533.632501] nvidia: probe of 0000:c1:03.3 failed with error -1
[  533.632504] pci-pf-stub 0000:c1:03.3: claimed by pci-pf-stub
[  533.632520] pci 0000:c1:03.4: [10de:26b5] type 00 class 0x030200
[  533.632535] pci 0000:c1:03.4: enabling Extended Tags
[  533.632550] pci 0000:c1:03.4: Enabling HDA controller
[  533.632711] pci 0000:c1:03.4: Adding to iommu group 99
[  533.632817] NVRM: Aborting probe for VF 0000:c1:03.4 since PF is not bound to nvidia driver.
[  533.632818] nvidia: probe of 0000:c1:03.4 failed with error -1
[  533.632833] pci-pf-stub 0000:c1:03.4: claimed by pci-pf-stub
[  533.632848] pci 0000:c1:03.5: [10de:26b5] type 00 class 0x030200
[  533.632856] pci 0000:c1:03.5: enabling Extended Tags
[  533.632865] pci 0000:c1:03.5: Enabling HDA controller
[  533.633021] pci 0000:c1:03.5: Adding to iommu group 100
[  533.633062] NVRM: Aborting probe for VF 0000:c1:03.5 since PF is not bound to nvidia driver.
[  533.633063] nvidia: probe of 0000:c1:03.5 failed with error -1
[  533.633066] pci-pf-stub 0000:c1:03.5: claimed by pci-pf-stub
[  533.633089] pci 0000:c1:03.6: [10de:26b5] type 00 class 0x030200
[  533.633098] pci 0000:c1:03.6: enabling Extended Tags
[  533.633112] pci 0000:c1:03.6: Enabling HDA controller
[  533.633204] pci 0000:c1:03.6: Adding to iommu group 101
[  533.633237] NVRM: Aborting probe for VF 0000:c1:03.6 since PF is not bound to nvidia driver.
[  533.633238] nvidia: probe of 0000:c1:03.6 failed with error -1
[  533.633249] pci-pf-stub 0000:c1:03.6: claimed by pci-pf-stub
[  533.633263] pci 0000:c1:03.7: [10de:26b5] type 00 class 0x030200
[  533.633272] pci 0000:c1:03.7: enabling Extended Tags
[  533.633281] pci 0000:c1:03.7: Enabling HDA controller
[  533.633515] pci 0000:c1:03.7: Adding to iommu group 102
[  533.633643] NVRM: Aborting probe for VF 0000:c1:03.7 since PF is not bound to nvidia driver.
[  533.633644] nvidia: probe of 0000:c1:03.7 failed with error -1
[  533.633651] pci-pf-stub 0000:c1:03.7: claimed by pci-pf-stub
[  533.633669] pci 0000:c1:04.0: [10de:26b5] type 00 class 0x030200
[  533.633679] pci 0000:c1:04.0: enabling Extended Tags
[  533.633693] pci 0000:c1:04.0: Enabling HDA controller
[  533.633817] pci 0000:c1:04.0: Adding to iommu group 103
[  533.633849] NVRM: Aborting probe for VF 0000:c1:04.0 since PF is not bound to nvidia driver.
[  533.633850] nvidia: probe of 0000:c1:04.0 failed with error -1
[  533.633881] pci-pf-stub 0000:c1:04.0: claimed by pci-pf-stub
[  533.633896] pci 0000:c1:04.1: [10de:26b5] type 00 class 0x030200
[  533.633905] pci 0000:c1:04.1: enabling Extended Tags
[  533.633914] pci 0000:c1:04.1: Enabling HDA controller
[  533.634042] pci 0000:c1:04.1: Adding to iommu group 104
[  533.634086] NVRM: Aborting probe for VF 0000:c1:04.1 since PF is not bound to nvidia driver.
[  533.634087] nvidia: probe of 0000:c1:04.1 failed with error -1
[  533.634098] pci-pf-stub 0000:c1:04.1: claimed by pci-pf-stub
[  533.634120] pci 0000:c1:04.2: [10de:26b5] type 00 class 0x030200
[  533.634132] pci 0000:c1:04.2: enabling Extended Tags
[  533.634142] pci 0000:c1:04.2: Enabling HDA controller
[  533.634427] pci 0000:c1:04.2: Adding to iommu group 105
[  533.634514] NVRM: Aborting probe for VF 0000:c1:04.2 since PF is not bound to nvidia driver.
[  533.634515] nvidia: probe of 0000:c1:04.2 failed with error -1
[  533.634529] pci-pf-stub 0000:c1:04.2: claimed by pci-pf-stub
[  533.634544] pci 0000:c1:04.3: [10de:26b5] type 00 class 0x030200
[  533.634552] pci 0000:c1:04.3: enabling Extended Tags
[  533.634561] pci 0000:c1:04.3: Enabling HDA controller
[  533.634872] pci 0000:c1:04.3: Adding to iommu group 106
[  533.634944] NVRM: Aborting probe for VF 0000:c1:04.3 since PF is not bound to nvidia driver.
[  533.634945] nvidia: probe of 0000:c1:04.3 failed with error -1
[  533.634955] pci-pf-stub 0000:c1:04.3: claimed by pci-pf-stub
[  533.635022] pci-pf-stub 0000:c1:00.0: driver left SR-IOV enabled after remove
[  533.693468] NVRM: GPU at 0000:c1:00.0 has software scheduler ENABLED with policy BEST_EFFORT.
[  535.022363] nvidia 0000:c1:00.4: enabling device (0000 -> 0002)
[  535.022376] nvidia 0000:c1:00.4: Driver cannot be asked to release device
[  535.022448] nvidia 0000:c1:00.4: MDEV: Registered
[  535.022614] nvidia 0000:c1:00.5: enabling device (0000 -> 0002)
[  535.022625] nvidia 0000:c1:00.5: Driver cannot be asked to release device
[  535.022724] nvidia 0000:c1:00.5: MDEV: Registered
[  535.022879] nvidia 0000:c1:00.6: enabling device (0000 -> 0002)
[  535.022886] nvidia 0000:c1:00.6: Driver cannot be asked to release device
[  535.022999] nvidia 0000:c1:00.6: MDEV: Registered
[  535.023139] nvidia 0000:c1:00.7: enabling device (0000 -> 0002)
[  535.023146] nvidia 0000:c1:00.7: Driver cannot be asked to release device
[  535.023219] nvidia 0000:c1:00.7: MDEV: Registered
[  535.023348] nvidia 0000:c1:01.0: enabling device (0000 -> 0002)
[  535.023354] nvidia 0000:c1:01.0: Driver cannot be asked to release device
[  535.023444] nvidia 0000:c1:01.0: MDEV: Registered
[  535.023567] nvidia 0000:c1:01.1: enabling device (0000 -> 0002)
[  535.023573] nvidia 0000:c1:01.1: Driver cannot be asked to release device
[  535.023669] nvidia 0000:c1:01.1: MDEV: Registered
[  535.023795] nvidia 0000:c1:01.2: enabling device (0000 -> 0002)
[  535.023803] nvidia 0000:c1:01.2: Driver cannot be asked to release device
[  535.023864] nvidia 0000:c1:01.2: MDEV: Registered
[  535.023983] nvidia 0000:c1:01.3: enabling device (0000 -> 0002)
[  535.023988] nvidia 0000:c1:01.3: Driver cannot be asked to release device
[  535.024069] nvidia 0000:c1:01.3: MDEV: Registered
[  535.024197] nvidia 0000:c1:01.4: enabling device (0000 -> 0002)
[  535.024204] nvidia 0000:c1:01.4: Driver cannot be asked to release device
[  535.024287] nvidia 0000:c1:01.4: MDEV: Registered
[  535.024411] nvidia 0000:c1:01.5: enabling device (0000 -> 0002)
[  535.024419] nvidia 0000:c1:01.5: Driver cannot be asked to release device
[  535.024478] nvidia 0000:c1:01.5: MDEV: Registered
[  535.024603] nvidia 0000:c1:01.6: enabling device (0000 -> 0002)
[  535.024611] nvidia 0000:c1:01.6: Driver cannot be asked to release device
[  535.024702] nvidia 0000:c1:01.6: MDEV: Registered
[  535.024837] nvidia 0000:c1:01.7: enabling device (0000 -> 0002)
[  535.024844] nvidia 0000:c1:01.7: Driver cannot be asked to release device
[  535.024934] nvidia 0000:c1:01.7: MDEV: Registered
[  535.025053] nvidia 0000:c1:02.0: enabling device (0000 -> 0002)
[  535.025061] nvidia 0000:c1:02.0: Driver cannot be asked to release device
[  535.025144] nvidia 0000:c1:02.0: MDEV: Registered
[  535.025264] nvidia 0000:c1:02.1: enabling device (0000 -> 0002)
[  535.025270] nvidia 0000:c1:02.1: Driver cannot be asked to release device
[  535.025348] nvidia 0000:c1:02.1: MDEV: Registered
[  535.025476] nvidia 0000:c1:02.2: enabling device (0000 -> 0002)
[  535.025484] nvidia 0000:c1:02.2: Driver cannot be asked to release device
[  535.025559] nvidia 0000:c1:02.2: MDEV: Registered
[  535.025686] nvidia 0000:c1:02.3: enabling device (0000 -> 0002)
[  535.025693] nvidia 0000:c1:02.3: Driver cannot be asked to release device
[  535.025778] nvidia 0000:c1:02.3: MDEV: Registered
[  535.025909] nvidia 0000:c1:02.4: enabling device (0000 -> 0002)
[  535.025918] nvidia 0000:c1:02.4: Driver cannot be asked to release device
[  535.025994] nvidia 0000:c1:02.4: MDEV: Registered
[  535.026117] nvidia 0000:c1:02.5: enabling device (0000 -> 0002)
[  535.026125] nvidia 0000:c1:02.5: Driver cannot be asked to release device
[  535.026213] nvidia 0000:c1:02.5: MDEV: Registered
[  535.026340] nvidia 0000:c1:02.6: enabling device (0000 -> 0002)
[  535.026346] nvidia 0000:c1:02.6: Driver cannot be asked to release device
[  535.026419] nvidia 0000:c1:02.6: MDEV: Registered
[  535.026540] nvidia 0000:c1:02.7: enabling device (0000 -> 0002)
[  535.026547] nvidia 0000:c1:02.7: Driver cannot be asked to release device
[  535.026698] nvidia 0000:c1:02.7: MDEV: Registered
[  535.026828] nvidia 0000:c1:03.0: enabling device (0000 -> 0002)
[  535.026834] nvidia 0000:c1:03.0: Driver cannot be asked to release device
[  535.026911] nvidia 0000:c1:03.0: MDEV: Registered
[  535.027028] nvidia 0000:c1:03.1: enabling device (0000 -> 0002)
[  535.027039] nvidia 0000:c1:03.1: Driver cannot be asked to release device
[  535.027162] nvidia 0000:c1:03.1: MDEV: Registered
[  535.027299] nvidia 0000:c1:03.2: enabling device (0000 -> 0002)
[  535.027305] nvidia 0000:c1:03.2: Driver cannot be asked to release device
[  535.027416] nvidia 0000:c1:03.2: MDEV: Registered
[  535.027546] nvidia 0000:c1:03.3: enabling device (0000 -> 0002)
[  535.027556] nvidia 0000:c1:03.3: Driver cannot be asked to release device
[  535.027648] nvidia 0000:c1:03.3: MDEV: Registered
[  535.027768] nvidia 0000:c1:03.4: enabling device (0000 -> 0002)
[  535.027775] nvidia 0000:c1:03.4: Driver cannot be asked to release device
[  535.027866] nvidia 0000:c1:03.4: MDEV: Registered
[  535.028034] nvidia 0000:c1:03.5: enabling device (0000 -> 0002)
[  535.028042] nvidia 0000:c1:03.5: Driver cannot be asked to release device
[  535.028108] nvidia 0000:c1:03.5: MDEV: Registered
[  535.028232] nvidia 0000:c1:03.6: enabling device (0000 -> 0002)
[  535.028238] nvidia 0000:c1:03.6: Driver cannot be asked to release device
[  535.028344] nvidia 0000:c1:03.6: MDEV: Registered
[  535.028458] nvidia 0000:c1:03.7: enabling device (0000 -> 0002)
[  535.028465] nvidia 0000:c1:03.7: Driver cannot be asked to release device
[  535.028558] nvidia 0000:c1:03.7: MDEV: Registered
[  535.028695] nvidia 0000:c1:04.0: enabling device (0000 -> 0002)
[  535.028704] nvidia 0000:c1:04.0: Driver cannot be asked to release device
[  535.028785] nvidia 0000:c1:04.0: MDEV: Registered
[  535.028922] nvidia 0000:c1:04.1: enabling device (0000 -> 0002)
[  535.028929] nvidia 0000:c1:04.1: Driver cannot be asked to release device
[  535.029043] nvidia 0000:c1:04.1: MDEV: Registered
[  535.029177] nvidia 0000:c1:04.2: enabling device (0000 -> 0002)
[  535.029188] nvidia 0000:c1:04.2: Driver cannot be asked to release device
[  535.029254] nvidia 0000:c1:04.2: MDEV: Registered
[  535.029390] nvidia 0000:c1:04.3: enabling device (0000 -> 0002)
[  535.029396] nvidia 0000:c1:04.3: Driver cannot be asked to release device
[  535.029563] nvidia 0000:c1:04.3: MDEV: Registered
[  541.746299] vfio_mdev a9742d32-7e28-412e-9e84-d49c99ea4d32: Adding to iommu group 107
[  541.746302] vfio_mdev a9742d32-7e28-412e-9e84-d49c99ea4d32: MDEV: group_id = 107
[  549.024672] tun: Universal TUN/TAP device driver, 1.6
