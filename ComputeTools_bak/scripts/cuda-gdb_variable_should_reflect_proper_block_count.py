import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:v:", ['help', 'spawn=', 'cuda_version='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            elif name in ('-v', '--cuda_version'):
                cuda_version = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    c.execute_and_compare("break t68.cu:5", "Breakpoint 1 at\s+")
    c.execute_and_compare("run", "5\s+.*int.* idx=")
    c.execute_and_compare("p gridDim")
    m = re.search(".*x.* = (\d+),.*y.* = (\d+),.*z.* = (\d+)", c.result)
    if m:
        a = "(%s,%s,%s)" % (m.group(1), m.group(2), m.group(3))
    c.execute_and_compare("p blockDim")
    m1 = re.search(".*x.* = (\d+),.*y.* = (\d+),.*z.* = (\d+)", c.result)
    if m1:
        b = "(%s,%s,%s)" % (m1.group(1), m1.group(2), m1.group(3))
    c.execute_and_compare("info cuda kernels")
    m2 = re.search("(\(\d+,\d+,\d+\))\s+(\(\d+,\d+,\d+\))", c.result)
    if m2:
        c = m2.group(1)
        d = m2.group(2)
    if a == c:
        print('\n&&&& PASS\n')
    else:
        print('\n&&&& FAIL\n')
    if b == d:
        print('\n&&&& PASS\n')
    else:
        print('\n&&&& FAIL\n')
