import getopt
import os
import re
import sys
import pexpect
import time


def usage():
    script_name = sys.argv[0]
    print 'python %s -h|-s <spawn command>' % script_name
    print ' -h help\n' \
          ' -s spawn command\n' \
          ''
    exit()


def execute_and_compare(c, step, expect, prompt="\(cuda-gdb\) ", timeout=180):
    c.sendline(step)
#    if step == 'run':
#        time.sleep(120)
#        c.sendcontrol('c')
    c.expect(prompt, timeout)
    # compare with expection
    if re.search(expect, c.before.strip(step).lstrip().rstrip()):
        print '\n&&&& PASS\n'
    else:
        print '\n&&&& FAIL\n'


if __name__ == '__main__':
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:", ['help', 'spawn='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
    except getopt.GetoptError:
        usage()

    VM_PROMPT = '\(cuda-gdb\) '
    # access to gdb
    c = pexpect.spawn(spawn_command, timeout=60, maxread=10000)
    c.logfile = sys.stdout
    i = c.expect([pexpect.TIMEOUT, VM_PROMPT])
    execute_and_compare(c, "set pagination off", "")
    execute_and_compare(c, "set demangle-style none", ".*")
    execute_and_compare(c, "set cuda kernel_events application", ".*")
    execute_and_compare(c, "run", "Launch of CUDA Kernel", "_GPU_.*on Device 0, level 0", timeout=1800)
