# -*- encoding: utf-8 -*-
import os
import sys
import yaml
try:
    # Python3
    import urllib.request as urllib2
except ImportError:
    # Python2
    import urllib2
try:
    # Python3
    import http.client as httplib
except ImportError:
    # Python2
    import httplib
try:
    # Python3
    from configparser import ConfigParser
except ImportError:
    # Python2
    from ConfigParser import ConfigParser
import argparse
from common_utils import *
import threading
import logging
import random
import time
from yaml_mgr import YamlManger
from run_cublas_case import get_sm, prepare_cupti


math_lib_yaml = math_lib_yaml = '{}/../yaml/math_lib_case.yaml'.format(os.getcwd())
yaml_mgr = YamlManger(math_lib_yaml)
case_config = yaml_mgr.load()
tools_home = case_config['global']['env']['TOOLS_HOME']
platform = case_config['global']['env']['PLATFORM'].lower()
cuda_short_version = case_config['global']['env']['CUDA_SHORT_VERSION']
date1 = case_config['global']['env']['DATE1']
tools_home = case_config['global']['env']['TOOLS_HOME1']
cutensor_skip_list = case_config['CUTENSOR_SKIP_CASE']['CASE_LIST']
logger = logging.getLogger()
logger.setLevel(level=logging.INFO)
# StreamHandler
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(level=logging.INFO)
formatter = logging.Formatter(fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y/%m/%d %H:%M:%S')
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# FileHandler
file_handler = logging.FileHandler('%s/cutensor_case.log' % tools_home)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

logger = logging.getLogger(__name__)


if platform == 'x86':
    download_to_path = case_config['global']['env']['CUTENSOR_X86_PATH']
elif platform == 'arm':
    download_to_path = case_config['global']['env']['CUTENSOR_ARM_PATH']
else:
    download_to_path = case_config['global']['env']['CUTENSOR_P9_PATH']


def get_sm():
    # prepare env
    sm_cmd = case_config['PREPARE']['CMD']
    sm_out = run_loc_cmd(sm_cmd)
    if sm_out.succeeded:
        sm = sm_out['output'].split('\n')[-1].strip(' ')
        return sm
    else:
        logger.info('we get the sm failed')
        exit()


def get_cmd(cmd_file):
    cmd_list = []
    with open(cmd_file, 'r') as f:
        for line in f.readlines():
            cmd_list.append(line)


gpu_dict = {
    'v100': 'gv100',
    'RTX 2070': 'tu106',
    'RTX 2080': 'tu104',
    'RTX 2080 Ti': 'tu102',
    'RTX 3080 Ti': 'ga102',
    'GV100': 'gv100',
    'T4': 'tu104',
    'TITAN V': 'tu102',
    'A100': 'ga100',
    'P100': 'gp100',
    'ga100': 'ga100',
    'gv100': 'gv100'
}


def get_execute_time(func):
    def wrapper(*args, **kw):
        local_time = time.time()
        func(*args, **kw)
        logger.info('current Function [%s] with %s option, it run time is %.2f' % (func.__name__, args, time.time() - local_time))
        with open('%s/cubtensor_execute_time.txt' % tools_home, 'a+') as f:
            f.write('%s---current Function [%s] with %s option, it run time is %.2f' % (time.strftime('%Y/%m/%d %H:%M:%S', time.localtime(time.time())), func.__name__, args, time.time() - local_time))
            f.write('\n')
    return wrapper


def prepare_gpu():
    build_cmd = case_config['PREPARE1']['CMD1']
    gpu_cmd = case_config['PREPARE1']['CMD2']
    sm_cmd = case_config['PREPARE1']['CMD3']
    out1 = run_loc_cmd(build_cmd)
    if out1.succeeded:
        out2 = run_loc_cmd(gpu_cmd)
        out3 = run_loc_cmd(sm_cmd)
        for key, value in gpu_dict.items():
            if key in out2['output']:
                return value
        if 'Graphics Device' in out2['output'] and out3['output'] == '7.0':
            return 'gv100'
        elif 'Graphics Device' in out2['output'] and out3['output'] == '8.0':
            return 'ga100'
        else:
            pass


def prepare_package():
    base_url = case_config['global']['env']['CUTENSOR_BASE_URL']
    password1 = case_config['global']['env']['USER_PASSWORD']
    user1 = case_config['global']['env']['USER']
    password = b64_strip_decode(password1)
    user = b64_strip_decode(user1)
    cmd = 'curl -u %s:"%s" %s' % (user, password, base_url)
    arch = platform
    out = run_loc_cmd(cmd)
    cutensor_list = []
    if arch == 'power':
        for line in out['output'].split("<a href="):
            if 'libcutensor-linux-' in line:
                if 'libcutensor-linux-ppc64le' in line.split('"')[1]:
                    cutensor_list.append(line.split('"')[1])
    elif arch == 'arm':
        for line in out['output'].split("<a href="):
            if 'libcutensor-linux-' in line:
                if 'libcutensor-linux-sbsa' in line.split('"')[1]:
                    cutensor_list.append(line.split('"')[1])
    else:
        for line in out['output'].split("<a href="):
            if 'libcutensor-linux-' in line:
                if 'libcutensor-linux-x86_64' in line.split('"')[1]:
                    cutensor_list.append(line.split('"')[1])
    latest_build_name = cutensor_list[-1]
    mkdir(download_to_path)
    create_file(download_to_path, 'cutensor_build.txt')
    if check_file(download_to_path, 'cutensor_build.txt') is True:
        logger.info('we had download the package--%s, no need download again' % latest_build_name)
    else:
        cmd1 = 'cd %s; wget --user %s --password "%s" %s/%s' % (download_to_path, user, password, base_url, latest_build_name)
        out1 = run_loc_cmd(cmd1)
        if out1.succeeded:
            logger.info('we download  the cutensor package successful')
            cmd = 'cd %s; tar zxvf *.tar.gz' % download_to_path
            out2 = run_loc_cmd(cmd)
            if out2.succeeded:
                logger.info('we extract the cuTensor package successful')
                with open('%s/cutensor_build.txt' % download_to_path, 'a+') as f:
                    f.write(latest_build_name)
                    f.write('\n')
            else:
                logger.info('we extract the cuTensor package fail')
        else:
            logger.info('we download  the cutensor package fail')


def get_test_list():
    prepare_package()
    L0_list = []
    with open('%s/libcutensor/test/cutensorContractionL0.sh' % download_to_path, 'r') as f1:
        for line in f1.readlines():
            L0_list.append(line.split('\n')[0])
    with open('%s/libcutensor/test/cutensorReductionL0.sh' % download_to_path, 'r') as f2:
        for line in f2.readlines():
            L0_list.append(line.split('\n')[0])
    with open('%s/libcutensor/test/cutensorElementwiseL0.sh' % download_to_path, 'r') as f3:
        for line in f3.readlines():
            L0_list.append(line.split('\n')[0])
    sm = get_sm()
    L0_ampere_list, L0_others_list = [], []
    for cmd in L0_list:
        if '-Pas - Pbs - Pcs - Pcompb' in cmd or '-Pas -Pbs -Pcs -Pcompt' in cmd or '-Pab -Pbb -Pcb -Pcomps' in cmd or '-Pac -Pbc -Pcc -Pcompt' in cmd:
            L0_ampere_list.append(cmd + ' -disableVerify -algo0 -disableWaitKernel')
        else:
            L0_others_list.append(cmd + ' -disableVerify -algo0 -disableWaitKernel')
    if sm >= '8.0':
        l0_list1 = L0_ampere_list + L0_others_list
        excute_list = [i for i in l0_list1 if i not in cutensor_skip_list]
        return excute_list
    else:
        excute_list = [i for i in L0_others_list if i not in cutensor_skip_list]
        return excute_list


@get_execute_time
def sanitizer_cutensor_case(tools_option):
    result = {}
    passed, failed = 0, 0
    log_name = case_config['SANITIZER_CUTENSOR']['LOG_NAME'] % (tools_option, tools_option)
    log_path = case_config['global']['env']['SANITIZER_CUTENSOR_LOG_PATH'] % tools_option
    mkdir(log_path)
    print('=========================================')
    if tools_option == 'racecheck':
        check_point = case_config['SANITIZER_CUTENSOR']['CHECK_POINT1']
    else:
        check_point = case_config['SANITIZER_CUTENSOR']['CHECK_POINT']
    check_point2 = case_config['SANITIZER_CUTENSOR']['CHECK_POINT2']
    L0_list = get_test_list()
    sanitizer_list = []
    for cmd in L0_list[::4]:
        if tools_option == 'memcheck':
            sanitizer_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s --leak-check full --report-api-errors no  %s' % (download_to_path, cuda_short_version, cuda_short_version, tools_option, cmd))
        elif tools_option == 'initcheck':
            sanitizer_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s %s' % (download_to_path, cuda_short_version, cuda_short_version, tools_option, cmd))
        elif tools_option == 'racecheck':
            sanitizer_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s -c 10 %s' % (download_to_path, cuda_short_version, cuda_short_version, tools_option, cmd))
        else:
            sanitizer_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/compute-sanitizer --tool %s %s' % (download_to_path, cuda_short_version, cuda_short_version, tools_option, cmd))
    for cmd2 in sanitizer_list:
        if tools_option == 'memcheck':
            # run_cmd(cmd2, 'run_sanitizer_cutensor------%s' % cmd2, 'passed', 'failed', log_name % tools_option, result, check_point1=check_point, check_point2=check_point2)
            check_result(cmd2, 'run_sanitizer_cutensor------%s' % cmd2, log_name, result, check_point, check_point2)
        else:
            # run_cmd(cmd2, 'run_sanitizer_cutensor------%s' % cmd2, 'passed', 'failed', log_name % tools_option, result, check_point1=check_point)
            check_result(cmd2, 'run_sanitizer_cutensor------%s' % cmd2, log_name, result, check_point)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/sanitizer_%s_cutensor.json' % (log_path, tools_option))
    return result, passed, failed


@get_execute_time
def ncu_cutensor_case():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NCU_CUTENSOR']['LOG_NAME']
    log_path = case_config['global']['env']['NCU_CUTENSOR_LOG_PATH']
    mkdir(log_path)
    L0_list = get_test_list()
    ncu_list = []
    for cmd in L0_list[::4]:
        ncu_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/ncu -c 10 %s' % (download_to_path, cuda_short_version, cuda_short_version, cmd))
    for cmd in ncu_list:
        if platform == 'x86':
            check_result(cmd, 'run_ncu_cmd--%s' % cmd, log_name, result, 'nan', 'n/a', 'error', 'ERROR', flag=2)
        else:
            check_result(cmd, 'run_ncu_cmd--%s' % cmd, log_name, result, 'nan', 'n/a', 'error', 'ERROR', flag=3)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/ncu_cutensor.json' % log_path)
    return result, passed, failed


@get_execute_time
def ncu_option_cutensor_case(ncu_option):
    result = {}
    passed, failed = 0, 0
    log_name = case_config['NCU_CUTENSOR']['LOG_NAME1'] % (ncu_option, ncu_option)
    log_path = case_config['global']['env']['NCU_OPTION_CUTENSOR_LOG_PATH'] % ncu_option
    mkdir(log_path)
    L0_list = get_test_list()
    ncu_list = []
    if ncu_option == 'single':
        options = case_config['NCU_OPTION']['OPTIONS1']
    elif ncu_option == 'replay':
        options = case_config['NCU_OPTION']['OPTIONS2']
    else:
        logger.info('please give the correctly ncu options')
        exit(2)
    for cmd in L0_list[::4]:
        ncu_list.append('cd %s/libcutensor/bin; export LD_LIBRARY_PATH=`pwd`/../lib:/usr/local/cuda-%s/lib64:$LD_LIBRARY_PATH; /usr/local/cuda-%s/bin/ncu -c 10 %s %s' % (download_to_path, cuda_short_version, cuda_short_version, options, cmd))
    for cmd1 in ncu_list:
        if platform == 'x86':
            check_result(cmd1, 'run_ncu_cmd--%s' % cmd1, log_name, result, 'n/a', 'error', 'ERROR', flag=2)
        else:
            check_result(cmd1, 'run_ncu_cmd--%s' % cmd1, log_name, result, 'n/a', 'error', 'ERROR', flag=3)
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/ncu_cutensor_%s.json' % (log_path, ncu_option))
    return result, passed, failed


@get_execute_time
def cupti_profile_injection_cutensor():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['LOG_NAME3']
    log_path = case_config['global']['env']['CUPTI_PROFILE_INJECTION_CUTENSOR_LOG_PATH']
    mkdir(log_path)
    L0_list = get_test_list()
    cupti_path = prepare_cupti(arch=platform)
    print('====================================')
    if platform == 'x86':
        cupti_run_path = '%s/target/linux-desktop-GNU_glibc_2_11_3-x64' % cupti_path
        bin_path = '%s' % download_to_path + '/libcutensor/bin'
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
        bin_path = '%s' % download_to_path + '/libcutensor/bin'
    else:
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-x86_64" % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-gcc_4_8_3_glibc_2_19_0-ppc64le" % cupti_path
        bin_path = '%s' % download_to_path + '/libcutensor/bin'
    lib_path = '%s' % download_to_path + '/libcutensor/lib'
    gpu_type = prepare_gpu()
    cupti_injection_list = []
    L0_list_1 = [i.split('/')[-1] for i in L0_list[::3]]
    for i in range(0, len(L0_list_1)):
        if cuda_short_version < '11.2':
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1'] % (cupti_run_path, lib_path, gpu_dict['%s' % gpu_type], i, L0_list_1[i])
        elif '11.2' <= cuda_short_version < '11.7':
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1_1'] % (cupti_run_path, lib_path, i, L0_list_1[i])
        else:
            step1_cmd1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['STEP1']['CMD1_2'] % (cupti_run_path, lib_path, i, L0_list_1[i])
        cupti_injection_list.append(step1_cmd1)
    # prepare cutensor bin
    os.chdir(cupti_run_path)
    if os.path.exists('cutensorTest'):
        logger.info('we had prepared the bin file, no need prepare again')
    else:
        prepare_cmd = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['PREPARE']['CMD'] % (bin_path, cupti_run_path)
        print(prepare_cmd)
        out = run_loc_cmd(prepare_cmd, timeout=7200)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)

    # run cupti inject cutensor case
    check_point = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT']
    check_point1 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT']
    check_point2 = case_config['CUPTI_PROFILE_INJECTION_MATH_LIB']['CHECK_POINT']
    for cmd1 in cupti_injection_list[::3]:
        if platform == 'x86':
            check_result(cmd1, 'run cupti injection case ---%s' % cmd1, log_name, result, check_point, check_point1, check_point2, flag=2)
        else:
            check_result(cmd1, 'run cupti injection case ---%s' % cmd1, log_name, result, check_point, check_point1, check_point2, flag=3)
        '''
        out = run_loc_cmd(cmd1, timeout=7200)
        save_log(log_name, cmd1, 'run_sanitizer_cutensor------%s' % cmd1, out['output'])
        print(out['output'])
        if out.succeeded and check_point not in out['output'] and check_point1 not in out['output'] and check_point2 not in out['output']:
            result['run_sanitizer_cutensor------%s' % cmd1] = 'passed'
            logger.info('we run the case--%s successful' % cmd1)
        else:
            result['run_sanitizer_cutensor------%s' % cmd1] = 'failed'
            logger.info('we run the case--%s failed' % cmd1)
        '''
    result1 = calculate_result(result)
    dict_to_json(result1, '%s/cupti_profile_cutensor.json' % log_path)
    return result, passed, failed


@get_execute_time
def cupti_trace_injection_cutensor():
    result = {}
    passed, failed = 0, 0
    log_name = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['LOG_NAME3']
    log_path = case_config['global']['env']['CUPTI_TRACE_INJECTION_CUTENSOR_LOG_PATH']
    mkdir(log_path)
    prepare_package()
    L0_list = get_test_list()
    cupti_path = prepare_cupti(arch=platform)
    if platform == 'x86':
        cupti_run_path = '%s/target/linux-desktop-GNU_glibc_2_11_3-x64' % cupti_path
        cupti_host_path = '%s/host/linux-desktop-GNU_glibc_2_11_3-x64' % cupti_path
    elif platform == 'arm':
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-arm" % cupti_path
            cupti_host_path = '%s/host/linux-desktop-glibc_2_19_0-arm' % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-t210-a64" % cupti_path
            cupti_host_path = '%s/host/linux-desktop-t210-a64' % cupti_path
    else:
        if cuda_short_version < '11.5':
            cupti_run_path = "%s/target/linux-desktop-glibc_2_19_0-x86_64" % cupti_path
            cupti_host_path = '%s/host/linux-desktop-glibc_2_19_0-x86_64' % cupti_path
        else:
            cupti_run_path = "%s/target/linux-desktop-gcc_4_8_3_glibc_2_19_0-ppc64le" % cupti_path
            cupti_host_path = '%s/host/linux-desktop-gcc_4_8_3_glibc_2_19_0-ppc64le' % cupti_path
    print(download_to_path)
    bin_path = '%s' % download_to_path + '/libcutensor/bin'
    lib_path = '%s' % download_to_path + '/libcutensor/lib'
    prepare_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['PREPARE']['CMD'] % (bin_path, cupti_run_path)
    prepare_cmd1 = prepare_cmd + ';' + 'cp -r %s/* %s' % (lib_path, cupti_run_path)
    print(prepare_cmd1)
    # prepare cutensor bin & json
    if os.path.exists('%s/cutensorTest' % cupti_run_path):
        logger.info('we had prepared the bin file, no need prepare again')
    else:
        prepare_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['PREPARE']['CMD'] % (bin_path, cupti_run_path)
        prepare_cmd1 = prepare_cmd + ';' + 'cp -r %s/* %s' % (lib_path, cupti_run_path)
        print(prepare_cmd1)
        out = run_loc_cmd(prepare_cmd1)
        if out.succeeded:
            logger.info('we prepare bin file successful')
        else:
            logger.info('we prepare bin file fail, exit.......')
            exit(2)
    prepare_cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['PREPARE']['CMD1'] % (cupti_host_path, 'cutensor', 'cutensor')
    # run_cmd(prepare_cmd1, 'run_prepare_cmd1', 'passed', 'failed', log_name, result)
    check_result(prepare_cmd1, 'run_prepare_cmd1', log_name, result)
    if cuda_short_version <= '11.4':
        dict1 = {"tests": []}
        dict2 = {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}
    else:
        dict1 = {"tests": []}
        dict2 = {"tracing-injection": {'common': {'verify': {'type': ['basic', 'stream', 'host-device-timestamp']}}}}
    for index, cmd in enumerate(L0_list[::4]):
        # dict1["tests"].append({"name": "%s" % cmd, "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "verify": {}})
        args_list = [x.strip() for x in cmd.split(" ")[1::] if x.strip() != '']
        if cuda_short_version <= '11.4':
            dict1["tests"].append({"name": "%s_%s" % (cmd.split(" ")[0].split("/")[-1], index), "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "exe_args": args_list})
        else:
            dict1["tests"].append({"name": "%s_%s" % (cmd.split(" ")[0].split("/")[-1], index), "exe": "%s" % cmd.split(" ")[0].split("/")[-1], "exe-args": args_list})
    if cuda_short_version <= '11.4':
        dict2.update(dict1)
    else:
        dict2["tracing-injection"].update(dict1)
    dict_to_json(dict2, '%s' % (cupti_host_path + '/' + 'test_cutensor.json'))
    # run cutensor case coverage
    if cuda_short_version <= '11.4':
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1']['CMD'] % (cupti_host_path, 'cutensor', 'cutensor')
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_1']['CMD'] % (cupti_host_path, 'cutensor', 'cutensor')
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_2']['CMD'] % (cupti_host_path, 'cutensor', 'cutensor')
    else:
        if platform == 'x86':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1']['CMD1'] % (cupti_host_path, 'cutensor', 'cutensor')
        elif platform == 'arm':
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_1']['CMD1'] % (cupti_host_path, 'cutensor', 'cutensor')
        else:
            step1_cmd = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP1_2']['CMD1'] % (cupti_host_path, 'cutensor', 'cutensor')
    # run_cmd(step1_run, 'run_trace_injection', 'passed', 'failed', log_name, result)
    check_result(step1_cmd, 'run_trace_injection', log_name, result)
    # caluate the result
    if cuda_short_version <= '11.4':
        cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD1'] % (cupti_host_path, 'cutensor', log_path, 'cutensor')
        cmd2 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD2'] % (cupti_host_path, 'cutensor', log_path, 'cutensor')
    else:
        cmd1 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD1_1'] % (cupti_host_path, 'cutensor', log_path, 'cutensor')
        cmd2 = case_config['CUPTI_TRACE_INJECTION_MATH_LIB']['STEP2']['CMD2_1'] % (cupti_host_path, 'cutensor', log_path, 'cutensor')
    out1 = run_loc_cmd(cmd1)
    if out1.succeeded:
        result['failed'] = int(out1['output'])
    else:
        result['failed'] = 0
    out2 = run_loc_cmd(cmd2)
    if out2.succeeded:
        result['passed'] = int(out2['output'])
    print(result)
    dict_to_json(result, '%s/cupti_trace_cutensor_coverage.json' % log_path)
    return result, passed, failed


example_text = """
We use compute-sanitizer/nsight compute/cupti to run cutensor case
Example:
python run_cutensor_case.py -t all # run all the tools check for centensor
python run_cutensor_case.py -t sanitizer -o racecheck
python run_cutensor_case.py -t ncu -o replay
python run_cutensor_case.py -t cupti -o trace
sanitizer has four options: racecheck, initcheck, memcheck, synccheck
cupti: will run trace injection and profile injection
ncu:  run replay/single or ncu
"""
reversion = '1.0'


if __name__ == '__main__':

    parser = argparse.ArgumentParser(
        description=None, epilog=example_text,
        formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version=reversion)
    parser.add_argument(
        "-t", "--tools", required=True,
        help='Specify tools to run cutensor case')
    parser.add_argument(
        "-o", "--option", required=False,
        help='Specify the option of the tools will used')
    args = parser.parse_args()
    if args.tools == 'sanitizer':
        if args.option:
            sanitizer_cutensor_case(args.option)
        else:
            logger.info('please give correct option when use sanitizer to run cutensor case')
    elif args.tools == 'ncu':
        if args.option == 'single':
            ncu_option_cutensor_case('single')
        elif args.option == 'replay':
            ncu_option_cutensor_case('replay')
        else:
            ncu_cutensor_case()
    elif args.tools == 'all':
        for option in ['memcheck', 'initcheck', 'synccheck', 'racecheck']:
            sanitizer_cutensor_case(option)
        cupti_profile_injection_cutensor()
        cupti_trace_injection_cutensor()
        ncu_cutensor_case()
        ncu_option_cutensor_case('replay')
        ncu_option_cutensor_case('single')
    elif args.tools == 'cupti':
        if args.option == 'profile':
            cupti_profile_injection_cutensor()
        elif args.option == 'trace':
            cupti_trace_injection_cutensor()
        else:
            logger.info('please give the correct cupti injection option')
    else:
        logger.info('please give correct tools to run cutensor')
