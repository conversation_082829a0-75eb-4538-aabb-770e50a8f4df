import getopt
from gdb_utils import *


def usage():
    script_name = sys.argv[0]
    print('python3 %s -h|-s <spawn command>' % script_name)
    print('''
    -h help
    -s spawn command
    ''')
    exit()


if __name__ == '__main__':
    # get options
    try:
        options, args = getopt.getopt(sys.argv[1:], "hs:v:", ['help', 'spawn=', 'cuda_version='])
        for name, value in options:
            if name in ('-h', '--help'):
                usage()
            elif name in ('-s', '--spawn'):
                spawn_command = value
            elif name in ('-v', '--cuda_version'):
                cuda_version = value
            else:
                assert False, "unhandled option"
    except getopt.GetoptError as err:
        print(err)
        usage()
        sys.exit(2)

    c = Gdb(spawn_command)
    # testing part
    if cuda_version >= '11.6':
        c.execute_and_compare("show cuda software_preemption", "Software preemption debugging is auto")
        c.execute_and_compare("target remote :2345")
        c.execute_and_compare("set cuda break_on_launch all")
        c.execute_and_compare("c", "\d+\s+int bx = blockIdx.x;")
        c.execute_and_compare("n", "\d+\s+int by = blockIdx.y;")
        c.execute_and_compare("si", "\s+\d+\s+int by = blockIdx.y;")
        c.execute_and_compare("c", "Switching focus to CUDA kernel")
        c.execute_and_compare("set cuda break_on_launch none")
        c.execute_and_compare("c", "exited normally")
        c.execute_and_compare("quit")
    else:
        c.execute_and_compare("show cuda software_preemption", "Software preemption debugging is auto")
        c.execute_and_compare("target remote :2345")
        c.execute_and_compare("set cuda break_on_launch all")
        c.execute_and_compare("c", "44\s+int bx = blockIdx.x;")
        c.execute_and_compare("n", "45\s+int by = blockIdx.y;")
        c.execute_and_compare("si", "\s+45\s+int by = blockIdx.y;")
        c.execute_and_compare("c", "Switching focus to CUDA kernel")
        c.execute_and_compare("set cuda break_on_launch none")
        c.execute_and_compare("c", "exited normally")
        c.execute_and_compare("quit")
