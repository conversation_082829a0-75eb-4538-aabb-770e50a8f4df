Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2810581
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - 'Fail to compile'
      cmd: |
            mkdir ${TEST_WS}/2810581
            cd ${TEST_WS}/2810581
            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/2810581/exception_err.cu
            /usr/local/cuda-${TK_BRANCH}/bin/nvcc -g -G -o exception_err exception_err.cu
            if [ -f "exception_err" ]; then
               echo "Compile successfully!"
            else
               echo "[Error]: Fail to compile! Please check!"
            fi
    - fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            cd ${TEST_WS}/2810581
            CUDA_DEVICE_WAITS_ON_EXCEPTION=1 nohup ./exception_err &
            process_id=$(ps -ef|grep exception_err|grep -v grep|awk '{print $2}')
            echo $process_id
            sleep 3
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_attach_when_enable_CUDA_DEVICE_WAITS_ON_EXCEPTION.py -s "cuda-gdb --pid=$process_id"
            ps -ef|grep exception_err|grep -v grep|awk '{print $2}'|xargs kill -9
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
