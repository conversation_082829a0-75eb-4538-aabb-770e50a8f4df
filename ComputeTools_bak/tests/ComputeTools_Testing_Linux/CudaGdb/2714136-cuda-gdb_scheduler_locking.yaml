Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2714136
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_scheduler_locking.py -s "cuda-gdb"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
