Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 3110358
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            vgpu=`echo "${VGPU}" | tr [:upper:] [:lower:]`
            if [ "$vgpu" = "1" ]; then
              echo "&&&& NA"
            fi
            if [ "$vgpu" = "n" ]; then
              echo "&&&& NA"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            wget --no-check-certificate *******************************/Compute_Tools/ComputeToolsTest/P1072_T3110358/SetMem-Readonly.cu
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            SM=`$HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/deviceQuery|grep "CUDA Capability"|awk -F":" '{print $2}'|sed 's/^[ \t]*//;s/\.//'|head -1`
            echo "SM:$SM"
            /usr/local/cuda-${TK_BRANCH}/bin/nvcc -g -G -lcuda -lcudart -arch sm_${SM} -o SetMem-Readonly SetMem-Readonly.cu
            if [ -f "SetMem-Readonly" ]; then
               echo "Compile successfully!"
            else
               echo "&&&& FAIL to compile! Please check!"
            fi
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_write_read-only_device_memory.py -s "cuda-gdb ./SetMem-Readonly"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
