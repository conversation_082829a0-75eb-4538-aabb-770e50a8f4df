Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1820922
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/debug/
            if [ -x "UnifiedMemoryPerf" ]; then
                echo "&&&& PASS"
            else
                echo "&&&& FAIL"
            fi
            echo $SUDO_PASSWORD | sudo -S sh -c "echo 0 >/proc/sys/kernel/yama/ptrace_scope"
    - expected_returncode: 0
      timeout: 300
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/debug/
            nohup ./UnifiedMemoryPerf &
            process_id=$(ps -ef|grep UnifiedMemoryPerf|grep -v grep|awk '{print $2}')
            echo $process_id
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_remote_debug_attached_process1.py -s "cuda-gdb --pid=$process_id" -v ${TK_BRANCH}
    - expected_returncode: 0
      timeout: 300
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/debug/
            nohup cuda-gdbserver :2345 ./UnifiedMemoryPerf &
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_remote_debug_attached_process2.py -s "cuda-gdb" -v ${TK_BRANCH}
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
