Settings:
  env: ~
  enabled: True
  print_instance: True

Tests: !!seq
  - name: cupti_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: debugger_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: memcheck_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: nvprof_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: rebel_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
  - name: sanitizer_smoke
    setup: ~
    call: ${yaml_dir_path}/${name}.yaml
    teardown: ~
