Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1902707
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml
            grep 'smoke_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/tool_smoke.py
            if [ "$?" = 0 ]; then
                sed -i '/smoke_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/tool_smoke.py
                sed -i "/yaml_mgr = /ismoke_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/tool_smoke.py
            else
                sed -i "/yaml_mgr = /ismoke_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/tool_smoke.py
            fi
    - expected_returncode: 0
      timeout: 10800s
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/tool_smoke.py -t rebel -d rebel -r rebel
    - expected_returncode: 0
      fail_if_contains: '0 PASSED'
      cmd: |
          date1=`grep DATE: ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml |awk -F ' ' '{print $2}'`
          grep 'Test results: ' ~/tesla_automation/smoke_device${CUDA_VISIBLE_DEVICES}_$date1/rebel_smoke.txt | awk -F ',' '{print $1}'|awk -F ':' '{print $2}'
    - expected_returncode: 0
      expected_contains: '0'
      cmd: |
            date1=`grep DATE: ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/smoke.yaml |awk -F ' ' '{print $2}'`
            grep 'Test results: ' ~/tesla_automation/smoke_device${CUDA_VISIBLE_DEVICES}_$date1/rebel_smoke.txt | awk -F ',' '{print $2}'|awk -F ' ' '{print $1}'
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
