Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2074639
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      expected_regex_matches_many:
        - '&&&& PASS'
      fail_if_regex_matches_any:
        - '&&&& FAIL'
      cmd: |
            export hpc_folder=hpc_sdk
            export PGI=/opt/nvidia/$hpc_folder
            export pgi_version=24.1
            export pgi_version1=241
            distro=`echo ${DISTRO} | tr '[:upper:]' '[:lower:]'`
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [ "$VGPU" = "True" -a "$platform" = "x86" ]; then
                if [ $distro = 'ubuntu' ]; then
                    echo $SUDO_PASSWORD | sudo -S ${PKG_CMD} install -y nfs-kernel-server
                fi
                if [ $distro = 'rhel' ] || [ $distro = 'centos' ]; then
                    echo $SUDO_PASSWORD | sudo -S ${PKG_CMD} install -y nfs-utils
                fi
                echo "Mount network disk to /opt/nvidia"
                echo $SUDO_PASSWORD | sudo -S mkdir -p /opt/nvidia
                echo $SUDO_PASSWORD | sudo -S umount /opt/nvidia
                echo $SUDO_PASSWORD | sudo -S mount 10.19.173.11:/opt/nvidia /opt/nvidia
            else
                platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
                if ! ls /opt/nvidia/$hpc_folder/*/$pgi_version 1> /dev/null 2>&1; then
                        echo "Install PGI.."
                        mkdir pgi
                        cd pgi
                        rm -rf *
                        if [ "$platform" = "x86" ]; then
                            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/pgi/$platform/nvhpc_2024_${pgi_version1}_Linux_x86_64_cuda_multi.tar.gz
                        fi
                        if [ "$platform" = "arm" ]; then
                            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/pgi/$platform/nvhpc_2024_${pgi_version1}_Linux_aarch64_cuda_multi.tar.gz
                        fi
                        tar xvf nvhpc_2024_${pgi_version1}_Linux_*.tar.gz
                        cd `ls|grep tar.gz| cut -d"." -f1`
                        sed -i "s/paassword/$SUDO_PASSWORD/g" ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py
                        python3 ${SCRIPTS_FOLDER}/cuda-gdb_install_pgi.py -s "sudo ./install"
                fi
            fi
            echo "Preparing env for PGI"
            if [ "$platform" = "x86" ]; then
                export PATH=/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_x86_64/$pgi_version/compilers/man/:"$MANPATH" 
            fi
            if [ "$platform" = "arm" ]; then
                export PATH=/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_aarch64/$pgi_version/compilers/man/:"$MANPATH"
            fi
            if [ "$platform" = "power" ]; then
                export PATH=/usr/local/cuda/bin:/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/bin:/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/include:"$PATH"
                export MANPATH=/opt/nvidia/$hpc_folder/Linux_ppc64le/$pgi_version/compilers/man/:"$MANPATH"
            fi
            mkdir ${TEST_WS}/2594437
            cd ${TEST_WS}/2594437
            rm -rf *
            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/2594437/Makefile
            if [ ! -n "$VISIBLE_DEVICE_SM" ]; then
                TARGET_ARCH=$(uname -m)
                [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
                cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/debug/
                VISIBLE_DEVICE_SM=`./deviceQuery|grep "CUDA Capability"|awk -F":" '{print $2}'|sed 's/^[ \t]*//;s/\.//'|head -1`
                cd -
            fi
            echo "VISIBLE_DEVICE_SM:$VISIBLE_DEVICE_SM"
            if [ "${VISIBLE_DEVICE_SM}" = "75" ]; then
                sed -i "s/cc70/cc75/" ${TEST_WS}/2594437/Makefile
            fi
            if [ "${VISIBLE_DEVICE_SM}" -ge "80" ]; then
                sed -i "s/cc70/cc80/" ${TEST_WS}/2594437/Makefile
            fi
            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/2594437/bug.f90
            make
            if [ -f "a.out" ]; then
                echo "&&&& PASS"
            else
                echo "&&&& FAILED"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
        - '&&&& FAIL'
      cmd: |
            cd ${TEST_WS}/2594437
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_support_calls_to_cudaHostRegister.py -s "cuda-gdb ./a.out"
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
