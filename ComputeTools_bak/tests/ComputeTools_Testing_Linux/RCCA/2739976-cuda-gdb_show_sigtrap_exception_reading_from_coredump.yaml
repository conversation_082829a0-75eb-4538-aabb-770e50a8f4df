Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2739976
  log_parser_type: GDB
  nvbugs_module: ["CUDA GDB", "Unified Debugger"]
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            mkdir ${TEST_WS}/2739976
            cd ${TEST_WS}/2739976
            rm -rf a.out
            wget --no-check-certificate *******************************/Automation/CUDA_Linux/Sources/ComputeTools/2739976/test.cu
            /usr/local/cuda-${TK_BRANCH}/bin/nvcc -g -G test.cu
            if [ -x "a.out" ]; then
                echo "&&&& PASS"
            else
                echo "&&&& FAIL"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            cd ${TEST_WS}/2739976
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_show_sigtrap_exception_reading_from_coredump.py -s "cuda-gdb ./a.out"
    - expected_returncode: 0
      fail_if_regex_matches_any:
          - '&&&& FAIL'
      cmd: |
            cd ${TEST_WS}/2739976
            rm -rf core_*.nvcudmp
            CUDA_ENABLE_COREDUMP_ON_EXCEPTION=1 ./a.out
            coredump_file=`ls -lrt|tail -1|awk '{print $9}'`
            if [[ $coredump_file =~ ^core_[0-9]+_`hostname`_[0-9]+.nvcudmp$ ]];
            then
                  echo "&&&& PASS"
            else
                  echo "&&&& FAIL"
            fi
            sed -i "s/COREDUMP_FILE/$coredump_file/g" ${SCRIPTS_FOLDER}/cuda-gdb_show_sigtrap_exception_reading_from_coredump2.py
            python3 ${SCRIPTS_FOLDER}/cuda-gdb_show_sigtrap_exception_reading_from_coredump2.py -s "cuda-gdb"
            sed -i "s/$coredump_file/COREDUMP_FILE/g" ${SCRIPTS_FOLDER}/cuda-gdb_show_sigtrap_exception_reading_from_coredump2.py
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
