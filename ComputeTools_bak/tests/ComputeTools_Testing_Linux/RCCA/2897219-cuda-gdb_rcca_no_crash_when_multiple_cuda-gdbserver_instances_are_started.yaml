Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - TOOL_BINARY_ABSPATH: /usr/local/cuda-${TK_BRANCH}/bin/cuda-gdbserver # Define tool binary path
    - SAMPLE_FOLDER_ABSPATH: ${HOME}/NVIDIA_CUDA-${TK_BRANCH}_Samples/ # Define sample installation path with cuda version
    - SAMPLE_APP_NAME: '0_Introduction/matrixMul' # Define which sample app
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2897219
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |

            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            # Have to define sample_name and sample_app_abs here for TARGET_ARCH
            sample_name=$(echo ${SAMPLE_APP_NAME} | awk -F/ '{print $2}')
            sample_app_abs=${SAMPLE_FOLDER_ABSPATH}/bin/x86_64/linux/debug/${sample_name}

            echo -e "\ncurrent PID:"
            # Calling Tool CMD with specific sample app
            python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.run_cmd_in_new_process('${TOOL_BINARY_ABSPATH} :2345  ${sample_app_abs}', async_flag=True)"

            # Unlcoking CUDA GDB by removing file
            rm /tmp/cuda-dbg/cuda-gdb.lock

            # Try getting tools version
            cuda_gdb_version=`python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.show_tool_version('${TOOL_BINARY_ABSPATH} --version', r'CUDA gdbserver\s+(\d+\.\d+)')"`
            echo -e "\nFind CUDA version: ${cuda_gdb_version}"

            # Compare whether figure is expected and get ret value
            ret=`python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.is_equal_string('${cuda_gdb_version}', '${TK_BRANCH}')"`

            # Print specific log based on ret value of last step
            python -c "import sys;sys.path.append('${SCRIPTS_FOLDER}');import common_utils;common_utils.print_log_result('${ret}')"

            # Recycle resources(e.g., subprocess generated)

  teardown: !!seq
    - expected_returncode: 0
      cmd: |
