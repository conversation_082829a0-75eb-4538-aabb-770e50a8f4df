Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: False
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1821144
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml
            grep 'memcheck_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_memcheck_case.py
            if [ "$?" = 0 ]; then
                sed -i '/memcheck_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_memcheck_case.py
                sed -i "/yaml_mgr = /imemcheck_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_memcheck_case.py
            else
                sed -i "/yaml_mgr = /imemcheck_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_memcheck_case.py
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "arm" ]; then
                echo "&&&& NA"
            fi
            vgpu=`echo "${VGPU}" | tr [:upper:] [:lower:]`
            if [ "$vgpu" = "1" ]; then
            echo "&&&& NA"
            fi
            if [ "$vgpu" = "n" ]; then
            echo "&&&& NA"
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#bin/x86_64#bin/ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            rm -r ~/tesla_automation/t744
            mkdir ~/tesla_automation/t744
            cd ~/tesla_automation/t744
            RES_ROOT_REMOTE1=http://cqauser:<EMAIL>/Compute_Tools/ComputeToolsTest
            wget ${RES_ROOT_REMOTE1}/t744/t744.cu

    - expected_returncode: 0
      na_if_contains: "do not support cuda memcheck if sm is more than 8.6"
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_memcheck_case.py -e memcheck_racecheck
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/memcheck_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/memcheck_device${CUDA_VISIBLE_DEVICES}/memcheck_racecheck_$date1/memcheck_racecheck.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
