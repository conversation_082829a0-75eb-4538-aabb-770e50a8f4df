Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
  enabled: True
  print_instance: False

Test:
  name: ${name}
  devtest_id: 1821156
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            gpu_family=`echo "${GPU_FAMILY}" | tr '[:upper:]' '[:lower:]'`
            if [[ "${gpu_family}" != "maxwell" && "${gpu_family}" != "pascal" && "${gpu_family}" != "volta" ]]; then
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr '[:upper:]' '[:lower:]'`
            if [[ "$platform" = "dgx" || "$platform" = "power" || "$platform" = "x86" ]]; then
                echo "&&&& Supported Platform $PLATFORM"
            else
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
        - '(Fail|FAIL|fail|ERROR|Error)'
      expected_regex_matches_many:
        - '&&&& PASS'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo ${SUDO_PASSWORD} | sudo -S rm -rf asyncAPI*.pdm
            export __CUDA_PM_INTERNAL=1
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --analysis-metrics --replay-mode application -f -o asyncAPI1.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --events all --replay-mode application -f -o asyncAPI2.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --metrics all --replay-mode application -f -o asyncAPI3.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --events all --replay-mode application -f -o asyncAPI4.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --metrics all --replay-mode application -f -o asyncAPI5.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --source-level-analysis global_access,shared_access,branch,instruction_execution,pc_sampling --replay-mode application -f -o asyncAPI6.pdm ./asyncAPI
            if [ -f "asyncAPI1.pdm" ] && [ -f "asyncAPI2.pdm" ] && [ -f "asyncAPI3.pdm" ] && [ -f "asyncAPI4.pdm" ] && [ -f "asyncAPI5.pdm" ] && [ -f "asyncAPI6.pdm" ]; then
               echo "&&&& PASS"
            else
               echo "&&&& FAIL"
            fi
    - expected_returncode: 0
      fail_if_regex_matches_any:
        - '(Fail|FAIL|fail|ERROR|Error)'
      expected_regex_matches_many:
        - '&&&& PASS'
      cmd: |
            TARGET_ARCH=$(uname -m)
            [[ "$TARGET_ARCH" == "aarch64" ]] && TARGET_ARCH="sbsa"
            cd $HOME/NVIDIA_CUDA-${TK_BRANCH}_Samples/bin/${TARGET_ARCH}/linux/release/
            echo ${SUDO_PASSWORD} | sudo -S rm -rf asyncAPI*.pdm
            export __CUDA_PM_INTERNAL=0
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --analysis-metrics --replay-mode application -f -o asyncAPI7.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --events all --replay-mode application -f -o asyncAPI8.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode on --metrics all --replay-mode application -f -o asyncAPI9.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --events all --replay-mode application -f -o asyncAPI10.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --metrics all --replay-mode application -f -o asyncAPI11.pdm ./asyncAPI
            echo ${SUDO_PASSWORD} | sudo -E -S LD_LIBRARY_PATH=$LD_LIBRARY_PATH /usr/local/cuda-${TK_BRANCH}/bin/nvprof --aggregate-mode off --source-level-analysis global_access,shared_access,branch,instruction_execution,pc_sampling --replay-mode application -f -o asyncAPI12.pdm ./asyncAPI
            if [ -f "asyncAPI7.pdm" ] && [ -f "asyncAPI8.pdm" ] && [ -f "asyncAPI9.pdm" ] && [ -f "asyncAPI10.pdm" ] && [ -f "asyncAPI11.pdm" ] && [ -f "asyncAPI12.pdm" ]; then
               echo "&&&& PASS"
            else
               echo "&&&& FAIL"
            fi
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
