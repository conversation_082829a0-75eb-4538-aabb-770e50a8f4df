Settings:
  env: !!seq
    - CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES}"
    - DEVICE_ID: ${DEVICE_ID}
  enabled: False
  print_instance: False

Test:
  name: ${name}
  devtest_id: 2450769
  setup: ~
  steps: !!seq
    - expected_returncode: 0
      na_if_contains: "&&&& NA"
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [[ "$platform" = "arm" || "$platform" = "p9" ]]; then
                echo "&&&& NA"
            fi
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
            mig=grep 'MIG:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
            host_home=`echo ~`
            sed -i "s/${mig}/MIG: 4_2/" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
    - expected_returncode: 0
      cmd: |
            script_path=${SCRIPT_DIR}
            ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/modify_yaml.sh ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
            grep 'mig_yaml = '  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_mig_case.py
            if [ "$?" = 0 ]; then
                sed -i '/mig_yaml = /d' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_mig_case.py
                sed -i "/yaml_mgr = /imig_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_mig_case.py
            else
                sed -i "/yaml_mgr = /imig_yaml = '${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml'"  ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_mig_case.py
            fi
    - expected_returncode: 0
      cmd: |
            platform=`echo "${PLATFORM}" | tr [:upper:] [:lower:]`
            if [ "$platform" = "power" ]; then
                sed -i "s#x86_64#ppc64le#" ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml
            fi
    - expected_returncode: 0
      cmd: |
            python ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/scripts/run_mig_case.py -e cupti_7_1
    - expected_returncode: 0
      expected_contains: '"failed": 0'
      cmd: |
            date1=`grep 'DATE:' ${SCRIPT_DIR}/tesla_automation_test/ComputeTools/yaml/mig_case.yaml |awk -F ':' '{print $2}'|sed 's/^[ \t]*//g'`
            echo $date1
            cat ~/tesla_automation/mig_case/7_1_cupti_result_$date1/*.json
  teardown: !!seq
    - expected_returncode: 0
      cmd: |
