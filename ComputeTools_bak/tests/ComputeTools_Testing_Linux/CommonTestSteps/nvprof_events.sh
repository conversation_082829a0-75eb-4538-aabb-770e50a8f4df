#!/bin/bash

echo "Usage: $0 [app|list|cuda] [device_ID] [debug|release] [32]"
OS=`uname -s | tr [:upper:] [:lower:]`
Arch=`uname -m`
if [ "$4" == "32" ] || [ "$Arch" == "i386" ]; then
	Arch=i686
fi
if [ `whoami` != "root" ] && [ "$OS" == "darwin" ]; then {
	echo "Due to bug 1031488, please switch to root to run this script!!!"
	exit; }
fi
cudaPath=/usr/local/cuda
currentDir=`pwd`
logPath=$currentDir/log/prof
if [ ! -e "$logPath" ]; then
	mkdir -p $logPath
fi
binDir=$cudaPath/samples/bin/$Arch/$OS/release
if [ "$3" == "debug" ]; then
	binDir=$cudaPath/samples/bin/$Arch/$OS/debug
fi
queryApp=$binDir/deviceQuery
if [ "$Arch" = "x86_64" ] && [ "$OS" == "linux" ]; then
	export LD_LIBRARY_PATH=$cudaPath/lib64:$LD_LIBRARY_PATH
elif [ "$OS" == "linux" ]; then
	export LD_LIBRARY_PATH=$cudaPath/lib:$LD_LIBRARY_PATH
else
	export DYLD_LIBRARY_PATH=$cudaPath/lib:$DYLD_LIBRARY_PATH
fi

cd $binDir		# for bug 1012446
devices=`$queryApp | grep -oE NumDevs\ \=\ .`
devices=${devices: -1}
if [ "$1" != "" ]; then {
	case "${1:0:4}" in
		"list" ) 
			if [ -e $currentDir/$1 ]; then {
				apps=`cat $currentDir/$1`
				logFile=$logPath/event-$1-$$.log
				echo -n "&&&& Collecting events for cuda samples in $1" | tee $logFile; }
			else {
				echo "Couldn't find $1 in $currentDir!!!"
				exit; }
			fi;;
		"cuda" )
			if [ "$OS" == "darwin" ]; then 
				apps=`file * | grep Mach | grep executable | cut -d: -f1`
			else
				apps=`file * | grep "LSB" | cut -d: -f1`
			fi
			logFile=$logPath/event-cuda-$$.log
			echo -n "&&&& Collecting events for all cuda samples" | tee $logFile;;
		* 		)
			if [ -e "$1" ]; then {
				apps=$1
				logFile=$logPath/event-$apps-$$.log
				echo -n "&&&& Collecting events for cuda sample $apps" | tee $logFile; }
			else {
				echo "Couldn't find cuda sample $1 in `pwd`!!!"
				exit; }
			fi;;
	esac; }
else {
	apps=`cat $currentDir/list`
	logFile=$logPath/event-list-$$.log
	echo -n "&&&& Collecting events for cuda samples in list" | tee $logFile; }
fi

if [ "$2" != "" ] && [[ "${2:0:1}" < "$devices" ]]; then
    device=$2
else
    device=0
fi
export CUDA_VISIBLE_DEVICES=$device
echo " on device $device" >> $logFile
echo >> $logFile

# get events for target $device
events=`nvprof --query-events | cut -d: -f1 | grep -vE "Available|Name|Device|Domain" | tr -d [:blank:] | tr -s '\n' ','`
length=${#events}
events=${events:0:$((length-1))}

if [ "$apps" == "" ]; then
	apps=alignedTypes
fi
t=1
Graphics="bicubicTexture bilateralFilter bindlessTexture boxFilter cudaDecodeGL fluidsGL FunctionPointers grabcutNPP imageDenoising Mandelbrot marchingCubes nbody oceanFFT particles postProcessGL randomFog recursiveGaussian simpleCUDA2GL simpleGL simpleTexture3D smokeParticles SobelFilter volumeFiltering volumeRender"
filterOut="deviceQuery deviceQueryDrv simpleAssert simpleP2P bandwidthTest p2pBandwidthLatencyTest"
for testApp in $apps; do
    if [ ! -e "$binDir/$testApp" ]; then {
        echo "&&&&----Test #$t: $testApp does not exist!----" | tee -a $logFile
        t=$((t+1))
        continue;}
    fi
    if echo $filterOut | grep -qw $testApp; then {
        echo "&&&&----Test #$t: $testApp will be waived!----" | tee -a $logFile
        t=$((t+1))
        continue;}
    fi
	echo "&&&&----Start collecting events for application #$t: $testApp----" | tee -a $logFile
    rm -f *.tmp
    if echo $Graphics | grep -qw $testApp; then {
		nvprof --events "$events" -o $logPath/"$testApp"_nvprof_events -s ./$testApp $option 2>&1 | tee -a output.tmp &
        sleep 10
        if [ "$testApp" == "grabcutNPP" ]; then
            while ps acx | grep grabcutNPP | grep R+; do
                sleep 10
            done
        fi
		echo "sending ESC to $testApp" >> $logFile
        if [ "$OS" == "darwin" ]; then {
            echo "set testapp to \"$binDir/$testApp\"" > quit-gl.tmp
            cat $currentDir/quit-gl-mac >> quit-gl.tmp
            osascript quit-gl.tmp; }
        else
            $currentDir/xsendkey -window `wmctrl -l | grep -E "fps|CUDA|NPP|Random" | cut -d" " -f1` Escape
        fi
        sleep 5; }
    else
		nvprof --events "$events" -o $logPath/"$testApp"_nvprof_events -s ./$testApp $option 2>&1 | tee -a output.tmp
	fi
    cat output.tmp >> $logFile
    if grep -E "==\ Error:|No\ kernels\ were\ profiled" output.tmp; then
        echo "&&&& FAILED" | tee -a $logFile
    else
        echo "&&&& PASSED" | tee -a $logFile
    fi
	echo "&&&&----Finish collecting events for application #$t: $testApp----" | tee -a $logFile
	echo >> $logFile
    t=$((t+1))
done
rm -f *.dat *.jpg *.pgm *.ppm *.bin *.csv *.txt *.bmp *.flo *.send *.recv
unset CUDA_VISIBLE_DEVICES
exit
