  - name: enable HWS if test app is win10_64_hws or win11 by default
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers
      name: HwSchMode
      data: 00000002
      type: dword
    register: hws_enable
    when: '("WIN10" in "{{ app }}" and "H<PERSON>" in "{{ app }}") or ("WIN11" in "{{ app }}" and "OFF" not in "{{ app }}")'

  - name: disable HWS for win10 by default or win11 force disable
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers
      name: HwSchMode
      state: absent
    register: hws_disable
    when: '("WIN10" in "{{ app }}" and "HWS" not in "{{ app }}") or ("WIN11" in "{{ app }}" and "OFF" in "{{ app }}")'
  
  - name: enable non_admin profiling permission for wsl/non_admin
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Services\nvlddmkm\Global\NVTweak
      name: RmProfilingAdminOnly
      data: 00000000
      type: dword
    register: nonAdmin_profiling_enable
    when: '"WSL" in "{{ app }}" or ("nonadmin" in "{{ taskExtraArgs | lower}}" and "rmprofilingadminonly=default" not in "{{ taskExtraArgs | lower}}")'
  
  - name: disable non_admin profiling permission for non_admin negative test
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Services\nvlddmkm\Global\NVTweak
      name: RmProfilingAdminOnly
      data: 00000001
      type: dword
    register: nonAdmin_profiling_disable
    when: '"WSL" not in "{{ app }}" and ("nonadmin" in "{{ taskExtraArgs | lower}}" and "rmprofilingadminonly=default" in "{{ taskExtraArgs | lower}}")'
  
  - name: enable DMA remapping for wsl IOMMU feature
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers\Smm
      name: ForceDmaRemapping
      data: 000000001
      type: dword
    register: DmaRemapping_Enable
    when: '"WSL" in "{{ app }}" and "RemappingOn" in "{{ chip }}"'
  
  - name: disable DMA remapping for non_wsl and wsl_iommu disable test
    win_regedit:
      path: HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers\Smm
      name: ForceDmaRemapping
      data: 000000000
      type: dword
    register: DmaRemapping_Disable
    when: '"WIN11_64" in "{{ app }}" and "RemappingOn" not in "{{ chip }}"'


  # - name: set reboot flag
  #   set_fact:
  #     reboot_flag: true
  #   when: hws_enable.changed or hws_disable.changed or nonAdmin_profiling_enable.changed or nonAdmin_profiling_disable.changed or DmaRemapping_Enable.changed or DmaRemapping_Disable.changed

  #reboot immediately due to MCDM bug(nonadmin failed with setting order nonadmin regkey setting + MCDM enable --> reboot)
  - name: reboot machine if regkey changes
    win_reboot: 
      reboot_timeout: 1800
    when: hws_enable.changed or hws_disable.changed or nonAdmin_profiling_enable.changed or nonAdmin_profiling_disable.changed or DmaRemapping_Enable.changed or DmaRemapping_Disable.changed