- name: convert colossus os version to short version
  vars:
    os_map:
      'windows10-enterprise-21h2-19044-uefi': 'WIN10_64_21H2'
      'windows11-enterprise-21h2-22000-uefi': 'WIN11_64_21H2'
      'windows11-enterprise-23h2-22631-uefi': 'WIN11_64_23H2'
      'windows-11-24h2-26100-x86_64-standard-uefi': 'WIN11_64_24H2'
      'ubuntu-22.04-desktop-uefi': 'UBUNTU2204_64'
      'ubuntu-20.04-desktop-uefi': 'UBUNTU2004_64'
      'rhel-9.1-standard-aarch64-uefi': 'RedHat91_64'

  block:
    - name: get the os short version map
      set_fact:
        os_short: "{{ os_map[osinfo] if osinfo in os_map.keys() else
                     'WIN10_64' if 'windows10' in osinfo else
                     'WIN11_64' if 'windows11' in osinfo else
                     'UBUNTU_64' if 'ubuntu' in osinfo else
                     'RedHat' if 'redhat' in osinfo else osinfo }}"

    - debug:
        msg: "osinfo: {{ osinfo }} os_short: {{ os_short }}"
