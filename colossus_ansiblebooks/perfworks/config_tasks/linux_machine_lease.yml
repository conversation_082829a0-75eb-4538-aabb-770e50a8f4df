- name: Enable root SSH permission
  hosts: all
  become: yes
  tasks:
    - name: Set root password
      user:
        name: root
        password: "{{ 'devtoolsqa123' | password_hash('sha512') }}"
        update_password: always
    
    - name: remove /etc/ssh/sshd_config.d/00-ansible_system_role.conf
      file:
        path: '/etc/ssh/sshd_config.d/00-ansible_system_role.conf'
        state: absent

    - name: Update SSH configuration for root login
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^PermitRootLogin'
        line: 'PermitRootLogin yes'
      notify:
        - Restart SSH service

  handlers:
    - name: Restart SSH service
      service:
        name: sshd
        state: restarted