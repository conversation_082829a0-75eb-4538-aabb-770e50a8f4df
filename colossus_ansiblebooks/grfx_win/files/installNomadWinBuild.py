import os,sys
import subprocess
import re
import datetime

root = r"C:\nomadBuilds"
scriptDir = os.path.dirname(__file__)
sys.path.append(os.path.join(scriptDir, "gtlapi"))
import gtl_api

token = 'eyJhbGciOiJIUzI1NiJ9.eyJpZCI6ImIxOTFiNmNhLTFkZWMtNDc1Yi1iNjE0LWRjMTMzYzY5ZDgyYiIsInNlY3JldCI6ImlFNEpaRmpWa2NJc0lreCtTaUhiY2k0VXN5ZjZiRUN2U2VEWklHOXRMZ2M9In0.VmZkWppQyZ3MLk7kwqVUCCqgzaPh1VM8JmdCXpU4-Qw'
api = gtl_api.GTLAPI(token=token)

branch_dict = {
    'relgrpx': r"\\builds\prerelease\devtools\Nomad\Rel\Grpx",
    'relgrfx': r"\\builds\prerelease\devtools\Nomad\Rel\Grfx",
    'devgrfx': r"\\builds\Nightly\devtools\Nomad\Dev\Grfx"
}
build_types = [
    "NDA",
    "Internal",
    "External",
]
app_names = {
    "relgrfx": "Nomad_RelGrfx_Prerelease_{}_Win_Desktop",
    "relgrpx": "Nomad_RelGrpx_Prerelease_{}_Win_Desktop",
    "devgrfx": "Nomad_DevGrfx_Nightly_{}_Win_Desktop",
}
type_match = {
    "nda": "NDA",
    "internal": "Internal",
    "external": "External"
}


def getLatestBuildVer(buildName):
    buildQryStr = """
    <Search type='build'>
        <Equals> 
            <Field name='ApplicationName'/>
            <Value>{app}</Value>
        </Equals>
    </Search>
    """.format(app=buildName)

    bids = api.Build.SearchBuildsIds(buildQryStr)

    buildId = bids[0] if len(bids) > 0 else 0
    print(f"latest build id is {buildId}")
    return buildId

def downloadBuild(buildId, targetDir):
    packageId = api.Build.GetBuildPackage(buildId)
    package = api.PackageManagement.GetPackage(packageId)
    destDir = package.Name[:-2]+'-1-data'
    destPath = os.path.join(targetDir, destDir)
    gtldownloader = os.path.join(scriptDir, 'gtlfs.exe')
    cmd = f"{gtldownloader} getbuild -K {token} -x -o {destPath} {buildId}"
    os.system(cmd)
    return destDir

def findEXEPath(root, subpath):
    for file in os.listdir(os.path.join(root, subpath, "data")):
        if os.path.splitext(file)[-1] == ".msi":
            return os.path.join(root, subpath, "data", file)
    return False

def findMatchedServerPath(installer):
    serverPath = ''
    # find the build version key
    buildVerstr = installer.split('\\')[-1]
    buildVerkey = re.match("NVIDIA_Nomad_[\d.]+_([\d]+)_*", buildVerstr).groups()[0]
    for branch in branch_dict.keys():
        if branch in installer.lower():
            serverPath = branch_dict[branch]
    for f in os.listdir(serverPath):
        buildPath = os.path.join(serverPath, f)
        if os.path.isdir(buildPath) and buildVerkey in f:
            return buildPath
    return None

def findMatchedBuildVer(appName, ver):
    buildQryStr = """
    <Search type='build'>
        <Equals> 
            <Field name='ApplicationName'/>
            <Value>{app}</Value>
        </Equals>
    </Search>
    """.format(app=appName)

    bids = api.Build.SearchBuildsIds(buildQryStr)

    for buildId in bids:
        packageId = api.Build.GetBuildPackage(buildId)
        package = api.PackageManagement.GetPackage(packageId)
        packageName = package.Name
        if ver in packageName:
            return buildId
    return False

def writeLog(installer):
    #serverPath = findMatchedServerPath(installer)
    with open(os.path.join(root, 'install_build.log'), "a+") as f:
        f.write(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        f.write("\n")
        f.write(installer)
        f.write("\n")
        #f.write(serverPath)
        #f.write("\n")

def install_build(path):
    print("Start to install Nomad build in the folder {}".format(path))
    installer = path
    if os.path.isfile(installer) and re.match(".*msi", installer):
        child = subprocess.Popen(["msiexec.exe", "/i", installer, "/passive"])
        child.wait()
        writeLog(path)
        print("Nomad build installation is complete.")
    pass

def installBuild(buildId, buildBranch):
    dest = os.path.join(root, buildBranch)    
    installerDir = downloadBuild(buildId, dest)

    if installerDir:
        installerPath = findEXEPath(dest, installerDir)
        install_build(installerPath)
    else:
        print("the package isn't downloaded.")
    pass

def execute(branch, type, ver, server):
    if ver == 'TOT':
        appName = app_names[branch]
        buildName = appName.format(type)
        buildId = getLatestBuildVer(buildName)
        installBuild(buildId, branch)
    elif branch == '' and server:
        server = server.replace("/", "\\")
        ver = (server.split("\\")[-1]).split("_")[-1]
        buildId = ''
        appName = ''
        for buildbranch in app_names.keys():
            if buildbranch in server.replace("\\",'').lower():
                appName = app_names[buildbranch]
                buildName = appName.format(type)
                buildId = findMatchedBuildVer(buildName, ver)
                installBuild(buildId, buildbranch)

if __name__ == "__main__":
    branch = ''
    ver = ''
    server = ''
    if len(sys.argv) > 1 and sys.argv[1] in branch_dict.keys() and sys.argv[2].lower() in type_match.keys() and type_match[sys.argv[2].lower()] in build_types:
        branch = sys.argv[1]
        ver = 'TOT'
        server = branch_dict[branch]
        type = type_match[sys.argv[2].lower()]
    elif len(sys.argv) > 1 and sys.argv[1] and sys.argv[2].lower() in type_match.keys() and type_match[sys.argv[2].lower()] in build_types:
        server = sys.argv[1]
        type = type_match[sys.argv[2].lower()]
    execute(branch, type, ver, server)
