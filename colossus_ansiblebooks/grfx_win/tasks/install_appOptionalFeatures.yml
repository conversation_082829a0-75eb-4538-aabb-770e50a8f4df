---
# Install Graphics Tools, enable D3D12 debug layer
  - name: Check if Graphics Tools is installed
    win_shell: DISM /Online /Get-CapabilityInfo /CapabilityName:Tools.Graphics.DirectX~~~~0.0.1.0
    register: ps_log

  - debug:
      msg: "Graphics Tools is installed"
    when: ps_log.stdout.find('Installed') != -1

  - block:
    - name: Install Graphics Tools
      win_shell: dism /online /add-capability /capabilityname:Tools.Graphics.DirectX~~~~0.0.1.0
      vars:
        ansible_become: yes
        ansible_become_method: runas
        ansible_become_user: "{{ login_user }}"
        ansible_become_pass: "{{ login_pass }}"
        ansible_become_flags: logon_type=new_credentials logon_flags=netcredentials_only
    when: ps_log.stdout.find('Installed') == -1
