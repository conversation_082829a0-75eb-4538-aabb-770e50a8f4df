- name: Run Nsight Systems GUI job for CTK
  hosts: all 
  vars: 
    timestamp: "{{lookup('pipe', 'date +%Y%m%d%H')}}"
  tasks: 
    - name: Remove any installed nsys (yum)
      yum:
        name:
          - nsight-systems*
        state: absent
      ignore_errors: yes
      become: yes
      when: ansible_distribution == 'RedHat'

    - name: Remove any installed CUDA toolkit (apt)
      apt: 
        name:
          - cuda*
        state: absent
      ignore_errors: yes
      become: yes
      when: ansible_distribution == 'Ubuntu'

    - name: Remove any installed CUDA toolkit (yum)
      yum: 
        name:
          - cuda*
        state: absent
      ignore_errors: yes
      become: yes
      when: ansible_distribution == 'RedHat'

    - name: Download CUDA toolkit (deb)
      get_url:
        url: "http://{{ cuda_toolkit_link }}"
        dest: "/tmp/cuda.deb"
        mode: '0644'
      when: ansible_distribution == 'Ubuntu'

    - name: Download CUDA toolkit (rpm)
      get_url:
        url: "http://{{ cuda_toolkit_link }}"
        dest: "/tmp/cuda.rpm"
        mode: '0644'
      when: ansible_distribution == 'RedHat'

    - name: Install CUDA toolkit (deb)
      shell: 
        cmd: "apt install /tmp/cuda.deb && cp $(find /var/cuda-repo*-local -name cuda-*-keyring.gpg) /usr/share/keyrings/ && apt update && apt install -y cuda-toolkit-{{ cuda_minor_version }}"
        executable: /bin/bash
      become: yes 
      when: ansible_distribution == 'Ubuntu'

    - name: Install CUDA toolkit (rpm)
      shell: 
        cmd: "rpm -i /tmp/cuda.rpm && yum clean all && yum -y install cuda-toolkit-{{ cuda_minor_version }}"
      become: yes 
      when: ansible_distribution == 'RedHat'

    - name: Install packages
      apt:
        name:
          - libxml2-dev
          - libxslt-dev
          - mesa-utils
          - python3.10
          - python3-pip
        state: present
      become: yes

    - name: Reboot before task
      become: yes
      reboot:

    - name: Check if /work exists
      stat:
        path: /work
      register: dir_work

    - name: Make dir if /work doesn't exist
      shell: |
        mkdir -p /work
        chmod a+rw /work
      become: true
      when: dir_work.stat.isdir is not defined or not dir_work.stat.isdir

    - name: Cleanup /work files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "/work/nsys_gui_automation"
        - "/work/nsys_gui_automation_lin.zip"
        - "/work/squish-for-qt-7.1.0"
        - "/work/squish-for-qt-7.1.0.tar"
        - "/work/reports.zip"
      become: yes

    - name: Copy /root/DevToolsQaAutomation/playbooks/colossus_ansiblebooks/grfx_win/files/download_gtl_build.py to remote /work
      ansible.builtin.copy:
        src: '/root/DevToolsQaAutomation/playbooks/colossus_ansiblebooks/grfx_win/files/download_gtl_build.py'
        dest: '/work'
        mode: '0766'

    - name: Download and untar squish7.1.1_for_qt6.3 for DTQA-Squish-Linux from gtl
      shell: cd /work && python3 download_gtl_build.py -app DTQA-Squish-Linux -build squish7.1.1_for_qt6.3 -untardir /work/SQUISH_DOWNLOAD
    
    - name: copy squish to ~/squish from /work/SQUISH_DOWNLOAD
      shell: rm -rf ~/squish && cp -r $(find /work/SQUISH_DOWNLOAD/ -name data | head -n 1) ~/squish

    - name: run squishconfig
      shell: ~/squish/bin/squishconfig --licensekey=qafarm-sh01:49345 --python=3
      ignore_errors: yes

    - name: Copy {{ gui_automation_lin_zip }} to remote /work
      ansible.builtin.copy:
        src: '{{ gui_automation_lin_zip }}'
        dest: '/work'
        mode: '0766'

    - name: Unarchive /work/nsys_gui_automation.zip to /work on the remote machine
      ansible.builtin.unarchive:
        src: /work/nsys_gui_automation.zip
        dest: /work
        remote_src: yes

    - name: run chmod +x /work/nsys_gui_automation/src/guitest/TargetPlatformInfo_glx
      shell: chmod +x /work/nsys_gui_automation/src/guitest/TargetPlatformInfo_glx
      ignore_errors: yes

    - name: run sed -i 's/\r//' /work/nsys_gui_automation/run_lin_suites.sh
      shell: sed -i 's/\r//' /work/nsys_gui_automation/run_lin_suites.sh
      ignore_errors: yes

    - name: run sudo sh -c 'echo 1 >/proc/sys/kernel/perf_event_paranoid'
      become: yes
      shell: sh -c 'echo 1 >/proc/sys/kernel/perf_event_paranoid'

    - name: find full path for nsys-ui
      ansible.builtin.find:
        paths: /opt/nvidia/nsight-systems/{{ release_brief }}
        file_type: file
        use_regex: yes
        recurse: yes
        patterns:
          - '^nsys-ui.bin$'
          # - '^_[0-9]{2,4}_.*.log$'
      register: find_result

    - name: Debug nsys-ui paths
      debug:
        var: find_result.files[-1].path

    - name: launch linux guiautomation test - DISPLAY=:0 gnome-terminal --wait -- bash -c 'bash /work/nsys_gui_automation/run_lin_suites.sh'
      #become: yes
      shell:
      args: 
        cmd: DISPLAY=:0 gnome-terminal --wait -- bash -c 'bash /work/nsys_gui_automation/run_lin_suites.sh'
        executable: /bin/bash
      ignore_errors: true
      async: 10800   #the maxiam execution time for the step
      poll: 30
